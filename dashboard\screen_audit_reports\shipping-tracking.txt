📄 Route: shipping/tracking
📂 Controller: controller\shipping\tracking.php
🧱 Models used (1):
   ✅ shipping/tracking (14 functions)
🎨 Twig templates (1):
   ✅ view\template\shipping\tracking.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\shipping\tracking.php (38 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\shipping\tracking.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - date_format_short
   - error_permission
   - error_update_failed
   - heading_title
   - text_bulk_update_success
   - text_home
   - text_shipping
   - text_status_delivered
   - text_status_failed
   - text_status_in_transit
   - text_status_out_for_delivery
   - text_status_pending
   - text_status_returned
   - text_success_update
   - text_view_shipment

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_shipping

❌ Missing in English (15):
   - date_format_short
   - error_permission
   - error_update_failed
   - heading_title
   - text_bulk_update_success
   - text_home
   - text_shipping
   - text_status_delivered
   - text_status_failed
   - text_status_in_transit
   - text_status_out_for_delivery
   - text_status_pending
   - text_status_returned
   - text_success_update
   - text_view_shipment

🗄️ Database Tables Used (2):
   ❌ DESC
   ❌ carrier

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - date_format_short
      - text_home
      - text_shipping
   🟡 MISSING_ENGLISH_VARIABLES: 15 items
      - text_view_shipment
      - text_home
      - text_status_delivered
      - text_status_in_transit
      - error_permission
   🔴 INVALID_DATABASE_TABLES: 2 items
      - carrier
      - DESC

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 15 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:18
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.