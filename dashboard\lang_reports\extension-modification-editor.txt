📄 Route: extension/modification/editor
📂 Controller: controller\extension\modification\editor.php
🧱 Models used (2):
   - extension/modification/editor
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\modification\editor.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (1):
   - language\en-gb\extension\modification\editor.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_return
   - error_code_used
   - error_exception
   - error_file
   - error_file_tag
   - error_headers
   - error_modification_id
   - error_permission
   - error_xml
   - heading_title
   - text_erase_data
   - text_erase_image
   - text_erase_theme
   - text_home
   - text_modifications
   - text_new
   - text_success_add
   - text_success_edit

❌ Missing in Arabic:
   - button_return
   - error_code_used
   - error_exception
   - error_file
   - error_file_tag
   - error_headers
   - error_modification_id
   - error_permission
   - error_xml
   - heading_title
   - text_erase_data
   - text_erase_image
   - text_erase_theme
   - text_home
   - text_modifications
   - text_new
   - text_success_add
   - text_success_edit

❌ Missing in English:
   - button_return
   - error_code_used
   - error_exception
   - error_file
   - error_file_tag
   - error_headers
   - error_modification_id
   - error_permission
   - error_xml
   - heading_title
   - text_erase_data
   - text_erase_image
   - text_erase_theme
   - text_home
   - text_modifications
   - text_new
   - text_success_add
   - text_success_edit

💡 Suggested Arabic Additions:
   - button_return = ""  # TODO: ترجمة عربية
   - error_code_used = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_file = ""  # TODO: ترجمة عربية
   - error_file_tag = ""  # TODO: ترجمة عربية
   - error_headers = ""  # TODO: ترجمة عربية
   - error_modification_id = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_xml = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_erase_data = ""  # TODO: ترجمة عربية
   - text_erase_image = ""  # TODO: ترجمة عربية
   - text_erase_theme = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_modifications = ""  # TODO: ترجمة عربية
   - text_new = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_return = ""  # TODO: English translation
   - error_code_used = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_file = ""  # TODO: English translation
   - error_file_tag = ""  # TODO: English translation
   - error_headers = ""  # TODO: English translation
   - error_modification_id = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_xml = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_erase_data = ""  # TODO: English translation
   - text_erase_image = ""  # TODO: English translation
   - text_erase_theme = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_modifications = ""  # TODO: English translation
   - text_new = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
