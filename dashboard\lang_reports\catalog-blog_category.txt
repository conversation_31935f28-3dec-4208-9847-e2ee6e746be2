📄 Route: catalog/blog_category
📂 Controller: controller\catalog\blog_category.php
🧱 Models used (2):
   - catalog/blog_category
   - tool/image
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\catalog\blog_category.php
🇬🇧 English Language Files (1):
   - language\en-gb\catalog\blog_category.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_name
   - error_parent_self
   - error_permission
   - error_slug_exists
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in Arabic:
   - error_name
   - error_parent_self
   - error_permission
   - error_slug_exists
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in English:
   - error_name
   - error_parent_self
   - error_permission
   - error_slug_exists
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success_add
   - text_success_delete
   - text_success_edit

💡 Suggested Arabic Additions:
   - error_name = ""  # TODO: ترجمة عربية
   - error_parent_self = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_slug_exists = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_name = ""  # TODO: English translation
   - error_parent_self = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_slug_exists = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
