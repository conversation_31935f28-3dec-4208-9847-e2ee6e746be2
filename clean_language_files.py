#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
from collections import OrderedDict

def clean_language_file(input_file, output_file):
    """تنظيف ملف اللغة من التكرارات مع الحفاظ على الترتيب"""
    
    variables = OrderedDict()
    comments = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    current_comment = []
    
    for line in lines:
        line = line.rstrip()
        
        # إذا كان السطر تعليق
        if line.startswith('//') or line.startswith('#') or line.strip() == '':
            current_comment.append(line)
            continue
        
        # إذا كان السطر يحتوي على متغير لغة
        match = re.match(r"\$_\['([^']+)'\]\s*=\s*'([^']*)'", line)
        if match:
            var_name = match.group(1)
            var_value = match.group(2)
            
            # إذا لم يكن المتغير موجود من قبل، أضفه مع التعليقات
            if var_name not in variables:
                if current_comment:
                    variables[var_name] = {
                        'value': var_value,
                        'comments': current_comment.copy()
                    }
                else:
                    variables[var_name] = {
                        'value': var_value,
                        'comments': []
                    }
            
            current_comment = []
            continue
        
        # إذا كان السطر يحتوي على <?php أو ?>
        if '<?php' in line or '?>' in line:
            current_comment.append(line)
            continue
    
    # كتابة الملف الجديد
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("<?php\n")
        f.write("// Language file: " + output_file.split('/')[-1] + "\n")
        f.write("// Auto-generated clean version without duplicates\n\n")
        
        for var_name, data in variables.items():
            # كتابة التعليقات
            for comment in data['comments']:
                if comment.strip():
                    f.write(comment + "\n")
            
            # كتابة المتغير
            f.write(f"$_['{var_name}'] = '{data['value']}';\n")
        
        f.write("\n?>")

if __name__ == "__main__":
    # تنظيف ملف اللغة العربية
    clean_language_file(
        'dashboard/language/ar/common/column_left.php',
        'dashboard/language/ar/common/column_left_clean.php'
    )
    
    # تنظيف ملف اللغة الإنجليزية
    clean_language_file(
        'dashboard/language/en-gb/common/column_left.php',
        'dashboard/language/en-gb/common/column_left_clean.php'
    )
    
    print("✅ تم تنظيف ملفات اللغة وإزالة التكرارات")
    print("📁 الملفات الجديدة:")
    print("   - dashboard/language/ar/common/column_left_clean.php")
    print("   - dashboard/language/en-gb/common/column_left_clean.php")
