📄 Route: common/header
📂 Controller: controller\common\header.php
🧱 Models used (15):
   ✅ user/user (47 functions)
   ✅ tool/image (1 functions)
   ✅ setting/store (14 functions)
   ✅ core/central_service_manager (60 functions)
   ✅ communication/unified_notification (16 functions)
   ✅ communication/messages (14 functions)
   ✅ communication/teams (17 functions)
   ✅ inventory/product (78 functions)
   ✅ workflow/task (15 functions)
   ❌ communication/message (0 functions)
   ✅ workflow/approval (15 functions)
   ✅ workflow/workflow (30 functions)
   ✅ unified_document (16 functions)
   ❌ security/security_log (0 functions)
   ✅ common/dashboard (323 functions)
🎨 Twig templates (1):
   ✅ view\template\common\header.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\common\header.php (185 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\common\header.php (182 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (43):
   - base
   - error_loading_notifications
   - error_permission
   - error_updating_notification
   - error_updating_notifications
   - firstname
   - image
   - keywords
   - lastname
   - logout
   - profile
   - text_approval_request
   - text_current_stock
   - text_days_ago
   - text_due
   - text_expires_in
   - text_minimum_limit
   - text_now
   - text_overdue_task
   - title
   ... و 23 متغير آخر

❌ Missing in Arabic (16):
   - base
   - code
   - description
   - direction
   - firstname
   - home
   - image
   - keywords
   - lang
   - logout
   - profile
   - script
   - title
   - user_token
   - username
   ... و 1 متغير آخر

❌ Missing in English (16):
   - base
   - code
   - description
   - direction
   - firstname
   - home
   - image
   - keywords
   - lang
   - logout
   - profile
   - script
   - title
   - user_token
   - username
   ... و 1 متغير آخر

🗄️ Database Tables Used (23):
   ❌ Rate
   ❌ cod_document
   ✅ cod_document_permission
   ❌ cod_project
   ✅ cod_task
   ❌ cod_team
   ❌ cod_team_member
   ✅ cod_unified_workflow
   ✅ cod_user
   ✅ cod_user_group
   ❌ cod_user_to_group
   ✅ cod_workflow_approval
   ✅ cod_workflow_request
   ✅ cod_workflow_step
   ❌ current
   ❌ instance
   ❌ node
   ❌ quantity
   ❌ schedule
   ❌ template
   ❌ the
   ❌ via
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 16 items
      - home
      - code
      - user_token
      - description
      - profile
   🟡 MISSING_ENGLISH_VARIABLES: 16 items
      - home
      - code
      - user_token
      - description
      - profile
   🔴 INVALID_DATABASE_TABLES: 15 items
      - workflow
      - cod_project
      - node
      - instance
      - current
   🟢 MISSING_MODEL_FILES: 2 items
      - communication/message
      - security/security_log

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 16 متغير عربي و 16 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 15 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:53
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.