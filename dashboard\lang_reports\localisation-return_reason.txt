📄 Route: localisation/return_reason
📂 Controller: controller\localisation\return_reason.php
🧱 Models used (3):
   - localisation/language
   - localisation/return_reason
   - sale/return
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\localisation\return_reason.php
🇬🇧 English Language Files (1):
   - language\en-gb\localisation\return_reason.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_name
   - error_permission
   - error_return
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_name
   - error_permission
   - error_return
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_name
   - error_permission
   - error_return
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_return = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_return = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
