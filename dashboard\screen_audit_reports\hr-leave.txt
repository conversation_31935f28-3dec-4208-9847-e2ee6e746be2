📄 Route: hr/leave
📂 Controller: controller\hr\leave.php
🧱 Models used (2):
   ✅ hr/leave (7 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\hr\leave.php (41 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\hr\leave.php (41 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (42):
   - button_close
   - button_filter
   - column_employee
   - column_leave_type
   - column_start_date
   - error_not_found
   - error_permission
   - text_ajax_error
   - text_all_leave_types
   - text_all_statuses
   - text_date_end
   - text_filter
   - text_leave_list
   - text_reason
   - text_select_employee
   - text_select_leave_type
   - text_status
   - text_status_approved
   - text_status_cancelled
   - text_status_pending
   ... و 22 متغير آخر

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (1):
   - text_home

🗄️ Database Tables Used (3):
   ✅ cod_leave_request
   ✅ cod_leave_type
   ✅ cod_user

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 1 items
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 1 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:03
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.