📄 Route: reports/profitability_analysis
📂 Controller: controller\reports\profitability_analysis.php
🧱 Models used (1):
   - reports/profitability_analysis
🎨 Twig templates (1):
   - view\template\reports\profitability_analysis.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_date_end
   - error_date_range
   - error_date_start
   - error_permission
   - heading_title
   - text_by_category
   - text_by_customer
   - text_by_location
   - text_by_period
   - text_by_product
   - text_by_salesperson
   - text_category_profitability
   - text_comparative
   - text_customer_profitability
   - text_detailed
   - text_home
   - text_overview
   - text_period_profitability
   - text_product_profitability
   - text_trend

❌ Missing in Arabic:
   - date_format_short
   - error_date_end
   - error_date_range
   - error_date_start
   - error_permission
   - heading_title
   - text_by_category
   - text_by_customer
   - text_by_location
   - text_by_period
   - text_by_product
   - text_by_salesperson
   - text_category_profitability
   - text_comparative
   - text_customer_profitability
   - text_detailed
   - text_home
   - text_overview
   - text_period_profitability
   - text_product_profitability
   - text_trend

❌ Missing in English:
   - date_format_short
   - error_date_end
   - error_date_range
   - error_date_start
   - error_permission
   - heading_title
   - text_by_category
   - text_by_customer
   - text_by_location
   - text_by_period
   - text_by_product
   - text_by_salesperson
   - text_category_profitability
   - text_comparative
   - text_customer_profitability
   - text_detailed
   - text_home
   - text_overview
   - text_period_profitability
   - text_product_profitability
   - text_trend

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_date_start = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_by_category = ""  # TODO: ترجمة عربية
   - text_by_customer = ""  # TODO: ترجمة عربية
   - text_by_location = ""  # TODO: ترجمة عربية
   - text_by_period = ""  # TODO: ترجمة عربية
   - text_by_product = ""  # TODO: ترجمة عربية
   - text_by_salesperson = ""  # TODO: ترجمة عربية
   - text_category_profitability = ""  # TODO: ترجمة عربية
   - text_comparative = ""  # TODO: ترجمة عربية
   - text_customer_profitability = ""  # TODO: ترجمة عربية
   - text_detailed = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_overview = ""  # TODO: ترجمة عربية
   - text_period_profitability = ""  # TODO: ترجمة عربية
   - text_product_profitability = ""  # TODO: ترجمة عربية
   - text_trend = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_date_end = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_date_start = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_by_category = ""  # TODO: English translation
   - text_by_customer = ""  # TODO: English translation
   - text_by_location = ""  # TODO: English translation
   - text_by_period = ""  # TODO: English translation
   - text_by_product = ""  # TODO: English translation
   - text_by_salesperson = ""  # TODO: English translation
   - text_category_profitability = ""  # TODO: English translation
   - text_comparative = ""  # TODO: English translation
   - text_customer_profitability = ""  # TODO: English translation
   - text_detailed = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_overview = ""  # TODO: English translation
   - text_period_profitability = ""  # TODO: English translation
   - text_product_profitability = ""  # TODO: English translation
   - text_trend = ""  # TODO: English translation
