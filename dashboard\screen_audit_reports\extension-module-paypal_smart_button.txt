📄 Route: extension/module/paypal_smart_button
📂 Controller: controller\extension\module\paypal_smart_button.php
🧱 Models used (2):
   ✅ extension/module/paypal_smart_button (1 functions)
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\paypal_smart_button.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\module\paypal_smart_button.php (55 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\paypal_smart_button.php (55 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (37):
   - action
   - cancel
   - column_left
   - entry_button_color
   - entry_button_label
   - entry_button_shape
   - entry_button_tagline
   - entry_insert_type
   - entry_message_flex_color
   - entry_message_layout
   - entry_message_size
   - entry_message_text_size
   - error_permission
   - header
   - help_message_status
   - message_flex_ratio
   - text_disabled
   - text_extensions
   - text_general
   - text_home
   ... و 17 متغير آخر

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - message_flex_ratio
   - message_text_size
   - text_disabled
   - text_enabled
   - text_home

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - message_flex_ratio
   - message_text_size
   - text_disabled
   - text_enabled
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_save
      - error_warning
      - column_left
      - action
      - text_disabled
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - error_warning
      - column_left
      - action
      - text_disabled

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:24
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.