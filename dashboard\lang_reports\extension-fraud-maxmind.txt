📄 Route: extension/fraud/maxmind
📂 Controller: controller\extension\fraud\maxmind.php
🧱 Models used (3):
   - extension/fraud/maxmind
   - localisation/order_status
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\fraud\maxmind.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\fraud\maxmind.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\fraud\maxmind.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_key
   - error_permission
   - heading_title
   - help_anonymous_proxy
   - help_bin_country
   - help_bin_match
   - help_bin_name
   - help_bin_name_match
   - help_bin_phone
   - help_bin_phone_match
   - help_carder_email
   - help_city_postal_match
   - help_country_code
   - help_country_match
   - help_customer_phone_in_billing_location
   - help_distance
   - help_error
   - help_explanation
   - help_free_mail
   - help_high_risk_country
   - help_high_risk_password
   - help_high_risk_username
   - help_ip_accuracy_radius
   - help_ip_area_code
   - help_ip_asnum
   - help_ip_city
   - help_ip_city_confidence
   - help_ip_continent_code
   - help_ip_corporate_proxy
   - help_ip_country_confidence
   - help_ip_country_name
   - help_ip_domain
   - help_ip_isp
   - help_ip_latitude
   - help_ip_longitude
   - help_ip_metro_code
   - help_ip_net_speed_cell
   - help_ip_org
   - help_ip_postal_code
   - help_ip_postal_confidence
   - help_ip_region
   - help_ip_region_confidence
   - help_ip_region_name
   - help_ip_time_zone
   - help_ip_user_type
   - help_is_trans_proxy
   - help_maxmind_id
   - help_proxy_score
   - help_queries_remaining
   - help_risk_score
   - help_score
   - help_ship_city_postal_match
   - help_ship_forward
   - text_anonymous_proxy
   - text_bin_country
   - text_bin_match
   - text_bin_name
   - text_bin_name_match
   - text_bin_phone
   - text_bin_phone_match
   - text_carder_email
   - text_city_postal_match
   - text_country_code
   - text_country_match
   - text_customer_phone_in_billing_location
   - text_distance
   - text_error
   - text_explanation
   - text_extension
   - text_free_mail
   - text_high_risk_country
   - text_high_risk_password
   - text_high_risk_username
   - text_home
   - text_ip_accuracy_radius
   - text_ip_area_code
   - text_ip_asnum
   - text_ip_city
   - text_ip_city_confidence
   - text_ip_continent_code
   - text_ip_corporate_proxy
   - text_ip_country_confidence
   - text_ip_country_name
   - text_ip_domain
   - text_ip_isp
   - text_ip_latitude
   - text_ip_longitude
   - text_ip_metro_code
   - text_ip_net_speed_cell
   - text_ip_org
   - text_ip_postal_code
   - text_ip_postal_confidence
   - text_ip_region
   - text_ip_region_confidence
   - text_ip_region_name
   - text_ip_time_zone
   - text_ip_user_type
   - text_is_trans_proxy
   - text_maxmind_id
   - text_proxy_score
   - text_queries_remaining
   - text_risk_score
   - text_score
   - text_ship_city_postal_match
   - text_ship_forward
   - text_success

❌ Missing in Arabic:
   - error_key
   - error_permission
   - heading_title
   - help_anonymous_proxy
   - help_bin_country
   - help_bin_match
   - help_bin_name
   - help_bin_name_match
   - help_bin_phone
   - help_bin_phone_match
   - help_carder_email
   - help_city_postal_match
   - help_country_code
   - help_country_match
   - help_customer_phone_in_billing_location
   - help_distance
   - help_error
   - help_explanation
   - help_free_mail
   - help_high_risk_country
   - help_high_risk_password
   - help_high_risk_username
   - help_ip_accuracy_radius
   - help_ip_area_code
   - help_ip_asnum
   - help_ip_city
   - help_ip_city_confidence
   - help_ip_continent_code
   - help_ip_corporate_proxy
   - help_ip_country_confidence
   - help_ip_country_name
   - help_ip_domain
   - help_ip_isp
   - help_ip_latitude
   - help_ip_longitude
   - help_ip_metro_code
   - help_ip_net_speed_cell
   - help_ip_org
   - help_ip_postal_code
   - help_ip_postal_confidence
   - help_ip_region
   - help_ip_region_confidence
   - help_ip_region_name
   - help_ip_time_zone
   - help_ip_user_type
   - help_is_trans_proxy
   - help_maxmind_id
   - help_proxy_score
   - help_queries_remaining
   - help_risk_score
   - help_score
   - help_ship_city_postal_match
   - help_ship_forward
   - text_anonymous_proxy
   - text_bin_country
   - text_bin_match
   - text_bin_name
   - text_bin_name_match
   - text_bin_phone
   - text_bin_phone_match
   - text_carder_email
   - text_city_postal_match
   - text_country_code
   - text_country_match
   - text_customer_phone_in_billing_location
   - text_distance
   - text_error
   - text_explanation
   - text_extension
   - text_free_mail
   - text_high_risk_country
   - text_high_risk_password
   - text_high_risk_username
   - text_home
   - text_ip_accuracy_radius
   - text_ip_area_code
   - text_ip_asnum
   - text_ip_city
   - text_ip_city_confidence
   - text_ip_continent_code
   - text_ip_corporate_proxy
   - text_ip_country_confidence
   - text_ip_country_name
   - text_ip_domain
   - text_ip_isp
   - text_ip_latitude
   - text_ip_longitude
   - text_ip_metro_code
   - text_ip_net_speed_cell
   - text_ip_org
   - text_ip_postal_code
   - text_ip_postal_confidence
   - text_ip_region
   - text_ip_region_confidence
   - text_ip_region_name
   - text_ip_time_zone
   - text_ip_user_type
   - text_is_trans_proxy
   - text_maxmind_id
   - text_proxy_score
   - text_queries_remaining
   - text_risk_score
   - text_score
   - text_ship_city_postal_match
   - text_ship_forward
   - text_success

❌ Missing in English:
   - error_key
   - error_permission
   - heading_title
   - help_anonymous_proxy
   - help_bin_country
   - help_bin_match
   - help_bin_name
   - help_bin_name_match
   - help_bin_phone
   - help_bin_phone_match
   - help_carder_email
   - help_city_postal_match
   - help_country_code
   - help_country_match
   - help_customer_phone_in_billing_location
   - help_distance
   - help_error
   - help_explanation
   - help_free_mail
   - help_high_risk_country
   - help_high_risk_password
   - help_high_risk_username
   - help_ip_accuracy_radius
   - help_ip_area_code
   - help_ip_asnum
   - help_ip_city
   - help_ip_city_confidence
   - help_ip_continent_code
   - help_ip_corporate_proxy
   - help_ip_country_confidence
   - help_ip_country_name
   - help_ip_domain
   - help_ip_isp
   - help_ip_latitude
   - help_ip_longitude
   - help_ip_metro_code
   - help_ip_net_speed_cell
   - help_ip_org
   - help_ip_postal_code
   - help_ip_postal_confidence
   - help_ip_region
   - help_ip_region_confidence
   - help_ip_region_name
   - help_ip_time_zone
   - help_ip_user_type
   - help_is_trans_proxy
   - help_maxmind_id
   - help_proxy_score
   - help_queries_remaining
   - help_risk_score
   - help_score
   - help_ship_city_postal_match
   - help_ship_forward
   - text_anonymous_proxy
   - text_bin_country
   - text_bin_match
   - text_bin_name
   - text_bin_name_match
   - text_bin_phone
   - text_bin_phone_match
   - text_carder_email
   - text_city_postal_match
   - text_country_code
   - text_country_match
   - text_customer_phone_in_billing_location
   - text_distance
   - text_error
   - text_explanation
   - text_extension
   - text_free_mail
   - text_high_risk_country
   - text_high_risk_password
   - text_high_risk_username
   - text_home
   - text_ip_accuracy_radius
   - text_ip_area_code
   - text_ip_asnum
   - text_ip_city
   - text_ip_city_confidence
   - text_ip_continent_code
   - text_ip_corporate_proxy
   - text_ip_country_confidence
   - text_ip_country_name
   - text_ip_domain
   - text_ip_isp
   - text_ip_latitude
   - text_ip_longitude
   - text_ip_metro_code
   - text_ip_net_speed_cell
   - text_ip_org
   - text_ip_postal_code
   - text_ip_postal_confidence
   - text_ip_region
   - text_ip_region_confidence
   - text_ip_region_name
   - text_ip_time_zone
   - text_ip_user_type
   - text_is_trans_proxy
   - text_maxmind_id
   - text_proxy_score
   - text_queries_remaining
   - text_risk_score
   - text_score
   - text_ship_city_postal_match
   - text_ship_forward
   - text_success

💡 Suggested Arabic Additions:
   - error_key = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - help_anonymous_proxy = ""  # TODO: ترجمة عربية
   - help_bin_country = ""  # TODO: ترجمة عربية
   - help_bin_match = ""  # TODO: ترجمة عربية
   - help_bin_name = ""  # TODO: ترجمة عربية
   - help_bin_name_match = ""  # TODO: ترجمة عربية
   - help_bin_phone = ""  # TODO: ترجمة عربية
   - help_bin_phone_match = ""  # TODO: ترجمة عربية
   - help_carder_email = ""  # TODO: ترجمة عربية
   - help_city_postal_match = ""  # TODO: ترجمة عربية
   - help_country_code = ""  # TODO: ترجمة عربية
   - help_country_match = ""  # TODO: ترجمة عربية
   - help_customer_phone_in_billing_location = ""  # TODO: ترجمة عربية
   - help_distance = ""  # TODO: ترجمة عربية
   - help_error = ""  # TODO: ترجمة عربية
   - help_explanation = ""  # TODO: ترجمة عربية
   - help_free_mail = ""  # TODO: ترجمة عربية
   - help_high_risk_country = ""  # TODO: ترجمة عربية
   - help_high_risk_password = ""  # TODO: ترجمة عربية
   - help_high_risk_username = ""  # TODO: ترجمة عربية
   - help_ip_accuracy_radius = ""  # TODO: ترجمة عربية
   - help_ip_area_code = ""  # TODO: ترجمة عربية
   - help_ip_asnum = ""  # TODO: ترجمة عربية
   - help_ip_city = ""  # TODO: ترجمة عربية
   - help_ip_city_confidence = ""  # TODO: ترجمة عربية
   - help_ip_continent_code = ""  # TODO: ترجمة عربية
   - help_ip_corporate_proxy = ""  # TODO: ترجمة عربية
   - help_ip_country_confidence = ""  # TODO: ترجمة عربية
   - help_ip_country_name = ""  # TODO: ترجمة عربية
   - help_ip_domain = ""  # TODO: ترجمة عربية
   - help_ip_isp = ""  # TODO: ترجمة عربية
   - help_ip_latitude = ""  # TODO: ترجمة عربية
   - help_ip_longitude = ""  # TODO: ترجمة عربية
   - help_ip_metro_code = ""  # TODO: ترجمة عربية
   - help_ip_net_speed_cell = ""  # TODO: ترجمة عربية
   - help_ip_org = ""  # TODO: ترجمة عربية
   - help_ip_postal_code = ""  # TODO: ترجمة عربية
   - help_ip_postal_confidence = ""  # TODO: ترجمة عربية
   - help_ip_region = ""  # TODO: ترجمة عربية
   - help_ip_region_confidence = ""  # TODO: ترجمة عربية
   - help_ip_region_name = ""  # TODO: ترجمة عربية
   - help_ip_time_zone = ""  # TODO: ترجمة عربية
   - help_ip_user_type = ""  # TODO: ترجمة عربية
   - help_is_trans_proxy = ""  # TODO: ترجمة عربية
   - help_maxmind_id = ""  # TODO: ترجمة عربية
   - help_proxy_score = ""  # TODO: ترجمة عربية
   - help_queries_remaining = ""  # TODO: ترجمة عربية
   - help_risk_score = ""  # TODO: ترجمة عربية
   - help_score = ""  # TODO: ترجمة عربية
   - help_ship_city_postal_match = ""  # TODO: ترجمة عربية
   - help_ship_forward = ""  # TODO: ترجمة عربية
   - text_anonymous_proxy = ""  # TODO: ترجمة عربية
   - text_bin_country = ""  # TODO: ترجمة عربية
   - text_bin_match = ""  # TODO: ترجمة عربية
   - text_bin_name = ""  # TODO: ترجمة عربية
   - text_bin_name_match = ""  # TODO: ترجمة عربية
   - text_bin_phone = ""  # TODO: ترجمة عربية
   - text_bin_phone_match = ""  # TODO: ترجمة عربية
   - text_carder_email = ""  # TODO: ترجمة عربية
   - text_city_postal_match = ""  # TODO: ترجمة عربية
   - text_country_code = ""  # TODO: ترجمة عربية
   - text_country_match = ""  # TODO: ترجمة عربية
   - text_customer_phone_in_billing_location = ""  # TODO: ترجمة عربية
   - text_distance = ""  # TODO: ترجمة عربية
   - text_error = ""  # TODO: ترجمة عربية
   - text_explanation = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_free_mail = ""  # TODO: ترجمة عربية
   - text_high_risk_country = ""  # TODO: ترجمة عربية
   - text_high_risk_password = ""  # TODO: ترجمة عربية
   - text_high_risk_username = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_ip_accuracy_radius = ""  # TODO: ترجمة عربية
   - text_ip_area_code = ""  # TODO: ترجمة عربية
   - text_ip_asnum = ""  # TODO: ترجمة عربية
   - text_ip_city = ""  # TODO: ترجمة عربية
   - text_ip_city_confidence = ""  # TODO: ترجمة عربية
   - text_ip_continent_code = ""  # TODO: ترجمة عربية
   - text_ip_corporate_proxy = ""  # TODO: ترجمة عربية
   - text_ip_country_confidence = ""  # TODO: ترجمة عربية
   - text_ip_country_name = ""  # TODO: ترجمة عربية
   - text_ip_domain = ""  # TODO: ترجمة عربية
   - text_ip_isp = ""  # TODO: ترجمة عربية
   - text_ip_latitude = ""  # TODO: ترجمة عربية
   - text_ip_longitude = ""  # TODO: ترجمة عربية
   - text_ip_metro_code = ""  # TODO: ترجمة عربية
   - text_ip_net_speed_cell = ""  # TODO: ترجمة عربية
   - text_ip_org = ""  # TODO: ترجمة عربية
   - text_ip_postal_code = ""  # TODO: ترجمة عربية
   - text_ip_postal_confidence = ""  # TODO: ترجمة عربية
   - text_ip_region = ""  # TODO: ترجمة عربية
   - text_ip_region_confidence = ""  # TODO: ترجمة عربية
   - text_ip_region_name = ""  # TODO: ترجمة عربية
   - text_ip_time_zone = ""  # TODO: ترجمة عربية
   - text_ip_user_type = ""  # TODO: ترجمة عربية
   - text_is_trans_proxy = ""  # TODO: ترجمة عربية
   - text_maxmind_id = ""  # TODO: ترجمة عربية
   - text_proxy_score = ""  # TODO: ترجمة عربية
   - text_queries_remaining = ""  # TODO: ترجمة عربية
   - text_risk_score = ""  # TODO: ترجمة عربية
   - text_score = ""  # TODO: ترجمة عربية
   - text_ship_city_postal_match = ""  # TODO: ترجمة عربية
   - text_ship_forward = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_key = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - help_anonymous_proxy = ""  # TODO: English translation
   - help_bin_country = ""  # TODO: English translation
   - help_bin_match = ""  # TODO: English translation
   - help_bin_name = ""  # TODO: English translation
   - help_bin_name_match = ""  # TODO: English translation
   - help_bin_phone = ""  # TODO: English translation
   - help_bin_phone_match = ""  # TODO: English translation
   - help_carder_email = ""  # TODO: English translation
   - help_city_postal_match = ""  # TODO: English translation
   - help_country_code = ""  # TODO: English translation
   - help_country_match = ""  # TODO: English translation
   - help_customer_phone_in_billing_location = ""  # TODO: English translation
   - help_distance = ""  # TODO: English translation
   - help_error = ""  # TODO: English translation
   - help_explanation = ""  # TODO: English translation
   - help_free_mail = ""  # TODO: English translation
   - help_high_risk_country = ""  # TODO: English translation
   - help_high_risk_password = ""  # TODO: English translation
   - help_high_risk_username = ""  # TODO: English translation
   - help_ip_accuracy_radius = ""  # TODO: English translation
   - help_ip_area_code = ""  # TODO: English translation
   - help_ip_asnum = ""  # TODO: English translation
   - help_ip_city = ""  # TODO: English translation
   - help_ip_city_confidence = ""  # TODO: English translation
   - help_ip_continent_code = ""  # TODO: English translation
   - help_ip_corporate_proxy = ""  # TODO: English translation
   - help_ip_country_confidence = ""  # TODO: English translation
   - help_ip_country_name = ""  # TODO: English translation
   - help_ip_domain = ""  # TODO: English translation
   - help_ip_isp = ""  # TODO: English translation
   - help_ip_latitude = ""  # TODO: English translation
   - help_ip_longitude = ""  # TODO: English translation
   - help_ip_metro_code = ""  # TODO: English translation
   - help_ip_net_speed_cell = ""  # TODO: English translation
   - help_ip_org = ""  # TODO: English translation
   - help_ip_postal_code = ""  # TODO: English translation
   - help_ip_postal_confidence = ""  # TODO: English translation
   - help_ip_region = ""  # TODO: English translation
   - help_ip_region_confidence = ""  # TODO: English translation
   - help_ip_region_name = ""  # TODO: English translation
   - help_ip_time_zone = ""  # TODO: English translation
   - help_ip_user_type = ""  # TODO: English translation
   - help_is_trans_proxy = ""  # TODO: English translation
   - help_maxmind_id = ""  # TODO: English translation
   - help_proxy_score = ""  # TODO: English translation
   - help_queries_remaining = ""  # TODO: English translation
   - help_risk_score = ""  # TODO: English translation
   - help_score = ""  # TODO: English translation
   - help_ship_city_postal_match = ""  # TODO: English translation
   - help_ship_forward = ""  # TODO: English translation
   - text_anonymous_proxy = ""  # TODO: English translation
   - text_bin_country = ""  # TODO: English translation
   - text_bin_match = ""  # TODO: English translation
   - text_bin_name = ""  # TODO: English translation
   - text_bin_name_match = ""  # TODO: English translation
   - text_bin_phone = ""  # TODO: English translation
   - text_bin_phone_match = ""  # TODO: English translation
   - text_carder_email = ""  # TODO: English translation
   - text_city_postal_match = ""  # TODO: English translation
   - text_country_code = ""  # TODO: English translation
   - text_country_match = ""  # TODO: English translation
   - text_customer_phone_in_billing_location = ""  # TODO: English translation
   - text_distance = ""  # TODO: English translation
   - text_error = ""  # TODO: English translation
   - text_explanation = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_free_mail = ""  # TODO: English translation
   - text_high_risk_country = ""  # TODO: English translation
   - text_high_risk_password = ""  # TODO: English translation
   - text_high_risk_username = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_ip_accuracy_radius = ""  # TODO: English translation
   - text_ip_area_code = ""  # TODO: English translation
   - text_ip_asnum = ""  # TODO: English translation
   - text_ip_city = ""  # TODO: English translation
   - text_ip_city_confidence = ""  # TODO: English translation
   - text_ip_continent_code = ""  # TODO: English translation
   - text_ip_corporate_proxy = ""  # TODO: English translation
   - text_ip_country_confidence = ""  # TODO: English translation
   - text_ip_country_name = ""  # TODO: English translation
   - text_ip_domain = ""  # TODO: English translation
   - text_ip_isp = ""  # TODO: English translation
   - text_ip_latitude = ""  # TODO: English translation
   - text_ip_longitude = ""  # TODO: English translation
   - text_ip_metro_code = ""  # TODO: English translation
   - text_ip_net_speed_cell = ""  # TODO: English translation
   - text_ip_org = ""  # TODO: English translation
   - text_ip_postal_code = ""  # TODO: English translation
   - text_ip_postal_confidence = ""  # TODO: English translation
   - text_ip_region = ""  # TODO: English translation
   - text_ip_region_confidence = ""  # TODO: English translation
   - text_ip_region_name = ""  # TODO: English translation
   - text_ip_time_zone = ""  # TODO: English translation
   - text_ip_user_type = ""  # TODO: English translation
   - text_is_trans_proxy = ""  # TODO: English translation
   - text_maxmind_id = ""  # TODO: English translation
   - text_proxy_score = ""  # TODO: English translation
   - text_queries_remaining = ""  # TODO: English translation
   - text_risk_score = ""  # TODO: English translation
   - text_score = ""  # TODO: English translation
   - text_ship_city_postal_match = ""  # TODO: English translation
   - text_ship_forward = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
