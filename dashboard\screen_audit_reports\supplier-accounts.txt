📄 Route: supplier/accounts
📂 Controller: controller\supplier\accounts.php
🧱 Models used (2):
   ✅ supplier/accounts (18 functions)
   ✅ supplier/supplier (21 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\supplier\accounts.php (158 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\supplier\accounts.php (158 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (29):
   - column_account_number
   - column_credit_limit
   - column_current_balance
   - column_payment_terms
   - column_supplier
   - date_format_short
   - error_amount
   - error_credit_limit
   - error_missing_data
   - error_payment_date
   - error_payment_method
   - error_permission
   - error_supplier
   - error_transaction_date
   - text_account_details
   - text_credit_limit_updated
   - text_home
   - text_statement
   - text_status_updated
   - text_transaction_success
   ... و 9 متغير آخر

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (3):
   - date_format_short
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.