📄 Route: localisation/tax_rate
📂 Controller: controller\localisation\tax_rate.php
🧱 Models used (4):
   ✅ localisation/tax_rate (8 functions)
   ✅ customer/customer_group (7 functions)
   ✅ localisation/geo_zone (11 functions)
   ✅ localisation/tax_class (8 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\localisation\tax_rate.php (23 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\localisation\tax_rate.php (23 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (13):
   - date_format_short
   - error_name
   - error_permission
   - error_rate
   - error_tax_rule
   - heading_title
   - text_add
   - text_amount
   - text_edit
   - text_home
   - text_pagination
   - text_percent
   - text_success

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (3):
   - date_format_short
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:09
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.