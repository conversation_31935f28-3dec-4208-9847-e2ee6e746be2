<?php
/**
 * نموذج إدارة مستويات المخزون المتطور - Enterprise Grade Plus
 * <AUTHOR> ERP Team - Enhanced by AI Agent
 * @version 4.0 - Enterprise Grade Plus
 * @since 2025-07-20
 */
class ModelInventoryStockLevelEnhanced extends Model {
    private $central_service;
    private $error = array();

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * الحصول على قائمة مستويات المخزون مع الخدمات المركزية
     */
    public function getStockLevels($data = array()) {
        try {
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view_stock_levels',
                'stock_level',
                'عرض قائمة مستويات المخزون مع الفلاتر',
                array(
                    'filters' => $data,
                    'user_id' => isset($this->user) ? $this->user->getId() : 0
                )
            );

            $sql = "SELECT sl.stock_level_id, sl.product_id, sl.branch_id, sl.unit_id, 
                        sl.minimum_stock, sl.reorder_point, sl.maximum_stock, sl.status, sl.notes,
                        pd.name AS product_name, p.model, p.sku, b.name AS branch_name, u.desc_en AS unit_name,
                        
                        -- حساب المخزون الحالي
                        (SELECT COALESCE(SUM(pi.quantity_available), 0) 
                         FROM " . DB_PREFIX . "product_inventory pi 
                         WHERE pi.product_id = sl.product_id AND pi.branch_id = sl.branch_id AND pi.unit_id = sl.unit_id) AS current_stock,
                        
                        -- حساب المخزون المحجوز
                        (SELECT COALESCE(SUM(pi.reserved_quantity), 0) 
                         FROM " . DB_PREFIX . "product_inventory pi 
                         WHERE pi.product_id = sl.product_id AND pi.branch_id = sl.branch_id AND pi.unit_id = sl.unit_id) AS reserved_stock
                        
                    FROM " . DB_PREFIX . "product_stock_level sl
                    LEFT JOIN " . DB_PREFIX . "product p ON (sl.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '1')
                    LEFT JOIN " . DB_PREFIX . "branch b ON (sl.branch_id = b.branch_id)
                    LEFT JOIN " . DB_PREFIX . "unit u ON (sl.unit_id = u.unit_id)
                    WHERE 1=1";

            // تطبيق الفلاتر
            if (!empty($data['filter_product'])) {
                $sql .= " AND (pd.name LIKE '%" . $this->db->escape($data['filter_product']) . "%' 
                         OR p.model LIKE '%" . $this->db->escape($data['filter_product']) . "%' 
                         OR p.sku LIKE '%" . $this->db->escape($data['filter_product']) . "%')";
            }

            if (!empty($data['filter_branch'])) {
                $sql .= " AND sl.branch_id = '" . (int)$data['filter_branch'] . "'";
            }

            if (isset($data['filter_status']) && $data['filter_status'] !== '') {
                $sql .= " AND sl.status = '" . (int)$data['filter_status'] . "'";
            }

            $query = $this->db->query($sql);
            return $query->rows;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_get_list',
                'خطأ في جلب قائمة مستويات المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            return array();
        }
    }
}