📄 Route: common/dashboard_new
📂 Controller: controller\common\dashboard_new.php
🧱 Models used (2):
   - common/dashboard_new
   - communication/unified_notification
🎨 Twig templates (1):
   - view\template\common\dashboard_new.twig
🈯 Arabic Language Files (1):
   - language\ar\common\dashboard.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\dashboard.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_home

❌ Missing in Arabic:
   - heading_title
   - text_home

❌ Missing in English:
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
