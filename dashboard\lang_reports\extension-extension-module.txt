📄 Route: extension/extension/module
📂 Controller: controller\extension\extension\module.php
🧱 Models used (3):
   - setting/extension
   - setting/module
   - user/user_group
🎨 Twig templates (1):
   - view\template\extension\extension\module.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\extension\module.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\extension\module.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_code_name
   - error_permission
   - extension
   - heading_title
   - text_disabled
   - text_enabled
   - text_layout
   - text_success

❌ Missing in Arabic:
   - error_code_name
   - error_permission
   - extension
   - heading_title
   - text_disabled
   - text_enabled
   - text_layout
   - text_success

❌ Missing in English:
   - error_code_name
   - error_permission
   - extension
   - heading_title
   - text_disabled
   - text_enabled
   - text_layout
   - text_success

💡 Suggested Arabic Additions:
   - error_code_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - extension = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_layout = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_code_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - extension = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_layout = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
