📄 Route: tool/upload
📂 Controller: controller\tool\upload.php
🧱 Models used (1):
   ✅ tool/upload (8 functions)
🎨 Twig templates (1):
   ✅ view\template\tool\upload.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\tool\upload.php (13 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\tool\upload.php (13 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (39):
   - button_delete
   - button_download
   - button_filter
   - column_date_added
   - column_name
   - date_format_short
   - delete
   - error_filetype
   - error_permission
   - error_upload
   - filter_date_added
   - filter_name
   - header
   - pagination
   - sort_date_added
   - sort_filename
   - text_confirm
   - text_filter
   - text_list
   - text_success
   ... و 19 متغير آخر

❌ Missing in Arabic (27):
   - button_delete
   - button_download
   - button_filter
   - date_format_short
   - delete
   - error_filetype
   - filter_date_added
   - filter_name
   - header
   - pagination
   - sort_date_added
   - sort_filename
   - text_confirm
   - text_filter
   - user_token
   ... و 12 متغير آخر

❌ Missing in English (27):
   - button_delete
   - button_download
   - button_filter
   - date_format_short
   - delete
   - error_filetype
   - filter_date_added
   - filter_name
   - header
   - pagination
   - sort_date_added
   - sort_filename
   - text_confirm
   - text_filter
   - user_token
   ... و 12 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 27 items
      - button_delete
      - pagination
      - button_download
      - filter_name
      - sort_date_added
   🟡 MISSING_ENGLISH_VARIABLES: 27 items
      - button_delete
      - pagination
      - button_download
      - filter_name
      - sort_date_added

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 27 متغير عربي و 27 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.