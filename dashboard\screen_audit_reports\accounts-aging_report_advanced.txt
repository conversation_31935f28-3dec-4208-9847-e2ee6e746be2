📄 Route: accounts/aging_report_advanced
📂 Controller: controller\accounts\aging_report_advanced.php
🧱 Models used (2):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/aging_report_advanced (23 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\aging_report_advanced.php (185 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\aging_report_advanced.php (185 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (13):
   - error_date_end
   - error_no_data
   - error_permission
   - error_report_type
   - heading_title
   - text_both
   - text_custom_periods
   - text_home
   - text_payables
   - text_receivables
   - text_standard_periods
   - text_success_generate
   - text_view

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (1):
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (1):
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ⚠️ 85%
📅 Analysis Date: 2025-07-21 18:32:32
🔧 Total Issues: 1

⚠️ جيد، لكن يحتاج بعض التحسينات.