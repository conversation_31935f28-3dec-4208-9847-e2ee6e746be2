📄 Route: marketplace/api
📂 Controller: controller\marketplace\api.php
🧱 Models used (1):
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\marketplace\api.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\marketplace\api.php (8 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\marketplace\api.php (8 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - button_save
   - entry_secret
   - entry_username
   - error_permission
   - error_secret
   - error_username
   - heading_title
   - text_loading
   - text_signup
   - text_success
   - user_token

❌ Missing in Arabic (3):
   - button_save
   - text_loading
   - user_token

❌ Missing in English (3):
   - button_save
   - text_loading
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_loading
      - button_save
      - user_token
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_loading
      - button_save
      - user_token

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.