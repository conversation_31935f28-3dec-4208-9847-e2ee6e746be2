📄 Route: reports/equity_changes
📂 Controller: controller\reports\equity_changes.php
🧱 Models used (1):
   ✅ reports/equity_changes (13 functions)
🎨 Twig templates (1):
   ✅ view\template\reports\equity_changes.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\reports\equity_changes.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\reports\equity_changes.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (36):
   - action
   - button_add
   - button_delete
   - button_filter
   - column_date_added
   - column_left
   - column_name
   - column_status
   - date_format_short
   - entry_status
   - error_permission
   - filter_date_added
   - filter_name
   - header
   - pagination
   - text_confirm
   - text_disabled
   - text_filter
   - text_home
   - user_token
   ... و 16 متغير آخر

❌ Missing in Arabic (36):
   - action
   - button_add
   - button_delete
   - button_filter
   - column_date_added
   - column_name
   - date_format_short
   - error_permission
   - filter_date_added
   - filter_name
   - header
   - pagination
   - text_confirm
   - text_disabled
   - text_filter
   ... و 21 متغير آخر

❌ Missing in English (36):
   - action
   - button_add
   - button_delete
   - button_filter
   - column_date_added
   - column_name
   - date_format_short
   - error_permission
   - filter_date_added
   - filter_name
   - header
   - pagination
   - text_confirm
   - text_disabled
   - text_filter
   ... و 21 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 36 items
      - button_delete
      - button_add
      - action
      - column_name
      - pagination
   🟡 MISSING_ENGLISH_VARIABLES: 36 items
      - button_delete
      - button_add
      - action
      - column_name
      - pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 36 متغير عربي و 36 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:17
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.