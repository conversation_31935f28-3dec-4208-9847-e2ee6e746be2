📄 Route: inventory/stock_counting
📂 Controller: controller\inventory\stock_counting.php
🧱 Models used (8):
   - core/central_service_manager
   - inventory/branch
   - inventory/category
   - inventory/stock_counting
   - inventory/stock_counting_enhanced
   - setting/setting
   - user/user
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (2):
   - language\ar\common\header.php
   - language\ar\inventory\stock_counting.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_branch
   - column_category
   - column_counting_date
   - column_counting_name
   - column_counting_number
   - column_counting_type
   - column_notes
   - column_status
   - column_total_items
   - column_total_variance_value
   - column_user
   - date_format_short
   - datetime_format
   - error_advanced_permission
   - error_branch_required
   - error_counting_cannot_delete_completed
   - error_counting_date
   - error_counting_name
   - error_counting_not_found
   - error_counting_number_exists
   - error_exception
   - error_permission
   - heading_title
   - text_add
   - text_all
   - text_all_categories
   - text_counting_type_cycle
   - text_counting_type_full
   - text_counting_type_partial
   - text_counting_type_spot
   - text_edit
   - text_home
   - text_pagination
   - text_status_cancelled
   - text_status_completed
   - text_status_draft
   - text_status_in_progress
   - text_status_posted
   - text_success

❌ Missing in Arabic:
   - column_branch
   - column_category
   - column_counting_date
   - column_counting_name
   - column_counting_number
   - column_counting_type
   - column_notes
   - column_status
   - column_total_items
   - column_total_variance_value
   - column_user
   - date_format_short
   - datetime_format
   - error_advanced_permission
   - error_branch_required
   - error_counting_cannot_delete_completed
   - error_counting_date
   - error_counting_name
   - error_counting_not_found
   - error_counting_number_exists
   - error_exception
   - error_permission
   - heading_title
   - text_add
   - text_all
   - text_all_categories
   - text_counting_type_cycle
   - text_counting_type_full
   - text_counting_type_partial
   - text_counting_type_spot
   - text_edit
   - text_home
   - text_pagination
   - text_status_cancelled
   - text_status_completed
   - text_status_draft
   - text_status_in_progress
   - text_status_posted
   - text_success

❌ Missing in English:
   - column_branch
   - column_category
   - column_counting_date
   - column_counting_name
   - column_counting_number
   - column_counting_type
   - column_notes
   - column_status
   - column_total_items
   - column_total_variance_value
   - column_user
   - date_format_short
   - datetime_format
   - error_advanced_permission
   - error_branch_required
   - error_counting_cannot_delete_completed
   - error_counting_date
   - error_counting_name
   - error_counting_not_found
   - error_counting_number_exists
   - error_exception
   - error_permission
   - heading_title
   - text_add
   - text_all
   - text_all_categories
   - text_counting_type_cycle
   - text_counting_type_full
   - text_counting_type_partial
   - text_counting_type_spot
   - text_edit
   - text_home
   - text_pagination
   - text_status_cancelled
   - text_status_completed
   - text_status_draft
   - text_status_in_progress
   - text_status_posted
   - text_success

💡 Suggested Arabic Additions:
   - column_branch = ""  # TODO: ترجمة عربية
   - column_category = ""  # TODO: ترجمة عربية
   - column_counting_date = ""  # TODO: ترجمة عربية
   - column_counting_name = ""  # TODO: ترجمة عربية
   - column_counting_number = ""  # TODO: ترجمة عربية
   - column_counting_type = ""  # TODO: ترجمة عربية
   - column_notes = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_total_items = ""  # TODO: ترجمة عربية
   - column_total_variance_value = ""  # TODO: ترجمة عربية
   - column_user = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_branch_required = ""  # TODO: ترجمة عربية
   - error_counting_cannot_delete_completed = ""  # TODO: ترجمة عربية
   - error_counting_date = ""  # TODO: ترجمة عربية
   - error_counting_name = ""  # TODO: ترجمة عربية
   - error_counting_not_found = ""  # TODO: ترجمة عربية
   - error_counting_number_exists = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_all_categories = ""  # TODO: ترجمة عربية
   - text_counting_type_cycle = ""  # TODO: ترجمة عربية
   - text_counting_type_full = ""  # TODO: ترجمة عربية
   - text_counting_type_partial = ""  # TODO: ترجمة عربية
   - text_counting_type_spot = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_completed = ""  # TODO: ترجمة عربية
   - text_status_draft = ""  # TODO: ترجمة عربية
   - text_status_in_progress = ""  # TODO: ترجمة عربية
   - text_status_posted = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_branch = ""  # TODO: English translation
   - column_category = ""  # TODO: English translation
   - column_counting_date = ""  # TODO: English translation
   - column_counting_name = ""  # TODO: English translation
   - column_counting_number = ""  # TODO: English translation
   - column_counting_type = ""  # TODO: English translation
   - column_notes = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_total_items = ""  # TODO: English translation
   - column_total_variance_value = ""  # TODO: English translation
   - column_user = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_branch_required = ""  # TODO: English translation
   - error_counting_cannot_delete_completed = ""  # TODO: English translation
   - error_counting_date = ""  # TODO: English translation
   - error_counting_name = ""  # TODO: English translation
   - error_counting_not_found = ""  # TODO: English translation
   - error_counting_number_exists = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_all_categories = ""  # TODO: English translation
   - text_counting_type_cycle = ""  # TODO: English translation
   - text_counting_type_full = ""  # TODO: English translation
   - text_counting_type_partial = ""  # TODO: English translation
   - text_counting_type_spot = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_completed = ""  # TODO: English translation
   - text_status_draft = ""  # TODO: English translation
   - text_status_in_progress = ""  # TODO: English translation
   - text_status_posted = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
