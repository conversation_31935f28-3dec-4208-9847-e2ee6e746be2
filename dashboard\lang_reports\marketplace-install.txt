📄 Route: marketplace/install
📂 Controller: controller\marketplace\install.php
🧱 Models used (2):
   - setting/extension
   - setting/modification
🎨 Twig templates (1):
   - view\template\marketplace\install.twig
🈯 Arabic Language Files (1):
   - language\ar\marketplace\install.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketplace\install.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_allowed
   - error_code
   - error_directory
   - error_exception
   - error_file
   - error_permission
   - error_unzip
   - text_move
   - text_remove
   - text_success
   - text_unzip
   - text_xml

❌ Missing in Arabic:
   - error_allowed
   - error_code
   - error_directory
   - error_exception
   - error_file
   - error_permission
   - error_unzip
   - text_move
   - text_remove
   - text_success
   - text_unzip
   - text_xml

❌ Missing in English:
   - error_allowed
   - error_code
   - error_directory
   - error_exception
   - error_file
   - error_permission
   - error_unzip
   - text_move
   - text_remove
   - text_success
   - text_unzip
   - text_xml

💡 Suggested Arabic Additions:
   - error_allowed = ""  # TODO: ترجمة عربية
   - error_code = ""  # TODO: ترجمة عربية
   - error_directory = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_file = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_unzip = ""  # TODO: ترجمة عربية
   - text_move = ""  # TODO: ترجمة عربية
   - text_remove = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_unzip = ""  # TODO: ترجمة عربية
   - text_xml = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_allowed = ""  # TODO: English translation
   - error_code = ""  # TODO: English translation
   - error_directory = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_file = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_unzip = ""  # TODO: English translation
   - text_move = ""  # TODO: English translation
   - text_remove = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_unzip = ""  # TODO: English translation
   - text_xml = ""  # TODO: English translation
