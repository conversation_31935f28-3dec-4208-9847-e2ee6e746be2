📄 Route: customer/customer_group
📂 Controller: controller\customer\customer_group.php
🧱 Models used (4):
   ✅ customer/customer_group (7 functions)
   ✅ localisation/language (7 functions)
   ✅ setting/store (14 functions)
   ✅ customer/customer (51 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\customer\customer_group.php (18 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\customer\customer_group.php (18 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (12):
   - error_customer
   - error_default
   - error_name
   - error_permission
   - error_store
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (3):
   - text_default
   - text_home
   - text_pagination

❌ Missing in English (3):
   - text_default
   - text_home
   - text_pagination

🗄️ Database Tables Used (1):
   ❌ credit_limit

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - text_default
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - text_default
   🔴 INVALID_DATABASE_TABLES: 1 items
      - credit_limit

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:59
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.