📄 Route: marketplace/install
📂 Controller: controller\marketplace\install.php
🧱 Models used (2):
   ✅ setting/extension (11 functions)
   ✅ setting/modification (9 functions)
🎨 Twig templates (1):
   ✅ view\template\marketplace\install.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\marketplace\install.php (14 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\marketplace\install.php (14 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (25):
   - action
   - button_save
   - column_left
   - error_allowed
   - error_code
   - error_directory
   - error_exception
   - error_file
   - error_heading_title
   - error_permission
   - error_warning
   - footer
   - header
   - text_heading_title
   - text_move
   - text_remove
   - text_success
   - text_unzip
   - text_xml
   - user_token
   ... و 5 متغير آخر

❌ Missing in Arabic (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_exception
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in English (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_exception
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 14 items
      - button_save
      - error_warning
      - user_token
      - text_heading_title
      - column_left
   🟡 MISSING_ENGLISH_VARIABLES: 14 items
      - button_save
      - error_warning
      - user_token
      - text_heading_title
      - column_left

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 14 متغير عربي و 14 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.