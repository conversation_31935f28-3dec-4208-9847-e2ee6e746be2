📄 Route: accounts/profitability_analysis
📂 Controller: controller\accounts\profitability_analysis.php
🧱 Models used (2):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/profitability_analysis (2 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\profitability_analysis.php (21 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\profitability_analysis.php (21 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (4):
   - heading_title
   - text_home
   - text_list
   - text_no_results

❌ Missing in Arabic (3):
   - text_home
   - text_list
   - text_no_results

❌ Missing in English (3):
   - text_home
   - text_list
   - text_no_results

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_list
      - text_no_results
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_list
      - text_no_results
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:44
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.