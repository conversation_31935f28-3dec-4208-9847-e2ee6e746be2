📄 Route: pos/cashier_handover
📂 Controller: controller\pos\cashier_handover.php
🧱 Models used (4):
   - pos/cashier_handover
   - pos/shift
   - pos/transaction
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\pos\cashier_handover.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - error_amount
   - error_insufficient_cash
   - error_no_active_shift
   - error_permission
   - error_to_user
   - heading_title
   - heading_title_add
   - heading_title_view
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - datetime_format
   - error_amount
   - error_insufficient_cash
   - error_no_active_shift
   - error_permission
   - error_to_user
   - heading_title
   - heading_title_add
   - heading_title_view
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - datetime_format
   - error_amount
   - error_insufficient_cash
   - error_no_active_shift
   - error_permission
   - error_to_user
   - heading_title
   - heading_title_add
   - heading_title_view
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_amount = ""  # TODO: ترجمة عربية
   - error_insufficient_cash = ""  # TODO: ترجمة عربية
   - error_no_active_shift = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_to_user = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_title_add = ""  # TODO: ترجمة عربية
   - heading_title_view = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - error_amount = ""  # TODO: English translation
   - error_insufficient_cash = ""  # TODO: English translation
   - error_no_active_shift = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_to_user = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_title_add = ""  # TODO: English translation
   - heading_title_view = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
