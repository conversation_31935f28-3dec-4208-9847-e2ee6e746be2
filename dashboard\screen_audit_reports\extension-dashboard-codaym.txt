📄 Route: extension/dashboard/codaym
📂 Controller: controller\extension\dashboard\codaym.php
🧱 Models used (2):
   ✅ extension/dashboard/codaym (3 functions)
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\dashboard\codaym.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\dashboard\codaym.php (4 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\dashboard\codaym.php (4 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (7):
   - date_format_short
   - heading_title
   - text_abandoned_carts
   - text_home
   - text_latest_orders
   - text_missing_orders
   - user_token

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - user_token

❌ Missing in English (3):
   - date_format_short
   - text_home
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - user_token
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - user_token
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:21
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.