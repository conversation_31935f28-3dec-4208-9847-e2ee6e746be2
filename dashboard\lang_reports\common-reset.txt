📄 Route: common/reset
📂 Controller: controller\common\reset.php
🧱 Models used (2):
   - setting/setting
   - user/user
🎨 Twig templates (1):
   - view\template\common\reset.twig
🈯 Arabic Language Files (1):
   - language\ar\common\reset.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\reset.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_confirm
   - error_password
   - heading_title
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_confirm
   - error_password
   - heading_title
   - text_home
   - text_success

❌ Missing in English:
   - error_confirm
   - error_password
   - heading_title
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_confirm = ""  # TODO: ترجمة عربية
   - error_password = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_confirm = ""  # TODO: English translation
   - error_password = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
