📄 Route: workflow/advanced_visual_editor
📂 Controller: controller\workflow\advanced_visual_editor.php
🧱 Models used (1):
   - workflow/visual_workflow_engine
🎨 Twig templates (1):
   - view\template\workflow\advanced_visual_editor.twig
🈯 Arabic Language Files (1):
   - language\ar\workflow\advanced_visual_editor.php
🇬🇧 English Language Files (1):
   - language\en-gb\workflow\advanced_visual_editor.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_invalid_request
   - error_name_required
   - error_permission
   - error_validation
   - error_workflow_not_found
   - heading_title
   - text_home
   - text_workflow_created
   - text_workflow_test_success
   - text_workflow_updated

❌ Missing in Arabic:
   - error_invalid_request
   - error_name_required
   - error_permission
   - error_validation
   - error_workflow_not_found
   - heading_title
   - text_home
   - text_workflow_created
   - text_workflow_test_success
   - text_workflow_updated

❌ Missing in English:
   - error_invalid_request
   - error_name_required
   - error_permission
   - error_validation
   - error_workflow_not_found
   - heading_title
   - text_home
   - text_workflow_created
   - text_workflow_test_success
   - text_workflow_updated

💡 Suggested Arabic Additions:
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_name_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_validation = ""  # TODO: ترجمة عربية
   - error_workflow_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_workflow_created = ""  # TODO: ترجمة عربية
   - text_workflow_test_success = ""  # TODO: ترجمة عربية
   - text_workflow_updated = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_invalid_request = ""  # TODO: English translation
   - error_name_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_validation = ""  # TODO: English translation
   - error_workflow_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_workflow_created = ""  # TODO: English translation
   - text_workflow_test_success = ""  # TODO: English translation
   - text_workflow_updated = ""  # TODO: English translation
