📄 Route: accounts/fixed_assets_report
📂 Controller: controller\accounts\fixed_assets_report.php
🧱 Models used (1):
   - accounts/fixed_assets_report
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\fixed_assets_report.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\fixed_assets_report.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_no_data
   - heading_title
   - print_title
   - text_asset_code
   - text_asset_name
   - text_current_value
   - text_fixed_assets_report
   - text_form
   - text_from
   - text_method
   - text_new_current_value
   - text_period
   - text_period_depreciation
   - text_purchase_date
   - text_purchase_value
   - text_salvage_value
   - text_to
   - text_total_depreciation
   - text_useful_life

❌ Missing in Arabic:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_no_data
   - heading_title
   - print_title
   - text_asset_code
   - text_asset_name
   - text_current_value
   - text_fixed_assets_report
   - text_form
   - text_from
   - text_method
   - text_new_current_value
   - text_period
   - text_period_depreciation
   - text_purchase_date
   - text_purchase_value
   - text_salvage_value
   - text_to
   - text_total_depreciation
   - text_useful_life

❌ Missing in English:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_no_data
   - heading_title
   - print_title
   - text_asset_code
   - text_asset_name
   - text_current_value
   - text_fixed_assets_report
   - text_form
   - text_from
   - text_method
   - text_new_current_value
   - text_period
   - text_period_depreciation
   - text_purchase_date
   - text_purchase_value
   - text_salvage_value
   - text_to
   - text_total_depreciation
   - text_useful_life

💡 Suggested Arabic Additions:
   - button_filter = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - entry_date_start = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_asset_code = ""  # TODO: ترجمة عربية
   - text_asset_name = ""  # TODO: ترجمة عربية
   - text_current_value = ""  # TODO: ترجمة عربية
   - text_fixed_assets_report = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_from = ""  # TODO: ترجمة عربية
   - text_method = ""  # TODO: ترجمة عربية
   - text_new_current_value = ""  # TODO: ترجمة عربية
   - text_period = ""  # TODO: ترجمة عربية
   - text_period_depreciation = ""  # TODO: ترجمة عربية
   - text_purchase_date = ""  # TODO: ترجمة عربية
   - text_purchase_value = ""  # TODO: ترجمة عربية
   - text_salvage_value = ""  # TODO: ترجمة عربية
   - text_to = ""  # TODO: ترجمة عربية
   - text_total_depreciation = ""  # TODO: ترجمة عربية
   - text_useful_life = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_filter = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - entry_date_start = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_asset_code = ""  # TODO: English translation
   - text_asset_name = ""  # TODO: English translation
   - text_current_value = ""  # TODO: English translation
   - text_fixed_assets_report = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_from = ""  # TODO: English translation
   - text_method = ""  # TODO: English translation
   - text_new_current_value = ""  # TODO: English translation
   - text_period = ""  # TODO: English translation
   - text_period_depreciation = ""  # TODO: English translation
   - text_purchase_date = ""  # TODO: English translation
   - text_purchase_value = ""  # TODO: English translation
   - text_salvage_value = ""  # TODO: English translation
   - text_to = ""  # TODO: English translation
   - text_total_depreciation = ""  # TODO: English translation
   - text_useful_life = ""  # TODO: English translation
