📄 Route: tool/backup
📂 Controller: controller\tool\backup.php
🧱 Models used (1):
   - tool/backup
🎨 Twig templates (1):
   - view\template\tool\backup.twig
🈯 Arabic Language Files (1):
   - language\ar\tool\backup.php
🇬🇧 English Language Files (1):
   - language\en-gb\tool\backup.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_export
   - error_file
   - error_permission
   - heading_title
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_export
   - error_file
   - error_permission
   - heading_title
   - text_home
   - text_success

❌ Missing in English:
   - error_export
   - error_file
   - error_permission
   - heading_title
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_export = ""  # TODO: ترجمة عربية
   - error_file = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_export = ""  # TODO: English translation
   - error_file = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
