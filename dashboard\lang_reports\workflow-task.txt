📄 Route: workflow/task
📂 Controller: controller\workflow\task.php
🧱 Models used (1):
   - workflow/task
🎨 Twig templates (1):
   - view\template\workflow\task.twig
🈯 Arabic Language Files (1):
   - language\ar\workflow\task.php
🇬🇧 English Language Files (1):
   - language\en-gb\workflow\task.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_assignee
   - error_invalid_data
   - error_permission
   - error_title
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_priority_high
   - text_priority_low
   - text_priority_medium
   - text_priority_urgent
   - text_status_cancelled
   - text_status_completed
   - text_status_in_progress
   - text_status_pending
   - text_status_review
   - text_success
   - text_success_comment_added
   - text_success_status_update
   - text_view_task
   - text_workflow

❌ Missing in Arabic:
   - date_format_short
   - error_assignee
   - error_invalid_data
   - error_permission
   - error_title
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_priority_high
   - text_priority_low
   - text_priority_medium
   - text_priority_urgent
   - text_status_cancelled
   - text_status_completed
   - text_status_in_progress
   - text_status_pending
   - text_status_review
   - text_success
   - text_success_comment_added
   - text_success_status_update
   - text_view_task
   - text_workflow

❌ Missing in English:
   - date_format_short
   - error_assignee
   - error_invalid_data
   - error_permission
   - error_title
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_priority_high
   - text_priority_low
   - text_priority_medium
   - text_priority_urgent
   - text_status_cancelled
   - text_status_completed
   - text_status_in_progress
   - text_status_pending
   - text_status_review
   - text_success
   - text_success_comment_added
   - text_success_status_update
   - text_view_task
   - text_workflow

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_assignee = ""  # TODO: ترجمة عربية
   - error_invalid_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_title = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_priority_high = ""  # TODO: ترجمة عربية
   - text_priority_low = ""  # TODO: ترجمة عربية
   - text_priority_medium = ""  # TODO: ترجمة عربية
   - text_priority_urgent = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_completed = ""  # TODO: ترجمة عربية
   - text_status_in_progress = ""  # TODO: ترجمة عربية
   - text_status_pending = ""  # TODO: ترجمة عربية
   - text_status_review = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_success_comment_added = ""  # TODO: ترجمة عربية
   - text_success_status_update = ""  # TODO: ترجمة عربية
   - text_view_task = ""  # TODO: ترجمة عربية
   - text_workflow = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_assignee = ""  # TODO: English translation
   - error_invalid_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_title = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_priority_high = ""  # TODO: English translation
   - text_priority_low = ""  # TODO: English translation
   - text_priority_medium = ""  # TODO: English translation
   - text_priority_urgent = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_completed = ""  # TODO: English translation
   - text_status_in_progress = ""  # TODO: English translation
   - text_status_pending = ""  # TODO: English translation
   - text_status_review = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_success_comment_added = ""  # TODO: English translation
   - text_success_status_update = ""  # TODO: English translation
   - text_view_task = ""  # TODO: English translation
   - text_workflow = ""  # TODO: English translation
