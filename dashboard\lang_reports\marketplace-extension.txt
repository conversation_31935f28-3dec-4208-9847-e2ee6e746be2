📄 Route: marketplace/extension
📂 Controller: controller\marketplace\extension.php
🧱 Models used (0):
🎨 Twig templates (1):
   - view\template\marketplace\extension.twig
🈯 Arabic Language Files (1):
   - language\ar\marketplace\extension.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketplace\extension.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - extension
   - heading_title
   - text_home

❌ Missing in Arabic:
   - extension
   - heading_title
   - text_home

❌ Missing in English:
   - extension
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - extension = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - extension = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
