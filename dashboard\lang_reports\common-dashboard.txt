📄 Route: common/dashboard
📂 Controller: controller\common\dashboard.php
🧱 Models used (6):
   - common/dashboard
   - communication/unified_notification
   - core/central_service_manager
   - setting/setting
   - workflow/approval
   - workflow/visual_workflow_engine
🎨 Twig templates (1):
   - view\template\common\dashboard.twig
🈯 Arabic Language Files (1):
   - language\ar\common\dashboard.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\dashboard.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_home

❌ Missing in Arabic:
   - heading_title
   - text_home

❌ Missing in English:
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
