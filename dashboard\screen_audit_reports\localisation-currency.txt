📄 Route: localisation/currency
📂 Controller: controller\localisation\currency.php
🧱 Models used (3):
   ✅ localisation/currency (8 functions)
   ✅ setting/store (14 functions)
   ✅ sale/order (27 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\localisation\currency.php (26 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\localisation\currency.php (27 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - date_format_short
   - error_code
   - error_currency_engine
   - error_default
   - error_order
   - error_permission
   - error_store
   - error_title
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (5):
   - date_format_short
   - error_currency_engine
   - text_default
   - text_home
   - text_pagination

❌ Missing in English (4):
   - date_format_short
   - text_default
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - text_home
      - error_currency_engine
      - text_pagination
      - text_default
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 4 items
      - date_format_short
      - text_home
      - text_pagination
      - text_default

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 4 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:08
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.