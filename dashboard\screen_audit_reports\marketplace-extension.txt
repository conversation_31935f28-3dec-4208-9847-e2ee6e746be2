📄 Route: marketplace/extension
📂 Controller: controller\marketplace\extension.php
🧱 Models used (0):
🎨 Twig templates (1):
   ✅ view\template\marketplace\extension.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\marketplace\extension.php (5 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\marketplace\extension.php (5 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - column_left
   - extension
   - footer
   - header
   - heading_title
   - text_confirm
   - text_filter
   - text_home
   - text_list
   - text_type
   - user_token

❌ Missing in Arabic (7):
   - column_left
   - extension
   - footer
   - header
   - text_confirm
   - text_home
   - user_token

❌ Missing in English (7):
   - column_left
   - extension
   - footer
   - header
   - text_confirm
   - text_home
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 7 items
      - user_token
      - column_left
      - extension
      - text_home
      - footer
   🟡 MISSING_ENGLISH_VARIABLES: 7 items
      - user_token
      - column_left
      - extension
      - text_home
      - footer

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 7 متغير عربي و 7 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.