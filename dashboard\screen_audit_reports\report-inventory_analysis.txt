📄 Route: report/inventory_analysis
📂 Controller: controller\report\inventory_analysis.php
🧱 Models used (3):
   ✅ report/inventory (5 functions)
   ✅ branch/branch (7 functions)
   ✅ catalog/category (15 functions)
🎨 Twig templates (1):
   ✅ view\template\report\inventory_analysis.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\report\inventory_analysis.php (57 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\report\inventory_analysis.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (32):
   - button_filter
   - column_left
   - column_model
   - column_product
   - column_sku
   - column_unit
   - column_value
   - date_format_short
   - entry_category
   - entry_product
   - header
   - heading_valuation
   - text_home
   - text_inventory_alerts
   - text_inventory_alerts_desc
   - text_inventory_turnover
   - text_inventory_valuation
   - text_inventory_valuation_desc
   - text_slow_moving_desc
   - text_total_value
   ... و 12 متغير آخر

❌ Missing in Arabic (5):
   - column_left
   - date_format_short
   - footer
   - header
   - text_home

❌ Missing in English (32):
   - button_filter
   - column_model
   - column_unit
   - column_value
   - date_format_short
   - entry_category
   - header
   - heading_valuation
   - text_inventory_alerts
   - text_inventory_alerts_desc
   - text_inventory_turnover
   - text_inventory_valuation
   - text_inventory_valuation_desc
   - text_slow_moving_desc
   - text_total_value
   ... و 17 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - column_left
      - text_home
      - footer
      - header
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 32 items
      - column_unit
      - text_total_value
      - text_inventory_alerts
      - heading_valuation
      - text_inventory_turnover

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 32 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.