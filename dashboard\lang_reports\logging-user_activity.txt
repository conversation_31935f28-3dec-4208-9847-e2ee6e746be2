📄 Route: logging/user_activity
📂 Controller: controller\logging\user_activity.php
🧱 Models used (3):
   - core/central_service_manager
   - logging/user_activity
   - user/user
🎨 Twig templates (1):
   - view\template\logging\user_activity.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_activity_create
   - text_activity_delete
   - text_activity_export
   - text_activity_import
   - text_activity_login
   - text_activity_logout
   - text_activity_update
   - text_activity_view
   - text_home
   - text_module_accounts
   - text_module_catalog
   - text_module_communication
   - text_module_crm
   - text_module_finance
   - text_module_hr
   - text_module_inventory
   - text_module_logging
   - text_module_notification
   - text_module_pos
   - text_module_purchase
   - text_module_sales
   - text_module_shipping
   - text_module_workflow
   - text_realtime_activity
   - text_user_activity_detail

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_activity_create
   - text_activity_delete
   - text_activity_export
   - text_activity_import
   - text_activity_login
   - text_activity_logout
   - text_activity_update
   - text_activity_view
   - text_home
   - text_module_accounts
   - text_module_catalog
   - text_module_communication
   - text_module_crm
   - text_module_finance
   - text_module_hr
   - text_module_inventory
   - text_module_logging
   - text_module_notification
   - text_module_pos
   - text_module_purchase
   - text_module_sales
   - text_module_shipping
   - text_module_workflow
   - text_realtime_activity
   - text_user_activity_detail

❌ Missing in English:
   - error_permission
   - heading_title
   - text_activity_create
   - text_activity_delete
   - text_activity_export
   - text_activity_import
   - text_activity_login
   - text_activity_logout
   - text_activity_update
   - text_activity_view
   - text_home
   - text_module_accounts
   - text_module_catalog
   - text_module_communication
   - text_module_crm
   - text_module_finance
   - text_module_hr
   - text_module_inventory
   - text_module_logging
   - text_module_notification
   - text_module_pos
   - text_module_purchase
   - text_module_sales
   - text_module_shipping
   - text_module_workflow
   - text_realtime_activity
   - text_user_activity_detail

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_activity_create = ""  # TODO: ترجمة عربية
   - text_activity_delete = ""  # TODO: ترجمة عربية
   - text_activity_export = ""  # TODO: ترجمة عربية
   - text_activity_import = ""  # TODO: ترجمة عربية
   - text_activity_login = ""  # TODO: ترجمة عربية
   - text_activity_logout = ""  # TODO: ترجمة عربية
   - text_activity_update = ""  # TODO: ترجمة عربية
   - text_activity_view = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_module_accounts = ""  # TODO: ترجمة عربية
   - text_module_catalog = ""  # TODO: ترجمة عربية
   - text_module_communication = ""  # TODO: ترجمة عربية
   - text_module_crm = ""  # TODO: ترجمة عربية
   - text_module_finance = ""  # TODO: ترجمة عربية
   - text_module_hr = ""  # TODO: ترجمة عربية
   - text_module_inventory = ""  # TODO: ترجمة عربية
   - text_module_logging = ""  # TODO: ترجمة عربية
   - text_module_notification = ""  # TODO: ترجمة عربية
   - text_module_pos = ""  # TODO: ترجمة عربية
   - text_module_purchase = ""  # TODO: ترجمة عربية
   - text_module_sales = ""  # TODO: ترجمة عربية
   - text_module_shipping = ""  # TODO: ترجمة عربية
   - text_module_workflow = ""  # TODO: ترجمة عربية
   - text_realtime_activity = ""  # TODO: ترجمة عربية
   - text_user_activity_detail = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_activity_create = ""  # TODO: English translation
   - text_activity_delete = ""  # TODO: English translation
   - text_activity_export = ""  # TODO: English translation
   - text_activity_import = ""  # TODO: English translation
   - text_activity_login = ""  # TODO: English translation
   - text_activity_logout = ""  # TODO: English translation
   - text_activity_update = ""  # TODO: English translation
   - text_activity_view = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_module_accounts = ""  # TODO: English translation
   - text_module_catalog = ""  # TODO: English translation
   - text_module_communication = ""  # TODO: English translation
   - text_module_crm = ""  # TODO: English translation
   - text_module_finance = ""  # TODO: English translation
   - text_module_hr = ""  # TODO: English translation
   - text_module_inventory = ""  # TODO: English translation
   - text_module_logging = ""  # TODO: English translation
   - text_module_notification = ""  # TODO: English translation
   - text_module_pos = ""  # TODO: English translation
   - text_module_purchase = ""  # TODO: English translation
   - text_module_sales = ""  # TODO: English translation
   - text_module_shipping = ""  # TODO: English translation
   - text_module_workflow = ""  # TODO: English translation
   - text_realtime_activity = ""  # TODO: English translation
   - text_user_activity_detail = ""  # TODO: English translation
