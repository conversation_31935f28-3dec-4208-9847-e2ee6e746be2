📄 Route: user/two_factor_setup
📂 Controller: controller\user\two_factor_setup.php
🧱 Models used (2):
   - user/two_factor_auth
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_cancel
   - button_continue
   - button_disable
   - button_download
   - button_enable
   - button_print
   - entry_verification_code
   - error_disable_failed
   - error_enable_failed
   - error_permission
   - error_phone_required
   - error_session_expired
   - error_verification_failed
   - error_verification_invalid
   - error_verification_required
   - heading_title
   - success_disabled
   - success_enabled
   - success_phone_updated
   - success_verification_sent
   - text_backup_codes
   - text_backup_warning
   - text_disabled
   - text_enabled
   - text_setup
   - text_step1
   - text_step2
   - text_step3
   - text_success

❌ Missing in Arabic:
   - button_cancel
   - button_continue
   - button_disable
   - button_download
   - button_enable
   - button_print
   - entry_verification_code
   - error_disable_failed
   - error_enable_failed
   - error_permission
   - error_phone_required
   - error_session_expired
   - error_verification_failed
   - error_verification_invalid
   - error_verification_required
   - heading_title
   - success_disabled
   - success_enabled
   - success_phone_updated
   - success_verification_sent
   - text_backup_codes
   - text_backup_warning
   - text_disabled
   - text_enabled
   - text_setup
   - text_step1
   - text_step2
   - text_step3
   - text_success

❌ Missing in English:
   - button_cancel
   - button_continue
   - button_disable
   - button_download
   - button_enable
   - button_print
   - entry_verification_code
   - error_disable_failed
   - error_enable_failed
   - error_permission
   - error_phone_required
   - error_session_expired
   - error_verification_failed
   - error_verification_invalid
   - error_verification_required
   - heading_title
   - success_disabled
   - success_enabled
   - success_phone_updated
   - success_verification_sent
   - text_backup_codes
   - text_backup_warning
   - text_disabled
   - text_enabled
   - text_setup
   - text_step1
   - text_step2
   - text_step3
   - text_success

💡 Suggested Arabic Additions:
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_continue = ""  # TODO: ترجمة عربية
   - button_disable = ""  # TODO: ترجمة عربية
   - button_download = ""  # TODO: ترجمة عربية
   - button_enable = ""  # TODO: ترجمة عربية
   - button_print = ""  # TODO: ترجمة عربية
   - entry_verification_code = ""  # TODO: ترجمة عربية
   - error_disable_failed = ""  # TODO: ترجمة عربية
   - error_enable_failed = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_phone_required = ""  # TODO: ترجمة عربية
   - error_session_expired = ""  # TODO: ترجمة عربية
   - error_verification_failed = ""  # TODO: ترجمة عربية
   - error_verification_invalid = ""  # TODO: ترجمة عربية
   - error_verification_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - success_disabled = ""  # TODO: ترجمة عربية
   - success_enabled = ""  # TODO: ترجمة عربية
   - success_phone_updated = ""  # TODO: ترجمة عربية
   - success_verification_sent = ""  # TODO: ترجمة عربية
   - text_backup_codes = ""  # TODO: ترجمة عربية
   - text_backup_warning = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_setup = ""  # TODO: ترجمة عربية
   - text_step1 = ""  # TODO: ترجمة عربية
   - text_step2 = ""  # TODO: ترجمة عربية
   - text_step3 = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_cancel = ""  # TODO: English translation
   - button_continue = ""  # TODO: English translation
   - button_disable = ""  # TODO: English translation
   - button_download = ""  # TODO: English translation
   - button_enable = ""  # TODO: English translation
   - button_print = ""  # TODO: English translation
   - entry_verification_code = ""  # TODO: English translation
   - error_disable_failed = ""  # TODO: English translation
   - error_enable_failed = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_phone_required = ""  # TODO: English translation
   - error_session_expired = ""  # TODO: English translation
   - error_verification_failed = ""  # TODO: English translation
   - error_verification_invalid = ""  # TODO: English translation
   - error_verification_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - success_disabled = ""  # TODO: English translation
   - success_enabled = ""  # TODO: English translation
   - success_phone_updated = ""  # TODO: English translation
   - success_verification_sent = ""  # TODO: English translation
   - text_backup_codes = ""  # TODO: English translation
   - text_backup_warning = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_setup = ""  # TODO: English translation
   - text_step1 = ""  # TODO: English translation
   - text_step2 = ""  # TODO: English translation
   - text_step3 = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
