📄 Route: extension/shipping/hitshippo_fedex
📂 Controller: controller\extension\shipping\hitshippo_fedex.php
🧱 Models used (2):
   - localisation/language
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\shipping\hitshippo_fedex.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (1):
   - language\en-gb\extension\shipping\hitshippo_fedex.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_account
   - error_key
   - error_meter
   - error_password
   - error_permission
   - error_postcode
   - error_rest_acc_num
   - error_rest_api_key
   - error_rest_api_sec
   - heading_title
   - text_extension
   - text_fedex_1
   - text_fedex_10
   - text_fedex_11
   - text_fedex_12
   - text_fedex_13
   - text_fedex_14
   - text_fedex_15
   - text_fedex_16
   - text_fedex_17
   - text_fedex_18
   - text_fedex_19
   - text_fedex_2
   - text_fedex_20
   - text_fedex_21
   - text_fedex_22
   - text_fedex_23
   - text_fedex_24
   - text_fedex_25
   - text_fedex_26
   - text_fedex_27
   - text_fedex_28
   - text_fedex_29
   - text_fedex_3
   - text_fedex_30
   - text_fedex_31
   - text_fedex_32
   - text_fedex_4
   - text_fedex_5
   - text_fedex_7
   - text_fedex_8
   - text_fedex_9
   - text_fedex_B
   - text_fedex_C
   - text_fedex_D
   - text_fedex_E
   - text_fedex_F
   - text_fedex_G
   - text_fedex_H
   - text_fedex_I
   - text_fedex_J
   - text_fedex_K
   - text_fedex_L
   - text_fedex_M
   - text_fedex_N
   - text_fedex_O
   - text_fedex_P
   - text_fedex_Q
   - text_fedex_R
   - text_fedex_S
   - text_fedex_T
   - text_fedex_U
   - text_fedex_V
   - text_fedex_W
   - text_fedex_X
   - text_fedex_Y
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_account
   - error_key
   - error_meter
   - error_password
   - error_permission
   - error_postcode
   - error_rest_acc_num
   - error_rest_api_key
   - error_rest_api_sec
   - heading_title
   - text_extension
   - text_fedex_1
   - text_fedex_10
   - text_fedex_11
   - text_fedex_12
   - text_fedex_13
   - text_fedex_14
   - text_fedex_15
   - text_fedex_16
   - text_fedex_17
   - text_fedex_18
   - text_fedex_19
   - text_fedex_2
   - text_fedex_20
   - text_fedex_21
   - text_fedex_22
   - text_fedex_23
   - text_fedex_24
   - text_fedex_25
   - text_fedex_26
   - text_fedex_27
   - text_fedex_28
   - text_fedex_29
   - text_fedex_3
   - text_fedex_30
   - text_fedex_31
   - text_fedex_32
   - text_fedex_4
   - text_fedex_5
   - text_fedex_7
   - text_fedex_8
   - text_fedex_9
   - text_fedex_B
   - text_fedex_C
   - text_fedex_D
   - text_fedex_E
   - text_fedex_F
   - text_fedex_G
   - text_fedex_H
   - text_fedex_I
   - text_fedex_J
   - text_fedex_K
   - text_fedex_L
   - text_fedex_M
   - text_fedex_N
   - text_fedex_O
   - text_fedex_P
   - text_fedex_Q
   - text_fedex_R
   - text_fedex_S
   - text_fedex_T
   - text_fedex_U
   - text_fedex_V
   - text_fedex_W
   - text_fedex_X
   - text_fedex_Y
   - text_home
   - text_success

❌ Missing in English:
   - error_account
   - error_key
   - error_meter
   - error_password
   - error_permission
   - error_postcode
   - error_rest_acc_num
   - error_rest_api_key
   - error_rest_api_sec
   - heading_title
   - text_extension
   - text_fedex_1
   - text_fedex_10
   - text_fedex_11
   - text_fedex_12
   - text_fedex_13
   - text_fedex_14
   - text_fedex_15
   - text_fedex_16
   - text_fedex_17
   - text_fedex_18
   - text_fedex_19
   - text_fedex_2
   - text_fedex_20
   - text_fedex_21
   - text_fedex_22
   - text_fedex_23
   - text_fedex_24
   - text_fedex_25
   - text_fedex_26
   - text_fedex_27
   - text_fedex_28
   - text_fedex_29
   - text_fedex_3
   - text_fedex_30
   - text_fedex_31
   - text_fedex_32
   - text_fedex_4
   - text_fedex_5
   - text_fedex_7
   - text_fedex_8
   - text_fedex_9
   - text_fedex_B
   - text_fedex_C
   - text_fedex_D
   - text_fedex_E
   - text_fedex_F
   - text_fedex_G
   - text_fedex_H
   - text_fedex_I
   - text_fedex_J
   - text_fedex_K
   - text_fedex_L
   - text_fedex_M
   - text_fedex_N
   - text_fedex_O
   - text_fedex_P
   - text_fedex_Q
   - text_fedex_R
   - text_fedex_S
   - text_fedex_T
   - text_fedex_U
   - text_fedex_V
   - text_fedex_W
   - text_fedex_X
   - text_fedex_Y
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_account = ""  # TODO: ترجمة عربية
   - error_key = ""  # TODO: ترجمة عربية
   - error_meter = ""  # TODO: ترجمة عربية
   - error_password = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_postcode = ""  # TODO: ترجمة عربية
   - error_rest_acc_num = ""  # TODO: ترجمة عربية
   - error_rest_api_key = ""  # TODO: ترجمة عربية
   - error_rest_api_sec = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_fedex_1 = ""  # TODO: ترجمة عربية
   - text_fedex_10 = ""  # TODO: ترجمة عربية
   - text_fedex_11 = ""  # TODO: ترجمة عربية
   - text_fedex_12 = ""  # TODO: ترجمة عربية
   - text_fedex_13 = ""  # TODO: ترجمة عربية
   - text_fedex_14 = ""  # TODO: ترجمة عربية
   - text_fedex_15 = ""  # TODO: ترجمة عربية
   - text_fedex_16 = ""  # TODO: ترجمة عربية
   - text_fedex_17 = ""  # TODO: ترجمة عربية
   - text_fedex_18 = ""  # TODO: ترجمة عربية
   - text_fedex_19 = ""  # TODO: ترجمة عربية
   - text_fedex_2 = ""  # TODO: ترجمة عربية
   - text_fedex_20 = ""  # TODO: ترجمة عربية
   - text_fedex_21 = ""  # TODO: ترجمة عربية
   - text_fedex_22 = ""  # TODO: ترجمة عربية
   - text_fedex_23 = ""  # TODO: ترجمة عربية
   - text_fedex_24 = ""  # TODO: ترجمة عربية
   - text_fedex_25 = ""  # TODO: ترجمة عربية
   - text_fedex_26 = ""  # TODO: ترجمة عربية
   - text_fedex_27 = ""  # TODO: ترجمة عربية
   - text_fedex_28 = ""  # TODO: ترجمة عربية
   - text_fedex_29 = ""  # TODO: ترجمة عربية
   - text_fedex_3 = ""  # TODO: ترجمة عربية
   - text_fedex_30 = ""  # TODO: ترجمة عربية
   - text_fedex_31 = ""  # TODO: ترجمة عربية
   - text_fedex_32 = ""  # TODO: ترجمة عربية
   - text_fedex_4 = ""  # TODO: ترجمة عربية
   - text_fedex_5 = ""  # TODO: ترجمة عربية
   - text_fedex_7 = ""  # TODO: ترجمة عربية
   - text_fedex_8 = ""  # TODO: ترجمة عربية
   - text_fedex_9 = ""  # TODO: ترجمة عربية
   - text_fedex_B = ""  # TODO: ترجمة عربية
   - text_fedex_C = ""  # TODO: ترجمة عربية
   - text_fedex_D = ""  # TODO: ترجمة عربية
   - text_fedex_E = ""  # TODO: ترجمة عربية
   - text_fedex_F = ""  # TODO: ترجمة عربية
   - text_fedex_G = ""  # TODO: ترجمة عربية
   - text_fedex_H = ""  # TODO: ترجمة عربية
   - text_fedex_I = ""  # TODO: ترجمة عربية
   - text_fedex_J = ""  # TODO: ترجمة عربية
   - text_fedex_K = ""  # TODO: ترجمة عربية
   - text_fedex_L = ""  # TODO: ترجمة عربية
   - text_fedex_M = ""  # TODO: ترجمة عربية
   - text_fedex_N = ""  # TODO: ترجمة عربية
   - text_fedex_O = ""  # TODO: ترجمة عربية
   - text_fedex_P = ""  # TODO: ترجمة عربية
   - text_fedex_Q = ""  # TODO: ترجمة عربية
   - text_fedex_R = ""  # TODO: ترجمة عربية
   - text_fedex_S = ""  # TODO: ترجمة عربية
   - text_fedex_T = ""  # TODO: ترجمة عربية
   - text_fedex_U = ""  # TODO: ترجمة عربية
   - text_fedex_V = ""  # TODO: ترجمة عربية
   - text_fedex_W = ""  # TODO: ترجمة عربية
   - text_fedex_X = ""  # TODO: ترجمة عربية
   - text_fedex_Y = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_account = ""  # TODO: English translation
   - error_key = ""  # TODO: English translation
   - error_meter = ""  # TODO: English translation
   - error_password = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_postcode = ""  # TODO: English translation
   - error_rest_acc_num = ""  # TODO: English translation
   - error_rest_api_key = ""  # TODO: English translation
   - error_rest_api_sec = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_fedex_1 = ""  # TODO: English translation
   - text_fedex_10 = ""  # TODO: English translation
   - text_fedex_11 = ""  # TODO: English translation
   - text_fedex_12 = ""  # TODO: English translation
   - text_fedex_13 = ""  # TODO: English translation
   - text_fedex_14 = ""  # TODO: English translation
   - text_fedex_15 = ""  # TODO: English translation
   - text_fedex_16 = ""  # TODO: English translation
   - text_fedex_17 = ""  # TODO: English translation
   - text_fedex_18 = ""  # TODO: English translation
   - text_fedex_19 = ""  # TODO: English translation
   - text_fedex_2 = ""  # TODO: English translation
   - text_fedex_20 = ""  # TODO: English translation
   - text_fedex_21 = ""  # TODO: English translation
   - text_fedex_22 = ""  # TODO: English translation
   - text_fedex_23 = ""  # TODO: English translation
   - text_fedex_24 = ""  # TODO: English translation
   - text_fedex_25 = ""  # TODO: English translation
   - text_fedex_26 = ""  # TODO: English translation
   - text_fedex_27 = ""  # TODO: English translation
   - text_fedex_28 = ""  # TODO: English translation
   - text_fedex_29 = ""  # TODO: English translation
   - text_fedex_3 = ""  # TODO: English translation
   - text_fedex_30 = ""  # TODO: English translation
   - text_fedex_31 = ""  # TODO: English translation
   - text_fedex_32 = ""  # TODO: English translation
   - text_fedex_4 = ""  # TODO: English translation
   - text_fedex_5 = ""  # TODO: English translation
   - text_fedex_7 = ""  # TODO: English translation
   - text_fedex_8 = ""  # TODO: English translation
   - text_fedex_9 = ""  # TODO: English translation
   - text_fedex_B = ""  # TODO: English translation
   - text_fedex_C = ""  # TODO: English translation
   - text_fedex_D = ""  # TODO: English translation
   - text_fedex_E = ""  # TODO: English translation
   - text_fedex_F = ""  # TODO: English translation
   - text_fedex_G = ""  # TODO: English translation
   - text_fedex_H = ""  # TODO: English translation
   - text_fedex_I = ""  # TODO: English translation
   - text_fedex_J = ""  # TODO: English translation
   - text_fedex_K = ""  # TODO: English translation
   - text_fedex_L = ""  # TODO: English translation
   - text_fedex_M = ""  # TODO: English translation
   - text_fedex_N = ""  # TODO: English translation
   - text_fedex_O = ""  # TODO: English translation
   - text_fedex_P = ""  # TODO: English translation
   - text_fedex_Q = ""  # TODO: English translation
   - text_fedex_R = ""  # TODO: English translation
   - text_fedex_S = ""  # TODO: English translation
   - text_fedex_T = ""  # TODO: English translation
   - text_fedex_U = ""  # TODO: English translation
   - text_fedex_V = ""  # TODO: English translation
   - text_fedex_W = ""  # TODO: English translation
   - text_fedex_X = ""  # TODO: English translation
   - text_fedex_Y = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
