📄 Route: hr/payroll_advanced
📂 Controller: controller\hr\payroll_advanced.php
🧱 Models used (1):
   - hr/payroll_advanced
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - datetime_format
   - error_cycle_id_required
   - error_cycle_name
   - error_cycle_not_found
   - error_pay_date
   - error_period_end
   - error_period_end_before_start
   - error_period_start
   - error_permission
   - error_required_fields
   - heading_title
   - text_create_cycle
   - text_cycle_approved
   - text_cycle_created
   - text_cycle_disbursed
   - text_home
   - text_payroll_dashboard
   - text_view_cycle

❌ Missing in Arabic:
   - date_format_short
   - datetime_format
   - error_cycle_id_required
   - error_cycle_name
   - error_cycle_not_found
   - error_pay_date
   - error_period_end
   - error_period_end_before_start
   - error_period_start
   - error_permission
   - error_required_fields
   - heading_title
   - text_create_cycle
   - text_cycle_approved
   - text_cycle_created
   - text_cycle_disbursed
   - text_home
   - text_payroll_dashboard
   - text_view_cycle

❌ Missing in English:
   - date_format_short
   - datetime_format
   - error_cycle_id_required
   - error_cycle_name
   - error_cycle_not_found
   - error_pay_date
   - error_period_end
   - error_period_end_before_start
   - error_period_start
   - error_permission
   - error_required_fields
   - heading_title
   - text_create_cycle
   - text_cycle_approved
   - text_cycle_created
   - text_cycle_disbursed
   - text_home
   - text_payroll_dashboard
   - text_view_cycle

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_cycle_id_required = ""  # TODO: ترجمة عربية
   - error_cycle_name = ""  # TODO: ترجمة عربية
   - error_cycle_not_found = ""  # TODO: ترجمة عربية
   - error_pay_date = ""  # TODO: ترجمة عربية
   - error_period_end = ""  # TODO: ترجمة عربية
   - error_period_end_before_start = ""  # TODO: ترجمة عربية
   - error_period_start = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_required_fields = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_create_cycle = ""  # TODO: ترجمة عربية
   - text_cycle_approved = ""  # TODO: ترجمة عربية
   - text_cycle_created = ""  # TODO: ترجمة عربية
   - text_cycle_disbursed = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_payroll_dashboard = ""  # TODO: ترجمة عربية
   - text_view_cycle = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_cycle_id_required = ""  # TODO: English translation
   - error_cycle_name = ""  # TODO: English translation
   - error_cycle_not_found = ""  # TODO: English translation
   - error_pay_date = ""  # TODO: English translation
   - error_period_end = ""  # TODO: English translation
   - error_period_end_before_start = ""  # TODO: English translation
   - error_period_start = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_required_fields = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_create_cycle = ""  # TODO: English translation
   - text_cycle_approved = ""  # TODO: English translation
   - text_cycle_created = ""  # TODO: English translation
   - text_cycle_disbursed = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_payroll_dashboard = ""  # TODO: English translation
   - text_view_cycle = ""  # TODO: English translation
