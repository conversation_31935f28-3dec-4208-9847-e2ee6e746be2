📄 Route: customer/customer
📂 Controller: controller\customer\customer.php
🧱 Models used (6):
   ✅ customer/customer (51 functions)
   ✅ setting/store (14 functions)
   ✅ customer/customer_group (7 functions)
   ✅ customer/custom_field (11 functions)
   ✅ tool/upload (8 functions)
   ✅ localisation/country (6 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\customer\customer.php (105 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\customer\customer.php (105 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (30):
   - date_format_short
   - error_bank_account_name
   - error_bank_account_number
   - error_city
   - error_confirm
   - error_country
   - error_custom_field
   - error_exists
   - error_lastname
   - error_paypal
   - error_permission
   - error_telephone
   - error_tracking
   - error_tracking_exists
   - error_zone
   - text_add
   - text_disabled
   - text_enabled
   - text_home
   - text_success
   ... و 10 متغير آخر

❌ Missing in Arabic (5):
   - date_format_short
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

❌ Missing in English (5):
   - date_format_short
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

🗄️ Database Tables Used (1):
   ❌ credit_limit

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - text_disabled
      - text_home
      - text_pagination
      - text_enabled
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 5 items
      - text_disabled
      - text_home
      - text_pagination
      - text_enabled
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 1 items
      - credit_limit

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 5 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:59
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.