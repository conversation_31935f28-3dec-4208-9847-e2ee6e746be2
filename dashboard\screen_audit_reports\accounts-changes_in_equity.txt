📄 Route: accounts/changes_in_equity
📂 Controller: controller\accounts\changes_in_equity.php
🧱 Models used (4):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/changes_in_equity (1 functions)
   ✅ accounts/chartaccount (19 functions)
   ✅ branch/branch (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\changes_in_equity.php (118 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\changes_in_equity.php (118 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (28):
   - button_filter
   - date_format_short
   - entry_date_end
   - entry_date_start
   - error_no_data
   - error_permission
   - print_title
   - text_account_name
   - text_changes_in_equity
   - text_closing_balance
   - text_decrease
   - text_from
   - text_home
   - text_increase
   - text_movement
   - text_no_results
   - text_opening_balance
   - text_period
   - text_success_generate
   - text_total
   ... و 8 متغير آخر

❌ Missing in Arabic (5):
   - code
   - date_format_short
   - direction
   - text_home
   - text_no_results

❌ Missing in English (5):
   - code
   - date_format_short
   - direction
   - text_home
   - text_no_results

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - code
      - text_home
      - text_no_results
      - direction
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 5 items
      - code
      - text_home
      - text_no_results
      - direction
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 5 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:37
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.