📄 Route: catalog/category
📂 Controller: controller\catalog\category.php
🧱 Models used (7):
   - catalog/category
   - catalog/filter
   - design/layout
   - design/seo_url
   - localisation/language
   - setting/store
   - tool/image
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\catalog\category.php
🇬🇧 English Language Files (1):
   - language\en-gb\catalog\category.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_keyword
   - error_meta_title
   - error_name
   - error_parent
   - error_permission
   - error_unique
   - error_warning
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_keyword
   - error_meta_title
   - error_name
   - error_parent
   - error_permission
   - error_unique
   - error_warning
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_keyword
   - error_meta_title
   - error_name
   - error_parent
   - error_permission
   - error_unique
   - error_warning
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_keyword = ""  # TODO: ترجمة عربية
   - error_meta_title = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_parent = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_unique = ""  # TODO: ترجمة عربية
   - error_warning = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_default = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_keyword = ""  # TODO: English translation
   - error_meta_title = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_parent = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_unique = ""  # TODO: English translation
   - error_warning = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_default = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
