📄 Route: accounts/journal_permissions
📂 Controller: controller\accounts\journal_permissions.php
🧱 Models used (6):
   ✅ accounts/journal_entry (19 functions)
   ✅ user/user_group (9 functions)
   ❌ accounts/fiscal_period (0 functions)
   ✅ accounts/chartaccount (19 functions)
   ✅ user/user (47 functions)
   ❌ accounts/approval (0 functions)
🎨 Twig templates (1):
   ✅ view\template\accounts\journal_permissions.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\accounts\journal_permissions.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\accounts\journal_permissions.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title
   🟢 MISSING_MODEL_FILES: 2 items
      - accounts/fiscal_period
      - accounts/approval

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ⚠️ 75%
📅 Analysis Date: 2025-07-21 18:32:41
🔧 Total Issues: 3

⚠️ جيد، لكن يحتاج بعض التحسينات.