📄 Route: extension/fraud/fraudlabspro
📂 Controller: controller\extension\fraud\fraudlabspro.php
🧱 Models used (3):
   ✅ setting/setting (5 functions)
   ✅ localisation/order_status (7 functions)
   ✅ extension/fraud/fraudlabspro (4 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\fraud\fraudlabspro.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\fraud\fraudlabspro.php (71 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\fraud\fraudlabspro.php (71 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (37):
   - action
   - column_left
   - entry_approve_status
   - entry_key
   - entry_order_status
   - entry_reject_status
   - entry_review_status
   - entry_score
   - entry_simulate_ip
   - entry_status
   - error_permission
   - fraud_fraudlabspro_simulate_ip
   - header
   - help_approve_status
   - help_review_status
   - help_simulate_ip
   - text_disabled
   - text_extension
   - text_home
   - text_success
   ... و 17 متغير آخر

❌ Missing in Arabic (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - fraud_fraudlabspro_key
   - fraud_fraudlabspro_score
   - fraud_fraudlabspro_simulate_ip
   - header
   - text_disabled
   - text_enabled
   - text_home

❌ Missing in English (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - fraud_fraudlabspro_key
   - fraud_fraudlabspro_score
   - fraud_fraudlabspro_simulate_ip
   - header
   - text_disabled
   - text_enabled
   - text_home

🗄️ Database Tables Used (2):
   ❌ fraud
   ❌ history

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 14 items
      - button_save
      - error_warning
      - cancel
      - column_left
      - text_disabled
   🟡 MISSING_ENGLISH_VARIABLES: 14 items
      - button_save
      - error_warning
      - cancel
      - column_left
      - text_disabled
   🔴 INVALID_DATABASE_TABLES: 2 items
      - history
      - fraud

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 14 متغير عربي و 14 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:22
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.