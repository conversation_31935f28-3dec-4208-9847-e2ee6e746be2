📄 Route: report/inventory_trends
📂 Controller: controller\report\inventory_trends.php
🧱 Models used (2):
   ✅ report/inventory_trends (6 functions)
   ✅ branch/branch (7 functions)
🎨 Twig templates (1):
   ✅ view\template\report\inventory_trends.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\report\inventory_trends.php (105 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\report\inventory_trends.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (34):
   - button_filter
   - header
   - text_adjustment
   - text_all_movements
   - text_day
   - text_forecast_analysis_desc
   - text_in
   - text_movement_analysis
   - text_out
   - text_quarter
   - text_seasonality_analysis_desc
   - text_trend_analysis
   - text_trend_analysis_desc
   - text_trend_down
   - text_trend_irregular
   - text_trend_seasonal
   - text_trend_stable
   - text_trend_up
   - text_week
   - text_year
   ... و 14 متغير آخر

❌ Missing in Arabic (4):
   - column_left
   - footer
   - header
   - text_home

❌ Missing in English (34):
   - header
   - text_adjustment
   - text_day
   - text_forecast_analysis_desc
   - text_in
   - text_movement_analysis
   - text_out
   - text_quarter
   - text_seasonality_analysis_desc
   - text_trend_analysis_desc
   - text_trend_irregular
   - text_trend_stable
   - text_trend_up
   - text_week
   - text_year
   ... و 19 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - text_home
      - header
      - footer
      - column_left
   🟡 MISSING_ENGLISH_VARIABLES: 34 items
      - text_day
      - text_out
      - text_movement_analysis
      - text_trend_stable
      - text_quarter

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 34 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.