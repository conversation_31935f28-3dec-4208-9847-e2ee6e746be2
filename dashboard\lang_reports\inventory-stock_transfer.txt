📄 Route: inventory/stock_transfer
📂 Controller: controller\inventory\stock_transfer.php
🧱 Models used (6):
   - catalog/product
   - common/central_service_manager
   - inventory/branch
   - inventory/stock_transfer
   - inventory/stock_transfer_enhanced
   - user/user
🎨 Twig templates (1):
   - view\template\inventory\stock_transfer.twig
🈯 Arabic Language Files (1):
   - language\ar\inventory\stock_transfer.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_from_branch
   - column_notes
   - column_priority
   - column_request_date
   - column_status
   - column_to_branch
   - column_total_items
   - column_total_value
   - column_transfer_name
   - column_transfer_number
   - column_transfer_type
   - column_user
   - date_format_short
   - datetime_format
   - error_approval_permission
   - error_exception
   - error_from_branch_required
   - error_insufficient_stock
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_not_shipped
   - error_permission
   - error_product_required
   - error_quantity_must_be_positive
   - error_quantity_required
   - error_request_date
   - error_same_branch
   - error_to_branch_required
   - error_transfer_already_completed
   - error_transfer_items_required
   - error_transfer_name
   - error_transfer_no_items
   - error_transfer_not_found
   - error_unit_cost_required
   - heading_title
   - success_approve
   - success_receive
   - success_ship
   - text_add
   - text_all
   - text_approved_success
   - text_completed_success
   - text_edit
   - text_home
   - text_no_reason
   - text_pagination
   - text_priority_high
   - text_priority_low
   - text_priority_normal
   - text_priority_urgent
   - text_received_success
   - text_shipped_success
   - text_status_approved
   - text_status_cancelled
   - text_status_completed
   - text_status_delivered
   - text_status_draft
   - text_status_in_transit
   - text_status_pending_approval
   - text_status_received
   - text_status_rejected
   - text_status_shipped
   - text_success
   - text_transfer_type_emergency
   - text_transfer_type_redistribution
   - text_transfer_type_regular
   - text_transfer_type_restock
   - text_transfer_type_return

❌ Missing in Arabic:
   - column_from_branch
   - column_notes
   - column_priority
   - column_request_date
   - column_status
   - column_to_branch
   - column_total_items
   - column_total_value
   - column_transfer_name
   - column_transfer_number
   - column_transfer_type
   - column_user
   - date_format_short
   - datetime_format
   - error_approval_permission
   - error_exception
   - error_from_branch_required
   - error_insufficient_stock
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_not_shipped
   - error_permission
   - error_product_required
   - error_quantity_must_be_positive
   - error_quantity_required
   - error_request_date
   - error_same_branch
   - error_to_branch_required
   - error_transfer_already_completed
   - error_transfer_items_required
   - error_transfer_name
   - error_transfer_no_items
   - error_transfer_not_found
   - error_unit_cost_required
   - heading_title
   - success_approve
   - success_receive
   - success_ship
   - text_add
   - text_all
   - text_approved_success
   - text_completed_success
   - text_edit
   - text_home
   - text_no_reason
   - text_pagination
   - text_priority_high
   - text_priority_low
   - text_priority_normal
   - text_priority_urgent
   - text_received_success
   - text_shipped_success
   - text_status_approved
   - text_status_cancelled
   - text_status_completed
   - text_status_delivered
   - text_status_draft
   - text_status_in_transit
   - text_status_pending_approval
   - text_status_received
   - text_status_rejected
   - text_status_shipped
   - text_success
   - text_transfer_type_emergency
   - text_transfer_type_redistribution
   - text_transfer_type_regular
   - text_transfer_type_restock
   - text_transfer_type_return

❌ Missing in English:
   - column_from_branch
   - column_notes
   - column_priority
   - column_request_date
   - column_status
   - column_to_branch
   - column_total_items
   - column_total_value
   - column_transfer_name
   - column_transfer_number
   - column_transfer_type
   - column_user
   - date_format_short
   - datetime_format
   - error_approval_permission
   - error_exception
   - error_from_branch_required
   - error_insufficient_stock
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_not_shipped
   - error_permission
   - error_product_required
   - error_quantity_must_be_positive
   - error_quantity_required
   - error_request_date
   - error_same_branch
   - error_to_branch_required
   - error_transfer_already_completed
   - error_transfer_items_required
   - error_transfer_name
   - error_transfer_no_items
   - error_transfer_not_found
   - error_unit_cost_required
   - heading_title
   - success_approve
   - success_receive
   - success_ship
   - text_add
   - text_all
   - text_approved_success
   - text_completed_success
   - text_edit
   - text_home
   - text_no_reason
   - text_pagination
   - text_priority_high
   - text_priority_low
   - text_priority_normal
   - text_priority_urgent
   - text_received_success
   - text_shipped_success
   - text_status_approved
   - text_status_cancelled
   - text_status_completed
   - text_status_delivered
   - text_status_draft
   - text_status_in_transit
   - text_status_pending_approval
   - text_status_received
   - text_status_rejected
   - text_status_shipped
   - text_success
   - text_transfer_type_emergency
   - text_transfer_type_redistribution
   - text_transfer_type_regular
   - text_transfer_type_restock
   - text_transfer_type_return

💡 Suggested Arabic Additions:
   - column_from_branch = ""  # TODO: ترجمة عربية
   - column_notes = ""  # TODO: ترجمة عربية
   - column_priority = ""  # TODO: ترجمة عربية
   - column_request_date = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_to_branch = ""  # TODO: ترجمة عربية
   - column_total_items = ""  # TODO: ترجمة عربية
   - column_total_value = ""  # TODO: ترجمة عربية
   - column_transfer_name = ""  # TODO: ترجمة عربية
   - column_transfer_number = ""  # TODO: ترجمة عربية
   - column_transfer_type = ""  # TODO: ترجمة عربية
   - column_user = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_approval_permission = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_from_branch_required = ""  # TODO: ترجمة عربية
   - error_insufficient_stock = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_not_shipped = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_product_required = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_quantity_required = ""  # TODO: ترجمة عربية
   - error_request_date = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_to_branch_required = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_items_required = ""  # TODO: ترجمة عربية
   - error_transfer_name = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - error_unit_cost_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - success_approve = ""  # TODO: ترجمة عربية
   - success_receive = ""  # TODO: ترجمة عربية
   - success_ship = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_approved_success = ""  # TODO: ترجمة عربية
   - text_completed_success = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_no_reason = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_priority_high = ""  # TODO: ترجمة عربية
   - text_priority_low = ""  # TODO: ترجمة عربية
   - text_priority_normal = ""  # TODO: ترجمة عربية
   - text_priority_urgent = ""  # TODO: ترجمة عربية
   - text_received_success = ""  # TODO: ترجمة عربية
   - text_shipped_success = ""  # TODO: ترجمة عربية
   - text_status_approved = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_completed = ""  # TODO: ترجمة عربية
   - text_status_delivered = ""  # TODO: ترجمة عربية
   - text_status_draft = ""  # TODO: ترجمة عربية
   - text_status_in_transit = ""  # TODO: ترجمة عربية
   - text_status_pending_approval = ""  # TODO: ترجمة عربية
   - text_status_received = ""  # TODO: ترجمة عربية
   - text_status_rejected = ""  # TODO: ترجمة عربية
   - text_status_shipped = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_transfer_type_emergency = ""  # TODO: ترجمة عربية
   - text_transfer_type_redistribution = ""  # TODO: ترجمة عربية
   - text_transfer_type_regular = ""  # TODO: ترجمة عربية
   - text_transfer_type_restock = ""  # TODO: ترجمة عربية
   - text_transfer_type_return = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_from_branch = ""  # TODO: English translation
   - column_notes = ""  # TODO: English translation
   - column_priority = ""  # TODO: English translation
   - column_request_date = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_to_branch = ""  # TODO: English translation
   - column_total_items = ""  # TODO: English translation
   - column_total_value = ""  # TODO: English translation
   - column_transfer_name = ""  # TODO: English translation
   - column_transfer_number = ""  # TODO: English translation
   - column_transfer_type = ""  # TODO: English translation
   - column_user = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_approval_permission = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_from_branch_required = ""  # TODO: English translation
   - error_insufficient_stock = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_not_shipped = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_product_required = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_quantity_required = ""  # TODO: English translation
   - error_request_date = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_to_branch_required = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_items_required = ""  # TODO: English translation
   - error_transfer_name = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - error_unit_cost_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - success_approve = ""  # TODO: English translation
   - success_receive = ""  # TODO: English translation
   - success_ship = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_approved_success = ""  # TODO: English translation
   - text_completed_success = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_no_reason = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_priority_high = ""  # TODO: English translation
   - text_priority_low = ""  # TODO: English translation
   - text_priority_normal = ""  # TODO: English translation
   - text_priority_urgent = ""  # TODO: English translation
   - text_received_success = ""  # TODO: English translation
   - text_shipped_success = ""  # TODO: English translation
   - text_status_approved = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_completed = ""  # TODO: English translation
   - text_status_delivered = ""  # TODO: English translation
   - text_status_draft = ""  # TODO: English translation
   - text_status_in_transit = ""  # TODO: English translation
   - text_status_pending_approval = ""  # TODO: English translation
   - text_status_received = ""  # TODO: English translation
   - text_status_rejected = ""  # TODO: English translation
   - text_status_shipped = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_transfer_type_emergency = ""  # TODO: English translation
   - text_transfer_type_redistribution = ""  # TODO: English translation
   - text_transfer_type_regular = ""  # TODO: English translation
   - text_transfer_type_restock = ""  # TODO: English translation
   - text_transfer_type_return = ""  # TODO: English translation
