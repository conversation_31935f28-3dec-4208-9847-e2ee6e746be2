📄 Route: common/header
📂 Controller: controller\common\header.php
🧱 Models used (15):
   - common/dashboard
   - communication/message
   - communication/messages
   - communication/teams
   - communication/unified_notification
   - core/central_service_manager
   - inventory/product
   - security/security_log
   - setting/store
   - tool/image
   - unified_document
   - user/user
   - workflow/approval
   - workflow/task
   - workflow/workflow
🎨 Twig templates (1):
   - view\template\common\header.twig
🈯 Arabic Language Files (1):
   - language\ar\common\header.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - code
   - direction
   - error_invalid_data
   - error_loading_notifications
   - error_permission
   - error_updating_notification
   - error_updating_notifications
   - text_all_notifications_marked_read
   - text_approval_request
   - text_current_stock
   - text_days
   - text_days_ago
   - text_due
   - text_expires_in
   - text_expiry_alert
   - text_from
   - text_hours_ago
   - text_logged
   - text_low_stock
   - text_message_from
   - text_minimum_limit
   - text_minutes_ago
   - text_moments_ago
   - text_now
   - text_overdue_by
   - text_overdue_task
   - text_upcoming_task

❌ Missing in Arabic:
   - code
   - direction
   - error_invalid_data
   - error_loading_notifications
   - error_permission
   - error_updating_notification
   - error_updating_notifications
   - text_all_notifications_marked_read
   - text_approval_request
   - text_current_stock
   - text_days
   - text_days_ago
   - text_due
   - text_expires_in
   - text_expiry_alert
   - text_from
   - text_hours_ago
   - text_logged
   - text_low_stock
   - text_message_from
   - text_minimum_limit
   - text_minutes_ago
   - text_moments_ago
   - text_now
   - text_overdue_by
   - text_overdue_task
   - text_upcoming_task

❌ Missing in English:
   - code
   - direction
   - error_invalid_data
   - error_loading_notifications
   - error_permission
   - error_updating_notification
   - error_updating_notifications
   - text_all_notifications_marked_read
   - text_approval_request
   - text_current_stock
   - text_days
   - text_days_ago
   - text_due
   - text_expires_in
   - text_expiry_alert
   - text_from
   - text_hours_ago
   - text_logged
   - text_low_stock
   - text_message_from
   - text_minimum_limit
   - text_minutes_ago
   - text_moments_ago
   - text_now
   - text_overdue_by
   - text_overdue_task
   - text_upcoming_task

💡 Suggested Arabic Additions:
   - code = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - error_invalid_data = ""  # TODO: ترجمة عربية
   - error_loading_notifications = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_updating_notification = ""  # TODO: ترجمة عربية
   - error_updating_notifications = ""  # TODO: ترجمة عربية
   - text_all_notifications_marked_read = ""  # TODO: ترجمة عربية
   - text_approval_request = ""  # TODO: ترجمة عربية
   - text_current_stock = ""  # TODO: ترجمة عربية
   - text_days = ""  # TODO: ترجمة عربية
   - text_days_ago = ""  # TODO: ترجمة عربية
   - text_due = ""  # TODO: ترجمة عربية
   - text_expires_in = ""  # TODO: ترجمة عربية
   - text_expiry_alert = ""  # TODO: ترجمة عربية
   - text_from = ""  # TODO: ترجمة عربية
   - text_hours_ago = ""  # TODO: ترجمة عربية
   - text_logged = ""  # TODO: ترجمة عربية
   - text_low_stock = ""  # TODO: ترجمة عربية
   - text_message_from = ""  # TODO: ترجمة عربية
   - text_minimum_limit = ""  # TODO: ترجمة عربية
   - text_minutes_ago = ""  # TODO: ترجمة عربية
   - text_moments_ago = ""  # TODO: ترجمة عربية
   - text_now = ""  # TODO: ترجمة عربية
   - text_overdue_by = ""  # TODO: ترجمة عربية
   - text_overdue_task = ""  # TODO: ترجمة عربية
   - text_upcoming_task = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - code = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - error_invalid_data = ""  # TODO: English translation
   - error_loading_notifications = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_updating_notification = ""  # TODO: English translation
   - error_updating_notifications = ""  # TODO: English translation
   - text_all_notifications_marked_read = ""  # TODO: English translation
   - text_approval_request = ""  # TODO: English translation
   - text_current_stock = ""  # TODO: English translation
   - text_days = ""  # TODO: English translation
   - text_days_ago = ""  # TODO: English translation
   - text_due = ""  # TODO: English translation
   - text_expires_in = ""  # TODO: English translation
   - text_expiry_alert = ""  # TODO: English translation
   - text_from = ""  # TODO: English translation
   - text_hours_ago = ""  # TODO: English translation
   - text_logged = ""  # TODO: English translation
   - text_low_stock = ""  # TODO: English translation
   - text_message_from = ""  # TODO: English translation
   - text_minimum_limit = ""  # TODO: English translation
   - text_minutes_ago = ""  # TODO: English translation
   - text_moments_ago = ""  # TODO: English translation
   - text_now = ""  # TODO: English translation
   - text_overdue_by = ""  # TODO: English translation
   - text_overdue_task = ""  # TODO: English translation
   - text_upcoming_task = ""  # TODO: English translation
