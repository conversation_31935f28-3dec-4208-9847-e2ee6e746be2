📄 Route: governance/risk_register
📂 Controller: controller\governance\risk_register.php
🧱 Models used (2):
   ✅ user/user_group (9 functions)
   ✅ governance/risk_register (5 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\governance\risk_register.php (44 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\governance\risk_register.php (44 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (4):
   - error_no_permission
   - heading_title
   - text_home
   - text_list

🗄️ Database Tables Used (1):
   ❌ risk

🚨 Issues Found (1):
   🔴 INVALID_DATABASE_TABLES: 1 items
      - risk

💡 Recommendations (1):
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ⚠️ 85%
📅 Analysis Date: 2025-07-21 18:33:03
🔧 Total Issues: 1

⚠️ جيد، لكن يحتاج بعض التحسينات.