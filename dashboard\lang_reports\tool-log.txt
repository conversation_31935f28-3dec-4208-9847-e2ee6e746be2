📄 Route: tool/log
📂 Controller: controller\tool\log.php
🧱 Models used (0):
🎨 Twig templates (1):
   - view\template\tool\log.twig
🈯 Arabic Language Files (1):
   - language\ar\tool\log.php
🇬🇧 English Language Files (1):
   - language\en-gb\tool\log.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - error_warning
   - heading_title
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_permission
   - error_warning
   - heading_title
   - text_home
   - text_success

❌ Missing in English:
   - error_permission
   - error_warning
   - heading_title
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - error_warning = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - error_warning = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
