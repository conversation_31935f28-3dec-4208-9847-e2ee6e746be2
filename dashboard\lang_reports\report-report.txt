📄 Route: report/report
📂 Controller: controller\report\report.php
🧱 Models used (1):
   - setting/extension
🎨 Twig templates (1):
   - view\template\report\report.twig
🈯 Arabic Language Files (1):
   - language\ar\report\report.php
🇬🇧 English Language Files (1):
   - language\en-gb\report\report.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - extension
   - heading_title
   - text_home

❌ Missing in Arabic:
   - extension
   - heading_title
   - text_home

❌ Missing in English:
   - extension
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - extension = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - extension = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
