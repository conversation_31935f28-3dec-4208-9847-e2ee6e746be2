📄 Route: purchase/notification_settings
📂 Controller: controller\purchase\notification_settings.php
🧱 Models used (4):
   ✅ purchase/notification_settings (25 functions)
   ✅ user/user_group (9 functions)
   ✅ user/user (47 functions)
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\purchase\notification_settings.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\purchase\notification_settings.php (243 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\purchase\notification_settings.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (123):
   - action
   - button_test_notification
   - delivery_push
   - entry_email_enabled
   - entry_event_name
   - entry_push_app_id
   - entry_sms_enabled
   - entry_sms_from_number
   - error_email_from_name
   - error_template_content
   - error_template_subject
   - event_purchase_order_created
   - event_purchase_order_delivered
   - help_email_from_address
   - help_push_provider
   - help_sms_enabled
   - priority_normal
   - provider_name
   - recipient_finance
   - tab_general
   ... و 103 متغير آخر

❌ Missing in Arabic (28):
   - action
   - analytics_url
   - button_close
   - column_left
   - date_format_short
   - email_from_address
   - email_from_name
   - email_reply_to
   - footer
   - header
   - provider_key
   - provider_name
   - sms_api_key
   - text_home
   - user_token
   ... و 13 متغير آخر

❌ Missing in English (123):
   - action
   - button_test_notification
   - entry_event_name
   - entry_push_app_id
   - error_email_from_name
   - error_template_content
   - error_template_subject
   - event_purchase_order_created
   - event_purchase_order_delivered
   - help_email_from_address
   - help_push_provider
   - help_sms_enabled
   - provider_name
   - recipient_finance
   - tab_general
   ... و 108 متغير آخر

🗄️ Database Tables Used (2):
   ❌ AYM
   ❌ existing

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 28 items
      - provider_key
      - button_close
      - action
      - provider_name
      - email_from_name
   🟡 MISSING_ENGLISH_VARIABLES: 123 items
      - recipient_finance
      - help_email_from_address
      - entry_push_app_id
      - error_template_content
      - action
   🔴 INVALID_DATABASE_TABLES: 2 items
      - AYM
      - existing

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 28 متغير عربي و 123 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:14
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.