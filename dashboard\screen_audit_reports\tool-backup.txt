📄 Route: tool/backup
📂 Controller: controller\tool\backup.php
🧱 Models used (1):
   ✅ tool/backup (2 functions)
🎨 Twig templates (1):
   ✅ view\template\tool\backup.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\tool\backup.php (9 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\tool\backup.php (9 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (22):
   - button_export
   - button_import
   - column_left
   - entry_progress
   - error_export
   - error_file
   - error_permission
   - error_warning
   - export
   - footer
   - header
   - heading_title
   - tab_backup
   - tab_restore
   - table
   - text_home
   - text_select_all
   - text_success
   - text_unselect_all
   - user_token
   ... و 2 متغير آخر

❌ Missing in Arabic (13):
   - button_export
   - button_import
   - column_left
   - error_warning
   - export
   - footer
   - header
   - success
   - table
   - text_home
   - text_select_all
   - text_unselect_all
   - user_token

❌ Missing in English (13):
   - button_export
   - button_import
   - column_left
   - error_warning
   - export
   - footer
   - header
   - success
   - table
   - text_home
   - text_select_all
   - text_unselect_all
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_export
      - error_warning
      - user_token
      - column_left
      - table
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_export
      - error_warning
      - user_token
      - column_left
      - table

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.