📄 Route: setting/store
📂 Controller: controller\setting\store.php
🧱 Models used (13):
   - catalog/information
   - customer/customer_group
   - design/layout
   - localisation/country
   - localisation/currency
   - localisation/language
   - localisation/location
   - localisation/order_status
   - sale/order
   - setting/extension
   - setting/setting
   - setting/store
   - tool/image
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\setting\store.php
🇬🇧 English Language Files (1):
   - language\en-gb\setting\store.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_address
   - error_customer_group_display
   - error_default
   - error_email
   - error_meta_title
   - error_name
   - error_owner
   - error_permission
   - error_store
   - error_telephone
   - error_url
   - error_warning
   - extension
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_settings
   - text_success

❌ Missing in Arabic:
   - error_address
   - error_customer_group_display
   - error_default
   - error_email
   - error_meta_title
   - error_name
   - error_owner
   - error_permission
   - error_store
   - error_telephone
   - error_url
   - error_warning
   - extension
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_settings
   - text_success

❌ Missing in English:
   - error_address
   - error_customer_group_display
   - error_default
   - error_email
   - error_meta_title
   - error_name
   - error_owner
   - error_permission
   - error_store
   - error_telephone
   - error_url
   - error_warning
   - extension
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_settings
   - text_success

💡 Suggested Arabic Additions:
   - error_address = ""  # TODO: ترجمة عربية
   - error_customer_group_display = ""  # TODO: ترجمة عربية
   - error_default = ""  # TODO: ترجمة عربية
   - error_email = ""  # TODO: ترجمة عربية
   - error_meta_title = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_owner = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_store = ""  # TODO: ترجمة عربية
   - error_telephone = ""  # TODO: ترجمة عربية
   - error_url = ""  # TODO: ترجمة عربية
   - error_warning = ""  # TODO: ترجمة عربية
   - extension = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_default = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_settings = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_address = ""  # TODO: English translation
   - error_customer_group_display = ""  # TODO: English translation
   - error_default = ""  # TODO: English translation
   - error_email = ""  # TODO: English translation
   - error_meta_title = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_owner = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_store = ""  # TODO: English translation
   - error_telephone = ""  # TODO: English translation
   - error_url = ""  # TODO: English translation
   - error_warning = ""  # TODO: English translation
   - extension = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_default = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_settings = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
