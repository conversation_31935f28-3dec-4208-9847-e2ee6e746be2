📄 Route: hr/employee
📂 Controller: controller\hr\employee.php
🧱 Models used (2):
   - hr/employee
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\hr\employee.php
🇬🇧 English Language Files (1):
   - language\en-gb\hr\employee.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add_document
   - button_add_employee
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_document_actions
   - column_document_description
   - column_document_name
   - column_employee_name
   - column_hiring_date
   - column_job_title
   - column_salary
   - column_status
   - error_attendance_failed
   - error_check_in_required
   - error_date_required
   - error_employee_required
   - error_end_date_required
   - error_invalid_data
   - error_invalid_date_range
   - error_invalid_request
   - error_leave_process_failed
   - error_leave_request_failed
   - error_leave_type_required
   - error_not_found
   - error_performance_review_failed
   - error_period_end_required
   - error_period_start_required
   - error_permission
   - error_rating_required
   - error_required
   - error_start_date_required
   - error_upload
   - heading_title
   - text_active
   - text_add_document
   - text_add_employee
   - text_ajax_error
   - text_all_statuses
   - text_attendance_recorded
   - text_confirm_delete
   - text_document_description
   - text_document_name
   - text_documents
   - text_edit_employee
   - text_employee_list
   - text_employee_name
   - text_employee_profile
   - text_enter_employee_name
   - text_file
   - text_filter
   - text_hiring_date
   - text_home
   - text_hr_dashboard
   - text_inactive
   - text_job_title
   - text_leave_request_submitted
   - text_performance_review_created
   - text_salary
   - text_save_employee_first
   - text_select_user
   - text_status
   - text_success_add
   - text_success_delete
   - text_success_document_add
   - text_success_document_delete
   - text_success_edit
   - text_terminated
   - text_user_id

❌ Missing in Arabic:
   - button_add_document
   - button_add_employee
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_document_actions
   - column_document_description
   - column_document_name
   - column_employee_name
   - column_hiring_date
   - column_job_title
   - column_salary
   - column_status
   - error_attendance_failed
   - error_check_in_required
   - error_date_required
   - error_employee_required
   - error_end_date_required
   - error_invalid_data
   - error_invalid_date_range
   - error_invalid_request
   - error_leave_process_failed
   - error_leave_request_failed
   - error_leave_type_required
   - error_not_found
   - error_performance_review_failed
   - error_period_end_required
   - error_period_start_required
   - error_permission
   - error_rating_required
   - error_required
   - error_start_date_required
   - error_upload
   - heading_title
   - text_active
   - text_add_document
   - text_add_employee
   - text_ajax_error
   - text_all_statuses
   - text_attendance_recorded
   - text_confirm_delete
   - text_document_description
   - text_document_name
   - text_documents
   - text_edit_employee
   - text_employee_list
   - text_employee_name
   - text_employee_profile
   - text_enter_employee_name
   - text_file
   - text_filter
   - text_hiring_date
   - text_home
   - text_hr_dashboard
   - text_inactive
   - text_job_title
   - text_leave_request_submitted
   - text_performance_review_created
   - text_salary
   - text_save_employee_first
   - text_select_user
   - text_status
   - text_success_add
   - text_success_delete
   - text_success_document_add
   - text_success_document_delete
   - text_success_edit
   - text_terminated
   - text_user_id

❌ Missing in English:
   - button_add_document
   - button_add_employee
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_document_actions
   - column_document_description
   - column_document_name
   - column_employee_name
   - column_hiring_date
   - column_job_title
   - column_salary
   - column_status
   - error_attendance_failed
   - error_check_in_required
   - error_date_required
   - error_employee_required
   - error_end_date_required
   - error_invalid_data
   - error_invalid_date_range
   - error_invalid_request
   - error_leave_process_failed
   - error_leave_request_failed
   - error_leave_type_required
   - error_not_found
   - error_performance_review_failed
   - error_period_end_required
   - error_period_start_required
   - error_permission
   - error_rating_required
   - error_required
   - error_start_date_required
   - error_upload
   - heading_title
   - text_active
   - text_add_document
   - text_add_employee
   - text_ajax_error
   - text_all_statuses
   - text_attendance_recorded
   - text_confirm_delete
   - text_document_description
   - text_document_name
   - text_documents
   - text_edit_employee
   - text_employee_list
   - text_employee_name
   - text_employee_profile
   - text_enter_employee_name
   - text_file
   - text_filter
   - text_hiring_date
   - text_home
   - text_hr_dashboard
   - text_inactive
   - text_job_title
   - text_leave_request_submitted
   - text_performance_review_created
   - text_salary
   - text_save_employee_first
   - text_select_user
   - text_status
   - text_success_add
   - text_success_delete
   - text_success_document_add
   - text_success_document_delete
   - text_success_edit
   - text_terminated
   - text_user_id

💡 Suggested Arabic Additions:
   - button_add_document = ""  # TODO: ترجمة عربية
   - button_add_employee = ""  # TODO: ترجمة عربية
   - button_close = ""  # TODO: ترجمة عربية
   - button_filter = ""  # TODO: ترجمة عربية
   - button_reset = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - column_actions = ""  # TODO: ترجمة عربية
   - column_document_actions = ""  # TODO: ترجمة عربية
   - column_document_description = ""  # TODO: ترجمة عربية
   - column_document_name = ""  # TODO: ترجمة عربية
   - column_employee_name = ""  # TODO: ترجمة عربية
   - column_hiring_date = ""  # TODO: ترجمة عربية
   - column_job_title = ""  # TODO: ترجمة عربية
   - column_salary = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - error_attendance_failed = ""  # TODO: ترجمة عربية
   - error_check_in_required = ""  # TODO: ترجمة عربية
   - error_date_required = ""  # TODO: ترجمة عربية
   - error_employee_required = ""  # TODO: ترجمة عربية
   - error_end_date_required = ""  # TODO: ترجمة عربية
   - error_invalid_data = ""  # TODO: ترجمة عربية
   - error_invalid_date_range = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_leave_process_failed = ""  # TODO: ترجمة عربية
   - error_leave_request_failed = ""  # TODO: ترجمة عربية
   - error_leave_type_required = ""  # TODO: ترجمة عربية
   - error_not_found = ""  # TODO: ترجمة عربية
   - error_performance_review_failed = ""  # TODO: ترجمة عربية
   - error_period_end_required = ""  # TODO: ترجمة عربية
   - error_period_start_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_rating_required = ""  # TODO: ترجمة عربية
   - error_required = ""  # TODO: ترجمة عربية
   - error_start_date_required = ""  # TODO: ترجمة عربية
   - error_upload = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_active = ""  # TODO: ترجمة عربية
   - text_add_document = ""  # TODO: ترجمة عربية
   - text_add_employee = ""  # TODO: ترجمة عربية
   - text_ajax_error = ""  # TODO: ترجمة عربية
   - text_all_statuses = ""  # TODO: ترجمة عربية
   - text_attendance_recorded = ""  # TODO: ترجمة عربية
   - text_confirm_delete = ""  # TODO: ترجمة عربية
   - text_document_description = ""  # TODO: ترجمة عربية
   - text_document_name = ""  # TODO: ترجمة عربية
   - text_documents = ""  # TODO: ترجمة عربية
   - text_edit_employee = ""  # TODO: ترجمة عربية
   - text_employee_list = ""  # TODO: ترجمة عربية
   - text_employee_name = ""  # TODO: ترجمة عربية
   - text_employee_profile = ""  # TODO: ترجمة عربية
   - text_enter_employee_name = ""  # TODO: ترجمة عربية
   - text_file = ""  # TODO: ترجمة عربية
   - text_filter = ""  # TODO: ترجمة عربية
   - text_hiring_date = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_hr_dashboard = ""  # TODO: ترجمة عربية
   - text_inactive = ""  # TODO: ترجمة عربية
   - text_job_title = ""  # TODO: ترجمة عربية
   - text_leave_request_submitted = ""  # TODO: ترجمة عربية
   - text_performance_review_created = ""  # TODO: ترجمة عربية
   - text_salary = ""  # TODO: ترجمة عربية
   - text_save_employee_first = ""  # TODO: ترجمة عربية
   - text_select_user = ""  # TODO: ترجمة عربية
   - text_status = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_document_add = ""  # TODO: ترجمة عربية
   - text_success_document_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية
   - text_terminated = ""  # TODO: ترجمة عربية
   - text_user_id = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add_document = ""  # TODO: English translation
   - button_add_employee = ""  # TODO: English translation
   - button_close = ""  # TODO: English translation
   - button_filter = ""  # TODO: English translation
   - button_reset = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - column_actions = ""  # TODO: English translation
   - column_document_actions = ""  # TODO: English translation
   - column_document_description = ""  # TODO: English translation
   - column_document_name = ""  # TODO: English translation
   - column_employee_name = ""  # TODO: English translation
   - column_hiring_date = ""  # TODO: English translation
   - column_job_title = ""  # TODO: English translation
   - column_salary = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - error_attendance_failed = ""  # TODO: English translation
   - error_check_in_required = ""  # TODO: English translation
   - error_date_required = ""  # TODO: English translation
   - error_employee_required = ""  # TODO: English translation
   - error_end_date_required = ""  # TODO: English translation
   - error_invalid_data = ""  # TODO: English translation
   - error_invalid_date_range = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_leave_process_failed = ""  # TODO: English translation
   - error_leave_request_failed = ""  # TODO: English translation
   - error_leave_type_required = ""  # TODO: English translation
   - error_not_found = ""  # TODO: English translation
   - error_performance_review_failed = ""  # TODO: English translation
   - error_period_end_required = ""  # TODO: English translation
   - error_period_start_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_rating_required = ""  # TODO: English translation
   - error_required = ""  # TODO: English translation
   - error_start_date_required = ""  # TODO: English translation
   - error_upload = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_active = ""  # TODO: English translation
   - text_add_document = ""  # TODO: English translation
   - text_add_employee = ""  # TODO: English translation
   - text_ajax_error = ""  # TODO: English translation
   - text_all_statuses = ""  # TODO: English translation
   - text_attendance_recorded = ""  # TODO: English translation
   - text_confirm_delete = ""  # TODO: English translation
   - text_document_description = ""  # TODO: English translation
   - text_document_name = ""  # TODO: English translation
   - text_documents = ""  # TODO: English translation
   - text_edit_employee = ""  # TODO: English translation
   - text_employee_list = ""  # TODO: English translation
   - text_employee_name = ""  # TODO: English translation
   - text_employee_profile = ""  # TODO: English translation
   - text_enter_employee_name = ""  # TODO: English translation
   - text_file = ""  # TODO: English translation
   - text_filter = ""  # TODO: English translation
   - text_hiring_date = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_hr_dashboard = ""  # TODO: English translation
   - text_inactive = ""  # TODO: English translation
   - text_job_title = ""  # TODO: English translation
   - text_leave_request_submitted = ""  # TODO: English translation
   - text_performance_review_created = ""  # TODO: English translation
   - text_salary = ""  # TODO: English translation
   - text_save_employee_first = ""  # TODO: English translation
   - text_select_user = ""  # TODO: English translation
   - text_status = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_document_add = ""  # TODO: English translation
   - text_success_document_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
   - text_terminated = ""  # TODO: English translation
   - text_user_id = ""  # TODO: English translation
