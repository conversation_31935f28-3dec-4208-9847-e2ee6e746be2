📄 Route: accounts/cash_flow
📂 Controller: controller\accounts\cash_flow.php
🧱 Models used (4):
   - accounts/cash_flow
   - accounts/chartaccount
   - branch/branch
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\cash_flow.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\cash_flow.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_account_required
   - error_date_end
   - error_date_range
   - error_date_required
   - error_date_start
   - error_no_analysis_data
   - error_no_data
   - error_permission
   - heading_title
   - text_activity
   - text_advanced_analysis
   - text_advanced_view
   - text_amount
   - text_analysis_view
   - text_comprehensive_analysis
   - text_home
   - text_no_results
   - text_success_analysis
   - text_success_generate

❌ Missing in Arabic:
   - error_account_required
   - error_date_end
   - error_date_range
   - error_date_required
   - error_date_start
   - error_no_analysis_data
   - error_no_data
   - error_permission
   - heading_title
   - text_activity
   - text_advanced_analysis
   - text_advanced_view
   - text_amount
   - text_analysis_view
   - text_comprehensive_analysis
   - text_home
   - text_no_results
   - text_success_analysis
   - text_success_generate

❌ Missing in English:
   - error_account_required
   - error_date_end
   - error_date_range
   - error_date_required
   - error_date_start
   - error_no_analysis_data
   - error_no_data
   - error_permission
   - heading_title
   - text_activity
   - text_advanced_analysis
   - text_advanced_view
   - text_amount
   - text_analysis_view
   - text_comprehensive_analysis
   - text_home
   - text_no_results
   - text_success_analysis
   - text_success_generate

💡 Suggested Arabic Additions:
   - error_account_required = ""  # TODO: ترجمة عربية
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_date_required = ""  # TODO: ترجمة عربية
   - error_date_start = ""  # TODO: ترجمة عربية
   - error_no_analysis_data = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_activity = ""  # TODO: ترجمة عربية
   - text_advanced_analysis = ""  # TODO: ترجمة عربية
   - text_advanced_view = ""  # TODO: ترجمة عربية
   - text_amount = ""  # TODO: ترجمة عربية
   - text_analysis_view = ""  # TODO: ترجمة عربية
   - text_comprehensive_analysis = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_success_analysis = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_account_required = ""  # TODO: English translation
   - error_date_end = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_date_required = ""  # TODO: English translation
   - error_date_start = ""  # TODO: English translation
   - error_no_analysis_data = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_activity = ""  # TODO: English translation
   - text_advanced_analysis = ""  # TODO: English translation
   - text_advanced_view = ""  # TODO: English translation
   - text_amount = ""  # TODO: English translation
   - text_analysis_view = ""  # TODO: English translation
   - text_comprehensive_analysis = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_success_analysis = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
