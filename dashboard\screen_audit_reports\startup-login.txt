📄 Route: startup/login
📂 Controller: controller\startup\login.php
🧱 Models used (0):
🎨 Twig templates (1):
   ✅ view\template\startup\login.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\startup\login.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\startup\login.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:18
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.