📄 Route: branch/branch
📂 Controller: controller\branch\branch.php
🧱 Models used (4):
   - branch/branch
   - localisation/country
   - localisation/zone
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\branch\branch.php
🇬🇧 English Language Files (1):
   - language\en-gb\branch\branch.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_eta_branch_id_required
   - error_name
   - error_no_main_branch
   - error_permission
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_eta_branch_id_required
   - error_name
   - error_no_main_branch
   - error_permission
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_eta_branch_id_required
   - error_name
   - error_no_main_branch
   - error_permission
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_eta_branch_id_required = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_no_main_branch = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_eta_branch_id_required = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_no_main_branch = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
