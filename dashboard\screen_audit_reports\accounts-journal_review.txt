📄 Route: accounts/journal_review
📂 Controller: controller\accounts\journal_review.php
🧱 Models used (1):
   ✅ accounts/journal_review (14 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\accounts\journal_review.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\accounts\journal_review.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (10):
   - date_format_short
   - datetime_format
   - error_journal_id
   - error_permission
   - error_rejection_reason
   - heading_title
   - text_home
   - text_pagination
   - text_success_approved
   - text_success_rejected

❌ Missing in Arabic (10):
   - date_format_short
   - datetime_format
   - error_journal_id
   - error_permission
   - error_rejection_reason
   - heading_title
   - text_home
   - text_pagination
   - text_success_approved
   - text_success_rejected

❌ Missing in English (10):
   - date_format_short
   - datetime_format
   - error_journal_id
   - error_permission
   - error_rejection_reason
   - heading_title
   - text_home
   - text_pagination
   - text_success_approved
   - text_success_rejected

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 10 items
      - text_home
      - error_permission
      - datetime_format
      - text_pagination
      - text_success_rejected
   🟡 MISSING_ENGLISH_VARIABLES: 10 items
      - text_home
      - error_permission
      - datetime_format
      - text_pagination
      - text_success_rejected

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 10 متغير عربي و 10 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:42
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.