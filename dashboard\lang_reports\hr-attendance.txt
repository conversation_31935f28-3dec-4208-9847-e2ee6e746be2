📄 Route: hr/attendance
📂 Controller: controller\hr\attendance.php
🧱 Models used (2):
   - hr/attendance
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\hr\attendance.php
🇬🇧 English Language Files (1):
   - language\en-gb\hr\attendance.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add_attendance
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_checkin
   - column_checkout
   - column_date
   - column_employee
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_absent
   - text_add_attendance
   - text_ajax_error
   - text_attendance_list
   - text_checkin
   - text_checkout
   - text_confirm_delete
   - text_date
   - text_date_end
   - text_date_start
   - text_edit_attendance
   - text_employee
   - text_filter
   - text_home
   - text_late
   - text_notes
   - text_on_leave
   - text_present
   - text_select_employee
   - text_status
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in Arabic:
   - button_add_attendance
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_checkin
   - column_checkout
   - column_date
   - column_employee
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_absent
   - text_add_attendance
   - text_ajax_error
   - text_attendance_list
   - text_checkin
   - text_checkout
   - text_confirm_delete
   - text_date
   - text_date_end
   - text_date_start
   - text_edit_attendance
   - text_employee
   - text_filter
   - text_home
   - text_late
   - text_notes
   - text_on_leave
   - text_present
   - text_select_employee
   - text_status
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in English:
   - button_add_attendance
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_checkin
   - column_checkout
   - column_date
   - column_employee
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_absent
   - text_add_attendance
   - text_ajax_error
   - text_attendance_list
   - text_checkin
   - text_checkout
   - text_confirm_delete
   - text_date
   - text_date_end
   - text_date_start
   - text_edit_attendance
   - text_employee
   - text_filter
   - text_home
   - text_late
   - text_notes
   - text_on_leave
   - text_present
   - text_select_employee
   - text_status
   - text_success_add
   - text_success_delete
   - text_success_edit

💡 Suggested Arabic Additions:
   - button_add_attendance = ""  # TODO: ترجمة عربية
   - button_close = ""  # TODO: ترجمة عربية
   - button_filter = ""  # TODO: ترجمة عربية
   - button_reset = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - column_actions = ""  # TODO: ترجمة عربية
   - column_checkin = ""  # TODO: ترجمة عربية
   - column_checkout = ""  # TODO: ترجمة عربية
   - column_date = ""  # TODO: ترجمة عربية
   - column_employee = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_absent = ""  # TODO: ترجمة عربية
   - text_add_attendance = ""  # TODO: ترجمة عربية
   - text_ajax_error = ""  # TODO: ترجمة عربية
   - text_attendance_list = ""  # TODO: ترجمة عربية
   - text_checkin = ""  # TODO: ترجمة عربية
   - text_checkout = ""  # TODO: ترجمة عربية
   - text_confirm_delete = ""  # TODO: ترجمة عربية
   - text_date = ""  # TODO: ترجمة عربية
   - text_date_end = ""  # TODO: ترجمة عربية
   - text_date_start = ""  # TODO: ترجمة عربية
   - text_edit_attendance = ""  # TODO: ترجمة عربية
   - text_employee = ""  # TODO: ترجمة عربية
   - text_filter = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_late = ""  # TODO: ترجمة عربية
   - text_notes = ""  # TODO: ترجمة عربية
   - text_on_leave = ""  # TODO: ترجمة عربية
   - text_present = ""  # TODO: ترجمة عربية
   - text_select_employee = ""  # TODO: ترجمة عربية
   - text_status = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add_attendance = ""  # TODO: English translation
   - button_close = ""  # TODO: English translation
   - button_filter = ""  # TODO: English translation
   - button_reset = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - column_actions = ""  # TODO: English translation
   - column_checkin = ""  # TODO: English translation
   - column_checkout = ""  # TODO: English translation
   - column_date = ""  # TODO: English translation
   - column_employee = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_absent = ""  # TODO: English translation
   - text_add_attendance = ""  # TODO: English translation
   - text_ajax_error = ""  # TODO: English translation
   - text_attendance_list = ""  # TODO: English translation
   - text_checkin = ""  # TODO: English translation
   - text_checkout = ""  # TODO: English translation
   - text_confirm_delete = ""  # TODO: English translation
   - text_date = ""  # TODO: English translation
   - text_date_end = ""  # TODO: English translation
   - text_date_start = ""  # TODO: English translation
   - text_edit_attendance = ""  # TODO: English translation
   - text_employee = ""  # TODO: English translation
   - text_filter = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_late = ""  # TODO: English translation
   - text_notes = ""  # TODO: English translation
   - text_on_leave = ""  # TODO: English translation
   - text_present = ""  # TODO: English translation
   - text_select_employee = ""  # TODO: English translation
   - text_status = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
