📄 Route: extension/feed/google_base
📂 Controller: controller\extension\feed\google_base.php
🧱 Models used (2):
   - extension/feed/google_base
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\feed\google_base.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\feed\google_base.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\feed\google_base.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_filetype
   - error_permission
   - error_upload
   - heading_title
   - text_extension
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_filetype
   - error_permission
   - error_upload
   - heading_title
   - text_extension
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_filetype
   - error_permission
   - error_upload
   - heading_title
   - text_extension
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_filetype = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_upload = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_filetype = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_upload = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
