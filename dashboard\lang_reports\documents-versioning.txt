📄 Route: documents/versioning
📂 Controller: controller\documents\versioning.php
🧱 Models used (7):
   - communication/unified_notification
   - core/central_service_manager
   - documents/archive
   - documents/versioning
   - logging/user_activity
   - user/user
   - workflow/workflow
🎨 Twig templates (1):
   - view\template\documents\versioning.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_action_required
   - error_approval_validation
   - error_bulk_operation_validation
   - error_change_description_required
   - error_document_required
   - error_invalid_action
   - error_permission
   - error_title_required
   - error_version_creation_failed
   - error_version_id_required
   - error_version_type_required
   - heading_title
   - text_approval_committee
   - text_approval_manager
   - text_approval_none
   - text_approval_processed
   - text_approval_supervisor
   - text_audit_reports
   - text_bulk_operation_result
   - text_catalog_documents
   - text_catalog_documents_desc
   - text_category_definitions
   - text_change_content_update
   - text_change_correction
   - text_change_enhancement
   - text_change_format
   - text_change_regulatory
   - text_compare_versions
   - text_compliance_documents
   - text_compliance_documents_desc
   - text_compliance_standards
   - text_create_version
   - text_feature_auto_backup
   - text_feature_auto_backup_desc
   - text_feature_branching
   - text_feature_branching_desc
   - text_feature_change_tracking
   - text_feature_change_tracking_desc
   - text_feature_merge_capabilities
   - text_feature_merge_capabilities_desc
   - text_feature_rollback
   - text_feature_rollback_desc
   - text_feature_version_comparison
   - text_feature_version_comparison_desc
   - text_home
   - text_inventory_documents
   - text_inventory_documents_desc
   - text_movement_logs
   - text_price_lists
   - text_procedure_code
   - text_procedure_documents
   - text_procedure_documents_desc
   - text_product_id
   - text_product_specifications
   - text_quality_manuals
   - text_safety_procedures
   - text_safety_requirements
   - text_sop_documents
   - text_spec_version
   - text_status_approved
   - text_status_approved_desc
   - text_status_archived
   - text_status_archived_desc
   - text_status_draft
   - text_status_draft_desc
   - text_status_obsolete
   - text_status_obsolete_desc
   - text_status_published
   - text_status_published_desc
   - text_status_review
   - text_status_review_desc
   - text_stock_reports
   - text_strategy_major_minor
   - text_strategy_major_minor_desc
   - text_strategy_regulatory
   - text_strategy_regulatory_desc
   - text_strategy_semantic
   - text_strategy_semantic_desc
   - text_strategy_sequential
   - text_strategy_sequential_desc
   - text_strategy_timestamp
   - text_strategy_timestamp_desc
   - text_template_inventory_procedure
   - text_template_product_spec
   - text_test_results
   - text_training_required
   - text_version_created
   - text_version_published
   - text_version_type_hotfix
   - text_version_type_major
   - text_version_type_minor
   - text_version_type_patch
   - text_warehouse_scope
   - text_work_instructions

❌ Missing in Arabic:
   - error_action_required
   - error_approval_validation
   - error_bulk_operation_validation
   - error_change_description_required
   - error_document_required
   - error_invalid_action
   - error_permission
   - error_title_required
   - error_version_creation_failed
   - error_version_id_required
   - error_version_type_required
   - heading_title
   - text_approval_committee
   - text_approval_manager
   - text_approval_none
   - text_approval_processed
   - text_approval_supervisor
   - text_audit_reports
   - text_bulk_operation_result
   - text_catalog_documents
   - text_catalog_documents_desc
   - text_category_definitions
   - text_change_content_update
   - text_change_correction
   - text_change_enhancement
   - text_change_format
   - text_change_regulatory
   - text_compare_versions
   - text_compliance_documents
   - text_compliance_documents_desc
   - text_compliance_standards
   - text_create_version
   - text_feature_auto_backup
   - text_feature_auto_backup_desc
   - text_feature_branching
   - text_feature_branching_desc
   - text_feature_change_tracking
   - text_feature_change_tracking_desc
   - text_feature_merge_capabilities
   - text_feature_merge_capabilities_desc
   - text_feature_rollback
   - text_feature_rollback_desc
   - text_feature_version_comparison
   - text_feature_version_comparison_desc
   - text_home
   - text_inventory_documents
   - text_inventory_documents_desc
   - text_movement_logs
   - text_price_lists
   - text_procedure_code
   - text_procedure_documents
   - text_procedure_documents_desc
   - text_product_id
   - text_product_specifications
   - text_quality_manuals
   - text_safety_procedures
   - text_safety_requirements
   - text_sop_documents
   - text_spec_version
   - text_status_approved
   - text_status_approved_desc
   - text_status_archived
   - text_status_archived_desc
   - text_status_draft
   - text_status_draft_desc
   - text_status_obsolete
   - text_status_obsolete_desc
   - text_status_published
   - text_status_published_desc
   - text_status_review
   - text_status_review_desc
   - text_stock_reports
   - text_strategy_major_minor
   - text_strategy_major_minor_desc
   - text_strategy_regulatory
   - text_strategy_regulatory_desc
   - text_strategy_semantic
   - text_strategy_semantic_desc
   - text_strategy_sequential
   - text_strategy_sequential_desc
   - text_strategy_timestamp
   - text_strategy_timestamp_desc
   - text_template_inventory_procedure
   - text_template_product_spec
   - text_test_results
   - text_training_required
   - text_version_created
   - text_version_published
   - text_version_type_hotfix
   - text_version_type_major
   - text_version_type_minor
   - text_version_type_patch
   - text_warehouse_scope
   - text_work_instructions

❌ Missing in English:
   - error_action_required
   - error_approval_validation
   - error_bulk_operation_validation
   - error_change_description_required
   - error_document_required
   - error_invalid_action
   - error_permission
   - error_title_required
   - error_version_creation_failed
   - error_version_id_required
   - error_version_type_required
   - heading_title
   - text_approval_committee
   - text_approval_manager
   - text_approval_none
   - text_approval_processed
   - text_approval_supervisor
   - text_audit_reports
   - text_bulk_operation_result
   - text_catalog_documents
   - text_catalog_documents_desc
   - text_category_definitions
   - text_change_content_update
   - text_change_correction
   - text_change_enhancement
   - text_change_format
   - text_change_regulatory
   - text_compare_versions
   - text_compliance_documents
   - text_compliance_documents_desc
   - text_compliance_standards
   - text_create_version
   - text_feature_auto_backup
   - text_feature_auto_backup_desc
   - text_feature_branching
   - text_feature_branching_desc
   - text_feature_change_tracking
   - text_feature_change_tracking_desc
   - text_feature_merge_capabilities
   - text_feature_merge_capabilities_desc
   - text_feature_rollback
   - text_feature_rollback_desc
   - text_feature_version_comparison
   - text_feature_version_comparison_desc
   - text_home
   - text_inventory_documents
   - text_inventory_documents_desc
   - text_movement_logs
   - text_price_lists
   - text_procedure_code
   - text_procedure_documents
   - text_procedure_documents_desc
   - text_product_id
   - text_product_specifications
   - text_quality_manuals
   - text_safety_procedures
   - text_safety_requirements
   - text_sop_documents
   - text_spec_version
   - text_status_approved
   - text_status_approved_desc
   - text_status_archived
   - text_status_archived_desc
   - text_status_draft
   - text_status_draft_desc
   - text_status_obsolete
   - text_status_obsolete_desc
   - text_status_published
   - text_status_published_desc
   - text_status_review
   - text_status_review_desc
   - text_stock_reports
   - text_strategy_major_minor
   - text_strategy_major_minor_desc
   - text_strategy_regulatory
   - text_strategy_regulatory_desc
   - text_strategy_semantic
   - text_strategy_semantic_desc
   - text_strategy_sequential
   - text_strategy_sequential_desc
   - text_strategy_timestamp
   - text_strategy_timestamp_desc
   - text_template_inventory_procedure
   - text_template_product_spec
   - text_test_results
   - text_training_required
   - text_version_created
   - text_version_published
   - text_version_type_hotfix
   - text_version_type_major
   - text_version_type_minor
   - text_version_type_patch
   - text_warehouse_scope
   - text_work_instructions

💡 Suggested Arabic Additions:
   - error_action_required = ""  # TODO: ترجمة عربية
   - error_approval_validation = ""  # TODO: ترجمة عربية
   - error_bulk_operation_validation = ""  # TODO: ترجمة عربية
   - error_change_description_required = ""  # TODO: ترجمة عربية
   - error_document_required = ""  # TODO: ترجمة عربية
   - error_invalid_action = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_title_required = ""  # TODO: ترجمة عربية
   - error_version_creation_failed = ""  # TODO: ترجمة عربية
   - error_version_id_required = ""  # TODO: ترجمة عربية
   - error_version_type_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_approval_committee = ""  # TODO: ترجمة عربية
   - text_approval_manager = ""  # TODO: ترجمة عربية
   - text_approval_none = ""  # TODO: ترجمة عربية
   - text_approval_processed = ""  # TODO: ترجمة عربية
   - text_approval_supervisor = ""  # TODO: ترجمة عربية
   - text_audit_reports = ""  # TODO: ترجمة عربية
   - text_bulk_operation_result = ""  # TODO: ترجمة عربية
   - text_catalog_documents = ""  # TODO: ترجمة عربية
   - text_catalog_documents_desc = ""  # TODO: ترجمة عربية
   - text_category_definitions = ""  # TODO: ترجمة عربية
   - text_change_content_update = ""  # TODO: ترجمة عربية
   - text_change_correction = ""  # TODO: ترجمة عربية
   - text_change_enhancement = ""  # TODO: ترجمة عربية
   - text_change_format = ""  # TODO: ترجمة عربية
   - text_change_regulatory = ""  # TODO: ترجمة عربية
   - text_compare_versions = ""  # TODO: ترجمة عربية
   - text_compliance_documents = ""  # TODO: ترجمة عربية
   - text_compliance_documents_desc = ""  # TODO: ترجمة عربية
   - text_compliance_standards = ""  # TODO: ترجمة عربية
   - text_create_version = ""  # TODO: ترجمة عربية
   - text_feature_auto_backup = ""  # TODO: ترجمة عربية
   - text_feature_auto_backup_desc = ""  # TODO: ترجمة عربية
   - text_feature_branching = ""  # TODO: ترجمة عربية
   - text_feature_branching_desc = ""  # TODO: ترجمة عربية
   - text_feature_change_tracking = ""  # TODO: ترجمة عربية
   - text_feature_change_tracking_desc = ""  # TODO: ترجمة عربية
   - text_feature_merge_capabilities = ""  # TODO: ترجمة عربية
   - text_feature_merge_capabilities_desc = ""  # TODO: ترجمة عربية
   - text_feature_rollback = ""  # TODO: ترجمة عربية
   - text_feature_rollback_desc = ""  # TODO: ترجمة عربية
   - text_feature_version_comparison = ""  # TODO: ترجمة عربية
   - text_feature_version_comparison_desc = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_inventory_documents = ""  # TODO: ترجمة عربية
   - text_inventory_documents_desc = ""  # TODO: ترجمة عربية
   - text_movement_logs = ""  # TODO: ترجمة عربية
   - text_price_lists = ""  # TODO: ترجمة عربية
   - text_procedure_code = ""  # TODO: ترجمة عربية
   - text_procedure_documents = ""  # TODO: ترجمة عربية
   - text_procedure_documents_desc = ""  # TODO: ترجمة عربية
   - text_product_id = ""  # TODO: ترجمة عربية
   - text_product_specifications = ""  # TODO: ترجمة عربية
   - text_quality_manuals = ""  # TODO: ترجمة عربية
   - text_safety_procedures = ""  # TODO: ترجمة عربية
   - text_safety_requirements = ""  # TODO: ترجمة عربية
   - text_sop_documents = ""  # TODO: ترجمة عربية
   - text_spec_version = ""  # TODO: ترجمة عربية
   - text_status_approved = ""  # TODO: ترجمة عربية
   - text_status_approved_desc = ""  # TODO: ترجمة عربية
   - text_status_archived = ""  # TODO: ترجمة عربية
   - text_status_archived_desc = ""  # TODO: ترجمة عربية
   - text_status_draft = ""  # TODO: ترجمة عربية
   - text_status_draft_desc = ""  # TODO: ترجمة عربية
   - text_status_obsolete = ""  # TODO: ترجمة عربية
   - text_status_obsolete_desc = ""  # TODO: ترجمة عربية
   - text_status_published = ""  # TODO: ترجمة عربية
   - text_status_published_desc = ""  # TODO: ترجمة عربية
   - text_status_review = ""  # TODO: ترجمة عربية
   - text_status_review_desc = ""  # TODO: ترجمة عربية
   - text_stock_reports = ""  # TODO: ترجمة عربية
   - text_strategy_major_minor = ""  # TODO: ترجمة عربية
   - text_strategy_major_minor_desc = ""  # TODO: ترجمة عربية
   - text_strategy_regulatory = ""  # TODO: ترجمة عربية
   - text_strategy_regulatory_desc = ""  # TODO: ترجمة عربية
   - text_strategy_semantic = ""  # TODO: ترجمة عربية
   - text_strategy_semantic_desc = ""  # TODO: ترجمة عربية
   - text_strategy_sequential = ""  # TODO: ترجمة عربية
   - text_strategy_sequential_desc = ""  # TODO: ترجمة عربية
   - text_strategy_timestamp = ""  # TODO: ترجمة عربية
   - text_strategy_timestamp_desc = ""  # TODO: ترجمة عربية
   - text_template_inventory_procedure = ""  # TODO: ترجمة عربية
   - text_template_product_spec = ""  # TODO: ترجمة عربية
   - text_test_results = ""  # TODO: ترجمة عربية
   - text_training_required = ""  # TODO: ترجمة عربية
   - text_version_created = ""  # TODO: ترجمة عربية
   - text_version_published = ""  # TODO: ترجمة عربية
   - text_version_type_hotfix = ""  # TODO: ترجمة عربية
   - text_version_type_major = ""  # TODO: ترجمة عربية
   - text_version_type_minor = ""  # TODO: ترجمة عربية
   - text_version_type_patch = ""  # TODO: ترجمة عربية
   - text_warehouse_scope = ""  # TODO: ترجمة عربية
   - text_work_instructions = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_action_required = ""  # TODO: English translation
   - error_approval_validation = ""  # TODO: English translation
   - error_bulk_operation_validation = ""  # TODO: English translation
   - error_change_description_required = ""  # TODO: English translation
   - error_document_required = ""  # TODO: English translation
   - error_invalid_action = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_title_required = ""  # TODO: English translation
   - error_version_creation_failed = ""  # TODO: English translation
   - error_version_id_required = ""  # TODO: English translation
   - error_version_type_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_approval_committee = ""  # TODO: English translation
   - text_approval_manager = ""  # TODO: English translation
   - text_approval_none = ""  # TODO: English translation
   - text_approval_processed = ""  # TODO: English translation
   - text_approval_supervisor = ""  # TODO: English translation
   - text_audit_reports = ""  # TODO: English translation
   - text_bulk_operation_result = ""  # TODO: English translation
   - text_catalog_documents = ""  # TODO: English translation
   - text_catalog_documents_desc = ""  # TODO: English translation
   - text_category_definitions = ""  # TODO: English translation
   - text_change_content_update = ""  # TODO: English translation
   - text_change_correction = ""  # TODO: English translation
   - text_change_enhancement = ""  # TODO: English translation
   - text_change_format = ""  # TODO: English translation
   - text_change_regulatory = ""  # TODO: English translation
   - text_compare_versions = ""  # TODO: English translation
   - text_compliance_documents = ""  # TODO: English translation
   - text_compliance_documents_desc = ""  # TODO: English translation
   - text_compliance_standards = ""  # TODO: English translation
   - text_create_version = ""  # TODO: English translation
   - text_feature_auto_backup = ""  # TODO: English translation
   - text_feature_auto_backup_desc = ""  # TODO: English translation
   - text_feature_branching = ""  # TODO: English translation
   - text_feature_branching_desc = ""  # TODO: English translation
   - text_feature_change_tracking = ""  # TODO: English translation
   - text_feature_change_tracking_desc = ""  # TODO: English translation
   - text_feature_merge_capabilities = ""  # TODO: English translation
   - text_feature_merge_capabilities_desc = ""  # TODO: English translation
   - text_feature_rollback = ""  # TODO: English translation
   - text_feature_rollback_desc = ""  # TODO: English translation
   - text_feature_version_comparison = ""  # TODO: English translation
   - text_feature_version_comparison_desc = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_inventory_documents = ""  # TODO: English translation
   - text_inventory_documents_desc = ""  # TODO: English translation
   - text_movement_logs = ""  # TODO: English translation
   - text_price_lists = ""  # TODO: English translation
   - text_procedure_code = ""  # TODO: English translation
   - text_procedure_documents = ""  # TODO: English translation
   - text_procedure_documents_desc = ""  # TODO: English translation
   - text_product_id = ""  # TODO: English translation
   - text_product_specifications = ""  # TODO: English translation
   - text_quality_manuals = ""  # TODO: English translation
   - text_safety_procedures = ""  # TODO: English translation
   - text_safety_requirements = ""  # TODO: English translation
   - text_sop_documents = ""  # TODO: English translation
   - text_spec_version = ""  # TODO: English translation
   - text_status_approved = ""  # TODO: English translation
   - text_status_approved_desc = ""  # TODO: English translation
   - text_status_archived = ""  # TODO: English translation
   - text_status_archived_desc = ""  # TODO: English translation
   - text_status_draft = ""  # TODO: English translation
   - text_status_draft_desc = ""  # TODO: English translation
   - text_status_obsolete = ""  # TODO: English translation
   - text_status_obsolete_desc = ""  # TODO: English translation
   - text_status_published = ""  # TODO: English translation
   - text_status_published_desc = ""  # TODO: English translation
   - text_status_review = ""  # TODO: English translation
   - text_status_review_desc = ""  # TODO: English translation
   - text_stock_reports = ""  # TODO: English translation
   - text_strategy_major_minor = ""  # TODO: English translation
   - text_strategy_major_minor_desc = ""  # TODO: English translation
   - text_strategy_regulatory = ""  # TODO: English translation
   - text_strategy_regulatory_desc = ""  # TODO: English translation
   - text_strategy_semantic = ""  # TODO: English translation
   - text_strategy_semantic_desc = ""  # TODO: English translation
   - text_strategy_sequential = ""  # TODO: English translation
   - text_strategy_sequential_desc = ""  # TODO: English translation
   - text_strategy_timestamp = ""  # TODO: English translation
   - text_strategy_timestamp_desc = ""  # TODO: English translation
   - text_template_inventory_procedure = ""  # TODO: English translation
   - text_template_product_spec = ""  # TODO: English translation
   - text_test_results = ""  # TODO: English translation
   - text_training_required = ""  # TODO: English translation
   - text_version_created = ""  # TODO: English translation
   - text_version_published = ""  # TODO: English translation
   - text_version_type_hotfix = ""  # TODO: English translation
   - text_version_type_major = ""  # TODO: English translation
   - text_version_type_minor = ""  # TODO: English translation
   - text_version_type_patch = ""  # TODO: English translation
   - text_warehouse_scope = ""  # TODO: English translation
   - text_work_instructions = ""  # TODO: English translation
