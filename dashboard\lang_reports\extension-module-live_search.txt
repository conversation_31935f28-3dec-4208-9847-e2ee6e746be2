📄 Route: extension/module/live_search
📂 Controller: controller\extension\module\live_search.php
🧱 Models used (3):
   - localisation/language
   - setting/event
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\module\live_search.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (1):
   - language\en-gb\extension\module\live_search.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_description_length
   - error_height
   - error_limit
   - error_permission
   - error_title_length
   - error_view_all_results
   - error_width
   - heading_title
   - text_extension
   - text_home
   - text_success
   - text_view_all_results

❌ Missing in Arabic:
   - error_description_length
   - error_height
   - error_limit
   - error_permission
   - error_title_length
   - error_view_all_results
   - error_width
   - heading_title
   - text_extension
   - text_home
   - text_success
   - text_view_all_results

❌ Missing in English:
   - error_description_length
   - error_height
   - error_limit
   - error_permission
   - error_title_length
   - error_view_all_results
   - error_width
   - heading_title
   - text_extension
   - text_home
   - text_success
   - text_view_all_results

💡 Suggested Arabic Additions:
   - error_description_length = ""  # TODO: ترجمة عربية
   - error_height = ""  # TODO: ترجمة عربية
   - error_limit = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_title_length = ""  # TODO: ترجمة عربية
   - error_view_all_results = ""  # TODO: ترجمة عربية
   - error_width = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_view_all_results = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_description_length = ""  # TODO: English translation
   - error_height = ""  # TODO: English translation
   - error_limit = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_title_length = ""  # TODO: English translation
   - error_view_all_results = ""  # TODO: English translation
   - error_width = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_view_all_results = ""  # TODO: English translation
