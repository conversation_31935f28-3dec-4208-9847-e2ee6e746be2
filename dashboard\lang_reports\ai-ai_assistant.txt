📄 Route: ai/ai_assistant
📂 Controller: controller\ai\ai_assistant.php
🧱 Models used (1):
   - ai/ai_assistant
🎨 Twig templates (1):
   - view\template\ai\ai_assistant.twig
🈯 Arabic Language Files (1):
   - language\ar\ai\ai_assistant.php
🇬🇧 English Language Files (1):
   - language\en-gb\ai\ai_assistant.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_empty_message
   - error_invalid_request
   - heading_title
   - text_home

❌ Missing in Arabic:
   - error_empty_message
   - error_invalid_request
   - heading_title
   - text_home

❌ Missing in English:
   - error_empty_message
   - error_invalid_request
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - error_empty_message = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_empty_message = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
