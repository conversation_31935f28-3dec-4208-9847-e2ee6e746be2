📄 Route: supplier/price_agreement
📂 Controller: controller\supplier\price_agreement.php
🧱 Models used (2):
   ✅ supplier/price_agreement (11 functions)
   ✅ supplier/supplier (21 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\supplier\price_agreement.php (101 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\supplier\price_agreement.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - date_format_short
   - error_agreement_name
   - error_date_range
   - error_end_date
   - error_permission
   - error_start_date
   - error_supplier
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (15):
   - date_format_short
   - error_agreement_name
   - error_date_range
   - error_end_date
   - error_permission
   - error_start_date
   - error_supplier
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 15 items
      - error_date_range
      - text_success
      - text_add
      - text_home
      - text_disabled

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 15 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.