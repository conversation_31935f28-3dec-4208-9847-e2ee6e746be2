📄 Route: sale/dynamic_pricing
📂 Controller: controller\sale\dynamic_pricing.php
🧱 Models used (1):
   - sale/dynamic_pricing
🎨 Twig templates (1):
   - view\template\sale\dynamic_pricing.twig
🈯 Arabic Language Files (1):
   - language\ar\sale\dynamic_pricing.php
🇬🇧 English Language Files (1):
   - language\en-gb\sale\dynamic_pricing.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_action_value
   - error_condition_value
   - error_name
   - error_permission
   - error_test_data
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_sales
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - error_action_value
   - error_condition_value
   - error_name
   - error_permission
   - error_test_data
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_sales
   - text_success

❌ Missing in English:
   - date_format_short
   - error_action_value
   - error_condition_value
   - error_name
   - error_permission
   - error_test_data
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_sales
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_action_value = ""  # TODO: ترجمة عربية
   - error_condition_value = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_test_data = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_sales = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_action_value = ""  # TODO: English translation
   - error_condition_value = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_test_data = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_sales = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
