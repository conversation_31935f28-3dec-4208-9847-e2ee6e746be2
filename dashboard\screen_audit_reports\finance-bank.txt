📄 Route: finance/bank
📂 Controller: controller\finance\bank.php
🧱 Models used (2):
   ✅ bank/bank (23 functions)
   ✅ finance/bank (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\finance\bank.php (28 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\finance\bank.php (18 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (22):
   - button_add
   - button_delete
   - button_edit
   - column_account_number
   - column_name
   - column_status
   - error_account_number
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_confirm
   - text_disabled
   - text_enabled
   - text_home
   - text_list
   - text_no_results
   - text_pagination
   - text_success
   - text_success_transaction
   ... و 2 متغير آخر

❌ Missing in Arabic (4):
   - text_home
   - text_no_results
   - text_pagination
   - text_success_transaction

❌ Missing in English (10):
   - button_add
   - button_delete
   - button_edit
   - text_confirm
   - text_disabled
   - text_enabled
   - text_home
   - text_no_results
   - text_pagination
   - text_success_transaction

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - text_success_transaction
      - text_home
      - text_pagination
      - text_no_results
   🟡 MISSING_ENGLISH_VARIABLES: 10 items
      - button_delete
      - text_disabled
      - button_add
      - text_success_transaction
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 10 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:02
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.