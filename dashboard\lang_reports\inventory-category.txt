📄 Route: inventory/category
📂 Controller: controller\inventory\category.php
🧱 Models used (5):
   - catalog/seo_url
   - design/layout
   - inventory/category
   - localisation/language
   - setting/store
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_category_in_use
   - error_keyword
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_default
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_category_in_use
   - error_keyword
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_default
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in English:
   - error_category_in_use
   - error_keyword
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_default
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_category_in_use = ""  # TODO: ترجمة عربية
   - error_keyword = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_default = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_category_in_use = ""  # TODO: English translation
   - error_keyword = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_default = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
