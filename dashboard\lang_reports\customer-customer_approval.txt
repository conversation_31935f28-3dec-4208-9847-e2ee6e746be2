📄 Route: customer/customer_approval
📂 Controller: controller\customer\customer_approval.php
🧱 Models used (2):
   - customer/customer_approval
   - customer/customer_group
🎨 Twig templates (1):
   - view\template\customer\customer_approval.twig
🈯 Arabic Language Files (1):
   - language\ar\customer\customer_approval.php
🇬🇧 English Language Files (1):
   - language\en-gb\customer\customer_approval.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_permission
   - heading_title
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - error_permission
   - heading_title
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - date_format_short
   - error_permission
   - heading_title
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
