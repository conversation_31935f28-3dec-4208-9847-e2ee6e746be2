📄 Route: user/user_group
📂 Controller: controller\user\user_group.php
🧱 Models used (2):
   ✅ user/user_group (9 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\user\user_group.php (13 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\user\user_group.php (13 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (9):
   - error_name
   - error_permission
   - error_user
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (2):
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - text_home
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:20
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.