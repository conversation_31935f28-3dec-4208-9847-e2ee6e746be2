📄 Route: api/customer_journey
📂 Controller: controller\api\customer_journey.php
🧱 Models used (4):
   ✅ crm/customer_journey (42 functions)
   ❌ crm/touchpoint (0 functions)
   ❌ api/authentication (0 functions)
   ❌ api/rate_limit (0 functions)
🎨 Twig templates (1):
   ✅ view\template\api\customer_journey.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\api\customer_journey.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\api\customer_journey.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title
   🟢 MISSING_MODEL_FILES: 3 items
      - crm/touchpoint
      - api/authentication
      - api/rate_limit

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 3 ملف موديل

📈 Screen Health Score: ⚠️ 75%
📅 Analysis Date: 2025-07-21 18:32:47
🔧 Total Issues: 3

⚠️ جيد، لكن يحتاج بعض التحسينات.