📄 Route: payment/transaction
📂 Controller: controller\payment\transaction.php
🧱 Models used (3):
   - customer/customer
   - payment/gateway
   - payment/transaction
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - error_amount
   - error_external_id
   - error_gateway
   - error_permission
   - error_refund_amount
   - error_refund_reason
   - error_settlement_date
   - error_transaction
   - error_transaction_date
   - error_transaction_type
   - heading_title
   - text_add
   - text_cancelled
   - text_chargeback
   - text_completed
   - text_edit
   - text_failed
   - text_home
   - text_partially_refunded
   - text_payment
   - text_pending
   - text_processing
   - text_refund
   - text_refunded
   - text_report
   - text_settled
   - text_settlement
   - text_success
   - text_success_refunded
   - text_success_settled

❌ Missing in Arabic:
   - datetime_format
   - error_amount
   - error_external_id
   - error_gateway
   - error_permission
   - error_refund_amount
   - error_refund_reason
   - error_settlement_date
   - error_transaction
   - error_transaction_date
   - error_transaction_type
   - heading_title
   - text_add
   - text_cancelled
   - text_chargeback
   - text_completed
   - text_edit
   - text_failed
   - text_home
   - text_partially_refunded
   - text_payment
   - text_pending
   - text_processing
   - text_refund
   - text_refunded
   - text_report
   - text_settled
   - text_settlement
   - text_success
   - text_success_refunded
   - text_success_settled

❌ Missing in English:
   - datetime_format
   - error_amount
   - error_external_id
   - error_gateway
   - error_permission
   - error_refund_amount
   - error_refund_reason
   - error_settlement_date
   - error_transaction
   - error_transaction_date
   - error_transaction_type
   - heading_title
   - text_add
   - text_cancelled
   - text_chargeback
   - text_completed
   - text_edit
   - text_failed
   - text_home
   - text_partially_refunded
   - text_payment
   - text_pending
   - text_processing
   - text_refund
   - text_refunded
   - text_report
   - text_settled
   - text_settlement
   - text_success
   - text_success_refunded
   - text_success_settled

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_amount = ""  # TODO: ترجمة عربية
   - error_external_id = ""  # TODO: ترجمة عربية
   - error_gateway = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_refund_amount = ""  # TODO: ترجمة عربية
   - error_refund_reason = ""  # TODO: ترجمة عربية
   - error_settlement_date = ""  # TODO: ترجمة عربية
   - error_transaction = ""  # TODO: ترجمة عربية
   - error_transaction_date = ""  # TODO: ترجمة عربية
   - error_transaction_type = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_cancelled = ""  # TODO: ترجمة عربية
   - text_chargeback = ""  # TODO: ترجمة عربية
   - text_completed = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_failed = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_partially_refunded = ""  # TODO: ترجمة عربية
   - text_payment = ""  # TODO: ترجمة عربية
   - text_pending = ""  # TODO: ترجمة عربية
   - text_processing = ""  # TODO: ترجمة عربية
   - text_refund = ""  # TODO: ترجمة عربية
   - text_refunded = ""  # TODO: ترجمة عربية
   - text_report = ""  # TODO: ترجمة عربية
   - text_settled = ""  # TODO: ترجمة عربية
   - text_settlement = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_success_refunded = ""  # TODO: ترجمة عربية
   - text_success_settled = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - error_amount = ""  # TODO: English translation
   - error_external_id = ""  # TODO: English translation
   - error_gateway = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_refund_amount = ""  # TODO: English translation
   - error_refund_reason = ""  # TODO: English translation
   - error_settlement_date = ""  # TODO: English translation
   - error_transaction = ""  # TODO: English translation
   - error_transaction_date = ""  # TODO: English translation
   - error_transaction_type = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_cancelled = ""  # TODO: English translation
   - text_chargeback = ""  # TODO: English translation
   - text_completed = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_failed = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_partially_refunded = ""  # TODO: English translation
   - text_payment = ""  # TODO: English translation
   - text_pending = ""  # TODO: English translation
   - text_processing = ""  # TODO: English translation
   - text_refund = ""  # TODO: English translation
   - text_refunded = ""  # TODO: English translation
   - text_report = ""  # TODO: English translation
   - text_settled = ""  # TODO: English translation
   - text_settlement = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_success_refunded = ""  # TODO: English translation
   - text_success_settled = ""  # TODO: English translation
