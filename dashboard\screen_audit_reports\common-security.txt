📄 Route: common/security
📂 Controller: controller\common\security.php
🧱 Models used (0):
🎨 Twig templates (1):
   ✅ view\template\common\security.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\common\security.php (18 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\common\security.php (18 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (23):
   - button_move
   - document_root
   - entry_directory
   - error_directory
   - error_exists
   - error_permission
   - error_writable
   - heading_title
   - storage
   - text_automatic
   - text_choose
   - text_config
   - text_instruction
   - text_loading
   - text_manual
   - text_move
   - text_security
   - text_success
   - text_to
   - user_token
   ... و 3 متغير آخر

❌ Missing in Arabic (6):
   - document_root
   - path
   - storage
   - text_instruction
   - text_loading
   - user_token

❌ Missing in English (6):
   - document_root
   - path
   - storage
   - text_instruction
   - text_loading
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 6 items
      - user_token
      - storage
      - text_loading
      - document_root
      - text_instruction
   🟡 MISSING_ENGLISH_VARIABLES: 6 items
      - user_token
      - storage
      - text_loading
      - document_root
      - text_instruction

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 6 متغير عربي و 6 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:54
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.