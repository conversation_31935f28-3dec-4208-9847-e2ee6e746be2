📄 Route: user/user
📂 Controller: controller\user\user.php
🧱 Models used (8):
   - communication/unified_notification
   - core/central_service_manager
   - hr/employee
   - localisation/location
   - mail/mail
   - tool/image
   - user/user
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\user\user.php
🇬🇧 English Language Files (1):
   - language\en-gb\user\user.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_branch
   - column_date_added
   - column_email
   - column_firstname
   - column_last_activity
   - column_lastname
   - column_status
   - column_user_group
   - column_user_id
   - column_username
   - date_format_short
   - datetime_format
   - error_account
   - error_cannot_delete_self
   - error_confirm
   - error_email
   - error_exists_email
   - error_exists_username
   - error_firstname
   - error_import_email_exists
   - error_import_email_invalid
   - error_import_file_invalid
   - error_import_row_failed
   - error_import_row_incomplete
   - error_import_user_group_invalid
   - error_import_username_exists
   - error_import_username_invalid
   - error_lastname
   - error_only_admin_user
   - error_password
   - error_permission
   - error_permission_access
   - error_permission_activity
   - error_permission_delete
   - error_permission_export
   - error_permission_import
   - error_permission_modify
   - error_permission_profile
   - error_single_user
   - error_upload_failed
   - error_user_creation_failed
   - error_user_has_journal_entries
   - error_user_has_orders
   - error_user_id_required
   - error_user_not_found
   - error_user_update_failed
   - error_username
   - heading_title
   - heading_title_activity
   - heading_title_add
   - heading_title_edit
   - heading_title_profile
   - mail_welcome_message
   - mail_welcome_subject
   - notification_user_created_message
   - notification_user_created_title
   - notification_user_deleted_message
   - notification_user_deleted_title
   - notification_user_general_message
   - notification_user_general_title
   - notification_user_updated_message
   - notification_user_updated_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_hours_ago
   - text_import_success
   - text_minutes_ago
   - text_never
   - text_online
   - text_pagination
   - text_success_add
   - text_success_delete_count
   - text_success_edit
   - warning_delete_failed
   - warning_self_edit_restricted

❌ Missing in Arabic:
   - column_branch
   - column_date_added
   - column_email
   - column_firstname
   - column_last_activity
   - column_lastname
   - column_status
   - column_user_group
   - column_user_id
   - column_username
   - date_format_short
   - datetime_format
   - error_account
   - error_cannot_delete_self
   - error_confirm
   - error_email
   - error_exists_email
   - error_exists_username
   - error_firstname
   - error_import_email_exists
   - error_import_email_invalid
   - error_import_file_invalid
   - error_import_row_failed
   - error_import_row_incomplete
   - error_import_user_group_invalid
   - error_import_username_exists
   - error_import_username_invalid
   - error_lastname
   - error_only_admin_user
   - error_password
   - error_permission
   - error_permission_access
   - error_permission_activity
   - error_permission_delete
   - error_permission_export
   - error_permission_import
   - error_permission_modify
   - error_permission_profile
   - error_single_user
   - error_upload_failed
   - error_user_creation_failed
   - error_user_has_journal_entries
   - error_user_has_orders
   - error_user_id_required
   - error_user_not_found
   - error_user_update_failed
   - error_username
   - heading_title
   - heading_title_activity
   - heading_title_add
   - heading_title_edit
   - heading_title_profile
   - mail_welcome_message
   - mail_welcome_subject
   - notification_user_created_message
   - notification_user_created_title
   - notification_user_deleted_message
   - notification_user_deleted_title
   - notification_user_general_message
   - notification_user_general_title
   - notification_user_updated_message
   - notification_user_updated_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_hours_ago
   - text_import_success
   - text_minutes_ago
   - text_never
   - text_online
   - text_pagination
   - text_success_add
   - text_success_delete_count
   - text_success_edit
   - warning_delete_failed
   - warning_self_edit_restricted

❌ Missing in English:
   - column_branch
   - column_date_added
   - column_email
   - column_firstname
   - column_last_activity
   - column_lastname
   - column_status
   - column_user_group
   - column_user_id
   - column_username
   - date_format_short
   - datetime_format
   - error_account
   - error_cannot_delete_self
   - error_confirm
   - error_email
   - error_exists_email
   - error_exists_username
   - error_firstname
   - error_import_email_exists
   - error_import_email_invalid
   - error_import_file_invalid
   - error_import_row_failed
   - error_import_row_incomplete
   - error_import_user_group_invalid
   - error_import_username_exists
   - error_import_username_invalid
   - error_lastname
   - error_only_admin_user
   - error_password
   - error_permission
   - error_permission_access
   - error_permission_activity
   - error_permission_delete
   - error_permission_export
   - error_permission_import
   - error_permission_modify
   - error_permission_profile
   - error_single_user
   - error_upload_failed
   - error_user_creation_failed
   - error_user_has_journal_entries
   - error_user_has_orders
   - error_user_id_required
   - error_user_not_found
   - error_user_update_failed
   - error_username
   - heading_title
   - heading_title_activity
   - heading_title_add
   - heading_title_edit
   - heading_title_profile
   - mail_welcome_message
   - mail_welcome_subject
   - notification_user_created_message
   - notification_user_created_title
   - notification_user_deleted_message
   - notification_user_deleted_title
   - notification_user_general_message
   - notification_user_general_title
   - notification_user_updated_message
   - notification_user_updated_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_hours_ago
   - text_import_success
   - text_minutes_ago
   - text_never
   - text_online
   - text_pagination
   - text_success_add
   - text_success_delete_count
   - text_success_edit
   - warning_delete_failed
   - warning_self_edit_restricted

💡 Suggested Arabic Additions:
   - column_branch = ""  # TODO: ترجمة عربية
   - column_date_added = ""  # TODO: ترجمة عربية
   - column_email = ""  # TODO: ترجمة عربية
   - column_firstname = ""  # TODO: ترجمة عربية
   - column_last_activity = ""  # TODO: ترجمة عربية
   - column_lastname = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_user_group = ""  # TODO: ترجمة عربية
   - column_user_id = ""  # TODO: ترجمة عربية
   - column_username = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_account = ""  # TODO: ترجمة عربية
   - error_cannot_delete_self = ""  # TODO: ترجمة عربية
   - error_confirm = ""  # TODO: ترجمة عربية
   - error_email = ""  # TODO: ترجمة عربية
   - error_exists_email = ""  # TODO: ترجمة عربية
   - error_exists_username = ""  # TODO: ترجمة عربية
   - error_firstname = ""  # TODO: ترجمة عربية
   - error_import_email_exists = ""  # TODO: ترجمة عربية
   - error_import_email_invalid = ""  # TODO: ترجمة عربية
   - error_import_file_invalid = ""  # TODO: ترجمة عربية
   - error_import_row_failed = ""  # TODO: ترجمة عربية
   - error_import_row_incomplete = ""  # TODO: ترجمة عربية
   - error_import_user_group_invalid = ""  # TODO: ترجمة عربية
   - error_import_username_exists = ""  # TODO: ترجمة عربية
   - error_import_username_invalid = ""  # TODO: ترجمة عربية
   - error_lastname = ""  # TODO: ترجمة عربية
   - error_only_admin_user = ""  # TODO: ترجمة عربية
   - error_password = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_permission_access = ""  # TODO: ترجمة عربية
   - error_permission_activity = ""  # TODO: ترجمة عربية
   - error_permission_delete = ""  # TODO: ترجمة عربية
   - error_permission_export = ""  # TODO: ترجمة عربية
   - error_permission_import = ""  # TODO: ترجمة عربية
   - error_permission_modify = ""  # TODO: ترجمة عربية
   - error_permission_profile = ""  # TODO: ترجمة عربية
   - error_single_user = ""  # TODO: ترجمة عربية
   - error_upload_failed = ""  # TODO: ترجمة عربية
   - error_user_creation_failed = ""  # TODO: ترجمة عربية
   - error_user_has_journal_entries = ""  # TODO: ترجمة عربية
   - error_user_has_orders = ""  # TODO: ترجمة عربية
   - error_user_id_required = ""  # TODO: ترجمة عربية
   - error_user_not_found = ""  # TODO: ترجمة عربية
   - error_user_update_failed = ""  # TODO: ترجمة عربية
   - error_username = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_title_activity = ""  # TODO: ترجمة عربية
   - heading_title_add = ""  # TODO: ترجمة عربية
   - heading_title_edit = ""  # TODO: ترجمة عربية
   - heading_title_profile = ""  # TODO: ترجمة عربية
   - mail_welcome_message = ""  # TODO: ترجمة عربية
   - mail_welcome_subject = ""  # TODO: ترجمة عربية
   - notification_user_created_message = ""  # TODO: ترجمة عربية
   - notification_user_created_title = ""  # TODO: ترجمة عربية
   - notification_user_deleted_message = ""  # TODO: ترجمة عربية
   - notification_user_deleted_title = ""  # TODO: ترجمة عربية
   - notification_user_general_message = ""  # TODO: ترجمة عربية
   - notification_user_general_title = ""  # TODO: ترجمة عربية
   - notification_user_updated_message = ""  # TODO: ترجمة عربية
   - notification_user_updated_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_hours_ago = ""  # TODO: ترجمة عربية
   - text_import_success = ""  # TODO: ترجمة عربية
   - text_minutes_ago = ""  # TODO: ترجمة عربية
   - text_never = ""  # TODO: ترجمة عربية
   - text_online = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_delete_count = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية
   - warning_delete_failed = ""  # TODO: ترجمة عربية
   - warning_self_edit_restricted = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_branch = ""  # TODO: English translation
   - column_date_added = ""  # TODO: English translation
   - column_email = ""  # TODO: English translation
   - column_firstname = ""  # TODO: English translation
   - column_last_activity = ""  # TODO: English translation
   - column_lastname = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_user_group = ""  # TODO: English translation
   - column_user_id = ""  # TODO: English translation
   - column_username = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_account = ""  # TODO: English translation
   - error_cannot_delete_self = ""  # TODO: English translation
   - error_confirm = ""  # TODO: English translation
   - error_email = ""  # TODO: English translation
   - error_exists_email = ""  # TODO: English translation
   - error_exists_username = ""  # TODO: English translation
   - error_firstname = ""  # TODO: English translation
   - error_import_email_exists = ""  # TODO: English translation
   - error_import_email_invalid = ""  # TODO: English translation
   - error_import_file_invalid = ""  # TODO: English translation
   - error_import_row_failed = ""  # TODO: English translation
   - error_import_row_incomplete = ""  # TODO: English translation
   - error_import_user_group_invalid = ""  # TODO: English translation
   - error_import_username_exists = ""  # TODO: English translation
   - error_import_username_invalid = ""  # TODO: English translation
   - error_lastname = ""  # TODO: English translation
   - error_only_admin_user = ""  # TODO: English translation
   - error_password = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_permission_access = ""  # TODO: English translation
   - error_permission_activity = ""  # TODO: English translation
   - error_permission_delete = ""  # TODO: English translation
   - error_permission_export = ""  # TODO: English translation
   - error_permission_import = ""  # TODO: English translation
   - error_permission_modify = ""  # TODO: English translation
   - error_permission_profile = ""  # TODO: English translation
   - error_single_user = ""  # TODO: English translation
   - error_upload_failed = ""  # TODO: English translation
   - error_user_creation_failed = ""  # TODO: English translation
   - error_user_has_journal_entries = ""  # TODO: English translation
   - error_user_has_orders = ""  # TODO: English translation
   - error_user_id_required = ""  # TODO: English translation
   - error_user_not_found = ""  # TODO: English translation
   - error_user_update_failed = ""  # TODO: English translation
   - error_username = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_title_activity = ""  # TODO: English translation
   - heading_title_add = ""  # TODO: English translation
   - heading_title_edit = ""  # TODO: English translation
   - heading_title_profile = ""  # TODO: English translation
   - mail_welcome_message = ""  # TODO: English translation
   - mail_welcome_subject = ""  # TODO: English translation
   - notification_user_created_message = ""  # TODO: English translation
   - notification_user_created_title = ""  # TODO: English translation
   - notification_user_deleted_message = ""  # TODO: English translation
   - notification_user_deleted_title = ""  # TODO: English translation
   - notification_user_general_message = ""  # TODO: English translation
   - notification_user_general_title = ""  # TODO: English translation
   - notification_user_updated_message = ""  # TODO: English translation
   - notification_user_updated_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_hours_ago = ""  # TODO: English translation
   - text_import_success = ""  # TODO: English translation
   - text_minutes_ago = ""  # TODO: English translation
   - text_never = ""  # TODO: English translation
   - text_online = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_delete_count = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
   - warning_delete_failed = ""  # TODO: English translation
   - warning_self_edit_restricted = ""  # TODO: English translation
