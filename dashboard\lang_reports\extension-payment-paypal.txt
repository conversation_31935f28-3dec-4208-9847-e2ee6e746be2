📄 Route: extension/payment/paypal
📂 Controller: controller\extension\payment\paypal.php
🧱 Models used (7):
   - extension/payment/paypal
   - localisation/country
   - localisation/geo_zone
   - localisation/order_status
   - sale/recurring
   - setting/event
   - setting/setting
🎨 Twig templates (13):
   - view\template\extension\payment\paypal\applepay_button.twig
   - view\template\extension\payment\paypal\auth.twig
   - view\template\extension\payment\paypal\button.twig
   - view\template\extension\payment\paypal\card.twig
   - view\template\extension\payment\paypal\contact.twig
   - view\template\extension\payment\paypal\dashboard.twig
   - view\template\extension\payment\paypal\general.twig
   - view\template\extension\payment\paypal\googlepay_button.twig
   - view\template\extension\payment\paypal\message_configurator.twig
   - view\template\extension\payment\paypal\message_setting.twig
   - view\template\extension\payment\paypal\order.twig
   - view\template\extension\payment\paypal\order_status.twig
   - view\template\extension\payment\paypal\recurring.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\payment\paypal.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\payment\paypal.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_agree
   - error_connect
   - error_permission
   - error_timeout
   - heading_title_main
   - success_agree
   - success_capture_payment
   - success_disable_recurring
   - success_download_host
   - success_enable_recurring
   - success_reauthorize_payment
   - success_refund_payment
   - success_save
   - success_send
   - success_void_payment
   - text_all_sales
   - text_checkout_express
   - text_connect
   - text_extensions
   - text_home
   - text_message_alert_uk
   - text_message_alert_us
   - text_message_footnote_uk
   - text_message_footnote_us
   - text_paypal_sales
   - text_support
   - text_version

❌ Missing in Arabic:
   - error_agree
   - error_connect
   - error_permission
   - error_timeout
   - heading_title_main
   - success_agree
   - success_capture_payment
   - success_disable_recurring
   - success_download_host
   - success_enable_recurring
   - success_reauthorize_payment
   - success_refund_payment
   - success_save
   - success_send
   - success_void_payment
   - text_all_sales
   - text_checkout_express
   - text_connect
   - text_extensions
   - text_home
   - text_message_alert_uk
   - text_message_alert_us
   - text_message_footnote_uk
   - text_message_footnote_us
   - text_paypal_sales
   - text_support
   - text_version

❌ Missing in English:
   - error_agree
   - error_connect
   - error_permission
   - error_timeout
   - heading_title_main
   - success_agree
   - success_capture_payment
   - success_disable_recurring
   - success_download_host
   - success_enable_recurring
   - success_reauthorize_payment
   - success_refund_payment
   - success_save
   - success_send
   - success_void_payment
   - text_all_sales
   - text_checkout_express
   - text_connect
   - text_extensions
   - text_home
   - text_message_alert_uk
   - text_message_alert_us
   - text_message_footnote_uk
   - text_message_footnote_us
   - text_paypal_sales
   - text_support
   - text_version

💡 Suggested Arabic Additions:
   - error_agree = ""  # TODO: ترجمة عربية
   - error_connect = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_timeout = ""  # TODO: ترجمة عربية
   - heading_title_main = ""  # TODO: ترجمة عربية
   - success_agree = ""  # TODO: ترجمة عربية
   - success_capture_payment = ""  # TODO: ترجمة عربية
   - success_disable_recurring = ""  # TODO: ترجمة عربية
   - success_download_host = ""  # TODO: ترجمة عربية
   - success_enable_recurring = ""  # TODO: ترجمة عربية
   - success_reauthorize_payment = ""  # TODO: ترجمة عربية
   - success_refund_payment = ""  # TODO: ترجمة عربية
   - success_save = ""  # TODO: ترجمة عربية
   - success_send = ""  # TODO: ترجمة عربية
   - success_void_payment = ""  # TODO: ترجمة عربية
   - text_all_sales = ""  # TODO: ترجمة عربية
   - text_checkout_express = ""  # TODO: ترجمة عربية
   - text_connect = ""  # TODO: ترجمة عربية
   - text_extensions = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_message_alert_uk = ""  # TODO: ترجمة عربية
   - text_message_alert_us = ""  # TODO: ترجمة عربية
   - text_message_footnote_uk = ""  # TODO: ترجمة عربية
   - text_message_footnote_us = ""  # TODO: ترجمة عربية
   - text_paypal_sales = ""  # TODO: ترجمة عربية
   - text_support = ""  # TODO: ترجمة عربية
   - text_version = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_agree = ""  # TODO: English translation
   - error_connect = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_timeout = ""  # TODO: English translation
   - heading_title_main = ""  # TODO: English translation
   - success_agree = ""  # TODO: English translation
   - success_capture_payment = ""  # TODO: English translation
   - success_disable_recurring = ""  # TODO: English translation
   - success_download_host = ""  # TODO: English translation
   - success_enable_recurring = ""  # TODO: English translation
   - success_reauthorize_payment = ""  # TODO: English translation
   - success_refund_payment = ""  # TODO: English translation
   - success_save = ""  # TODO: English translation
   - success_send = ""  # TODO: English translation
   - success_void_payment = ""  # TODO: English translation
   - text_all_sales = ""  # TODO: English translation
   - text_checkout_express = ""  # TODO: English translation
   - text_connect = ""  # TODO: English translation
   - text_extensions = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_message_alert_uk = ""  # TODO: English translation
   - text_message_alert_us = ""  # TODO: English translation
   - text_message_footnote_uk = ""  # TODO: English translation
   - text_message_footnote_us = ""  # TODO: English translation
   - text_paypal_sales = ""  # TODO: English translation
   - text_support = ""  # TODO: English translation
   - text_version = ""  # TODO: English translation
