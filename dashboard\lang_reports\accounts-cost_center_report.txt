📄 Route: accounts/cost_center_report
📂 Controller: controller\accounts\cost_center_report.php
🧱 Models used (6):
   - accounts/cost_center_report
   - branch/branch
   - core/central_service_manager
   - cost_center/cost_center
   - department/department
   - project/project
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\cost_center_report.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\cost_center_report.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - text_abc_analysis
   - text_cost_center
   - text_cost_forecast
   - text_dashboard
   - text_direct_costs
   - text_forecast_generated
   - text_home
   - text_indirect_costs
   - text_no_results
   - text_profit_loss
   - text_profitability_analysis
   - text_revenue
   - text_success_abc_analysis
   - text_success_generate
   - text_total_costs

❌ Missing in Arabic:
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - text_abc_analysis
   - text_cost_center
   - text_cost_forecast
   - text_dashboard
   - text_direct_costs
   - text_forecast_generated
   - text_home
   - text_indirect_costs
   - text_no_results
   - text_profit_loss
   - text_profitability_analysis
   - text_revenue
   - text_success_abc_analysis
   - text_success_generate
   - text_total_costs

❌ Missing in English:
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - text_abc_analysis
   - text_cost_center
   - text_cost_forecast
   - text_dashboard
   - text_direct_costs
   - text_forecast_generated
   - text_home
   - text_indirect_costs
   - text_no_results
   - text_profit_loss
   - text_profitability_analysis
   - text_revenue
   - text_success_abc_analysis
   - text_success_generate
   - text_total_costs

💡 Suggested Arabic Additions:
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_date_start = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_abc_analysis = ""  # TODO: ترجمة عربية
   - text_cost_center = ""  # TODO: ترجمة عربية
   - text_cost_forecast = ""  # TODO: ترجمة عربية
   - text_dashboard = ""  # TODO: ترجمة عربية
   - text_direct_costs = ""  # TODO: ترجمة عربية
   - text_forecast_generated = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_indirect_costs = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_profit_loss = ""  # TODO: ترجمة عربية
   - text_profitability_analysis = ""  # TODO: ترجمة عربية
   - text_revenue = ""  # TODO: ترجمة عربية
   - text_success_abc_analysis = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_total_costs = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_date_end = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_date_start = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_abc_analysis = ""  # TODO: English translation
   - text_cost_center = ""  # TODO: English translation
   - text_cost_forecast = ""  # TODO: English translation
   - text_dashboard = ""  # TODO: English translation
   - text_direct_costs = ""  # TODO: English translation
   - text_forecast_generated = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_indirect_costs = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_profit_loss = ""  # TODO: English translation
   - text_profitability_analysis = ""  # TODO: English translation
   - text_revenue = ""  # TODO: English translation
   - text_success_abc_analysis = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_total_costs = ""  # TODO: English translation
