📄 Route: store/setting
📂 Controller: controller\store\setting.php
🧱 Models used (2):
   - setting/extension
   - setting/module
🎨 Twig templates (1):
   - view\template\store\setting.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - text_store_settingx

❌ Missing in Arabic:
   - error_permission
   - text_store_settingx

❌ Missing in English:
   - error_permission
   - text_store_settingx

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - text_store_settingx = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - text_store_settingx = ""  # TODO: English translation
