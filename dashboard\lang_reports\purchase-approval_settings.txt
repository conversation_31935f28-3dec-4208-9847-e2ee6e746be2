📄 Route: purchase/approval_settings
📂 Controller: controller\purchase\approval_settings.php
🧱 Models used (6):
   - catalog/category
   - localisation/currency
   - purchase/approval_settings
   - setting/setting
   - user/user
   - user/user_group
🎨 Twig templates (1):
   - view\template\purchase\approval_settings.twig
🈯 Arabic Language Files (1):
   - language\ar\purchase\approval_settings.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_approval_percentage
   - error_escalation_days
   - error_import_failed
   - error_import_file
   - error_import_invalid
   - error_permission
   - error_step_approver
   - error_step_name
   - error_step_sort_order
   - error_test_data
   - error_threshold_amount
   - error_threshold_approver
   - error_timeout_days
   - heading_title
   - text_home
   - text_import_success
   - text_success
   - text_success_workflow
   - text_test_success
   - text_workflow

❌ Missing in Arabic:
   - error_approval_percentage
   - error_escalation_days
   - error_import_failed
   - error_import_file
   - error_import_invalid
   - error_permission
   - error_step_approver
   - error_step_name
   - error_step_sort_order
   - error_test_data
   - error_threshold_amount
   - error_threshold_approver
   - error_timeout_days
   - heading_title
   - text_home
   - text_import_success
   - text_success
   - text_success_workflow
   - text_test_success
   - text_workflow

❌ Missing in English:
   - error_approval_percentage
   - error_escalation_days
   - error_import_failed
   - error_import_file
   - error_import_invalid
   - error_permission
   - error_step_approver
   - error_step_name
   - error_step_sort_order
   - error_test_data
   - error_threshold_amount
   - error_threshold_approver
   - error_timeout_days
   - heading_title
   - text_home
   - text_import_success
   - text_success
   - text_success_workflow
   - text_test_success
   - text_workflow

💡 Suggested Arabic Additions:
   - error_approval_percentage = ""  # TODO: ترجمة عربية
   - error_escalation_days = ""  # TODO: ترجمة عربية
   - error_import_failed = ""  # TODO: ترجمة عربية
   - error_import_file = ""  # TODO: ترجمة عربية
   - error_import_invalid = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_step_approver = ""  # TODO: ترجمة عربية
   - error_step_name = ""  # TODO: ترجمة عربية
   - error_step_sort_order = ""  # TODO: ترجمة عربية
   - error_test_data = ""  # TODO: ترجمة عربية
   - error_threshold_amount = ""  # TODO: ترجمة عربية
   - error_threshold_approver = ""  # TODO: ترجمة عربية
   - error_timeout_days = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_import_success = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_success_workflow = ""  # TODO: ترجمة عربية
   - text_test_success = ""  # TODO: ترجمة عربية
   - text_workflow = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_approval_percentage = ""  # TODO: English translation
   - error_escalation_days = ""  # TODO: English translation
   - error_import_failed = ""  # TODO: English translation
   - error_import_file = ""  # TODO: English translation
   - error_import_invalid = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_step_approver = ""  # TODO: English translation
   - error_step_name = ""  # TODO: English translation
   - error_step_sort_order = ""  # TODO: English translation
   - error_test_data = ""  # TODO: English translation
   - error_threshold_amount = ""  # TODO: English translation
   - error_threshold_approver = ""  # TODO: English translation
   - error_timeout_days = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_import_success = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_success_workflow = ""  # TODO: English translation
   - text_test_success = ""  # TODO: English translation
   - text_workflow = ""  # TODO: English translation
