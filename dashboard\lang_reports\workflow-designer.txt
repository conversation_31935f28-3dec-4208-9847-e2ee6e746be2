📄 Route: workflow/designer
📂 Controller: controller\workflow\designer.php
🧱 Models used (1):
   - workflow/workflow
🎨 Twig templates (1):
   - view\template\workflow\designer.twig
🈯 Arabic Language Files (1):
   - language\ar\workflow\designer.php
🇬🇧 English Language Files (1):
   - language\en-gb\workflow\designer.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_cancel
   - button_save
   - entry_description
   - entry_name
   - entry_status
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_clear
   - text_decision
   - text_delay
   - text_designer
   - text_disabled
   - text_edit
   - text_email
   - text_enabled
   - text_end
   - text_home
   - text_start
   - text_success
   - text_task
   - text_workflow
   - text_workflow_designer
   - text_zoom_in
   - text_zoom_out

❌ Missing in Arabic:
   - button_cancel
   - button_save
   - entry_description
   - entry_name
   - entry_status
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_clear
   - text_decision
   - text_delay
   - text_designer
   - text_disabled
   - text_edit
   - text_email
   - text_enabled
   - text_end
   - text_home
   - text_start
   - text_success
   - text_task
   - text_workflow
   - text_workflow_designer
   - text_zoom_in
   - text_zoom_out

❌ Missing in English:
   - button_cancel
   - button_save
   - entry_description
   - entry_name
   - entry_status
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_clear
   - text_decision
   - text_delay
   - text_designer
   - text_disabled
   - text_edit
   - text_email
   - text_enabled
   - text_end
   - text_home
   - text_start
   - text_success
   - text_task
   - text_workflow
   - text_workflow_designer
   - text_zoom_in
   - text_zoom_out

💡 Suggested Arabic Additions:
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - entry_description = ""  # TODO: ترجمة عربية
   - entry_name = ""  # TODO: ترجمة عربية
   - entry_status = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_clear = ""  # TODO: ترجمة عربية
   - text_decision = ""  # TODO: ترجمة عربية
   - text_delay = ""  # TODO: ترجمة عربية
   - text_designer = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_email = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_end = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_start = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_task = ""  # TODO: ترجمة عربية
   - text_workflow = ""  # TODO: ترجمة عربية
   - text_workflow_designer = ""  # TODO: ترجمة عربية
   - text_zoom_in = ""  # TODO: ترجمة عربية
   - text_zoom_out = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_cancel = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - entry_description = ""  # TODO: English translation
   - entry_name = ""  # TODO: English translation
   - entry_status = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_clear = ""  # TODO: English translation
   - text_decision = ""  # TODO: English translation
   - text_delay = ""  # TODO: English translation
   - text_designer = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_email = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_end = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_start = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_task = ""  # TODO: English translation
   - text_workflow = ""  # TODO: English translation
   - text_workflow_designer = ""  # TODO: English translation
   - text_zoom_in = ""  # TODO: English translation
   - text_zoom_out = ""  # TODO: English translation
