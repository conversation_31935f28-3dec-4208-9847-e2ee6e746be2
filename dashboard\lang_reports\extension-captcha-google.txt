📄 Route: extension/captcha/google
📂 Controller: controller\extension\captcha\google.php
🧱 Models used (1):
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\captcha\google.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\captcha\google.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\captcha\google.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_key
   - error_permission
   - error_secret
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_key
   - error_permission
   - error_secret
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_key
   - error_permission
   - error_secret
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_key = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_secret = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_key = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_secret = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
