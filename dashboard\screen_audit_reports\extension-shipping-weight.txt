📄 Route: extension/shipping/weight
📂 Controller: controller\extension\shipping\weight.php
🧱 Models used (3):
   ✅ setting/setting (5 functions)
   ✅ localisation/geo_zone (11 functions)
   ✅ localisation/tax_class (8 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\shipping\weight.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\shipping\weight.php (11 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\shipping\weight.php (11 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (24):
   - action
   - button_save
   - column_left
   - entry_rate
   - entry_sort_order
   - entry_status
   - entry_tax_class
   - error_permission
   - error_warning
   - footer
   - header
   - help_rate
   - shipping_weight_sort_order
   - tab_general
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_none
   - text_success
   ... و 4 متغير آخر

❌ Missing in Arabic (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_weight_sort_order
   - tab_general
   - text_disabled
   - text_enabled
   - text_home
   - text_none

❌ Missing in English (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_weight_sort_order
   - tab_general
   - text_disabled
   - text_enabled
   - text_home
   - text_none

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 14 items
      - button_save
      - shipping_weight_sort_order
      - error_warning
      - column_left
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 14 items
      - button_save
      - shipping_weight_sort_order
      - error_warning
      - column_left
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 14 متغير عربي و 14 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:30
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.