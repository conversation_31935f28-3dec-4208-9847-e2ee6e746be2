📄 Route: hr/performance
📂 Controller: controller\hr\performance.php
🧱 Models used (2):
   - hr/performance
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\hr\performance.php
🇬🇧 English Language Files (1):
   - language\en-gb\hr\performance.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add_review
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_comments
   - column_criteria_name
   - column_employee
   - column_overall_score
   - column_review_date
   - column_reviewer
   - column_score
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_add_review
   - text_ajax_error
   - text_all_statuses
   - text_comments
   - text_confirm_delete
   - text_criteria_scores
   - text_edit_review
   - text_employee
   - text_filter
   - text_home
   - text_overall_score
   - text_performance_list
   - text_review_date
   - text_review_date_end
   - text_review_date_start
   - text_reviewer
   - text_select_employee
   - text_select_reviewer
   - text_status
   - text_status_completed
   - text_status_pending
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in Arabic:
   - button_add_review
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_comments
   - column_criteria_name
   - column_employee
   - column_overall_score
   - column_review_date
   - column_reviewer
   - column_score
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_add_review
   - text_ajax_error
   - text_all_statuses
   - text_comments
   - text_confirm_delete
   - text_criteria_scores
   - text_edit_review
   - text_employee
   - text_filter
   - text_home
   - text_overall_score
   - text_performance_list
   - text_review_date
   - text_review_date_end
   - text_review_date_start
   - text_reviewer
   - text_select_employee
   - text_select_reviewer
   - text_status
   - text_status_completed
   - text_status_pending
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in English:
   - button_add_review
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_comments
   - column_criteria_name
   - column_employee
   - column_overall_score
   - column_review_date
   - column_reviewer
   - column_score
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_add_review
   - text_ajax_error
   - text_all_statuses
   - text_comments
   - text_confirm_delete
   - text_criteria_scores
   - text_edit_review
   - text_employee
   - text_filter
   - text_home
   - text_overall_score
   - text_performance_list
   - text_review_date
   - text_review_date_end
   - text_review_date_start
   - text_reviewer
   - text_select_employee
   - text_select_reviewer
   - text_status
   - text_status_completed
   - text_status_pending
   - text_success_add
   - text_success_delete
   - text_success_edit

💡 Suggested Arabic Additions:
   - button_add_review = ""  # TODO: ترجمة عربية
   - button_close = ""  # TODO: ترجمة عربية
   - button_filter = ""  # TODO: ترجمة عربية
   - button_reset = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - column_actions = ""  # TODO: ترجمة عربية
   - column_comments = ""  # TODO: ترجمة عربية
   - column_criteria_name = ""  # TODO: ترجمة عربية
   - column_employee = ""  # TODO: ترجمة عربية
   - column_overall_score = ""  # TODO: ترجمة عربية
   - column_review_date = ""  # TODO: ترجمة عربية
   - column_reviewer = ""  # TODO: ترجمة عربية
   - column_score = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add_review = ""  # TODO: ترجمة عربية
   - text_ajax_error = ""  # TODO: ترجمة عربية
   - text_all_statuses = ""  # TODO: ترجمة عربية
   - text_comments = ""  # TODO: ترجمة عربية
   - text_confirm_delete = ""  # TODO: ترجمة عربية
   - text_criteria_scores = ""  # TODO: ترجمة عربية
   - text_edit_review = ""  # TODO: ترجمة عربية
   - text_employee = ""  # TODO: ترجمة عربية
   - text_filter = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_overall_score = ""  # TODO: ترجمة عربية
   - text_performance_list = ""  # TODO: ترجمة عربية
   - text_review_date = ""  # TODO: ترجمة عربية
   - text_review_date_end = ""  # TODO: ترجمة عربية
   - text_review_date_start = ""  # TODO: ترجمة عربية
   - text_reviewer = ""  # TODO: ترجمة عربية
   - text_select_employee = ""  # TODO: ترجمة عربية
   - text_select_reviewer = ""  # TODO: ترجمة عربية
   - text_status = ""  # TODO: ترجمة عربية
   - text_status_completed = ""  # TODO: ترجمة عربية
   - text_status_pending = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add_review = ""  # TODO: English translation
   - button_close = ""  # TODO: English translation
   - button_filter = ""  # TODO: English translation
   - button_reset = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - column_actions = ""  # TODO: English translation
   - column_comments = ""  # TODO: English translation
   - column_criteria_name = ""  # TODO: English translation
   - column_employee = ""  # TODO: English translation
   - column_overall_score = ""  # TODO: English translation
   - column_review_date = ""  # TODO: English translation
   - column_reviewer = ""  # TODO: English translation
   - column_score = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add_review = ""  # TODO: English translation
   - text_ajax_error = ""  # TODO: English translation
   - text_all_statuses = ""  # TODO: English translation
   - text_comments = ""  # TODO: English translation
   - text_confirm_delete = ""  # TODO: English translation
   - text_criteria_scores = ""  # TODO: English translation
   - text_edit_review = ""  # TODO: English translation
   - text_employee = ""  # TODO: English translation
   - text_filter = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_overall_score = ""  # TODO: English translation
   - text_performance_list = ""  # TODO: English translation
   - text_review_date = ""  # TODO: English translation
   - text_review_date_end = ""  # TODO: English translation
   - text_review_date_start = ""  # TODO: English translation
   - text_reviewer = ""  # TODO: English translation
   - text_select_employee = ""  # TODO: English translation
   - text_select_reviewer = ""  # TODO: English translation
   - text_status = ""  # TODO: English translation
   - text_status_completed = ""  # TODO: English translation
   - text_status_pending = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
