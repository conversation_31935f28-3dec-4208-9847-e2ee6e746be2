📄 Route: mail/transaction
📂 Controller: controller\mail\transaction.php
🧱 Models used (2):
   - customer/customer
   - setting/store
🎨 Twig templates (1):
   - view\template\mail\transaction.twig
🈯 Arabic Language Files (1):
   - language\ar\mail\transaction.php
🇬🇧 English Language Files (1):
   - language\en-gb\mail\transaction.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - text_received
   - text_subject
   - text_total

❌ Missing in Arabic:
   - text_received
   - text_subject
   - text_total

❌ Missing in English:
   - text_received
   - text_subject
   - text_total

💡 Suggested Arabic Additions:
   - text_received = ""  # TODO: ترجمة عربية
   - text_subject = ""  # TODO: ترجمة عربية
   - text_total = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - text_received = ""  # TODO: English translation
   - text_subject = ""  # TODO: English translation
   - text_total = ""  # TODO: English translation
