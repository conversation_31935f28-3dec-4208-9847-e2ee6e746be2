📄 Route: report/inventory_analysis
📂 Controller: controller\report\inventory_analysis.php
🧱 Models used (3):
   - branch/branch
   - catalog/category
   - report/inventory
🎨 Twig templates (1):
   - view\template\report\inventory_analysis.twig
🈯 Arabic Language Files (1):
   - language\ar\report\inventory_analysis.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_branch
   - column_category
   - column_cost
   - column_last_movement
   - column_model
   - column_product
   - column_quantity
   - column_sku
   - column_unit
   - column_value
   - date_format_short
   - entry_branch
   - entry_category
   - entry_date
   - entry_product
   - heading_title
   - heading_valuation
   - text_home
   - text_inventory_alerts
   - text_inventory_alerts_desc
   - text_inventory_turnover
   - text_inventory_turnover_desc
   - text_inventory_valuation
   - text_inventory_valuation_desc
   - text_pagination
   - text_slow_moving
   - text_slow_moving_desc
   - text_total_value

❌ Missing in Arabic:
   - column_branch
   - column_category
   - column_cost
   - column_last_movement
   - column_model
   - column_product
   - column_quantity
   - column_sku
   - column_unit
   - column_value
   - date_format_short
   - entry_branch
   - entry_category
   - entry_date
   - entry_product
   - heading_title
   - heading_valuation
   - text_home
   - text_inventory_alerts
   - text_inventory_alerts_desc
   - text_inventory_turnover
   - text_inventory_turnover_desc
   - text_inventory_valuation
   - text_inventory_valuation_desc
   - text_pagination
   - text_slow_moving
   - text_slow_moving_desc
   - text_total_value

❌ Missing in English:
   - column_branch
   - column_category
   - column_cost
   - column_last_movement
   - column_model
   - column_product
   - column_quantity
   - column_sku
   - column_unit
   - column_value
   - date_format_short
   - entry_branch
   - entry_category
   - entry_date
   - entry_product
   - heading_title
   - heading_valuation
   - text_home
   - text_inventory_alerts
   - text_inventory_alerts_desc
   - text_inventory_turnover
   - text_inventory_turnover_desc
   - text_inventory_valuation
   - text_inventory_valuation_desc
   - text_pagination
   - text_slow_moving
   - text_slow_moving_desc
   - text_total_value

💡 Suggested Arabic Additions:
   - column_branch = ""  # TODO: ترجمة عربية
   - column_category = ""  # TODO: ترجمة عربية
   - column_cost = ""  # TODO: ترجمة عربية
   - column_last_movement = ""  # TODO: ترجمة عربية
   - column_model = ""  # TODO: ترجمة عربية
   - column_product = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_sku = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - column_value = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - entry_branch = ""  # TODO: ترجمة عربية
   - entry_category = ""  # TODO: ترجمة عربية
   - entry_date = ""  # TODO: ترجمة عربية
   - entry_product = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_valuation = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_inventory_alerts = ""  # TODO: ترجمة عربية
   - text_inventory_alerts_desc = ""  # TODO: ترجمة عربية
   - text_inventory_turnover = ""  # TODO: ترجمة عربية
   - text_inventory_turnover_desc = ""  # TODO: ترجمة عربية
   - text_inventory_valuation = ""  # TODO: ترجمة عربية
   - text_inventory_valuation_desc = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_slow_moving = ""  # TODO: ترجمة عربية
   - text_slow_moving_desc = ""  # TODO: ترجمة عربية
   - text_total_value = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_branch = ""  # TODO: English translation
   - column_category = ""  # TODO: English translation
   - column_cost = ""  # TODO: English translation
   - column_last_movement = ""  # TODO: English translation
   - column_model = ""  # TODO: English translation
   - column_product = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_sku = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - column_value = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - entry_branch = ""  # TODO: English translation
   - entry_category = ""  # TODO: English translation
   - entry_date = ""  # TODO: English translation
   - entry_product = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_valuation = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_inventory_alerts = ""  # TODO: English translation
   - text_inventory_alerts_desc = ""  # TODO: English translation
   - text_inventory_turnover = ""  # TODO: English translation
   - text_inventory_turnover_desc = ""  # TODO: English translation
   - text_inventory_valuation = ""  # TODO: English translation
   - text_inventory_valuation_desc = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_slow_moving = ""  # TODO: English translation
   - text_slow_moving_desc = ""  # TODO: English translation
   - text_total_value = ""  # TODO: English translation
