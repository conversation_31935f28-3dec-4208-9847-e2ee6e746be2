📄 Route: inventory/manufacturer
📂 Controller: controller\inventory\manufacturer.php
🧱 Models used (4):
   ✅ inventory/manufacturer (14 functions)
   ✅ localisation/language (7 functions)
   ✅ setting/store (14 functions)
   ✅ localisation/country (6 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\inventory\manufacturer.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\manufacturer.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - error_manufacturer_in_use
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_default
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in Arabic (11):
   - error_manufacturer_in_use
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_default
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in English (11):
   - error_manufacturer_in_use
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_default
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 11 items
      - error_manufacturer_in_use
      - text_success
      - text_add
      - text_home
      - text_disabled
   🟡 MISSING_ENGLISH_VARIABLES: 11 items
      - error_manufacturer_in_use
      - text_success
      - text_add
      - text_home
      - text_disabled

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 11 متغير عربي و 11 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:05
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.