📄 Route: catalog/attribute_group
📂 Controller: controller\catalog\attribute_group.php
🧱 Models used (3):
   ✅ catalog/attribute_group (7 functions)
   ✅ localisation/language (7 functions)
   ✅ catalog/attribute (8 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\catalog\attribute_group.php (14 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\catalog\attribute_group.php (14 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (9):
   - error_attribute
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (2):
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - text_home
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:47
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.