📄 Route: sale/dynamic_pricing
📂 Controller: controller\sale\dynamic_pricing.php
🧱 Models used (1):
   ✅ sale/dynamic_pricing (16 functions)
🎨 Twig templates (1):
   ✅ view\template\sale\dynamic_pricing.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\sale\dynamic_pricing.php (79 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\sale\dynamic_pricing.php (79 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (65):
   - button_add
   - button_filter
   - button_test
   - column_name
   - column_priority
   - column_status
   - entry_customer
   - error_permission
   - pagination
   - sort_date_added
   - text_add
   - text_category
   - text_discount
   - text_discount_percentage
   - text_global
   - text_list
   - text_original_price
   - text_select
   - text_test_results
   - user_token
   ... و 45 متغير آخر

❌ Missing in Arabic (20):
   - column_left
   - date_format_short
   - delete
   - error_warning
   - filter_name
   - footer
   - header
   - pagination
   - results
   - sort_date_added
   - sort_rule_type
   - text_filter
   - text_home
   - text_sales
   - user_token
   ... و 5 متغير آخر

❌ Missing in English (20):
   - column_left
   - date_format_short
   - delete
   - error_warning
   - filter_name
   - footer
   - header
   - pagination
   - results
   - sort_date_added
   - sort_rule_type
   - text_filter
   - text_home
   - text_sales
   - user_token
   ... و 5 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 20 items
      - text_sales
      - pagination
      - filter_name
      - sort_date_added
      - text_filter
   🟡 MISSING_ENGLISH_VARIABLES: 20 items
      - text_sales
      - pagination
      - filter_name
      - sort_date_added
      - text_filter

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 20 متغير عربي و 20 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:17
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.