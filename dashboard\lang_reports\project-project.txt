📄 Route: project/project
📂 Controller: controller\project\project.php
🧱 Models used (1):
   - project/project
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_date_required
   - error_description_required
   - error_due_date_required
   - error_end_date
   - error_hours_required
   - error_impact_required
   - error_invalid_data
   - error_invalid_date_range
   - error_invalid_request
   - error_milestone_add_failed
   - error_name
   - error_name_required
   - error_permission
   - error_probability_required
   - error_project_required
   - error_risk_add_failed
   - error_start_date
   - error_task_required
   - error_task_update_failed
   - error_time_log_failed
   - error_title_required
   - heading_title
   - text_gantt_chart
   - text_home
   - text_milestone_added_success
   - text_project_dashboard
   - text_project_view
   - text_projects
   - text_risk_added_success
   - text_success
   - text_task_status_updated
   - text_time_logged_success

❌ Missing in Arabic:
   - error_date_required
   - error_description_required
   - error_due_date_required
   - error_end_date
   - error_hours_required
   - error_impact_required
   - error_invalid_data
   - error_invalid_date_range
   - error_invalid_request
   - error_milestone_add_failed
   - error_name
   - error_name_required
   - error_permission
   - error_probability_required
   - error_project_required
   - error_risk_add_failed
   - error_start_date
   - error_task_required
   - error_task_update_failed
   - error_time_log_failed
   - error_title_required
   - heading_title
   - text_gantt_chart
   - text_home
   - text_milestone_added_success
   - text_project_dashboard
   - text_project_view
   - text_projects
   - text_risk_added_success
   - text_success
   - text_task_status_updated
   - text_time_logged_success

❌ Missing in English:
   - error_date_required
   - error_description_required
   - error_due_date_required
   - error_end_date
   - error_hours_required
   - error_impact_required
   - error_invalid_data
   - error_invalid_date_range
   - error_invalid_request
   - error_milestone_add_failed
   - error_name
   - error_name_required
   - error_permission
   - error_probability_required
   - error_project_required
   - error_risk_add_failed
   - error_start_date
   - error_task_required
   - error_task_update_failed
   - error_time_log_failed
   - error_title_required
   - heading_title
   - text_gantt_chart
   - text_home
   - text_milestone_added_success
   - text_project_dashboard
   - text_project_view
   - text_projects
   - text_risk_added_success
   - text_success
   - text_task_status_updated
   - text_time_logged_success

💡 Suggested Arabic Additions:
   - error_date_required = ""  # TODO: ترجمة عربية
   - error_description_required = ""  # TODO: ترجمة عربية
   - error_due_date_required = ""  # TODO: ترجمة عربية
   - error_end_date = ""  # TODO: ترجمة عربية
   - error_hours_required = ""  # TODO: ترجمة عربية
   - error_impact_required = ""  # TODO: ترجمة عربية
   - error_invalid_data = ""  # TODO: ترجمة عربية
   - error_invalid_date_range = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_milestone_add_failed = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_name_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_probability_required = ""  # TODO: ترجمة عربية
   - error_project_required = ""  # TODO: ترجمة عربية
   - error_risk_add_failed = ""  # TODO: ترجمة عربية
   - error_start_date = ""  # TODO: ترجمة عربية
   - error_task_required = ""  # TODO: ترجمة عربية
   - error_task_update_failed = ""  # TODO: ترجمة عربية
   - error_time_log_failed = ""  # TODO: ترجمة عربية
   - error_title_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_gantt_chart = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_milestone_added_success = ""  # TODO: ترجمة عربية
   - text_project_dashboard = ""  # TODO: ترجمة عربية
   - text_project_view = ""  # TODO: ترجمة عربية
   - text_projects = ""  # TODO: ترجمة عربية
   - text_risk_added_success = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_task_status_updated = ""  # TODO: ترجمة عربية
   - text_time_logged_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_date_required = ""  # TODO: English translation
   - error_description_required = ""  # TODO: English translation
   - error_due_date_required = ""  # TODO: English translation
   - error_end_date = ""  # TODO: English translation
   - error_hours_required = ""  # TODO: English translation
   - error_impact_required = ""  # TODO: English translation
   - error_invalid_data = ""  # TODO: English translation
   - error_invalid_date_range = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_milestone_add_failed = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_name_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_probability_required = ""  # TODO: English translation
   - error_project_required = ""  # TODO: English translation
   - error_risk_add_failed = ""  # TODO: English translation
   - error_start_date = ""  # TODO: English translation
   - error_task_required = ""  # TODO: English translation
   - error_task_update_failed = ""  # TODO: English translation
   - error_time_log_failed = ""  # TODO: English translation
   - error_title_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_gantt_chart = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_milestone_added_success = ""  # TODO: English translation
   - text_project_dashboard = ""  # TODO: English translation
   - text_project_view = ""  # TODO: English translation
   - text_projects = ""  # TODO: English translation
   - text_risk_added_success = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_task_status_updated = ""  # TODO: English translation
   - text_time_logged_success = ""  # TODO: English translation
