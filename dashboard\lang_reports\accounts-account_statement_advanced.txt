📄 Route: accounts/account_statement_advanced
📂 Controller: controller\accounts\account_statement_advanced.php
🧱 Models used (5):
   - accounts/account_statement_advanced
   - accounts/audit_trail
   - accounts/chartaccount
   - core/central_service_manager
   - localisation/currency
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\account_statement.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\account_statement.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_long
   - date_format_short
   - error_account_not_found
   - error_permission
   - heading_title
   - text_home
   - text_view

❌ Missing in Arabic:
   - date_format_long
   - date_format_short
   - error_account_not_found
   - error_permission
   - heading_title
   - text_home
   - text_view

❌ Missing in English:
   - date_format_long
   - date_format_short
   - error_account_not_found
   - error_permission
   - heading_title
   - text_home
   - text_view

💡 Suggested Arabic Additions:
   - date_format_long = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_account_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_view = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_long = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_account_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_view = ""  # TODO: English translation
