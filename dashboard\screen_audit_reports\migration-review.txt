📄 Route: migration/review
📂 Controller: controller\migration\review.php
🧱 Models used (1):
   ✅ migration/migration (10 functions)
🎨 Twig templates (1):
   ✅ view\template\migration\review.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\migration\review.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\migration\review.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (40):
   - button_approve
   - button_rollback
   - column_date
   - column_destination
   - column_left
   - column_source
   - column_status
   - error_no_backup
   - error_permission
   - error_reject_reason
   - error_validation
   - footer
   - header
   - status_approved
   - status_rejected
   - success
   - text_home
   - text_migration_approved
   - text_success
   - user_token
   ... و 20 متغير آخر

❌ Missing in Arabic (40):
   - button_approve
   - button_rollback
   - column_date
   - column_destination
   - column_status
   - error_no_backup
   - error_permission
   - error_reject_reason
   - error_validation
   - header
   - status_approved
   - status_rejected
   - text_migration_approved
   - text_success
   - user_token
   ... و 25 متغير آخر

❌ Missing in English (40):
   - button_approve
   - button_rollback
   - column_date
   - column_destination
   - column_status
   - error_no_backup
   - error_permission
   - error_reject_reason
   - error_validation
   - header
   - status_approved
   - status_rejected
   - text_migration_approved
   - text_success
   - user_token
   ... و 25 متغير آخر

🗄️ Database Tables Used (2):
   ❌ backup
   ❌ migration

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 40 items
      - error_reject_reason
      - status_rejected
      - header
      - column_destination
      - status_approved
   🟡 MISSING_ENGLISH_VARIABLES: 40 items
      - error_reject_reason
      - status_rejected
      - header
      - column_destination
      - status_approved
   🔴 INVALID_DATABASE_TABLES: 2 items
      - backup
      - migration

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 40 متغير عربي و 40 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.