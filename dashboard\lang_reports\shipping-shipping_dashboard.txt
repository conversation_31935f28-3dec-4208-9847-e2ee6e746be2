📄 Route: shipping/shipping_dashboard
📂 Controller: controller\shipping\shipping_dashboard.php
🧱 Models used (5):
   - sale/order
   - shipping/order_fulfillment
   - shipping/shipment_tracking
   - shipping/shipping_integration
   - shipping/shipping_settlement
🎨 Twig templates (1):
   - view\template\shipping\shipping_dashboard.twig
🈯 Arabic Language Files (1):
   - language\ar\shipping\shipping_dashboard.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - heading_title
   - text_home

❌ Missing in Arabic:
   - datetime_format
   - heading_title
   - text_home

❌ Missing in English:
   - datetime_format
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
