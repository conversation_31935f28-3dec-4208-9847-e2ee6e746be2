📄 Route: localisation/geo_zone
📂 Controller: controller\localisation\geo_zone.php
🧱 Models used (3):
   - localisation/country
   - localisation/geo_zone
   - localisation/tax_rate
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\localisation\geo_zone.php
🇬🇧 English Language Files (1):
   - language\en-gb\localisation\geo_zone.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_description
   - error_name
   - error_permission
   - error_tax_rate
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_description
   - error_name
   - error_permission
   - error_tax_rate
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_description
   - error_name
   - error_permission
   - error_tax_rate
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_description = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_tax_rate = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_description = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_tax_rate = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
