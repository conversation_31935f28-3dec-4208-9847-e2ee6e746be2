# 8️⃣ ملاحظات هامة

## ⚠️ تحذيرات حرجة (Critical Warnings)

### **🚨 1. لا تحذف أي شيء من column_left.php**
- **السبب:** كل route قد يكون مرتبط بملفات موجودة في tree.txt
- **الاستثناء الوحيد:** Routes مكررة فعلياً (نفس المسار تماماً)
- **المثال:** إذا وجدت `accounts/chartaccount` و `accounts/chart_account` فهما مختلفان
- **القاعدة:** حتى لو بدا الـ route غير مهم، لا تحذفه

### **🚨 2. لا ترقي Bootstrap للإصدار الرابع**
- **السبب:** جميع القوالب مبنية على Bootstrap 3.3.7
- **المشكلة:** الإصدار الرابع يكسر التصميم بالكامل
- **الحل:** تحسين الموجود بدلاً من الترقية
- **ملاحظة:** هذا قرار تقني مدروس وليس إهمال

### **🚨 3. احترم بنية OpenCart 3.0.3.x**
- **لا تخلط:** مع إصدارات أخرى من OpenCart
- **لا تستخدم:** ميزات من الإصدار الرابع
- **التزم:** بالـ MVC Pattern الخاص بالإصدار 3.x
- **تذكر:** Twig Templates وليس PHP templates

### **🚨 4. البادئة cod_ إلزامية**
- **جميع الجداول الجديدة:** يجب أن تبدأ بـ `cod_`
- **لا تستخدم:** `oc_` للجداول الجديدة
- **السبب:** التمييز بين جداول OpenCart الأصلية وجداول AYM ERP
- **مثال:** `cod_inventory_alert` وليس `oc_inventory_alert`

## 📋 قواعد التطوير الإلزامية

### **✅ 1. قراءة شاملة قبل أي تعديل**
- **اقرأ الملف كاملاً:** سطر بسطر
- **افهم الوظيفة:** ما يفعله الكود
- **تتبع الترابطات:** مع الملفات الأخرى
- **راجع قاعدة البيانات:** في minidb.txt

### **✅ 2. استخدام متغيرات اللغة دائماً**
```php
// خطأ - نص مباشر
echo 'إدارة المخزون';

// صحيح - متغير لغة
echo $this->language->get('text_inventory_management');
```

### **✅ 3. تطبيق الصلاحيات في كل شاشة**
```php
// فحص صلاحية الوصول
if (!$this->user->hasPermission('access', 'inventory/current_stock')) {
    $this->response->redirect($this->url->link('error/permission'));
}

// فحص صلاحية التعديل
if (!$this->user->hasPermission('modify', 'inventory/current_stock')) {
    $data['error_permission'] = true;
}
```

### **✅ 4. استخدام الخدمات المركزية**
```php
// تحميل الخدمات المركزية
$this->load->model('central/service_manager');
$this->load->model('activity_log');

// تسجيل النشاط
$this->model_activity_log->addActivity([
    'action' => 'view_inventory',
    'data' => json_encode(['product_id' => $product_id])
]);
```

### **✅ 5. حماية CSRF في جميع النماذج**
```php
// في الـ controller
if ($this->request->server['REQUEST_METHOD'] == 'POST') {
    if (!hash_equals($this->session->data['csrf_token'], $this->request->post['csrf_token'])) {
        $this->error['csrf'] = 'Invalid CSRF token';
    }
}

// في الـ template
<input type="hidden" name="csrf_token" value="{{ csrf_token }}" />
```

## 🔧 معايير الجودة التقنية

### **📊 1. الأداء (Performance)**
- **تحميل الصفحة:** أقل من 2 ثانية
- **استعلامات قاعدة البيانات:** أقل من 100ms لكل استعلام
- **استخدام الذاكرة:** أقل من 128MB لكل طلب
- **حجم الصفحة:** أقل من 2MB مع الصور

### **🔒 2. الأمان (Security)**
- **تشفير كلمات المرور:** bcrypt أو أقوى
- **حماية SQL Injection:** Prepared Statements
- **حماية XSS:** تنظيف جميع المدخلات
- **حماية CSRF:** في جميع النماذج

### **🎨 3. تجربة المستخدم (UX)**
- **تصميم متجاوب:** يعمل على جميع الأجهزة
- **رسائل واضحة:** رسائل خطأ ونجاح مفهومة
- **تنقل بديهي:** سهولة الوصول للوظائف
- **سرعة استجابة:** تفاعل فوري مع المستخدم

### **🌐 4. التوطين (Localization)**
- **دعم RTL/LTR:** للعربية والإنجليزية
- **تنسيق التواريخ:** حسب المنطقة
- **تنسيق الأرقام:** فواصل الآلاف والعشرية
- **العملات:** دعم العملات المتعددة

## 📁 هيكل الملفات المطلوب

### **🗂️ 1. Controllers**
```
dashboard/controller/
├── accounts/          # النظام المحاسبي
├── inventory/         # إدارة المخزون
├── purchase/          # المشتريات
├── sale/             # المبيعات
├── catalog/          # إدارة الكتالوج
├── customer/         # إدارة العملاء
├── hr/               # الموارد البشرية
├── communication/    # التواصل
├── workflow/         # سير العمل
├── ai/               # الذكاء الاصطناعي
└── common/           # الملفات المشتركة
```

### **🗂️ 2. Models**
```
dashboard/model/
├── accounts/          # نماذج المحاسبة
├── inventory/         # نماذج المخزون
├── central/          # الخدمات المركزية
├── communication/    # التواصل
├── workflow/         # سير العمل
└── activity_log.php  # سجل الأنشطة
```

### **🗂️ 3. Templates**
```
dashboard/view/template/
├── accounts/          # قوالب المحاسبة
├── inventory/         # قوالب المخزون
├── common/           # القوالب المشتركة
│   ├── header.twig   # الهيدر
│   ├── footer.twig   # الفوتر
│   └── column_left.twig # العمود الجانبي
└── layout/           # تخطيطات الصفحات
```

### **🗂️ 4. Language Files**
```
dashboard/language/
├── en-gb/            # الإنجليزية
│   ├── accounts/     # ملفات المحاسبة
│   ├── inventory/    # ملفات المخزون
│   └── common/       # الملفات المشتركة
└── ar-eg/            # العربية المصرية
    ├── accounts/     # ملفات المحاسبة
    ├── inventory/    # ملفات المخزون
    └── common/       # الملفات المشتركة
```

## 🎯 أولويات التطوير

### **🥇 الأولوية الأولى (الأسبوع الأول)**
1. **إصلاح النصوص المباشرة** في الملفات الأساسية
2. **توحيد ملفات اللغة** للملفات الحرجة
3. **تفعيل الخدمات المركزية** في الشاشات الأساسية
4. **تحسين الاستعلامات البطيئة** في المحاسبة والمخزون

### **🥈 الأولوية الثانية (الأسبوع الثاني)**
1. **مراجعة النظام المحاسبي** بالدستور الشامل
2. **مراجعة نظام المخزون** بالدستور الشامل
3. **إصلاح التكامل** بين الوحدات
4. **تحسين الأداء** العام للنظام

### **🥉 الأولوية الثالثة (الأسبوع الثالث)**
1. **مراجعة المبيعات والمشتريات** بالدستور الشامل
2. **تحسين التجارة الإلكترونية**
3. **تطوير واجهات المستخدم**
4. **إضافة الاختبارات الأساسية**

## 📊 مؤشرات المتابعة اليومية

### **📈 KPIs يومية**
- **الشاشات المراجعة:** عدد الشاشات المكتملة
- **المشاكل المحلولة:** عدد المشاكل المصلحة
- **النصوص المصلحة:** عدد النصوص المحولة لمتغيرات
- **الاستعلامات المحسنة:** عدد الاستعلامات المحسنة

### **📊 تقرير يومي مطلوب**
```markdown
# تقرير يومي - [التاريخ]

## الإنجازات
- الشاشات المراجعة: [عدد]
- المشاكل المحلولة: [عدد]
- النصوص المصلحة: [عدد]

## المشاكل المكتشفة
- [قائمة المشاكل الجديدة]

## الخطة للغد
- [المهام المخططة]

## ملاحظات
- [أي ملاحظات مهمة]
```

## 🔍 نقاط التحقق الإلزامية

### **✅ قبل كل commit**
- [ ] لا توجد نصوص عربية مباشرة
- [ ] جميع متغيرات اللغة موجودة في ملفات اللغة
- [ ] الصلاحيات مطبقة بشكل صحيح
- [ ] CSRF protection مفعل
- [ ] لا توجد استعلامات بطيئة
- [ ] الكود موثق بشكل مناسب

### **✅ قبل كل release**
- [ ] جميع الاختبارات تمر بنجاح
- [ ] الأداء يلبي المعايير المطلوبة
- [ ] الأمان محقق بالكامل
- [ ] التوثيق محدث
- [ ] ملفات اللغة متطابقة

## 🎓 نصائح للمطورين

### **💡 أفضل الممارسات**
1. **اقرأ قبل أن تكتب:** فهم الكود الموجود أولاً
2. **اختبر باستمرار:** اختبر كل تغيير فور إجرائه
3. **وثق كل شيء:** اكتب تعليقات واضحة
4. **فكر في المستقبل:** اكتب كود قابل للصيانة
5. **تعلم من الأخطاء:** حلل الأخطاء وتجنب تكرارها

### **🚫 أخطاء شائعة يجب تجنبها**
1. **عدم قراءة الكود الموجود** قبل التعديل
2. **استخدام نصوص مباشرة** بدلاً من متغيرات اللغة
3. **إهمال الصلاحيات** في الشاشات الجديدة
4. **عدم اختبار التكامل** مع الوحدات الأخرى
5. **تجاهل الأداء** عند كتابة الاستعلامات

### **🔧 أدوات مساعدة**
- **IDE:** استخدم IDE يدعم PHP وTwig
- **Debugger:** استخدم Xdebug للتتبع
- **Database Tools:** استخدم phpMyAdmin أو مشابه
- **Version Control:** استخدم Git بشكل صحيح
- **Testing Tools:** استخدم PHPUnit للاختبارات

## 📞 الدعم والمساعدة

### **🆘 عند مواجهة مشاكل**
1. **راجع الوثائق** أولاً
2. **ابحث في الكود الموجود** عن حلول مشابهة
3. **اختبر على بيئة تطوير** قبل الإنتاج
4. **اطلب المساعدة** عند الحاجة
5. **وثق الحل** للمستقبل

### **📚 مصادر التعلم**
- **OpenCart Documentation:** للفهم الأساسي
- **PHP Manual:** للمراجع التقنية
- **Twig Documentation:** لقوالب العرض
- **Bootstrap 3 Documentation:** للواجهات
- **MySQL Documentation:** لقاعدة البيانات
