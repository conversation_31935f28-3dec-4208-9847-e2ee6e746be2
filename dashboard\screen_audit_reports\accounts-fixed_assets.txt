📄 Route: accounts/fixed_assets
📂 Controller: controller\accounts\fixed_assets.php
🧱 Models used (2):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/fixed_assets (9 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\fixed_assets.php (103 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\fixed_assets.php (103 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (31):
   - button_filter
   - date_format_short
   - error_asset_id_required
   - error_category_required
   - error_depreciation_method_required
   - error_name_required
   - print_title
   - text_accum_depr
   - text_assets
   - text_buildings
   - text_declining_balance
   - text_end_date
   - text_equipment
   - text_fixed_assets_report
   - text_furniture
   - text_home
   - text_other
   - text_straight_line
   - text_success_dispose
   - text_sum_of_years
   ... و 11 متغير آخر

❌ Missing in Arabic (3):
   - code
   - date_format_short
   - direction

❌ Missing in English (3):
   - code
   - date_format_short
   - direction

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - code
      - direction
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - code
      - direction
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:40
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.