📄 Route: extension/dashboard/customer
📂 Controller: controller\extension\dashboard\customer.php
🧱 Models used (2):
   ✅ setting/setting (5 functions)
   ✅ customer/customer (51 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\dashboard\customer.php (9 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\dashboard\customer.php (9 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (5):
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (1):
   - text_home

🗄️ Database Tables Used (1):
   ❌ credit_limit

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 1 items
      - text_home
   🔴 INVALID_DATABASE_TABLES: 1 items
      - credit_limit

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 1 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:21
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.