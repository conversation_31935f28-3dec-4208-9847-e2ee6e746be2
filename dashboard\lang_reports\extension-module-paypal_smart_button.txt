📄 Route: extension/module/paypal_smart_button
📂 Controller: controller\extension\module\paypal_smart_button.php
🧱 Models used (2):
   - extension/module/paypal_smart_button
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\module\paypal_smart_button.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\module\paypal_smart_button.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\module\paypal_smart_button.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - success_save
   - text_extensions
   - text_home

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - success_save
   - text_extensions
   - text_home

❌ Missing in English:
   - error_permission
   - heading_title
   - success_save
   - text_extensions
   - text_home

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - success_save = ""  # TODO: ترجمة عربية
   - text_extensions = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - success_save = ""  # TODO: English translation
   - text_extensions = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
