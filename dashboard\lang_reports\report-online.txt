📄 Route: report/online
📂 Controller: controller\report\online.php
🧱 Models used (2):
   - customer/customer
   - report/online
🎨 Twig templates (1):
   - view\template\report\online.twig
🈯 Arabic Language Files (1):
   - language\ar\report\online.php
🇬🇧 English Language Files (1):
   - language\en-gb\report\online.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - heading_title
   - text_guest
   - text_home
   - text_pagination

❌ Missing in Arabic:
   - datetime_format
   - heading_title
   - text_guest
   - text_home
   - text_pagination

❌ Missing in English:
   - datetime_format
   - heading_title
   - text_guest
   - text_home
   - text_pagination

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_guest = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_guest = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
