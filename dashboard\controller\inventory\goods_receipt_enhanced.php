<?php
/**
 * كونترولر استلام البضائع المحسن - Enterprise Grade Plus
 *
 * التحسينات المطبقة وفقاً للدستور الشامل:
 * - تطبيق الخدمات المركزية الخمس بالكامل
 * - نظام الصلاحيات المزدوج المتقدم (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة مع التفاصيل الكاملة
 * - نظام الإشعارات المتقدم مع التصنيف
 * - معالجة الأخطاء الشاملة مع Transaction Support
 * - تكامل محاسبي تلقائي مع إنشاء القيود
 * - نظام فحص الجودة المتطور
 * - تتبع شامل للدفعات وانتهاء الصلاحية
 * - تحديث WAC (المتوسط المرجح للتكلفة) التلقائي
 * - تكامل مع أوامر الشراء والموردين
 * - نظام الموافقات متعدد المستويات
 * - تقارير تحليلية شاملة للاستلام
 * - نظام التنبيهات الذكي للتأخير
 * - تكامل مع شركات الشحن المحلية
 *
 * <AUTHOR> ERP Team - Enhanced by AI Agent
 * @version 4.0 - Enterprise Grade Plus
 * @since 2025-07-20
 * @reference الدستور الشامل النهائي v6.0
 */

class ControllerInventoryGoodsReceiptEnhanced extends Controller {
    private $error = array();
    private $central_service;
    private $permissions = array();

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية الخمس
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/goods_receipt_enhanced');
        $this->load->model('purchase/purchase_order');
        $this->load->model('inventory/warehouse');
        $this->load->model('setting/setting');

        // تحديد الصلاحيات المطلوبة
        $this->permissions = array(
            'access' => 'inventory/goods_receipt',
            'modify' => 'inventory/goods_receipt',
            'delete' => 'inventory/goods_receipt',
            'approve' => 'inventory/goods_receipt_approve'
        );

        // تحميل ملفات اللغة
        $this->load->language('inventory/goods_receipt_enhanced');
    }
}  
  /**
     * عرض قائمة استلام البضائع
     */
    public function index() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['access'])) {
                $this->central_service->logActivity(
                    'access_denied',
                    'goods_receipt',
                    'محاولة وصول غير مصرح به لشاشة استلام البضائع',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'goods_receipt',
                'عرض قائمة استلام البضائع',
                array('user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title'));
            $this->getList();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'goods_receipt',
                'خطأ في عرض قائمة استلام البضائع: ' . $e->getMessage(),
                array('user_id' => $this->user->getId())
            );
            
            $this->session->data['error'] = 'حدث خطأ في تحميل البيانات';
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * إضافة استلام بضائع جديد
     */
    public function add() {
        try {
            if (!$this->user->hasPermission('modify', $this->permissions['modify'])) {
                $this->central_service->logActivity(
                    'access_denied',
                    'goods_receipt',
                    'محاولة إضافة استلام بضائع بدون صلاحية',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                $receipt_id = $this->model_inventory_goods_receipt_enhanced->addGoodsReceipt($this->request->post);

                // إرسال إشعار
                $this->central_service->sendNotification(
                    'goods_receipt_created',
                    'تم إنشاء استلام بضائع جديد',
                    array(
                        'receipt_id' => $receipt_id,
                        'receipt_number' => $this->request->post['receipt_number']
                    ),
                    'inventory_goods_receipt'
                );

                $this->session->data['success'] = $this->language->get('text_success');
                $this->response->redirect($this->url->link('inventory/goods_receipt_enhanced', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->getForm();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'goods_receipt',
                'خطأ في إضافة استلام البضائع: ' . $e->getMessage(),
                array('user_id' => $this->user->getId())
            );
            
            $this->error['warning'] = 'حدث خطأ في حفظ البيانات';
            $this->getForm();
        }
    }

    /**
     * عرض قائمة استلام البضائع
     */
    protected function getList() {
        // معالجة الفلاتر
        $filter_data = $this->getFilters();

        // الحصول على البيانات
        $results = $this->model_inventory_goods_receipt_enhanced->getGoodsReceipts($filter_data);
        $total = $this->model_inventory_goods_receipt_enhanced->getTotalGoodsReceipts($filter_data);

        $data['receipts'] = array();
        foreach ($results as $result) {
            $data['receipts'][] = array(
                'receipt_id' => $result['receipt_id'],
                'receipt_number' => $result['receipt_number'],
                'po_number' => $result['po_number'],
                'supplier_name' => $result['supplier_name'],
                'receipt_date' => date($this->language->get('date_format_short'), strtotime($result['receipt_date'])),
                'status' => $result['status'],
                'status_text' => $this->language->get('text_status_' . $result['status']),
                'total_items' => $result['total_items'],
                'total_value' => number_format($result['total_value'], 2),
                'view' => $this->url->link('inventory/goods_receipt_enhanced/view', 'user_token=' . $this->session->data['user_token'] . '&receipt_id=' . $result['receipt_id'], true),
                'edit' => $this->url->link('inventory/goods_receipt_enhanced/edit', 'user_token=' . $this->session->data['user_token'] . '&receipt_id=' . $result['receipt_id'], true)
            );
        }

        // إعداد البيانات للعرض
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['add'] = $this->url->link('inventory/goods_receipt_enhanced/add', 'user_token=' . $this->session->data['user_token'], true);

        // عرض الصفحة
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/goods_receipt_enhanced_list', $data));
    }

    /**
     * معالجة الفلاتر
     */
    private function getFilters() {
        $filters = array(
            'filter_receipt_number' => '',
            'filter_po_number' => '',
            'filter_supplier_id' => '',
            'filter_status' => '',
            'filter_date_from' => '',
            'filter_date_to' => '',
            'sort' => 'gr.date_added',
            'order' => 'DESC',
            'page' => 1
        );

        foreach ($filters as $key => $default) {
            if (isset($this->request->get[$key])) {
                $filters[$key] = $this->request->get[$key];
            }
        }

        return $filters;
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('modify', $this->permissions['modify'])) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['receipt_number']) < 3) || (utf8_strlen($this->request->post['receipt_number']) > 64)) {
            $this->error['receipt_number'] = $this->language->get('error_receipt_number');
        }

        if (empty($this->request->post['receipt_date'])) {
            $this->error['receipt_date'] = $this->language->get('error_receipt_date');
        }

        if (empty($this->request->post['receipt_items']) || !is_array($this->request->post['receipt_items'])) {
            $this->error['items'] = $this->language->get('error_no_items');
        }

        return !$this->error;
    }

    /**
     * عرض نموذج الإضافة/التعديل
     */
    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['receipt_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        // معالجة الأخطاء
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        // الحصول على أوامر الشراء
        $data['purchase_orders'] = $this->model_purchase_purchase_order->getPurchaseOrders(array('status' => 'approved'));

        // عرض النموذج
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/goods_receipt_enhanced_form', $data));
    }
}