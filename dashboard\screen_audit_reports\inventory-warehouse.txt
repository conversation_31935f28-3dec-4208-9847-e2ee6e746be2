📄 Route: inventory/warehouse
📂 Controller: controller\inventory\warehouse.php
🧱 Models used (7):
   ✅ core/central_service_manager (60 functions)
   ✅ inventory/warehouse_enhanced (40 functions)
   ✅ setting/setting (5 functions)
   ✅ user/user_group (9 functions)
   ✅ inventory/warehouse (47 functions)
   ❌ accounting/chartaccount (0 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\inventory\warehouse.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\warehouse.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (70):
   - button_add
   - button_delete
   - column_address
   - column_code
   - column_name
   - date_format_short
   - error_movement_type_required
   - error_product_required
   - error_products_required
   - error_same_warehouse
   - error_warehouse_create
   - error_warehouse_id_required
   - error_warehouse_not_found
   - error_warehouse_required
   - text_confirm
   - text_list
   - text_location_office
   - text_movement_success
   - text_transfer_success
   - text_warehouse_dashboard
   ... و 50 متغير آخر

❌ Missing in Arabic (70):
   - button_add
   - column_name
   - column_product_count
   - error_code_exists
   - error_movement_failed
   - error_permission
   - error_product_required
   - error_to_warehouse_required
   - error_warehouse_id_required
   - error_warehouse_not_found
   - error_warehouse_required
   - text_list
   - text_location_office
   - text_location_store
   - text_transfer_success
   ... و 55 متغير آخر

❌ Missing in English (70):
   - button_add
   - column_name
   - column_product_count
   - error_code_exists
   - error_movement_failed
   - error_permission
   - error_product_required
   - error_to_warehouse_required
   - error_warehouse_id_required
   - error_warehouse_not_found
   - error_warehouse_required
   - text_list
   - text_location_office
   - text_location_store
   - text_transfer_success
   ... و 55 متغير آخر

🗄️ Database Tables Used (4):
   ❌ existing
   ❌ stock
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 70 items
      - error_warehouse_required
      - button_add
      - column_name
      - error_warehouse_id_required
      - error_warehouse_not_found
   🟡 MISSING_ENGLISH_VARIABLES: 70 items
      - error_warehouse_required
      - button_add
      - column_name
      - error_warehouse_id_required
      - error_warehouse_not_found
   🔴 INVALID_DATABASE_TABLES: 4 items
      - workflow
      - stock
      - template
      - existing
   🟢 MISSING_MODEL_FILES: 1 items
      - accounting/chartaccount

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 70 متغير عربي و 70 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 4 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:08
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.