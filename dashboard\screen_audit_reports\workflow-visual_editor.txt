📄 Route: workflow/visual_editor
📂 Controller: controller\workflow\visual_editor.php
🧱 Models used (4):
   ✅ workflow/workflow (30 functions)
   ❌ hr/department (0 functions)
   ✅ user/user (47 functions)
   ✅ user/user_group (9 functions)
🎨 Twig templates (1):
   ✅ view\template\workflow\visual_editor.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\workflow\visual_editor.php (190 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\workflow\visual_editor.php (158 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (61):
   - action
   - column_left
   - entry_department
   - error_permission
   - footer
   - tab_general
   - tab_visual_editor
   - text_active
   - text_add
   - text_click_to_configure
   - text_configure
   - text_configure_node
   - text_delay
   - text_inactive
   - text_nodes
   - text_other
   - text_task
   - text_trigger
   - text_workflow
   - user_token
   ... و 41 متغير آخر

❌ Missing in Arabic (51):
   - action
   - header
   - tab_general
   - text_approval
   - text_click_to_configure
   - text_condition
   - text_delay
   - text_inactive
   - text_leave_request
   - text_no
   - text_other
   - text_purchase_approval
   - text_webhook
   - text_workflow
   - workflow_json
   ... و 36 متغير آخر

❌ Missing in English (13):
   - action
   - cancel
   - column_left
   - description
   - error_warning
   - escalation_after_days
   - footer
   - header
   - name
   - text_form
   - text_home
   - user_token
   - workflow_json

🗄️ Database Tables Used (5):
   ❌ current
   ❌ instance
   ❌ node
   ❌ schedule
   ❌ the

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 51 items
      - text_no
      - text_condition
      - action
      - tab_general
      - text_leave_request
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - error_warning
      - cancel
      - user_token
      - column_left
      - text_form
   🔴 INVALID_DATABASE_TABLES: 5 items
      - instance
      - node
      - current
      - schedule
      - the
   🟢 MISSING_MODEL_FILES: 1 items
      - hr/department

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 51 متغير عربي و 13 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 5 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:20
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.