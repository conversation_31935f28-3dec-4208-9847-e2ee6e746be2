📄 Route: catalog/product
📂 Controller: controller\catalog\product.php
🧱 Models used (29):
   ✅ core/central_service_manager (60 functions)
   ✅ logging/user_activity (13 functions)
   ✅ communication/unified_notification (16 functions)
   ❌ communication/internal_communication (0 functions)
   ✅ unified_document (16 functions)
   ✅ workflow/visual_workflow_engine (18 functions)
   ✅ catalog/product (128 functions)
   ✅ catalog/category (15 functions)
   ✅ catalog/manufacturer (9 functions)
   ✅ catalog/filter (10 functions)
   ✅ catalog/option (12 functions)
   ✅ catalog/attribute (8 functions)
   ✅ inventory/warehouse (47 functions)
   ❌ inventory/inventory_manager (0 functions)
   ❌ catalog/pricing_manager (0 functions)
   ❌ catalog/unit_manager (0 functions)
   ✅ accounts/journal (13 functions)
   ❌ accounts/account (0 functions)
   ✅ setting/setting (5 functions)
   ✅ design/layout (8 functions)
   ✅ localisation/stock_status (7 functions)
   ✅ localisation/tax_class (8 functions)
   ✅ localisation/weight_class (8 functions)
   ✅ localisation/length_class (8 functions)
   ✅ tool/image (1 functions)
   ✅ localisation/language (7 functions)
   ❌ tool/export (0 functions)
   ❌ tool/import (0 functions)
   ✅ ai/ai_assistant (19 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\catalog\product.php (1014 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\catalog\product.php (1533 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (56):
   - error_add_failed
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_meta_title
   - error_missing_required_data
   - error_model_exists
   - error_movement_failed_for_product
   - error_permission
   - error_transfer_already_completed
   - error_transfer_not_found
   - text_abc_recommendation_b
   - text_abc_recommendation_c
   - text_disabled
   - text_field_updated
   - text_import_success
   - text_inventory_adjustment
   - text_low_stock_alert
   - text_product_added_notification
   - text_success_copy
   ... و 36 متغير آخر

❌ Missing in Arabic (10):
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_movement_failed_for_product
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - text_home
   - text_pagination

❌ Missing in English (10):
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_movement_failed_for_product
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - text_home
   - text_pagination

🗄️ Database Tables Used (20):
   ❌ Excel
   ❌ cod_ai_conversation
   ❌ cod_ai_model
   ✅ cod_unified_workflow
   ❌ cod_user_ai_preferences
   ❌ cod_workflow_activity_log
   ✅ cod_workflow_approval
   ❌ cod_workflow_connection
   ❌ cod_workflow_execution
   ❌ cod_workflow_node
   ❌ cod_workflow_node_execution
   ❌ entries
   ❌ existing
   ❌ product
   ❌ statements
   ❌ stock
   ❌ template
   ❌ the
   ❌ via
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 10 items
      - error_insufficient_stock_for_product
      - error_insufficient_stock_for_transfer_item
      - text_home
      - error_movement_failed_for_product
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 10 items
      - error_insufficient_stock_for_product
      - error_insufficient_stock_for_transfer_item
      - text_home
      - error_movement_failed_for_product
      - text_pagination
   🔴 INVALID_DATABASE_TABLES: 18 items
      - workflow
      - cod_ai_conversation
      - cod_workflow_execution
      - cod_workflow_connection
      - template
   🟢 MISSING_MODEL_FILES: 7 items
      - communication/internal_communication
      - inventory/inventory_manager
      - catalog/pricing_manager
      - catalog/unit_manager
      - accounts/account

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 10 متغير عربي و 10 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 18 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 7 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:48
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.