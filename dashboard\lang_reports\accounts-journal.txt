📄 Route: accounts/journal
📂 Controller: controller\accounts\journal.php
🧱 Models used (3):
   - accounts/chartaccount
   - accounts/journal
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\journal.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\journal.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_cancel
   - button_save
   - code
   - date_format_short
   - direction
   - entry_account_code
   - entry_amount
   - entry_attachment
   - entry_description
   - entry_is_debit
   - error_date_required
   - error_delete
   - error_description_required
   - error_entries_required
   - error_method
   - error_no_data
   - error_no_journals_selected
   - error_permission
   - error_save
   - error_unbalanced
   - heading_title
   - text_add
   - text_balanced
   - text_delete
   - text_edit
   - text_home
   - text_multiple_journal_entries
   - text_no
   - text_no_results
   - text_pdf_generated_successfully
   - text_success
   - text_updated
   - text_view
   - text_yes

❌ Missing in Arabic:
   - button_cancel
   - button_save
   - code
   - date_format_short
   - direction
   - entry_account_code
   - entry_amount
   - entry_attachment
   - entry_description
   - entry_is_debit
   - error_date_required
   - error_delete
   - error_description_required
   - error_entries_required
   - error_method
   - error_no_data
   - error_no_journals_selected
   - error_permission
   - error_save
   - error_unbalanced
   - heading_title
   - text_add
   - text_balanced
   - text_delete
   - text_edit
   - text_home
   - text_multiple_journal_entries
   - text_no
   - text_no_results
   - text_pdf_generated_successfully
   - text_success
   - text_updated
   - text_view
   - text_yes

❌ Missing in English:
   - button_cancel
   - button_save
   - code
   - date_format_short
   - direction
   - entry_account_code
   - entry_amount
   - entry_attachment
   - entry_description
   - entry_is_debit
   - error_date_required
   - error_delete
   - error_description_required
   - error_entries_required
   - error_method
   - error_no_data
   - error_no_journals_selected
   - error_permission
   - error_save
   - error_unbalanced
   - heading_title
   - text_add
   - text_balanced
   - text_delete
   - text_edit
   - text_home
   - text_multiple_journal_entries
   - text_no
   - text_no_results
   - text_pdf_generated_successfully
   - text_success
   - text_updated
   - text_view
   - text_yes

💡 Suggested Arabic Additions:
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_account_code = ""  # TODO: ترجمة عربية
   - entry_amount = ""  # TODO: ترجمة عربية
   - entry_attachment = ""  # TODO: ترجمة عربية
   - entry_description = ""  # TODO: ترجمة عربية
   - entry_is_debit = ""  # TODO: ترجمة عربية
   - error_date_required = ""  # TODO: ترجمة عربية
   - error_delete = ""  # TODO: ترجمة عربية
   - error_description_required = ""  # TODO: ترجمة عربية
   - error_entries_required = ""  # TODO: ترجمة عربية
   - error_method = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_no_journals_selected = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_save = ""  # TODO: ترجمة عربية
   - error_unbalanced = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_balanced = ""  # TODO: ترجمة عربية
   - text_delete = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_multiple_journal_entries = ""  # TODO: ترجمة عربية
   - text_no = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_pdf_generated_successfully = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_updated = ""  # TODO: ترجمة عربية
   - text_view = ""  # TODO: ترجمة عربية
   - text_yes = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_cancel = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_account_code = ""  # TODO: English translation
   - entry_amount = ""  # TODO: English translation
   - entry_attachment = ""  # TODO: English translation
   - entry_description = ""  # TODO: English translation
   - entry_is_debit = ""  # TODO: English translation
   - error_date_required = ""  # TODO: English translation
   - error_delete = ""  # TODO: English translation
   - error_description_required = ""  # TODO: English translation
   - error_entries_required = ""  # TODO: English translation
   - error_method = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_no_journals_selected = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_save = ""  # TODO: English translation
   - error_unbalanced = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_balanced = ""  # TODO: English translation
   - text_delete = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_multiple_journal_entries = ""  # TODO: English translation
   - text_no = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_pdf_generated_successfully = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_updated = ""  # TODO: English translation
   - text_view = ""  # TODO: English translation
   - text_yes = ""  # TODO: English translation
