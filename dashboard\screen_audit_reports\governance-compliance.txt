📄 Route: governance/compliance
📂 Controller: controller\governance\compliance.php
🧱 Models used (1):
   ✅ governance/compliance (5 functions)
🎨 Twig templates (1):
   ✅ view\template\governance\compliance.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\governance\compliance.php (31 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\governance\compliance.php (31 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (31):
   - action
   - can_add
   - can_delete
   - can_edit
   - column_left
   - error_can_add
   - error_can_delete
   - error_can_edit
   - error_heading_title
   - error_user_token
   - header
   - text_can_add
   - text_can_delete
   - text_can_edit
   - text_filter_status
   - text_heading_title
   - text_home
   - text_list
   - text_text_list
   - user_token
   ... و 11 متغير آخر

❌ Missing in Arabic (27):
   - action
   - can_delete
   - can_edit
   - column_left
   - error_can_add
   - error_can_delete
   - error_can_edit
   - error_heading_title
   - error_user_token
   - header
   - text_can_add
   - text_can_delete
   - text_filter_status
   - text_heading_title
   - text_text_list
   ... و 12 متغير آخر

❌ Missing in English (27):
   - action
   - can_delete
   - can_edit
   - column_left
   - error_can_add
   - error_can_delete
   - error_can_edit
   - error_heading_title
   - error_user_token
   - header
   - text_can_add
   - text_can_delete
   - text_filter_status
   - text_heading_title
   - text_text_list
   ... و 12 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 27 items
      - action
      - error_can_edit
      - text_text_list
      - header
      - text_can_delete
   🟡 MISSING_ENGLISH_VARIABLES: 27 items
      - action
      - error_can_edit
      - text_text_list
      - header
      - text_can_delete

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 27 متغير عربي و 27 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:03
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.