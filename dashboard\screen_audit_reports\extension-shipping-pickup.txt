📄 Route: extension/shipping/pickup
📂 Controller: controller\extension\shipping\pickup.php
🧱 Models used (2):
   ✅ setting/setting (5 functions)
   ✅ localisation/geo_zone (11 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\shipping\pickup.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\shipping\pickup.php (8 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\shipping\pickup.php (8 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (21):
   - action
   - button_cancel
   - button_save
   - column_left
   - entry_geo_zone
   - entry_sort_order
   - entry_status
   - error_permission
   - error_warning
   - footer
   - header
   - heading_title
   - shipping_pickup_sort_order
   - text_all_zones
   - text_disabled
   - text_edit
   - text_enabled
   - text_extension
   - text_home
   - text_success
   ... و 1 متغير آخر

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_pickup_sort_order
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_pickup_sort_order
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_save
      - error_warning
      - column_left
      - text_home
      - action
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - error_warning
      - column_left
      - text_home
      - action

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:30
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.