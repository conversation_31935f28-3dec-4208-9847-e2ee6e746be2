📄 Route: accounts/bank_accounts_advanced
📂 Controller: controller\accounts\bank_accounts_advanced.php
🧱 Models used (3):
   - accounts/audit_trail
   - accounts/bank_accounts_advanced
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\bank_accounts_advanced.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\bank_accounts_advanced.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_account_name
   - error_account_number
   - error_bank_name
   - error_permission
   - heading_title
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - error_account_name
   - error_account_number
   - error_bank_name
   - error_permission
   - heading_title
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - date_format_short
   - error_account_name
   - error_account_number
   - error_bank_name
   - error_permission
   - heading_title
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_account_name = ""  # TODO: ترجمة عربية
   - error_account_number = ""  # TODO: ترجمة عربية
   - error_bank_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_account_name = ""  # TODO: English translation
   - error_account_number = ""  # TODO: English translation
   - error_bank_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
