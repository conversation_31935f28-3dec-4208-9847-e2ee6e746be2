📄 Route: communication/messages
📂 Controller: controller\communication\messages.php
🧱 Models used (4):
   - communication/messages
   - communication/teams
   - core/central_service_manager
   - user/user
🎨 Twig templates (1):
   - view\template\communication\messages.twig
🈯 Arabic Language Files (1):
   - language\ar\communication\messages.php
🇬🇧 English Language Files (1):
   - language\en-gb\communication\messages.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_message
   - error_permission
   - error_recipients
   - error_subject
   - heading_title
   - text_compose
   - text_delete_success
   - text_home
   - text_new_message_subject
   - text_priority_critical
   - text_priority_high
   - text_priority_low
   - text_priority_medium
   - text_success
   - text_type_catalog
   - text_type_finance
   - text_type_general
   - text_type_inventory
   - text_type_purchase
   - text_type_sales
   - text_you_have_new_message_from

❌ Missing in Arabic:
   - error_message
   - error_permission
   - error_recipients
   - error_subject
   - heading_title
   - text_compose
   - text_delete_success
   - text_home
   - text_new_message_subject
   - text_priority_critical
   - text_priority_high
   - text_priority_low
   - text_priority_medium
   - text_success
   - text_type_catalog
   - text_type_finance
   - text_type_general
   - text_type_inventory
   - text_type_purchase
   - text_type_sales
   - text_you_have_new_message_from

❌ Missing in English:
   - error_message
   - error_permission
   - error_recipients
   - error_subject
   - heading_title
   - text_compose
   - text_delete_success
   - text_home
   - text_new_message_subject
   - text_priority_critical
   - text_priority_high
   - text_priority_low
   - text_priority_medium
   - text_success
   - text_type_catalog
   - text_type_finance
   - text_type_general
   - text_type_inventory
   - text_type_purchase
   - text_type_sales
   - text_you_have_new_message_from

💡 Suggested Arabic Additions:
   - error_message = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_recipients = ""  # TODO: ترجمة عربية
   - error_subject = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_compose = ""  # TODO: ترجمة عربية
   - text_delete_success = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_new_message_subject = ""  # TODO: ترجمة عربية
   - text_priority_critical = ""  # TODO: ترجمة عربية
   - text_priority_high = ""  # TODO: ترجمة عربية
   - text_priority_low = ""  # TODO: ترجمة عربية
   - text_priority_medium = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_type_catalog = ""  # TODO: ترجمة عربية
   - text_type_finance = ""  # TODO: ترجمة عربية
   - text_type_general = ""  # TODO: ترجمة عربية
   - text_type_inventory = ""  # TODO: ترجمة عربية
   - text_type_purchase = ""  # TODO: ترجمة عربية
   - text_type_sales = ""  # TODO: ترجمة عربية
   - text_you_have_new_message_from = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_message = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_recipients = ""  # TODO: English translation
   - error_subject = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_compose = ""  # TODO: English translation
   - text_delete_success = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_new_message_subject = ""  # TODO: English translation
   - text_priority_critical = ""  # TODO: English translation
   - text_priority_high = ""  # TODO: English translation
   - text_priority_low = ""  # TODO: English translation
   - text_priority_medium = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_type_catalog = ""  # TODO: English translation
   - text_type_finance = ""  # TODO: English translation
   - text_type_general = ""  # TODO: English translation
   - text_type_inventory = ""  # TODO: English translation
   - text_type_purchase = ""  # TODO: English translation
   - text_type_sales = ""  # TODO: English translation
   - text_you_have_new_message_from = ""  # TODO: English translation
