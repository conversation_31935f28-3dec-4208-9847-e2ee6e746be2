📄 Route: accounts/aging_report_advanced
📂 Controller: controller\accounts\aging_report_advanced.php
🧱 Models used (2):
   - accounts/aging_report_advanced
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\aging_report_advanced.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\aging_report_advanced.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_date_end
   - error_no_data
   - error_permission
   - error_report_type
   - heading_title
   - text_both
   - text_custom_periods
   - text_home
   - text_payables
   - text_receivables
   - text_standard_periods
   - text_success_generate
   - text_view

❌ Missing in Arabic:
   - error_date_end
   - error_no_data
   - error_permission
   - error_report_type
   - heading_title
   - text_both
   - text_custom_periods
   - text_home
   - text_payables
   - text_receivables
   - text_standard_periods
   - text_success_generate
   - text_view

❌ Missing in English:
   - error_date_end
   - error_no_data
   - error_permission
   - error_report_type
   - heading_title
   - text_both
   - text_custom_periods
   - text_home
   - text_payables
   - text_receivables
   - text_standard_periods
   - text_success_generate
   - text_view

💡 Suggested Arabic Additions:
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_report_type = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_both = ""  # TODO: ترجمة عربية
   - text_custom_periods = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_payables = ""  # TODO: ترجمة عربية
   - text_receivables = ""  # TODO: ترجمة عربية
   - text_standard_periods = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_view = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_date_end = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_report_type = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_both = ""  # TODO: English translation
   - text_custom_periods = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_payables = ""  # TODO: English translation
   - text_receivables = ""  # TODO: English translation
   - text_standard_periods = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_view = ""  # TODO: English translation
