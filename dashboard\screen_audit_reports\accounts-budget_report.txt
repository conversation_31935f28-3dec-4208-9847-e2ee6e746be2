📄 Route: accounts/budget_report
📂 Controller: controller\accounts\budget_report.php
🧱 Models used (5):
   ✅ core/central_service_manager (60 functions)
   ❌ accounts/budget_report (0 functions)
   ✅ accounts/budget_management_advanced (40 functions)
   ❌ department/department (0 functions)
   ✅ branch/branch (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\budget_report.php (146 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\budget_report.php (146 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - text_account
   - text_actual
   - text_budget
   - text_home
   - text_no_results
   - text_success_generate
   - text_variance
   - text_variance_analysis
   - text_variance_percentage

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (1):
   - text_home

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 1 items
      - text_home
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template
   🟢 MISSING_MODEL_FILES: 2 items
      - accounts/budget_report
      - department/department

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 1 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:34
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.