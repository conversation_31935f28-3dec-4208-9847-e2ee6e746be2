📄 Route: design/theme
📂 Controller: controller\design\theme.php
🧱 Models used (3):
   ✅ setting/store (14 functions)
   ✅ design/theme (5 functions)
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\design\theme.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\design\theme.php (15 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\design\theme.php (15 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (22):
   - button_back
   - button_reset
   - button_save
   - column_left
   - date_format_short
   - error_permission
   - error_twig
   - footer
   - header
   - heading_title
   - text_confirm
   - text_default
   - text_history
   - text_home
   - text_loading
   - text_pagination
   - text_store
   - text_success
   - text_template
   - user_token
   ... و 2 متغير آخر

❌ Missing in Arabic (12):
   - button_back
   - button_reset
   - button_save
   - column_left
   - date_format_short
   - footer
   - header
   - text_confirm
   - text_home
   - text_loading
   - text_pagination
   - user_token

❌ Missing in English (12):
   - button_back
   - button_reset
   - button_save
   - column_left
   - date_format_short
   - footer
   - header
   - text_confirm
   - text_home
   - text_loading
   - text_pagination
   - user_token

🗄️ Database Tables Used (1):
   ❌ the

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 12 items
      - button_save
      - button_reset
      - user_token
      - column_left
      - text_loading
   🟡 MISSING_ENGLISH_VARIABLES: 12 items
      - button_save
      - button_reset
      - user_token
      - column_left
      - text_loading
   🔴 INVALID_DATABASE_TABLES: 1 items
      - the

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 12 متغير عربي و 12 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:00
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.