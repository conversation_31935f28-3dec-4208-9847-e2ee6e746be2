📄 Route: extension/eta/invoice
📂 Controller: controller\extension\eta\invoice.php
🧱 Models used (3):
   - extension/eta/invoice
   - sale/order
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\eta\invoice.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\eta\invoice.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_client_id
   - error_client_secret
   - error_order_id_required
   - error_permission
   - error_prepare_invoice_data
   - error_prepare_modification_note
   - error_prepare_note_data
   - error_prepare_receipt_data
   - error_required_fields
   - error_uuid_required
   - heading_title
   - heading_title_settings
   - text_connection_success
   - text_home
   - text_invoice_sent_success
   - text_list
   - text_logs
   - text_modification_note_sent_success
   - text_note_sent_success
   - text_preprod
   - text_production
   - text_queue_cleared
   - text_queue_processed
   - text_receipt_sent_success
   - text_success_settings

❌ Missing in Arabic:
   - error_client_id
   - error_client_secret
   - error_order_id_required
   - error_permission
   - error_prepare_invoice_data
   - error_prepare_modification_note
   - error_prepare_note_data
   - error_prepare_receipt_data
   - error_required_fields
   - error_uuid_required
   - heading_title
   - heading_title_settings
   - text_connection_success
   - text_home
   - text_invoice_sent_success
   - text_list
   - text_logs
   - text_modification_note_sent_success
   - text_note_sent_success
   - text_preprod
   - text_production
   - text_queue_cleared
   - text_queue_processed
   - text_receipt_sent_success
   - text_success_settings

❌ Missing in English:
   - error_client_id
   - error_client_secret
   - error_order_id_required
   - error_permission
   - error_prepare_invoice_data
   - error_prepare_modification_note
   - error_prepare_note_data
   - error_prepare_receipt_data
   - error_required_fields
   - error_uuid_required
   - heading_title
   - heading_title_settings
   - text_connection_success
   - text_home
   - text_invoice_sent_success
   - text_list
   - text_logs
   - text_modification_note_sent_success
   - text_note_sent_success
   - text_preprod
   - text_production
   - text_queue_cleared
   - text_queue_processed
   - text_receipt_sent_success
   - text_success_settings

💡 Suggested Arabic Additions:
   - error_client_id = ""  # TODO: ترجمة عربية
   - error_client_secret = ""  # TODO: ترجمة عربية
   - error_order_id_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_prepare_invoice_data = ""  # TODO: ترجمة عربية
   - error_prepare_modification_note = ""  # TODO: ترجمة عربية
   - error_prepare_note_data = ""  # TODO: ترجمة عربية
   - error_prepare_receipt_data = ""  # TODO: ترجمة عربية
   - error_required_fields = ""  # TODO: ترجمة عربية
   - error_uuid_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_title_settings = ""  # TODO: ترجمة عربية
   - text_connection_success = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_invoice_sent_success = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية
   - text_logs = ""  # TODO: ترجمة عربية
   - text_modification_note_sent_success = ""  # TODO: ترجمة عربية
   - text_note_sent_success = ""  # TODO: ترجمة عربية
   - text_preprod = ""  # TODO: ترجمة عربية
   - text_production = ""  # TODO: ترجمة عربية
   - text_queue_cleared = ""  # TODO: ترجمة عربية
   - text_queue_processed = ""  # TODO: ترجمة عربية
   - text_receipt_sent_success = ""  # TODO: ترجمة عربية
   - text_success_settings = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_client_id = ""  # TODO: English translation
   - error_client_secret = ""  # TODO: English translation
   - error_order_id_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_prepare_invoice_data = ""  # TODO: English translation
   - error_prepare_modification_note = ""  # TODO: English translation
   - error_prepare_note_data = ""  # TODO: English translation
   - error_prepare_receipt_data = ""  # TODO: English translation
   - error_required_fields = ""  # TODO: English translation
   - error_uuid_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_title_settings = ""  # TODO: English translation
   - text_connection_success = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_invoice_sent_success = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
   - text_logs = ""  # TODO: English translation
   - text_modification_note_sent_success = ""  # TODO: English translation
   - text_note_sent_success = ""  # TODO: English translation
   - text_preprod = ""  # TODO: English translation
   - text_production = ""  # TODO: English translation
   - text_queue_cleared = ""  # TODO: English translation
   - text_queue_processed = ""  # TODO: English translation
   - text_receipt_sent_success = ""  # TODO: English translation
   - text_success_settings = ""  # TODO: English translation
