📄 Route: inventory/barcode_management
📂 Controller: controller\inventory\barcode_management.php
🧱 Models used (7):
   - catalog/product
   - core/central_service_manager
   - inventory/barcode_management
   - inventory/barcode_management_enhanced
   - inventory/unit
   - setting/setting
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (2):
   - language\ar\common\header.php
   - language\ar\inventory\barcode_management.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_barcode_type
   - column_barcode_value
   - column_date_added
   - column_is_active
   - column_is_primary
   - column_option
   - column_print_count
   - column_product_name
   - column_scan_count
   - column_unit
   - datetime_format
   - error_advanced_permission
   - error_barcode_exists
   - error_barcode_in_use
   - error_barcode_invalid
   - error_barcode_not_found
   - error_barcode_type
   - error_barcode_type_required
   - error_barcode_value
   - error_barcode_value_required
   - error_exception
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_product
   - error_product_required
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_add
   - text_all
   - text_barcode_valid
   - text_base_unit
   - text_bulk_generated
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_never
   - text_no
   - text_pagination
   - text_success
   - text_yes

❌ Missing in Arabic:
   - column_barcode_type
   - column_barcode_value
   - column_date_added
   - column_is_active
   - column_is_primary
   - column_option
   - column_print_count
   - column_product_name
   - column_scan_count
   - column_unit
   - datetime_format
   - error_advanced_permission
   - error_barcode_exists
   - error_barcode_in_use
   - error_barcode_invalid
   - error_barcode_not_found
   - error_barcode_type
   - error_barcode_type_required
   - error_barcode_value
   - error_barcode_value_required
   - error_exception
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_product
   - error_product_required
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_add
   - text_all
   - text_barcode_valid
   - text_base_unit
   - text_bulk_generated
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_never
   - text_no
   - text_pagination
   - text_success
   - text_yes

❌ Missing in English:
   - column_barcode_type
   - column_barcode_value
   - column_date_added
   - column_is_active
   - column_is_primary
   - column_option
   - column_print_count
   - column_product_name
   - column_scan_count
   - column_unit
   - datetime_format
   - error_advanced_permission
   - error_barcode_exists
   - error_barcode_in_use
   - error_barcode_invalid
   - error_barcode_not_found
   - error_barcode_type
   - error_barcode_type_required
   - error_barcode_value
   - error_barcode_value_required
   - error_exception
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_product
   - error_product_required
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_add
   - text_all
   - text_barcode_valid
   - text_base_unit
   - text_bulk_generated
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_never
   - text_no
   - text_pagination
   - text_success
   - text_yes

💡 Suggested Arabic Additions:
   - column_barcode_type = ""  # TODO: ترجمة عربية
   - column_barcode_value = ""  # TODO: ترجمة عربية
   - column_date_added = ""  # TODO: ترجمة عربية
   - column_is_active = ""  # TODO: ترجمة عربية
   - column_is_primary = ""  # TODO: ترجمة عربية
   - column_option = ""  # TODO: ترجمة عربية
   - column_print_count = ""  # TODO: ترجمة عربية
   - column_product_name = ""  # TODO: ترجمة عربية
   - column_scan_count = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_barcode_exists = ""  # TODO: ترجمة عربية
   - error_barcode_in_use = ""  # TODO: ترجمة عربية
   - error_barcode_invalid = ""  # TODO: ترجمة عربية
   - error_barcode_not_found = ""  # TODO: ترجمة عربية
   - error_barcode_type = ""  # TODO: ترجمة عربية
   - error_barcode_type_required = ""  # TODO: ترجمة عربية
   - error_barcode_value = ""  # TODO: ترجمة عربية
   - error_barcode_value_required = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_product = ""  # TODO: ترجمة عربية
   - error_product_required = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_barcode_valid = ""  # TODO: ترجمة عربية
   - text_base_unit = ""  # TODO: ترجمة عربية
   - text_bulk_generated = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_never = ""  # TODO: ترجمة عربية
   - text_no = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_yes = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_barcode_type = ""  # TODO: English translation
   - column_barcode_value = ""  # TODO: English translation
   - column_date_added = ""  # TODO: English translation
   - column_is_active = ""  # TODO: English translation
   - column_is_primary = ""  # TODO: English translation
   - column_option = ""  # TODO: English translation
   - column_print_count = ""  # TODO: English translation
   - column_product_name = ""  # TODO: English translation
   - column_scan_count = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_barcode_exists = ""  # TODO: English translation
   - error_barcode_in_use = ""  # TODO: English translation
   - error_barcode_invalid = ""  # TODO: English translation
   - error_barcode_not_found = ""  # TODO: English translation
   - error_barcode_type = ""  # TODO: English translation
   - error_barcode_type_required = ""  # TODO: English translation
   - error_barcode_value = ""  # TODO: English translation
   - error_barcode_value_required = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_product = ""  # TODO: English translation
   - error_product_required = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_barcode_valid = ""  # TODO: English translation
   - text_base_unit = ""  # TODO: English translation
   - text_bulk_generated = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_never = ""  # TODO: English translation
   - text_no = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_yes = ""  # TODO: English translation
