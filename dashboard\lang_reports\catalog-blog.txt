📄 Route: catalog/blog
📂 Controller: controller\catalog\blog.php
🧱 Models used (5):
   - catalog/blog
   - catalog/blog_category
   - catalog/blog_tag
   - tool/image
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\catalog\blog.php
🇬🇧 English Language Files (1):
   - language\en-gb\catalog\blog.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_content
   - error_filename
   - error_filesize
   - error_filetype
   - error_form
   - error_permission
   - error_slug_exists
   - error_title
   - error_upload
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success_add
   - text_success_copy
   - text_success_delete
   - text_success_edit
   - text_unknown
   - text_uploaded

❌ Missing in Arabic:
   - date_format_short
   - error_content
   - error_filename
   - error_filesize
   - error_filetype
   - error_form
   - error_permission
   - error_slug_exists
   - error_title
   - error_upload
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success_add
   - text_success_copy
   - text_success_delete
   - text_success_edit
   - text_unknown
   - text_uploaded

❌ Missing in English:
   - date_format_short
   - error_content
   - error_filename
   - error_filesize
   - error_filetype
   - error_form
   - error_permission
   - error_slug_exists
   - error_title
   - error_upload
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success_add
   - text_success_copy
   - text_success_delete
   - text_success_edit
   - text_unknown
   - text_uploaded

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_content = ""  # TODO: ترجمة عربية
   - error_filename = ""  # TODO: ترجمة عربية
   - error_filesize = ""  # TODO: ترجمة عربية
   - error_filetype = ""  # TODO: ترجمة عربية
   - error_form = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_slug_exists = ""  # TODO: ترجمة عربية
   - error_title = ""  # TODO: ترجمة عربية
   - error_upload = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_copy = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية
   - text_unknown = ""  # TODO: ترجمة عربية
   - text_uploaded = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_content = ""  # TODO: English translation
   - error_filename = ""  # TODO: English translation
   - error_filesize = ""  # TODO: English translation
   - error_filetype = ""  # TODO: English translation
   - error_form = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_slug_exists = ""  # TODO: English translation
   - error_title = ""  # TODO: English translation
   - error_upload = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_copy = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
   - text_unknown = ""  # TODO: English translation
   - text_uploaded = ""  # TODO: English translation
