📄 Route: catalog/unit
📂 Controller: controller\catalog\unit.php
🧱 Models used (1):
   - catalog/unit
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\catalog\unit.php
🇬🇧 English Language Files (1):
   - language\en-gb\catalog\unit.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_code
   - error_code_exists
   - error_desc_en
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_home
   - text_pagination
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in Arabic:
   - error_code
   - error_code_exists
   - error_desc_en
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_home
   - text_pagination
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in English:
   - error_code
   - error_code_exists
   - error_desc_en
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_home
   - text_pagination
   - text_success_add
   - text_success_delete
   - text_success_edit

💡 Suggested Arabic Additions:
   - error_code = ""  # TODO: ترجمة عربية
   - error_code_exists = ""  # TODO: ترجمة عربية
   - error_desc_en = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_unit_in_use = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_code = ""  # TODO: English translation
   - error_code_exists = ""  # TODO: English translation
   - error_desc_en = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_unit_in_use = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
