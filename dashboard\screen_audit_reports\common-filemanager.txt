📄 Route: common/filemanager
📂 Controller: controller\common\filemanager.php
🧱 Models used (1):
   ✅ tool/image (1 functions)
🎨 Twig templates (1):
   ✅ view\template\common\filemanager.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\common\filemanager.php (15 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\common\filemanager.php (15 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (28):
   - button_delete
   - button_folder
   - button_refresh
   - button_upload
   - directory
   - entry_folder
   - entry_search
   - error_directory
   - error_exists
   - error_filename
   - error_filesize
   - error_folder
   - error_permission
   - filter_name
   - pagination
   - refresh
   - text_confirm
   - text_delete
   - text_directory
   - user_token
   ... و 8 متغير آخر

❌ Missing in Arabic (13):
   - button_delete
   - button_folder
   - button_parent
   - button_refresh
   - button_search
   - button_upload
   - directory
   - filter_name
   - pagination
   - parent
   - refresh
   - text_confirm
   - user_token

❌ Missing in English (13):
   - button_delete
   - button_folder
   - button_parent
   - button_refresh
   - button_search
   - button_upload
   - directory
   - filter_name
   - pagination
   - parent
   - refresh
   - text_confirm
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_upload
      - button_delete
      - user_token
      - directory
      - pagination
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_upload
      - button_delete
      - user_token
      - directory
      - pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:52
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.