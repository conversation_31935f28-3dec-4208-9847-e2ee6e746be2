📄 Route: extension/fraud/fraudlabspro
📂 Controller: controller\extension\fraud\fraudlabspro.php
🧱 Models used (3):
   - extension/fraud/fraudlabspro
   - localisation/order_status
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\fraud\fraudlabspro.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\fraud\fraudlabspro.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\fraud\fraudlabspro.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_key
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_key
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_key
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_key = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_key = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
