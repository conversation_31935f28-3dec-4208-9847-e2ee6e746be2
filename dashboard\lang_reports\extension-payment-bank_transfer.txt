📄 Route: extension/payment/bank_transfer
📂 Controller: controller\extension\payment\bank_transfer.php
🧱 Models used (4):
   - localisation/geo_zone
   - localisation/language
   - localisation/order_status
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\payment\bank_transfer.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\payment\bank_transfer.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\payment\bank_transfer.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_bank
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_bank
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_bank
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_bank = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_bank = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
