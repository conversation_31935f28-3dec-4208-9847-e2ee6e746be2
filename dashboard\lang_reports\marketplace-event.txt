📄 Route: marketplace/event
📂 Controller: controller\marketplace\event.php
🧱 Models used (1):
   - setting/event
🎨 Twig templates (1):
   - view\template\marketplace\event.twig
🈯 Arabic Language Files (1):
   - language\ar\marketplace\event.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketplace\event.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
