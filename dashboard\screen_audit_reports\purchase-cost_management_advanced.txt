📄 Route: purchase/cost_management_advanced
📂 Controller: controller\purchase\cost_management_advanced.php
🧱 Models used (5):
   ❌ purchase/cost_management_advanced (0 functions)
   ✅ accounts/audit_trail (13 functions)
   ✅ catalog/product (128 functions)
   ✅ catalog/category (15 functions)
   ✅ supplier/supplier (21 functions)
🎨 Twig templates (1):
   ✅ view\template\purchase\cost_management_advanced.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\purchase\cost_management_advanced.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\purchase\cost_management_advanced.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (63):
   - action
   - allocate_costs_url
   - calculate_wac_url
   - can_allocate_costs
   - can_calculate_wac
   - cancel
   - column_left
   - error_categories
   - error_optimization_url
   - error_transfer_already_completed
   - error_transfer_not_found
   - error_variance_analysis_url
   - export_url
   - products
   - text_allocate_costs_url
   - text_can_calculate_wac
   - text_cost_trends_url
   - text_heading_title
   - text_variance_analysis_url
   - user_token
   ... و 43 متغير آخر

❌ Missing in Arabic (63):
   - action
   - calculate_wac_url
   - can_calculate_wac
   - error_calculate_wac_url
   - error_can_allocate_costs
   - error_invalid_item
   - error_movement_failed_for_product
   - error_optimization_url
   - error_transfer_already_completed
   - error_transfer_not_found
   - header
   - text_calculate_wac_url
   - text_cost_trends_url
   - text_optimization_url
   - text_variance_analysis_url
   ... و 48 متغير آخر

❌ Missing in English (63):
   - action
   - calculate_wac_url
   - can_calculate_wac
   - error_calculate_wac_url
   - error_can_allocate_costs
   - error_invalid_item
   - error_movement_failed_for_product
   - error_optimization_url
   - error_transfer_already_completed
   - error_transfer_not_found
   - header
   - text_calculate_wac_url
   - text_cost_trends_url
   - text_optimization_url
   - text_variance_analysis_url
   ... و 48 متغير آخر

🗄️ Database Tables Used (3):
   ❌ journal_entry
   ❌ statements
   ❌ stock

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 63 items
      - action
      - can_calculate_wac
      - error_movement_failed_for_product
      - error_optimization_url
      - error_calculate_wac_url
   🟡 MISSING_ENGLISH_VARIABLES: 63 items
      - action
      - can_calculate_wac
      - error_movement_failed_for_product
      - error_optimization_url
      - error_calculate_wac_url
   🔴 INVALID_DATABASE_TABLES: 3 items
      - stock
      - journal_entry
      - statements
   🟢 MISSING_MODEL_FILES: 1 items
      - purchase/cost_management_advanced

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 63 متغير عربي و 63 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 3 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:14
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.