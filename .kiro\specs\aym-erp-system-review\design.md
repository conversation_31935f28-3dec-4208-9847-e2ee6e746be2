# Design Document - AYM ERP System Review and Enhancement

## Overview

The AYM ERP system is a comprehensive enterprise solution built on OpenCart 3.0.3.x with extensive customizations. Based on accurate analysis of tree.txt, the system contains **200+ controller files** across multiple modules, with **35% of advanced modules hidden** from the main navigation. The true complexity far exceeds initial estimates.

## Architecture

### System Foundation
- **Base Platform:** OpenCart 3.0.3.x with MVC architecture
- **Database Prefix:** `cod_` instead of standard `oc_`
- **Admin Panel:** `dashboard/` directory instead of `admin/`
- **Technology Stack:** PHP 7.4+, MySQL 5.7+, Bootstrap 3.3.7, jQuery 3.7.0
- **Hidden Complexity:** 35% of modules not displayed in sidebar navigation

### Actual Module Structure (Based on tree.txt Analysis)

#### 1. Accounts Module (`dashboard/controller/accounts/`) - **36 Controllers**
- **Displayed in Navigation:** ~15 routes
- **Actual Files:** 36+ controller files including advanced analytics
- **Hidden Advanced Features:**
  - `account_query.php` - Advanced account querying
  - `aging_report_advanced.php` - Advanced aging analysis
  - `bank_accounts_advanced.php` - Advanced banking features
  - `budget_management_advanced.php` - Advanced budgeting
  - `financial_reports_advanced.php` - Advanced financial reporting
  - `fixed_assets_advanced.php` - Advanced asset management
  - `journal_security_advanced.php` - Advanced security features

#### 2. Purchase Module (`dashboard/controller/purchase/`) - **23 Controllers**
- **Displayed in Navigation:** 15 routes (35% hidden)
- **Hidden Advanced Files:**
  - `accounting_integration_advanced.php` - Advanced accounting integration
  - `approval_settings.php` - Approval workflow settings
  - `cost_management_advanced.php` - Advanced cost management
  - `smart_approval_system.php` - AI-powered approval system
  - `supplier_analytics_advanced.php` - Advanced supplier analytics
  - `quality_check.php` - Quality control system
  - `notification_settings.php` - Advanced notifications

#### 3. Inventory Module (`dashboard/controller/inventory/`) - **32+ Controllers**
- **Advanced Hidden Features:**
  - `inventory_management_advanced.php` - Advanced inventory features
  - `interactive_dashboard.php` - Interactive analytics dashboard
  - `goods_receipt_enhanced.php` - Enhanced goods receipt
  - `location_management.php` - Advanced location management
  - `stock_alerts.php` - Intelligent stock alerting
  - `abc_analysis.php` - ABC inventory analysis

#### 4. Workflow Module (`dashboard/controller/workflow/`) - **8 Controllers**
- **Visual Workflow Engine:**
  - `advanced_visual_editor.php` - Advanced visual workflow editor
  - `visual_editor.php` - Basic visual editor
  - `designer.php` - Workflow designer
  - `actions.php`, `conditions.php`, `triggers.php` - Workflow components

## Components and Interfaces

### Central Services Architecture
The system implements 5 core central services managed through `central_service_manager.php`:

1. **Logging & Audit Service**
   - Activity logging across all modules
   - Audit trail for compliance
   - Performance monitoring

2. **Notification Service**
   - Real-time notifications
   - Email/SMS templates
   - Alert automation

3. **Communication Service**
   - Internal messaging
   - Announcements
   - Team collaboration

4. **Document Management Service**
   - File attachments (7 specialized tables)
   - Version control
   - Permission-based access

5. **Visual Workflow Engine**
   - Process automation
   - Approval workflows
   - Business rule management

### Permission System
Dual-layer permission system:
- `hasPermission()` - Basic access control
- `hasKey()` - Advanced feature access
- Group 1 has automatic full access (company management)

## Data Models

### Core Business Entities
- **Products:** Multi-unit support, virtual inventory, bundle capabilities
- **Customers:** Branch-specific access, loyalty programs, credit management
- **Orders:** Multi-channel (POS, Online, Phone), installment support
- **Inventory:** Real-time tracking, WAC costing, batch/expiry tracking
- **Accounting:** Automatic journal entries, Egyptian tax compliance

### Database Structure
- 340+ specialized tables with `cod_` prefix
- Multi-language support (Arabic RTL/English LTR)
- Branch-based data segregation
- Comprehensive audit trails

## Error Handling

### Exception Management
- Try-catch blocks in all critical operations
- Centralized error logging through central services
- User-friendly error messages with technical details logged
- Automatic fallback mechanisms for critical functions

### Data Validation
- Server-side validation for all inputs
- Client-side validation for user experience
- SQL injection prevention
- CSRF protection

## Testing Strategy

### Quality Assurance Framework
- 10-expert review methodology for each screen
- Enterprise Grade Plus quality standards
- Competitive analysis against SAP, Oracle, Microsoft
- Egyptian market compliance verification

### Testing Levels
1. **Unit Testing:** Individual component functionality
2. **Integration Testing:** Module interaction verification
3. **System Testing:** End-to-end business process validation
4. **User Acceptance Testing:** Real-world scenario validation
5. **Performance Testing:** Load and stress testing
6. **Security Testing:** Vulnerability assessment