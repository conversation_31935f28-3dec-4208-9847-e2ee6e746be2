CREATE TABLE `cod_2fa_message_templates` (
  `template_id` int(11) NOT NULL,
  `template_type` enum('sms','email') NOT NULL COMMENT 'نوع القالب',
  `language_code` varchar(5) NOT NULL DEFAULT 'ar' COMMENT 'رمز اللغة',
  `purpose` enum('2fa_code','phone_verify','device_alert','backup_codes') NOT NULL COMMENT 'الغرض',
  `subject` varchar(255) DEFAULT NULL COMMENT 'موضوع الرسالة (للبريد الإلكتروني)',
  `message_body` text NOT NULL COMMENT 'نص الرسالة',
  `variables` text DEFAULT NULL COMMENT 'المتغيرات المتاحة',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط أم لا',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `cod_2fa_settings` (
  `setting_id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL COMMENT 'مفتاح الإعداد',
  `setting_value` text NOT NULL COMMENT 'قيمة الإعداد',
  `description` varchar(255) DEFAULT NULL COMMENT 'وصف الإعداد',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط أم لا',
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='إعدادات المصادقة الثنائية';

CREATE TABLE `cod_abandoned_cart` (
  `cart_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` varchar(32) NOT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp(),
  `last_activity` datetime NOT NULL,
  `items_count` int(11) NOT NULL DEFAULT 0,
  `total_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `recovery_email_sent` tinyint(1) NOT NULL DEFAULT 0,
  `email_sent_date` datetime DEFAULT NULL,
  `recovery_date` datetime DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_abandoned_cart_recovery` (
  `recovery_id` int(11) NOT NULL,
  `cart_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` varchar(20) NOT NULL,
  `data` text NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_abandoned_cart_template` (
  `template_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `content` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_accounts` (
  `account_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` bigint(20) NOT NULL DEFAULT 0,
  `account_code` bigint(20) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp(),
  `account_type` enum('debit','credit') NOT NULL DEFAULT 'debit'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_account_description` (
  `account_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_activity_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action_type` varchar(50) NOT NULL,
  `module` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_address` (
  `address_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `company` varchar(40) DEFAULT NULL,
  `address_1` varchar(128) NOT NULL,
  `address_2` varchar(128) NOT NULL,
  `city` varchar(128) NOT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `country_id` int(11) NOT NULL DEFAULT 63,
  `zone_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_announcement` (
  `announcement_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` enum('general','catalog','inventory','system','urgent','info') NOT NULL DEFAULT 'general',
  `priority` enum('low','normal','high','urgent','critical') NOT NULL DEFAULT 'normal',
  `status` enum('draft','active','scheduled','expired','archived') NOT NULL DEFAULT 'draft',
  `start_date` datetime DEFAULT NULL COMMENT 'تاريخ بدء عرض الإعلان',
  `end_date` datetime DEFAULT NULL COMMENT 'تاريخ انتهاء عرض الإعلان',
  `target_groups` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'مجموعات المستخدمين المستهدفة' CHECK (json_valid(`target_groups`)),
  `target_users` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'المستخدمين المستهدفين' CHECK (json_valid(`target_users`)),
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'بيانات إضافية للإعلان' CHECK (json_valid(`metadata`)),
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول الإعلانات الرئيسي';

CREATE TABLE `cod_announcement_attachment` (
  `attachment_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `uploaded_by` int(11) NOT NULL,
  `uploaded_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول مرفقات الإعلانات';

CREATE TABLE `cod_announcement_comment` (
  `comment_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `parent_comment_id` int(11) DEFAULT NULL COMMENT 'للردود على التعليقات',
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'approved',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول تعليقات الإعلانات';

CREATE TABLE `cod_announcement_view` (
  `view_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `viewed_at` datetime NOT NULL DEFAULT current_timestamp(),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول مشاهدات الإعلانات';

CREATE TABLE `cod_api` (
  `api_id` int(11) NOT NULL,
  `username` varchar(64) NOT NULL,
  `key` mediumtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_api_ip` (
  `api_ip_id` int(11) NOT NULL,
  `api_id` int(11) NOT NULL,
  `ip` varchar(40) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_api_session` (
  `api_session_id` int(11) NOT NULL,
  `api_id` int(11) NOT NULL,
  `session_id` varchar(32) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_asset_types` (
  `asset_type_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attendance` (
  `attendance_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `checkin_time` datetime DEFAULT NULL,
  `checkout_time` datetime DEFAULT NULL,
  `status` enum('present','absent','late','on_leave') NOT NULL DEFAULT 'present',
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute` (
  `attribute_id` int(11) NOT NULL,
  `attribute_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute_description` (
  `attribute_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute_group` (
  `attribute_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute_group_description` (
  `attribute_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_audit_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `reference_type` varchar(50) NOT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `before_data` mediumtext DEFAULT NULL,
  `after_data` mediumtext DEFAULT NULL,
  `timestamp` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_audit_plan` (
  `plan_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `year` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('draft','approved','in_progress','completed') NOT NULL DEFAULT 'draft',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_audit_task` (
  `task_id` int(11) NOT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `process` varchar(100) DEFAULT NULL,
  `risk_level` enum('low','medium','high') NOT NULL DEFAULT 'medium',
  `status` enum('pending','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
  `start_date` date DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `assigned_to` int(11) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `findings` text DEFAULT NULL,
  `recommendations` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank` (
  `bank_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `account_number` varchar(64) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `branch` varchar(64) NOT NULL,
  `swift_code` varchar(64) NOT NULL,
  `address` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank_account` (
  `account_id` int(11) NOT NULL,
  `account_name` varchar(100) NOT NULL,
  `bank_name` varchar(100) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `currency` varchar(3) NOT NULL,
  `current_balance` decimal(15,4) NOT NULL,
  `account_type` enum('bank','credit_card','e_wallet') NOT NULL DEFAULT 'bank'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank_reconciliation` (
  `reconciliation_id` int(11) NOT NULL,
  `bank_account_id` int(11) NOT NULL,
  `statement_date` date NOT NULL,
  `statement_opening_balance` decimal(15,4) NOT NULL,
  `statement_closing_balance` decimal(15,4) NOT NULL,
  `system_closing_balance` decimal(15,4) NOT NULL COMMENT 'الرصيد المتوقع في النظام قبل التسوية',
  `difference` decimal(15,4) NOT NULL COMMENT 'الفارق إن وجد',
  `status` enum('open','closed') NOT NULL DEFAULT 'open',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank_transaction` (
  `bank_transaction_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `bank_account_id` int(11) NOT NULL,
  `transaction_date` datetime NOT NULL,
  `transaction_type` enum('deposit','withdraw','transfer_in','transfer_out','check_in','check_out') NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `reference` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_banner` (
  `banner_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `status` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_banner_image` (
  `banner_image_id` int(11) NOT NULL,
  `banner_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(64) NOT NULL,
  `link` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_category` (
  `category_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_comment` (
  `comment_id` int(11) NOT NULL,
  `post_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL DEFAULT 0,
  `author` varchar(64) NOT NULL,
  `email` varchar(96) NOT NULL,
  `website` varchar(255) DEFAULT NULL,
  `content` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `notify` tinyint(1) NOT NULL DEFAULT 0,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_post` (
  `post_id` int(11) NOT NULL,
  `author_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `short_description` text DEFAULT NULL,
  `content` longtext NOT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` varchar(255) DEFAULT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `comment_status` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `hits` int(11) NOT NULL DEFAULT 0,
  `date_published` datetime DEFAULT NULL,
  `date_created` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_post_to_category` (
  `post_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_post_to_tag` (
  `post_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_tag` (
  `tag_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `slug` varchar(64) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch` (
  `branch_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `address_id` int(11) NOT NULL,
  `type` enum('store','warehouse') NOT NULL,
  `eta_branch_id` varchar(50) DEFAULT NULL,
  `available_online` tinyint(1) NOT NULL DEFAULT 0,
  `telephone` varchar(32) NOT NULL,
  `email` varchar(96) NOT NULL,
  `manager_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch_address` (
  `address_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `company` varchar(40) DEFAULT NULL,
  `address_1` varchar(128) NOT NULL,
  `address_2` varchar(128) DEFAULT NULL,
  `city` varchar(128) NOT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `country_id` int(11) NOT NULL,
  `zone_id` int(11) NOT NULL,
  `custom_field` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch_distance` (
  `distance_id` int(11) NOT NULL,
  `from_branch_id` int(11) NOT NULL COMMENT 'الفرع المرسل',
  `to_zone_id` int(11) NOT NULL COMMENT 'المحافظة المستقبلة',
  `distance_km` decimal(8,2) NOT NULL COMMENT 'المسافة بالكيلومتر',
  `estimated_delivery_hours` decimal(5,2) NOT NULL COMMENT 'ساعات التوصيل المتوقعة',
  `shipping_cost_per_kg` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT 'تكلفة الشحن لكل كيلو',
  `priority` enum('primary','secondary','backup') NOT NULL DEFAULT 'primary',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch_inventory_snapshot` (
  `snapshot_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `snapshot_date` datetime NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `average_cost` decimal(15,4) NOT NULL,
  `last_movement_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_budget` (
  `budget_id` int(11) NOT NULL,
  `budget_name` varchar(100) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_budget_line` (
  `line_id` int(11) NOT NULL,
  `budget_id` int(11) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `amount` decimal(15,4) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bundle_performance_analysis` (
  `analysis_id` int(11) NOT NULL,
  `bundle_id` int(11) NOT NULL,
  `analysis_date` date NOT NULL,
  `total_views` int(11) NOT NULL DEFAULT 0,
  `total_orders` int(11) NOT NULL DEFAULT 0,
  `total_revenue` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_discount_given` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `conversion_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `average_order_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `profit_margin` decimal(5,2) NOT NULL DEFAULT 0.00,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='تحليل أداء الباقات';

CREATE TABLE `cod_bundle_usage_log` (
  `usage_id` int(11) NOT NULL,
  `bundle_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` varchar(32) DEFAULT NULL,
  `quantity_used` int(11) NOT NULL DEFAULT 1,
  `original_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_applied` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `final_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `bundle_items_data` text DEFAULT NULL COMMENT 'تفاصيل عناصر الباقة المستخدمة',
  `status` enum('reserved','confirmed','cancelled','refunded') NOT NULL DEFAULT 'reserved',
  `used_at` datetime NOT NULL DEFAULT current_timestamp(),
  `confirmed_at` datetime DEFAULT NULL,
  `cancelled_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='سجل استخدام الباقات';

CREATE TABLE `cod_cart` (
  `cart_id` int(10) UNSIGNED NOT NULL,
  `api_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `session_id` varchar(32) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL DEFAULT 37,
  `recurring_id` int(11) NOT NULL,
  `option` mediumtext NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(15,4) DEFAULT NULL,
  `is_free` tinyint(1) NOT NULL DEFAULT 0,
  `bundle_id` int(11) DEFAULT NULL,
  `product_quantity_discount_id` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `selected_bundles` text DEFAULT NULL,
  `bundle_options` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_cash` (
  `cash_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `code` varchar(64) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `responsible_user_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_cash_transaction` (
  `cash_transaction_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `cash_id` int(11) NOT NULL COMMENT 'الخزنة/الصندوق المرتبط',
  `transaction_type` enum('cash_in','cash_out') NOT NULL COMMENT 'إيداع أو سحب',
  `amount` decimal(15,4) NOT NULL,
  `reference` varchar(100) DEFAULT NULL COMMENT 'مثلاً رقم فاتورة البيع أو قيد محاسبي',
  `note` text DEFAULT NULL COMMENT 'ملاحظات عامة',
  `created_by` int(11) NOT NULL COMMENT 'user_id الذي قام بالتسجيل',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category` (
  `category_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int(11) NOT NULL DEFAULT 0,
  `top` tinyint(1) NOT NULL,
  `column` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_description` (
  `category_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` varchar(255) NOT NULL,
  `meta_keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_filter` (
  `category_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_path` (
  `category_id` int(11) NOT NULL,
  `path_id` int(11) NOT NULL,
  `level` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_to_layout` (
  `category_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_to_store` (
  `category_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_checks` (
  `check_id` int(11) NOT NULL,
  `check_number` varchar(50) NOT NULL,
  `check_type` enum('incoming','outgoing') NOT NULL DEFAULT 'incoming',
  `bank_account_id` int(11) DEFAULT NULL,
  `payee_name` varchar(255) NOT NULL,
  `issue_date` date NOT NULL,
  `due_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency_id` int(11) DEFAULT NULL,
  `status` enum('outstanding','cleared','bounced','cancelled') NOT NULL DEFAULT 'outstanding',
  `reference` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_compliance_record` (
  `compliance_id` int(11) NOT NULL,
  `compliance_type` varchar(100) NOT NULL COMMENT 'مثلاً: إقرار ضريبي، التأمينات الاجتماعية، بيئي، إلخ',
  `reference_code` varchar(100) DEFAULT NULL COMMENT 'رقم المستند الرسمي لدى الجهة المختصة',
  `description` text DEFAULT NULL COMMENT 'تفاصيل الالتزام',
  `due_date` date DEFAULT NULL COMMENT 'تاريخ الاستحقاق أو آخر موعد لتقديم الإقرار',
  `status` enum('pending','submitted','approved','rejected','closed') DEFAULT 'pending',
  `responsible_user_id` int(11) DEFAULT NULL COMMENT 'المسؤول عن المتابعة',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_consignment_inventory` (
  `consignment_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `unit_id` int(11) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','returned','sold') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_cost_calculation_settings` (
  `setting_id` int(11) NOT NULL,
  `cost_method` enum('weighted_average','fifo','specific') NOT NULL DEFAULT 'weighted_average' COMMENT 'طريقة حساب التكلفة',
  `apply_price_variance_to` enum('inventory','expense','proportional') NOT NULL DEFAULT 'proportional' COMMENT 'طريقة معالجة فروق الأسعار',
  `variance_threshold_percentage` decimal(5,2) NOT NULL DEFAULT 2.00 COMMENT 'نسبة العتبة لاعتبار الفرق كبيرًا',
  `update_cost_on_return` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'تحديث التكلفة عند الإرجاع',
  `transfer_cost_method` enum('source_cost','fixed_cost','include_transfer_cost') NOT NULL DEFAULT 'source_cost' COMMENT 'طريقة نقل التكلفة عند التحويل',
  `batch_tracking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل تتبع الدفعات',
  `expiry_tracking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل تتبع تاريخ الصلاحية',
  `allow_negative_inventory` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'السماح بالمخزون السالب',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_country` (
  `country_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL,
  `iso_code_2` varchar(2) NOT NULL,
  `iso_code_3` varchar(3) NOT NULL,
  `address_format` mediumtext NOT NULL,
  `postcode_required` tinyint(1) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon` (
  `coupon_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL,
  `code` varchar(20) NOT NULL,
  `type` char(1) NOT NULL,
  `discount` decimal(15,4) NOT NULL,
  `logged` tinyint(1) NOT NULL,
  `shipping` tinyint(1) NOT NULL,
  `total` decimal(15,4) NOT NULL,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `uses_total` int(11) NOT NULL,
  `uses_customer` varchar(11) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon_category` (
  `coupon_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon_history` (
  `coupon_history_id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon_product` (
  `coupon_product_id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_campaign` (
  `campaign_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('seo','adwords','social_media','email','other') NOT NULL DEFAULT 'other',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `budget` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `code` varchar(64) DEFAULT NULL,
  `status` enum('active','inactive','completed') NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL,
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `actual_spend` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `invoice_reference` varchar(50) DEFAULT NULL,
  `add_expense` tinyint(1) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_contact` (
  `contact_id` int(11) NOT NULL,
  `firstname` varchar(100) NOT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `position` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_deal` (
  `deal_id` int(11) NOT NULL,
  `account_id` int(11) DEFAULT NULL,
  `opportunity_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `stage` enum('qualification','proposal','negotiation','closed_won','closed_lost') NOT NULL DEFAULT 'qualification',
  `amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `probability` decimal(5,2) NOT NULL DEFAULT 0.00,
  `expected_close_date` date DEFAULT NULL,
  `status` enum('open','closed','on_hold') NOT NULL DEFAULT 'open',
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_lead` (
  `lead_id` int(11) NOT NULL,
  `firstname` varchar(100) NOT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `source` varchar(50) DEFAULT NULL,
  `status` enum('new','contacted','qualified','unqualified','converted') NOT NULL DEFAULT 'new',
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_opportunity` (
  `opportunity_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `lead_id` int(11) DEFAULT NULL,
  `stage` enum('qualification','proposal','negotiation','closed_won','closed_lost') NOT NULL DEFAULT 'qualification',
  `probability` decimal(5,2) NOT NULL DEFAULT 0.00,
  `amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `close_date` date DEFAULT NULL,
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('open','closed','on_hold') NOT NULL DEFAULT 'open',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_currency` (
  `currency_id` int(11) NOT NULL,
  `title` varchar(32) NOT NULL,
  `code` varchar(3) NOT NULL,
  `symbol_left` varchar(12) NOT NULL,
  `symbol_right` varchar(12) NOT NULL,
  `decimal_place` char(1) NOT NULL,
  `value` double(15,8) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_currency_rate_history` (
  `rate_history_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `rate_date` date NOT NULL,
  `exchange_rate` decimal(15,8) NOT NULL,
  `changed_by` int(11) DEFAULT NULL,
  `note` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer` (
  `customer_id` int(11) NOT NULL,
  `eta_customer_type` enum('P','B') DEFAULT 'P',
  `eta_tax_id` varchar(100) DEFAULT NULL,
  `eta_commercial_registration` varchar(100) DEFAULT NULL,
  `eta_activity_code` varchar(20) DEFAULT NULL,
  `account_code` bigint(20) DEFAULT NULL,
  `customer_group_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `language_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `password` varchar(40) NOT NULL,
  `salt` varchar(9) NOT NULL,
  `cart` mediumtext DEFAULT NULL,
  `wishlist` mediumtext DEFAULT NULL,
  `newsletter` tinyint(1) NOT NULL DEFAULT 0,
  `address_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL,
  `ip` varchar(40) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `safe` tinyint(1) NOT NULL,
  `is_vip` tinyint(1) NOT NULL DEFAULT 0,
  `vip_level` varchar(20) DEFAULT NULL,
  `vip_since` date DEFAULT NULL,
  `vip_notes` text DEFAULT NULL,
  `token` mediumtext NOT NULL,
  `code` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_activity` (
  `customer_activity_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `key` varchar(64) NOT NULL,
  `data` mediumtext NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_affiliate` (
  `customer_id` int(11) NOT NULL,
  `company` varchar(40) NOT NULL,
  `website` varchar(255) NOT NULL,
  `tracking` varchar(64) NOT NULL,
  `commission` decimal(4,2) NOT NULL DEFAULT 0.00,
  `tax` varchar(64) NOT NULL,
  `payment` varchar(6) NOT NULL,
  `cheque` varchar(100) NOT NULL,
  `paypal` varchar(64) NOT NULL,
  `bank_name` varchar(64) NOT NULL,
  `bank_branch_number` varchar(64) NOT NULL,
  `bank_swift_code` varchar(64) NOT NULL,
  `bank_account_name` varchar(64) NOT NULL,
  `bank_account_number` varchar(64) NOT NULL,
  `custom_field` mediumtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_approval` (
  `customer_approval_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `type` varchar(9) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_credit_limit` (
  `limit_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `credit_limit` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `current_balance` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `payment_terms` int(11) DEFAULT NULL COMMENT 'فترة السداد بالأيام',
  `status` enum('active','suspended','pending_approval') NOT NULL DEFAULT 'active',
  `approved_by` int(11) DEFAULT NULL,
  `approval_date` datetime DEFAULT NULL,
  `last_review_date` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_feedback` (
  `feedback_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `feedback_type` enum('complaint','suggestion','inquiry','appreciation') NOT NULL,
  `subject` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `status` enum('new','in_progress','resolved','closed','cancelled') NOT NULL DEFAULT 'new',
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `closed_at` datetime DEFAULT NULL,
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, product',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `assigned_to` int(11) DEFAULT NULL COMMENT 'الموظف المسؤول',
  `satisfaction_rating` int(11) DEFAULT NULL COMMENT 'تقييم رضا العميل (1-5)',
  `source` enum('website','email','phone','social_media','in_store','app') NOT NULL DEFAULT 'website'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_group` (
  `customer_group_id` int(11) NOT NULL,
  `approval` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_group_description` (
  `customer_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_history` (
  `customer_history_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `comment` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_ip` (
  `customer_ip_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_login` (
  `customer_login_id` int(11) NOT NULL,
  `email` varchar(96) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `total` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_note` (
  `note_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `note` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_online` (
  `ip` varchar(40) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `url` mediumtext NOT NULL,
  `referer` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_return_inventory` (
  `return_inventory_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `return_to_stock` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'هل ترجع للمخزون أم تالفة',
  `updated_inventory` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم تحديث المخزون',
  `movement_id` int(11) DEFAULT NULL COMMENT 'معرف حركة المخزون',
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_reward` (
  `customer_reward_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL DEFAULT 0,
  `order_id` int(11) NOT NULL DEFAULT 0,
  `description` mediumtext NOT NULL,
  `points` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_search` (
  `customer_search_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `keyword` varchar(255) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `sub_category` tinyint(1) NOT NULL,
  `description` tinyint(1) NOT NULL,
  `products` int(11) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_transaction` (
  `customer_transaction_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `description` mediumtext NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_wishlist` (
  `customer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field` (
  `custom_field_id` int(11) NOT NULL,
  `type` varchar(32) NOT NULL,
  `value` mediumtext NOT NULL,
  `validation` varchar(255) NOT NULL,
  `location` varchar(10) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_customer_group` (
  `custom_field_id` int(11) NOT NULL,
  `customer_group_id` int(11) NOT NULL,
  `required` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_description` (
  `custom_field_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_value` (
  `custom_field_value_id` int(11) NOT NULL,
  `custom_field_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_value_description` (
  `custom_field_value_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `custom_field_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_report` (
  `report_id` int(11) NOT NULL,
  `report_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `report_query` text NOT NULL,
  `parameters` text DEFAULT NULL,
  `output_format` varchar(20) NOT NULL DEFAULT 'table',
  `is_public` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_data_access_control` (
  `access_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_group_id` int(11) DEFAULT NULL,
  `resource_type` varchar(50) NOT NULL COMMENT 'branch, product_category, etc',
  `resource_id` int(11) NOT NULL,
  `permission_level` varchar(20) NOT NULL DEFAULT 'view',
  `granted_by` int(11) NOT NULL,
  `granted_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_document_permission` (
  `permission_id` int(11) NOT NULL,
  `document_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT 'user_id أو NULL إذا كان للمجموعة',
  `user_group_id` int(11) DEFAULT NULL COMMENT 'user_group_id أو NULL إذا كان للمستخدم',
  `permission_type` enum('view','edit','delete','approve','share') NOT NULL DEFAULT 'view',
  `granted_by` int(11) NOT NULL,
  `granted_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_dynamic_pricing_rule` (
  `rule_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('percentage','fixed','formula') NOT NULL,
  `value` decimal(15,4) NOT NULL,
  `formula` text DEFAULT NULL,
  `condition_type` enum('customer_group','total_spent','purchase_history','time_period','stock_level','competitor_price') NOT NULL,
  `condition_value` text NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 0,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_advance` (
  `advance_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `request_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `status` enum('pending','approved','rejected','settled') NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_advance_installment` (
  `installment_id` int(11) NOT NULL,
  `advance_id` int(11) NOT NULL,
  `installment_number` int(11) NOT NULL,
  `due_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `paid_date` date DEFAULT NULL,
  `paid_amount` decimal(15,4) DEFAULT NULL,
  `status` enum('unpaid','paid','partial') NOT NULL DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_documents` (
  `document_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `document_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_profile` (
  `employee_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `job_title` varchar(100) NOT NULL,
  `hiring_date` date NOT NULL,
  `salary` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` enum('active','inactive','terminated') NOT NULL DEFAULT 'active',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_eta_activity_codes` (
  `activity_id` int(11) NOT NULL,
  `activity_code` varchar(20) NOT NULL,
  `description_ar` varchar(255) NOT NULL,
  `description_en` varchar(255) NOT NULL,
  `parent_code` varchar(20) DEFAULT NULL,
  `level` int(11) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع الأنشطة الاقتصادية';

CREATE TABLE `cod_eta_activity_log` (
  `log_id` int(11) NOT NULL,
  `activity_type` varchar(50) NOT NULL,
  `entity_type` varchar(50) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `document_id` int(11) DEFAULT NULL,
  `queue_id` int(11) DEFAULT NULL,
  `description` text NOT NULL,
  `details` longtext DEFAULT NULL COMMENT 'تفاصيل إضافية بصيغة JSON',
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `status` enum('success','warning','error','info') DEFAULT 'info',
  `execution_time` decimal(8,3) DEFAULT NULL COMMENT 'وقت التنفيذ بالثواني',
  `memory_usage` int(11) DEFAULT NULL COMMENT 'استخدام الذاكرة بالبايت',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل أنشطة ETA';

CREATE TABLE `cod_eta_documents` (
  `document_id` int(11) NOT NULL,
  `entity_type` enum('order','invoice','credit_note','debit_note','receipt') NOT NULL,
  `entity_id` int(11) NOT NULL,
  `internal_id` varchar(100) NOT NULL COMMENT 'الرقم الداخلي للمستند',
  `eta_uuid` varchar(50) DEFAULT NULL COMMENT 'معرف ETA الفريد',
  `eta_long_id` varchar(100) DEFAULT NULL COMMENT 'المعرف الطويل من ETA',
  `submission_uuid` varchar(50) DEFAULT NULL COMMENT 'معرف الإرسال',
  `document_type` varchar(10) NOT NULL COMMENT 'I=Invoice, C=Credit, D=Debit',
  `document_version` varchar(10) DEFAULT '1.0',
  `status` enum('draft','submitted','processing','approved','rejected','cancelled','failed') NOT NULL DEFAULT 'draft',
  `eta_status` varchar(50) DEFAULT NULL COMMENT 'الحالة من ETA',
  `submission_data` longtext DEFAULT NULL COMMENT 'البيانات المرسلة',
  `eta_response` longtext DEFAULT NULL COMMENT 'الرد من ETA',
  `validation_errors` text DEFAULT NULL,
  `signature_value` text DEFAULT NULL COMMENT 'التوقيع الرقمي',
  `qr_code` text DEFAULT NULL COMMENT 'رمز QR',
  `pdf_url` varchar(500) DEFAULT NULL COMMENT 'رابط ملف PDF',
  `total_amount` decimal(15,4) DEFAULT 0.0000,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `currency_code` varchar(3) DEFAULT 'EGP',
  `exchange_rate` decimal(10,6) DEFAULT 1.000000,
  `date_issued` datetime DEFAULT NULL,
  `submitted_at` datetime DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `rejected_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='المستندات المرسلة إلى ETA';

CREATE TABLE `cod_eta_document_lines` (
  `line_id` int(11) NOT NULL,
  `document_id` int(11) NOT NULL,
  `line_number` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `item_code` varchar(100) DEFAULT NULL COMMENT 'كود الصنف',
  `internal_code` varchar(100) DEFAULT NULL COMMENT 'الكود الداخلي',
  `gpc_code` varchar(20) DEFAULT NULL COMMENT 'رمز GPC',
  `egs_code` varchar(20) DEFAULT NULL COMMENT 'رمز EGS',
  `description` text NOT NULL,
  `item_type` varchar(10) DEFAULT 'GS1' COMMENT 'GS1 أو EGS',
  `unit_type` varchar(10) DEFAULT 'EA' COMMENT 'وحدة القياس',
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `unit_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `sales_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,4) DEFAULT 0.0000,
  `discount_rate` decimal(5,2) DEFAULT 0.00,
  `net_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_type` varchar(10) DEFAULT 'T1' COMMENT 'نوع الضريبة',
  `tax_subtype` varchar(10) DEFAULT NULL COMMENT 'النوع الفرعي للضريبة',
  `tax_rate` decimal(5,2) DEFAULT 0.00,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `currency_sold` varchar(3) DEFAULT 'EGP',
  `currency_exchange_rate` decimal(10,6) DEFAULT 1.000000,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='بنود المستندات المرسلة إلى ETA';

CREATE TABLE `cod_eta_queue` (
  `queue_id` int(11) NOT NULL,
  `entity_type` enum('order','invoice','credit_note','debit_note','receipt') NOT NULL,
  `entity_id` int(11) NOT NULL,
  `action` enum('submit','cancel','update','retry') NOT NULL DEFAULT 'submit',
  `priority` tinyint(4) NOT NULL DEFAULT 5 COMMENT '1=عالي، 5=عادي، 10=منخفض',
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `attempts` int(11) NOT NULL DEFAULT 0,
  `max_attempts` int(11) NOT NULL DEFAULT 3,
  `payload` longtext DEFAULT NULL COMMENT 'البيانات المرسلة بصيغة JSON',
  `response` longtext DEFAULT NULL COMMENT 'الرد من ETA',
  `error_message` text DEFAULT NULL,
  `scheduled_at` datetime DEFAULT NULL COMMENT 'موعد التنفيذ المجدول',
  `processed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `created_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='طوابير معالجة ETA';

CREATE TABLE `cod_eta_settings` (
  `setting_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','integer','decimal','boolean','json','encrypted') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `is_encrypted` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات ETA';

CREATE TABLE `cod_eta_statistics` (
  `stat_id` int(11) NOT NULL,
  `stat_date` date NOT NULL,
  `stat_hour` tinyint(4) DEFAULT NULL COMMENT 'الساعة للإحصائيات بالساعة',
  `documents_submitted` int(11) DEFAULT 0,
  `documents_approved` int(11) DEFAULT 0,
  `documents_rejected` int(11) DEFAULT 0,
  `documents_failed` int(11) DEFAULT 0,
  `total_amount` decimal(15,4) DEFAULT 0.0000,
  `total_tax` decimal(15,4) DEFAULT 0.0000,
  `avg_response_time` decimal(8,3) DEFAULT NULL COMMENT 'متوسط وقت الاستجابة',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT 'معدل النجاح',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إحصائيات ETA';

CREATE TABLE `cod_eta_tax_codes` (
  `tax_code_id` int(11) NOT NULL,
  `tax_type` varchar(10) NOT NULL COMMENT 'T1=VAT, T2=Table, T3=Stamp, T4=Entertainment',
  `tax_subtype` varchar(10) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL,
  `description_ar` varchar(255) NOT NULL,
  `description_en` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `effective_from` date DEFAULT NULL,
  `effective_to` date DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='رموز الضرائب المصرية';

CREATE TABLE `cod_eta_tokens` (
  `token_id` int(11) NOT NULL,
  `access_token` text NOT NULL,
  `token_type` varchar(50) DEFAULT 'Bearer',
  `expires_at` datetime NOT NULL,
  `scope` varchar(255) DEFAULT 'InvoicingAPI',
  `environment` enum('sandbox','production') DEFAULT 'sandbox',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='رموز الوصول لـ ETA';

CREATE TABLE `cod_eta_unit_types` (
  `unit_id` int(11) NOT NULL,
  `unit_code` varchar(10) NOT NULL,
  `description_ar` varchar(100) NOT NULL,
  `description_en` varchar(100) NOT NULL,
  `category` varchar(50) DEFAULT NULL COMMENT 'فئة الوحدة',
  `conversion_factor` decimal(10,6) DEFAULT 1.000000 COMMENT 'معامل التحويل للوحدة الأساسية',
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع الوحدات المصرية';

CREATE TABLE `cod_event` (
  `event_id` int(11) NOT NULL,
  `code` varchar(64) NOT NULL,
  `trigger` mediumtext NOT NULL,
  `action` mediumtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_extension` (
  `extension_id` int(11) NOT NULL,
  `type` varchar(32) NOT NULL,
  `code` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_extension_install` (
  `extension_install_id` int(11) NOT NULL,
  `extension_download_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_extension_path` (
  `extension_path_id` int(11) NOT NULL,
  `extension_install_id` int(11) NOT NULL,
  `path` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_feedback_history` (
  `history_id` int(11) NOT NULL,
  `feedback_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` enum('created','updated','assigned','responded','resolved','reopened','closed') NOT NULL,
  `comment` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `visibility` enum('internal','customer','both') NOT NULL DEFAULT 'internal'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_feedback_message` (
  `message_id` int(11) NOT NULL,
  `feedback_id` int(11) NOT NULL,
  `sender_type` enum('customer','employee','system') NOT NULL,
  `sender_id` int(11) NOT NULL COMMENT 'customer_id أو user_id حسب sender_type',
  `message` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_internal` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل الرسالة داخلية فقط',
  `attachment_path` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_feedback_template` (
  `template_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `category` enum('complaint','suggestion','inquiry','appreciation') NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `tags` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter` (
  `filter_id` int(11) NOT NULL,
  `filter_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter_description` (
  `filter_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `filter_group_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter_group` (
  `filter_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter_group_description` (
  `filter_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_financial_forecast` (
  `forecast_id` int(11) NOT NULL,
  `forecast_name` varchar(100) NOT NULL,
  `forecast_type` varchar(20) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `forecast_value` decimal(15,4) NOT NULL,
  `actual_value` decimal(15,4) DEFAULT NULL,
  `variance` decimal(15,4) DEFAULT NULL,
  `variance_percentage` decimal(5,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_fixed_assets` (
  `asset_id` int(11) NOT NULL,
  `asset_code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `asset_type_id` int(11) NOT NULL,
  `purchase_date` date NOT NULL,
  `purchase_value` decimal(15,2) NOT NULL,
  `current_value` decimal(15,2) NOT NULL,
  `depreciation_method` varchar(50) NOT NULL,
  `useful_life` int(11) NOT NULL,
  `salvage_value` decimal(15,2) NOT NULL,
  `status` enum('active','disposed','under_maintenance') NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_fixed_asset_history` (
  `history_id` int(11) NOT NULL,
  `asset_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_geo_zone` (
  `geo_zone_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_gift_card` (
  `gift_card_id` int(11) NOT NULL,
  `code` varchar(255) NOT NULL,
  `from_name` varchar(255) NOT NULL,
  `from_email` varchar(255) NOT NULL,
  `to_name` varchar(255) NOT NULL,
  `to_email` varchar(255) NOT NULL,
  `message` mediumtext DEFAULT NULL,
  `amount` decimal(15,4) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_goods_receipt` (
  `goods_receipt_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `receipt_number` varchar(50) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `receipt_date` date NOT NULL,
  `status` enum('pending','received','partially_received','cancelled') NOT NULL DEFAULT 'pending',
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `invoice_number` varchar(50) DEFAULT NULL COMMENT 'رقم فاتورة المورد',
  `invoice_date` date DEFAULT NULL COMMENT 'تاريخ فاتورة المورد',
  `invoice_amount` decimal(15,4) DEFAULT NULL COMMENT 'قيمة فاتورة المورد',
  `currency_id` int(11) DEFAULT NULL COMMENT 'عملة الفاتورة',
  `exchange_rate` decimal(15,6) DEFAULT NULL COMMENT 'سعر الصرف عند الاستلام',
  `matching_status` enum('pending','matched','partial','mismatch') NOT NULL DEFAULT 'pending' COMMENT 'حالة المطابقة مع الفاتورة وأمر الشراء',
  `quality_check_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل يتطلب فحص جودة',
  `quality_checked_by` int(11) DEFAULT NULL COMMENT 'من قام بفحص الجودة',
  `quality_check_date` datetime DEFAULT NULL COMMENT 'تاريخ فحص الجودة',
  `quality_check_result` text DEFAULT NULL COMMENT 'نتيجة فحص الجودة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_goods_receipt_item` (
  `receipt_item_id` int(11) NOT NULL,
  `goods_receipt_id` int(11) NOT NULL,
  `po_item_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity_received` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quality_result` enum('pending','passed','failed','partial') NOT NULL DEFAULT 'pending',
  `remarks` text DEFAULT NULL,
  `invoice_unit_price` decimal(15,4) DEFAULT NULL COMMENT 'سعر الوحدة في الفاتورة',
  `cost_difference` decimal(15,4) DEFAULT NULL COMMENT 'فرق التكلفة عن أمر الشراء',
  `is_cost_updated` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم تحديث التكلفة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_governance_issue` (
  `issue_id` int(11) NOT NULL,
  `issue_type` enum('compliance','risk','legal','audit','other') NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `status` enum('open','in_progress','resolved','closed') NOT NULL DEFAULT 'open',
  `responsible_user_id` int(11) DEFAULT NULL,
  `responsible_group_id` int(11) DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `resolved_at` datetime DEFAULT NULL,
  `resolution_notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_governance_meeting` (
  `meeting_id` int(11) NOT NULL,
  `meeting_type` varchar(50) DEFAULT NULL COMMENT 'مثلاً: مجلس إدارة، لجنة مراجعة، جمعية عمومية...',
  `title` varchar(255) NOT NULL COMMENT 'عنوان الاجتماع أو موضوعه',
  `meeting_date` datetime NOT NULL COMMENT 'موعد الاجتماع',
  `location` varchar(255) DEFAULT NULL,
  `agenda` text DEFAULT NULL COMMENT 'جدول الأعمال',
  `decisions` text DEFAULT NULL COMMENT 'القرارات المتخذة في الاجتماع',
  `added_by` int(11) DEFAULT NULL COMMENT 'user_id الذي سجّل المحضر',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_gpc_codes` (
  `gpc_id` int(11) NOT NULL,
  `gpc_code` int(11) NOT NULL,
  `title` varchar(512) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_aramex_details_new` (
  `id` int(11) NOT NULL,
  `order_id` text NOT NULL,
  `tracking_num` text NOT NULL,
  `shipping_label` mediumtext NOT NULL,
  `invoice` mediumtext NOT NULL,
  `return_label` mediumtext DEFAULT NULL,
  `return_invoice` mediumtext DEFAULT NULL,
  `one` mediumtext DEFAULT NULL,
  `two` mediumtext DEFAULT NULL,
  `three` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_aramex_pickup_details` (
  `id` int(11) NOT NULL,
  `order_id` text NOT NULL,
  `status` text NOT NULL,
  `confirm_no` mediumtext NOT NULL,
  `ready_time` mediumtext NOT NULL,
  `pickup_date` mediumtext DEFAULT NULL,
  `one` mediumtext DEFAULT NULL,
  `two` mediumtext DEFAULT NULL,
  `three` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_fedex_details_new` (
  `id` int(11) NOT NULL,
  `order_id` text NOT NULL,
  `tracking_num` text NOT NULL,
  `shipping_label` mediumtext NOT NULL,
  `invoice` mediumtext NOT NULL,
  `return_label` mediumtext DEFAULT NULL,
  `return_invoice` mediumtext DEFAULT NULL,
  `one` mediumtext DEFAULT NULL,
  `two` mediumtext DEFAULT NULL,
  `three` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_fedex_token` (
  `id` int(11) NOT NULL,
  `token` text NOT NULL,
  `timestamp_created` text NOT NULL,
  `mode` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_import_allocation` (
  `allocation_id` int(11) NOT NULL,
  `shipment_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `allocated_amount` decimal(15,4) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_import_charge` (
  `charge_id` int(11) NOT NULL,
  `shipment_id` int(11) NOT NULL,
  `charge_type` varchar(50) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_import_shipment` (
  `shipment_id` int(11) NOT NULL,
  `reference_number` varchar(50) NOT NULL,
  `origin_country` varchar(3) DEFAULT NULL,
  `arrival_port` varchar(100) DEFAULT NULL,
  `shipment_date` date NOT NULL,
  `document_reference` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('in_progress','completed','cancelled') NOT NULL DEFAULT 'in_progress',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information` (
  `information_id` int(11) NOT NULL,
  `bottom` int(11) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information_description` (
  `information_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(64) NOT NULL,
  `description` longtext NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` varchar(255) NOT NULL,
  `meta_keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information_to_layout` (
  `information_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information_to_store` (
  `information_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_payment` (
  `payment_id` int(11) NOT NULL,
  `schedule_id` int(11) NOT NULL,
  `payment_date` datetime NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `payment_method` enum('cash','bank_transfer','credit_card','cheque','online') NOT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `received_by` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `bank_account_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_plan` (
  `plan_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `total_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ الإجمالي للخطة',
  `down_payment` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الدفعة المقدمة',
  `remaining_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ المتبقي بعد الدفعة المقدمة',
  `installment_amount` decimal(15,4) NOT NULL COMMENT 'قيمة القسط الواحد',
  `number_of_installments` int(11) NOT NULL,
  `interest_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total_interest` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `admin_fees` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_with_interest` decimal(15,4) NOT NULL COMMENT 'المبلغ الإجمالي شاملاً الفوائد والرسوم',
  `installment_frequency` enum('monthly','biweekly','weekly') NOT NULL DEFAULT 'monthly',
  `start_date` date NOT NULL,
  `status` enum('pending','active','completed','cancelled','defaulted') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `grace_period_days` int(11) NOT NULL DEFAULT 0 COMMENT 'فترة سماح بالأيام',
  `late_payment_fee` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم التأخير',
  `late_payment_fee_percentage` decimal(5,2) NOT NULL DEFAULT 0.00,
  `guarantor_id` int(11) DEFAULT NULL COMMENT 'كفيل العميل إن وجد'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_plan_template` (
  `template_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `number_of_installments` int(11) NOT NULL,
  `interest_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'نسبة الفائدة السنوية',
  `admin_fees` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم إدارية ثابتة',
  `admin_fees_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'نسبة الرسوم الإدارية',
  `down_payment_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'نسبة الدفعة المقدمة',
  `minimum_order_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `maximum_order_amount` decimal(15,4) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_reminder` (
  `reminder_id` int(11) NOT NULL,
  `schedule_id` int(11) NOT NULL,
  `reminder_type` enum('before_due','on_due','overdue','final_notice') NOT NULL,
  `channel` enum('email','sms','whatsapp','system','call') NOT NULL,
  `scheduled_date` datetime NOT NULL,
  `sent_date` datetime DEFAULT NULL,
  `status` enum('pending','sent','failed','cancelled') NOT NULL DEFAULT 'pending',
  `message` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_schedule` (
  `schedule_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `installment_number` int(11) NOT NULL,
  `due_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `principal_amount` decimal(15,4) NOT NULL COMMENT 'مبلغ أصل الدين',
  `interest_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ الفائدة',
  `status` enum('upcoming','due','paid','partial','late','defaulted') NOT NULL DEFAULT 'upcoming',
  `payment_date` datetime DEFAULT NULL,
  `payment_amount` decimal(15,4) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `late_fee_applied` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_attachment` (
  `attachment_id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `uploaded_at` datetime NOT NULL DEFAULT current_timestamp(),
  `thumbnail_path` varchar(255) DEFAULT NULL COMMENT 'مسار الصورة المصغرة إن وجدت'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_audit` (
  `audit_id` int(11) NOT NULL,
  `audit_subject` varchar(255) NOT NULL COMMENT 'موضوع التدقيق',
  `audit_type` varchar(50) DEFAULT NULL COMMENT 'نوع التدقيق: مالي, تشغيلي.. الخ',
  `description` text DEFAULT NULL COMMENT 'نطاق أو تفاصيل التدقيق',
  `auditor_user_id` int(11) NOT NULL COMMENT 'يتعيّن تلقائياً حسب المستخدم الحالي',
  `scheduled_date` date NOT NULL COMMENT 'تاريخ بدء التدقيق',
  `completion_date` date DEFAULT NULL COMMENT 'تاريخ إكمال التدقيق',
  `findings` text DEFAULT NULL COMMENT 'النتائج',
  `recommendations` text DEFAULT NULL COMMENT 'التوصيات',
  `status` enum('scheduled','in_progress','completed','cancelled') NOT NULL DEFAULT 'scheduled',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_control` (
  `control_id` int(11) NOT NULL,
  `control_name` varchar(255) NOT NULL COMMENT 'اسم الإجراء/السياسة',
  `description` text DEFAULT NULL COMMENT 'تفاصيل أو شرح للإجراء',
  `responsible_group_id` int(11) NOT NULL DEFAULT 0 COMMENT 'رقم مجموعة المستخدم المسؤولة (إدارة معينة)',
  `effective_date` date NOT NULL COMMENT 'تاريخ سريان السياسة',
  `review_date` date DEFAULT NULL COMMENT 'تاريخ آخر مراجعة',
  `status` enum('active','obsolete') NOT NULL DEFAULT 'active' COMMENT 'حالة الإجراء',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_conversation` (
  `conversation_id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `type` enum('private','group','department') NOT NULL DEFAULT 'private',
  `creator_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','archived') NOT NULL DEFAULT 'active',
  `associated_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, purchase, etc',
  `reference_id` int(11) DEFAULT NULL COMMENT 'المعرف المرتبط مثل order_id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_message` (
  `message_id` int(11) NOT NULL,
  `conversation_id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message_text` text DEFAULT NULL,
  `sent_at` datetime NOT NULL DEFAULT current_timestamp(),
  `edited_at` datetime DEFAULT NULL,
  `is_system_message` tinyint(1) NOT NULL DEFAULT 0,
  `message_type` enum('text','file','link','task','workflow','approval') NOT NULL DEFAULT 'text',
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, document, meeting',
  `reference_id` int(11) DEFAULT NULL COMMENT 'المعرف المرتبط بالمودل',
  `parent_message_id` int(11) DEFAULT NULL,
  `mentions` text DEFAULT NULL COMMENT 'قائمة المستخدمين المشار إليهم'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_participant` (
  `participant_id` int(11) NOT NULL,
  `conversation_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `joined_at` datetime NOT NULL DEFAULT current_timestamp(),
  `left_at` datetime DEFAULT NULL,
  `role` enum('member','admin') NOT NULL DEFAULT 'member',
  `last_read_message_id` int(11) DEFAULT NULL,
  `notification_settings` enum('all','mentions','none') NOT NULL DEFAULT 'all'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_abc_analysis` (
  `abc_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `value_contribution` decimal(15,4) NOT NULL,
  `percentage_of_total` decimal(5,2) NOT NULL,
  `cumulative_percentage` decimal(5,2) NOT NULL,
  `abc_class` char(1) NOT NULL,
  `analysis_date` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_accounting_reconciliation` (
  `reconciliation_id` int(11) NOT NULL,
  `period_start_date` date NOT NULL,
  `period_end_date` date NOT NULL,
  `branch_id` int(11) NOT NULL,
  `status` enum('draft','in_progress','completed','approved') NOT NULL DEFAULT 'draft',
  `total_system_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_accounting_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `difference_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `completed_at` datetime DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_accounting_reconciliation_item` (
  `item_id` int(11) NOT NULL,
  `reconciliation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `system_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `system_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `accounting_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `difference_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `adjustment_journal_id` int(11) DEFAULT NULL,
  `is_adjusted` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_account_mapping` (
  `mapping_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL COMMENT 'الفرع أو المستودع',
  `product_category_id` int(11) DEFAULT NULL COMMENT 'يمكن ربط فئة محددة، أو NULL للكل',
  `inventory_account_code` bigint(20) NOT NULL COMMENT 'حساب المخزون',
  `cogs_account_code` bigint(20) NOT NULL COMMENT 'حساب تكلفة المبيعات',
  `purchase_account_code` bigint(20) NOT NULL COMMENT 'حساب المشتريات (مؤقت)',
  `inventory_adjustment_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب تسويات المخزون',
  `price_variance_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب فروق أسعار المشتريات',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_alert` (
  `alert_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `alert_type` enum('minimum','maximum','expired','slow_moving','damaged') NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `threshold` decimal(15,4) NOT NULL,
  `status` enum('active','acknowledged','resolved','ignored') NOT NULL DEFAULT 'active',
  `days_no_movement` int(11) DEFAULT NULL,
  `last_movement_date` date DEFAULT NULL,
  `recommended_action` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `acknowledged_by` int(11) DEFAULT NULL,
  `acknowledged_at` datetime DEFAULT NULL,
  `resolved_at` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_cost_history` (
  `history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `old_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `new_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `change_reason` varchar(20) NOT NULL,
  `notes` mediumtext NOT NULL,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_cost_update` (
  `update_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `old_cost` decimal(15,4) NOT NULL,
  `new_cost` decimal(15,4) NOT NULL,
  `update_date` datetime NOT NULL DEFAULT current_timestamp(),
  `source_type` enum('purchase','adjustment','import') NOT NULL,
  `source_id` int(11) NOT NULL COMMENT 'معرف المصدر',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_count` (
  `count_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `system_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `counted_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_difference` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `counted_by` int(11) NOT NULL DEFAULT 0,
  `count_date` datetime NOT NULL,
  `notes` mediumtext NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `applied_by` int(11) DEFAULT NULL,
  `applied_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_reservation` (
  `reservation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `source_type` varchar(50) NOT NULL COMMENT 'order, quotation, etc',
  `source_id` int(11) NOT NULL,
  `reservation_date` datetime NOT NULL DEFAULT current_timestamp(),
  `expiry_date` datetime DEFAULT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_role_permissions` (
  `permission_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role_type` enum('warehouse_manager','branch_manager','ecommerce_manager','store_staff','cashier') NOT NULL,
  `branch_id` int(11) DEFAULT NULL COMMENT 'الفرع المخصص للمستخدم (null = جميع الفروع)',
  `can_view_physical_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_edit_physical_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_view_virtual_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_edit_virtual_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_view_cost` tinyint(1) NOT NULL DEFAULT 0,
  `can_edit_cost` tinyint(1) NOT NULL DEFAULT 0,
  `can_manage_bundles` tinyint(1) NOT NULL DEFAULT 0,
  `can_manage_units` tinyint(1) NOT NULL DEFAULT 0,
  `can_access_reports` tinyint(1) NOT NULL DEFAULT 0,
  `can_export_data` tinyint(1) NOT NULL DEFAULT 0,
  `restrictions` text DEFAULT NULL COMMENT 'قيود إضافية بصيغة JSON',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='صلاحيات الأدوار في نظام المخزون';

CREATE TABLE `cod_inventory_sheet` (
  `sheet_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `sheet_date` datetime NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `created_by` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  `completed_by` int(11) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_sheet_item` (
  `sheet_item_id` int(11) NOT NULL,
  `sheet_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `system_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `counted_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` mediumtext NOT NULL,
  `count_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_status_log` (
  `log_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL COMMENT 'معرف المنتج',
  `warehouse_id` int(11) NOT NULL COMMENT 'معرف المستودع',
  `unit_id` int(11) NOT NULL COMMENT 'معرف الوحدة',
  `batch_id` int(11) DEFAULT NULL COMMENT 'معرف الدفعة',
  `status_from` enum('available','reserved','maintenance','quality_check','damaged','expired','quarantine') NOT NULL,
  `status_to` enum('available','reserved','maintenance','quality_check','damaged','expired','quarantine') NOT NULL,
  `quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المنقولة',
  `reason_id` int(11) DEFAULT NULL COMMENT 'معرف السبب',
  `reason_notes` text DEFAULT NULL COMMENT 'ملاحظات السبب',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `reference_number` varchar(100) DEFAULT NULL COMMENT 'رقم المرجع',
  `created_by` int(11) NOT NULL COMMENT 'المنشئ',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_sync_rules` (
  `rule_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `sync_type` enum('auto','manual','threshold','percentage') NOT NULL DEFAULT 'manual',
  `sync_threshold` decimal(15,4) DEFAULT NULL COMMENT 'عتبة التحويل التلقائي',
  `max_virtual_ratio` decimal(5,2) DEFAULT 1.50 COMMENT 'أقصى نسبة للمخزون الوهمي من الفعلي',
  `min_physical_quantity` decimal(15,4) DEFAULT 0.0000 COMMENT 'أقل كمية فعلية مطلوبة',
  `auto_sync_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `notification_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='قواعد مزامنة المخزون الوهمي والفعلي';

CREATE TABLE `cod_inventory_transfer` (
  `transfer_id` int(11) NOT NULL,
  `transfer_number` varchar(30) NOT NULL,
  `source_branch_id` int(11) NOT NULL,
  `destination_branch_id` int(11) NOT NULL,
  `transfer_date` date NOT NULL,
  `notes` mediumtext NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `created_by` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  `completed_by` int(11) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_transfer_item` (
  `item_id` int(11) NOT NULL,
  `transfer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_turnover` (
  `analysis_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `beginning_inventory` decimal(15,4) NOT NULL,
  `ending_inventory` decimal(15,4) NOT NULL,
  `average_inventory` decimal(15,4) NOT NULL,
  `cost_of_goods_sold` decimal(15,4) NOT NULL,
  `turnover_ratio` decimal(10,2) NOT NULL,
  `days_on_hand` int(11) NOT NULL,
  `analysis_date` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_turnover_analysis` (
  `analysis_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `analysis_period` enum('daily','weekly','monthly','quarterly','yearly') NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `opening_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `closing_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `average_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_sold` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_received` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `turnover_ratio` decimal(8,4) NOT NULL DEFAULT 0.0000,
  `days_of_supply` decimal(8,2) NOT NULL DEFAULT 0.00,
  `stockout_days` int(11) NOT NULL DEFAULT 0,
  `overstock_days` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='تحليل دوران المخزون';

CREATE TABLE `cod_inventory_valuation` (
  `valuation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL DEFAULT 0,
  `valuation_date` date NOT NULL,
  `average_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `date_added` datetime NOT NULL,
  `transaction_reference_id` int(11) NOT NULL DEFAULT 0,
  `transaction_type` varchar(20) NOT NULL,
  `previous_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `previous_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `movement_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `movement_cost` decimal(15,4) NOT NULL DEFAULT 0.0000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_invoices` (
  `invoice_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `issuer_type` varchar(2) DEFAULT NULL,
  `issuer_id` varchar(50) DEFAULT NULL,
  `issuer_name` varchar(200) DEFAULT NULL,
  `issuer_country` varchar(3) DEFAULT NULL,
  `issuer_governate` varchar(100) DEFAULT NULL,
  `issuer_region_city` varchar(100) DEFAULT NULL,
  `issuer_street` varchar(200) DEFAULT NULL,
  `issuer_building_number` varchar(100) DEFAULT NULL,
  `issuer_postal_code` varchar(20) DEFAULT NULL,
  `issuer_floor` varchar(50) DEFAULT NULL,
  `issuer_room` varchar(50) DEFAULT NULL,
  `issuer_landmark` varchar(200) DEFAULT NULL,
  `issuer_additional_info` varchar(500) DEFAULT NULL,
  `receiver_type` varchar(2) DEFAULT NULL,
  `receiver_id` varchar(50) DEFAULT NULL,
  `receiver_name` varchar(200) DEFAULT NULL,
  `receiver_country` varchar(3) DEFAULT NULL,
  `receiver_governate` varchar(100) DEFAULT NULL,
  `receiver_region_city` varchar(100) DEFAULT NULL,
  `receiver_street` varchar(200) DEFAULT NULL,
  `receiver_building_number` varchar(100) DEFAULT NULL,
  `receiver_postal_code` varchar(20) DEFAULT NULL,
  `receiver_floor` varchar(50) DEFAULT NULL,
  `receiver_room` varchar(50) DEFAULT NULL,
  `receiver_landmark` varchar(200) DEFAULT NULL,
  `receiver_additional_info` varchar(500) DEFAULT NULL,
  `document_type` varchar(5) DEFAULT NULL,
  `document_version` varchar(10) DEFAULT NULL,
  `date_time_issued` datetime DEFAULT NULL,
  `taxpayer_activity_code` varchar(10) DEFAULT NULL,
  `internal_id` varchar(50) DEFAULT NULL,
  `purchase_order_reference` varchar(50) DEFAULT NULL,
  `purchase_order_description` varchar(255) DEFAULT NULL,
  `sales_order_reference` varchar(50) DEFAULT NULL,
  `sales_order_description` varchar(255) DEFAULT NULL,
  `proforma_invoice_number` varchar(50) DEFAULT NULL,
  `total_sales_amount` decimal(13,5) DEFAULT NULL,
  `total_discount_amount` decimal(13,5) DEFAULT NULL,
  `net_amount` decimal(13,5) DEFAULT NULL,
  `total_amount` decimal(13,5) DEFAULT NULL,
  `extra_discount_amount` decimal(13,5) DEFAULT NULL,
  `total_items_discount_amount` decimal(13,5) DEFAULT NULL,
  `submission_uuid` varchar(36) DEFAULT NULL,
  `status` enum('pending','submitted','accepted','rejected') DEFAULT 'pending',
  `rejection_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_journals` (
  `journal_id` int(11) NOT NULL,
  `refnum` varchar(50) DEFAULT NULL,
  `thedate` date NOT NULL,
  `description` mediumtext NOT NULL,
  `added_by` varchar(100) DEFAULT NULL,
  `last_edit_by` varchar(100) DEFAULT NULL,
  `audit_by` varchar(100) DEFAULT NULL,
  `is_cancelled` tinyint(1) NOT NULL DEFAULT 0,
  `cancelled_date` datetime DEFAULT NULL,
  `cancelled_by` varchar(100) DEFAULT NULL,
  `audited` tinyint(1) NOT NULL DEFAULT 0,
  `audit_date` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `entrytype` tinyint(1) NOT NULL DEFAULT 2 COMMENT '1 يدوي\r\n2 آلي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_journal_attachments` (
  `attachment_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_journal_entries` (
  `entry_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `is_debit` tinyint(1) NOT NULL,
  `amount` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_language` (
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `code` varchar(5) NOT NULL,
  `locale` varchar(255) NOT NULL,
  `image` varchar(64) NOT NULL,
  `directory` varchar(32) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_layout` (
  `layout_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_layout_module` (
  `layout_module_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL,
  `code` varchar(64) NOT NULL,
  `position` varchar(14) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_layout_route` (
  `layout_route_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `route` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_leave_request` (
  `leave_request_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `leave_type_id` int(11) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('pending','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
  `reason` text DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_leave_type` (
  `leave_type_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_legal_contract` (
  `contract_id` int(11) NOT NULL,
  `contract_type` varchar(50) NOT NULL COMMENT 'نوع العقد: تأجير فرع، توريد، ...',
  `title` varchar(255) NOT NULL COMMENT 'عنوان العقد',
  `party_id` int(11) DEFAULT NULL COMMENT 'ربط مع طرف خارجي (مورد، عميل...) أو فرع',
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL COMMENT 'تاريخ الانتهاء أو null إن كان مفتوح المدة',
  `status` enum('active','expired','terminated','draft') DEFAULT 'draft',
  `value` decimal(15,2) DEFAULT 0.00 COMMENT 'قيمة العقد المالية إن وجدت',
  `description` text DEFAULT NULL COMMENT 'وصف أو شروط العقد',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_length_class` (
  `length_class_id` int(11) NOT NULL,
  `value` decimal(15,8) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_length_class_description` (
  `length_class_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(32) NOT NULL,
  `unit` varchar(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_location` (
  `location_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `address` mediumtext NOT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `geocode` varchar(32) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `open` mediumtext NOT NULL,
  `comment` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_manufacturer` (
  `manufacturer_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_manufacturer_to_store` (
  `manufacturer_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_marketing` (
  `marketing_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` mediumtext NOT NULL,
  `code` varchar(64) NOT NULL,
  `clicks` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_meeting_attendees` (
  `attendee_id` int(11) NOT NULL,
  `meeting_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT 'إذا كان الحاضر موظف بالشركة',
  `external_name` varchar(255) DEFAULT NULL COMMENT 'إذا كان الحاضر من خارج الشركة',
  `role_in_meeting` varchar(50) DEFAULT NULL COMMENT 'عضو مجلس إدارة، مستشار، ...',
  `presence_status` enum('attended','excused','absent') DEFAULT 'attended'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_message_recipient` (
  `message_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_modification` (
  `modification_id` int(11) NOT NULL,
  `extension_install_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `code` varchar(64) NOT NULL,
  `author` varchar(64) NOT NULL,
  `version` varchar(32) NOT NULL,
  `link` varchar(255) NOT NULL,
  `xml` longtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_module` (
  `module_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `code` varchar(32) NOT NULL,
  `setting` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_notices` (
  `notice_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `type` enum('credit','debit') NOT NULL,
  `issuer_type` varchar(2) DEFAULT NULL,
  `issuer_id` varchar(50) DEFAULT NULL,
  `issuer_name` varchar(200) DEFAULT NULL,
  `receiver_type` varchar(2) DEFAULT NULL,
  `receiver_id` varchar(50) DEFAULT NULL,
  `receiver_name` varchar(200) DEFAULT NULL,
  `document_type` varchar(5) DEFAULT NULL,
  `document_version` varchar(10) DEFAULT NULL,
  `date_time_issued` datetime DEFAULT NULL,
  `taxpayer_activity_code` varchar(10) DEFAULT NULL,
  `internal_id` varchar(50) DEFAULT NULL,
  `references` text DEFAULT NULL,
  `total_amount` decimal(13,5) DEFAULT NULL,
  `total_sales_amount` decimal(13,5) DEFAULT NULL,
  `total_discount_amount` decimal(13,5) DEFAULT NULL,
  `net_amount` decimal(13,5) DEFAULT NULL,
  `submission_uuid` varchar(36) DEFAULT NULL,
  `status` enum('pending','submitted','accepted','rejected') DEFAULT 'pending',
  `rejection_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_notification_automation` (
  `rule_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `trigger_event` varchar(100) NOT NULL COMMENT 'الحدث المحفز',
  `trigger_conditions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'شروط التشغيل' CHECK (json_valid(`trigger_conditions`)),
  `notification_template_id` int(11) NOT NULL,
  `target_type` enum('specific_users','user_groups','dynamic','all_users') NOT NULL DEFAULT 'specific_users',
  `target_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'إعدادات المستهدفين' CHECK (json_valid(`target_config`)),
  `delay_minutes` int(11) NOT NULL DEFAULT 0 COMMENT 'تأخير الإرسال بالدقائق',
  `status` enum('active','inactive','testing') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول قواعد أتمتة الإشعارات';

CREATE TABLE `cod_notification_automation_log` (
  `log_id` int(11) NOT NULL,
  `rule_id` int(11) NOT NULL,
  `event_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'بيانات الحدث المحفز' CHECK (json_valid(`event_data`)),
  `notifications_sent` int(11) NOT NULL DEFAULT 0,
  `execution_status` enum('success','failed','partial') NOT NULL DEFAULT 'success',
  `error_message` text DEFAULT NULL,
  `executed_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول سجل تنفيذ قواعد الأتمتة';

CREATE TABLE `cod_notification_template` (
  `template_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` enum('info','success','warning','error','approval','reminder','system') NOT NULL DEFAULT 'info',
  `priority` enum('low','normal','high','urgent','critical') NOT NULL DEFAULT 'normal',
  `category` varchar(100) NOT NULL DEFAULT 'general',
  `channels` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'قنوات الإرسال المدعومة' CHECK (json_valid(`channels`)),
  `variables` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'المتغيرات المتاحة في القالب' CHECK (json_valid(`variables`)),
  `email_subject` varchar(255) DEFAULT NULL,
  `email_content` text DEFAULT NULL,
  `sms_content` varchar(500) DEFAULT NULL,
  `status` enum('draft','active','inactive') NOT NULL DEFAULT 'draft',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'قالب نظامي لا يمكن حذفه',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول قوالب الإشعارات';

CREATE TABLE `cod_notification_user` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option` (
  `option_id` int(11) NOT NULL,
  `type` varchar(32) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option_description` (
  `option_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option_value` (
  `option_value_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option_value_description` (
  `option_value_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order` (
  `order_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `invoice_no` int(11) NOT NULL DEFAULT 0,
  `invoice_prefix` varchar(26) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `store_name` varchar(64) NOT NULL,
  `store_url` varchar(255) NOT NULL,
  `customer_id` int(11) NOT NULL DEFAULT 0,
  `customer_group_id` int(11) NOT NULL DEFAULT 0,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `custom_field` mediumtext NOT NULL,
  `payment_firstname` varchar(255) NOT NULL,
  `payment_lastname` varchar(32) DEFAULT NULL,
  `payment_company` varchar(60) NOT NULL,
  `payment_address_1` varchar(128) NOT NULL,
  `payment_address_2` varchar(128) NOT NULL,
  `payment_city` varchar(128) NOT NULL,
  `payment_postcode` varchar(10) NOT NULL,
  `payment_country` varchar(128) NOT NULL,
  `payment_country_id` int(11) NOT NULL,
  `payment_zone` varchar(128) NOT NULL,
  `payment_zone_id` int(11) NOT NULL,
  `payment_address_format` mediumtext NOT NULL,
  `payment_custom_field` mediumtext NOT NULL,
  `payment_method` varchar(128) NOT NULL,
  `payment_code` varchar(128) NOT NULL,
  `shipping_firstname` varchar(255) NOT NULL,
  `shipping_lastname` varchar(32) DEFAULT NULL,
  `shipping_company` varchar(40) NOT NULL,
  `shipping_address_1` varchar(128) NOT NULL,
  `shipping_address_2` varchar(128) NOT NULL,
  `shipping_city` varchar(128) NOT NULL,
  `shipping_postcode` varchar(10) NOT NULL,
  `shipping_country` varchar(128) NOT NULL,
  `shipping_country_id` int(11) NOT NULL,
  `shipping_zone` varchar(128) NOT NULL,
  `shipping_zone_id` int(11) NOT NULL,
  `shipping_address_format` mediumtext NOT NULL,
  `shipping_custom_field` mediumtext NOT NULL,
  `shipping_method` varchar(128) NOT NULL,
  `shipping_code` varchar(128) NOT NULL,
  `comment` mediumtext NOT NULL,
  `total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `order_status_id` int(11) NOT NULL DEFAULT 0,
  `eta_status` enum('pending','queued','submitted','processing','approved','rejected','cancelled','failed') DEFAULT NULL,
  `eta_document_id` int(11) DEFAULT NULL,
  `eta_uuid` varchar(50) DEFAULT NULL,
  `eta_long_id` varchar(100) DEFAULT NULL,
  `eta_submission_uuid` varchar(50) DEFAULT NULL,
  `eta_submitted_at` datetime DEFAULT NULL,
  `eta_approved_at` datetime DEFAULT NULL,
  `eta_updated_at` datetime DEFAULT NULL,
  `affiliate_id` int(11) NOT NULL,
  `commission` decimal(15,4) NOT NULL,
  `marketing_id` int(11) NOT NULL,
  `tracking` varchar(64) NOT NULL,
  `language_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `currency_code` varchar(3) NOT NULL,
  `currency_value` decimal(15,8) NOT NULL DEFAULT 1.00000000,
  `ip` varchar(40) NOT NULL,
  `forwarded_ip` varchar(40) NOT NULL,
  `user_agent` varchar(255) NOT NULL,
  `accept_language` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  `order_posuser_id` int(11) NOT NULL,
  `order_posuser_name` varchar(255) NOT NULL,
  `shift_id` int(11) NOT NULL DEFAULT 0,
  `rin_customer` varchar(255) DEFAULT NULL,
  `qr_code` varchar(255) DEFAULT NULL,
  `uuid` varchar(255) DEFAULT NULL,
  `longId` varchar(255) DEFAULT NULL,
  `hashKey` varchar(255) DEFAULT NULL,
  `submissionId` varchar(255) DEFAULT NULL,
  `fbcapidyad_ordflag` tinyint(1) DEFAULT 0,
  `confirmation_status` enum('Canceled','Pending','Confirmed') NOT NULL DEFAULT 'Pending',
  `confirmation_token` varchar(64) DEFAULT NULL,
  `installment_plan_id` int(11) DEFAULT NULL,
  `eta_auto_submit` tinyint(1) DEFAULT 1 COMMENT 'إرسال تلقائي لـ ETA'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_cogs` (
  `order_cogs_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `total_cogs` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي',
  `calculation_method` varchar(20) NOT NULL DEFAULT 'weighted_average' COMMENT 'طريقة حساب التكلفة',
  `inventory_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب المخزون المستخدم',
  `cogs_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب تكلفة المبيعات المستخدم',
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_history` (
  `order_history_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `order_status_id` int(11) NOT NULL,
  `notify` tinyint(1) NOT NULL DEFAULT 0,
  `comment` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_option` (
  `order_option_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `order_product_id` int(11) NOT NULL,
  `product_option_id` int(11) NOT NULL,
  `product_option_value_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `type` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_product` (
  `order_product_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL DEFAULT 37,
  `name` varchar(255) NOT NULL,
  `model` varchar(64) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `reward` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_shipment` (
  `order_shipment_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  `shipping_courier_id` varchar(255) NOT NULL DEFAULT '',
  `tracking_number` varchar(255) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_shipment_history` (
  `history_id` int(11) NOT NULL,
  `order_shipment_id` int(11) NOT NULL,
  `status` varchar(50) NOT NULL COMMENT 'مثال: تم الاستلام من الفرع، في الطريق، إلخ',
  `location` varchar(255) DEFAULT NULL COMMENT 'موقع الشحنة أو ملاحظات',
  `updated_by` int(11) NOT NULL COMMENT 'user_id الموظف الذي حدّث الحالة',
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_status` (
  `order_status_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_total` (
  `order_total_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `code` varchar(32) NOT NULL,
  `title` varchar(255) NOT NULL,
  `value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_voucher` (
  `order_voucher_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `voucher_id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `code` varchar(10) NOT NULL,
  `from_name` varchar(64) NOT NULL,
  `from_email` varchar(96) NOT NULL,
  `to_name` varchar(64) NOT NULL,
  `to_email` varchar(96) NOT NULL,
  `voucher_theme_id` int(11) NOT NULL,
  `message` mediumtext NOT NULL,
  `amount` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_paymentlinks` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `paymentlink` mediumtext NOT NULL,
  `order_desc` mediumtext DEFAULT NULL,
  `order_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `order_currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_gateway` (
  `gateway_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive','testing') NOT NULL DEFAULT 'inactive',
  `supported_methods` varchar(255) NOT NULL COMMENT 'طرق الدفع المدعومة مفصولة بفواصل',
  `currencies` varchar(100) NOT NULL COMMENT 'العملات المدعومة مفصولة بفواصل',
  `logo` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `supports_refunds` tinyint(1) NOT NULL DEFAULT 0,
  `supports_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `fixed_fee` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `percentage_fee` decimal(5,2) NOT NULL DEFAULT 0.00,
  `minimum_fee` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `support_contact` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_gateway_config` (
  `config_id` int(11) NOT NULL,
  `gateway_id` int(11) NOT NULL,
  `key` varchar(100) NOT NULL,
  `value` text NOT NULL,
  `is_sensitive` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل البيانات حساسة (مثل API keys)',
  `environment` enum('live','test','both') NOT NULL DEFAULT 'both',
  `last_updated` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_invoice` (
  `payment_invoice_id` int(11) NOT NULL,
  `payment_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `amount_paid` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_settlement` (
  `settlement_id` int(11) NOT NULL,
  `gateway_id` int(11) NOT NULL,
  `settlement_date` date NOT NULL,
  `settlement_reference` varchar(100) DEFAULT NULL,
  `amount` decimal(15,4) NOT NULL,
  `fee_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_amount` decimal(15,4) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `status` enum('pending','reconciled','disputed') NOT NULL DEFAULT 'pending',
  `bank_account_id` int(11) DEFAULT NULL COMMENT 'الحساب البنكي المحول إليه',
  `bank_transaction_id` int(11) DEFAULT NULL COMMENT 'معرف المعاملة البنكية',
  `transaction_count` int(11) NOT NULL DEFAULT 0 COMMENT 'عدد المعاملات في التسوية',
  `settlement_file` varchar(255) DEFAULT NULL COMMENT 'ملف التسوية إن وجد',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `reconciled_by` int(11) DEFAULT NULL,
  `reconciled_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_settlement_transaction` (
  `settlement_transaction_id` int(11) NOT NULL,
  `settlement_id` int(11) NOT NULL,
  `transaction_id` int(11) NOT NULL,
  `status` enum('matched','unmatched','disputed') NOT NULL DEFAULT 'matched',
  `discrepancy_amount` decimal(15,4) DEFAULT NULL COMMENT 'قيمة التباين إن وجد',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_transaction` (
  `transaction_id` int(11) NOT NULL,
  `gateway_id` int(11) NOT NULL,
  `gateway_transaction_id` varchar(100) DEFAULT NULL COMMENT 'معرف المعاملة لدى البوابة',
  `order_id` int(11) DEFAULT NULL,
  `installment_payment_id` int(11) DEFAULT NULL,
  `customer_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `fee_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم البوابة',
  `net_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ الصافي = المبلغ - الرسوم',
  `transaction_type` enum('payment','refund','authorization','capture','void') NOT NULL,
  `payment_method` varchar(50) NOT NULL COMMENT 'طريقة الدفع المستخدمة',
  `status` enum('pending','completed','failed','refunded','partial_refund','voided') NOT NULL DEFAULT 'pending',
  `error_code` varchar(50) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `request_data` mediumtext DEFAULT NULL COMMENT 'بيانات الطلب المرسلة للبوابة',
  `response_data` mediumtext DEFAULT NULL COMMENT 'رد البوابة',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `reference_transaction_id` int(11) DEFAULT NULL COMMENT 'في حالة الاسترداد أو التقاط المعاملة المرجعية',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `settlement_status` enum('pending','settled','failed') NOT NULL DEFAULT 'pending',
  `settlement_date` date DEFAULT NULL,
  `bank_transfer_id` int(11) DEFAULT NULL COMMENT 'المعرف المرجعي للتحويل البنكي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payroll_entry` (
  `payroll_entry_id` int(11) NOT NULL,
  `payroll_period_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `base_salary` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `allowances` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `deductions` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_salary` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `payment_status` enum('pending','paid') NOT NULL DEFAULT 'pending',
  `payment_date` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payroll_period` (
  `payroll_period_id` int(11) NOT NULL,
  `period_name` varchar(50) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('open','closed') NOT NULL DEFAULT 'open',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_closed` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_performance_criteria` (
  `criteria_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_performance_review` (
  `review_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `review_date` date NOT NULL,
  `reviewer_id` int(11) NOT NULL,
  `overall_score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `comments` text DEFAULT NULL,
  `status` enum('pending','completed') NOT NULL DEFAULT 'pending',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_performance_review_criteria` (
  `review_criteria_id` int(11) NOT NULL,
  `review_id` int(11) NOT NULL,
  `criteria_id` int(11) NOT NULL,
  `score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `comments` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_permission` (
  `permission_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `key` varchar(255) NOT NULL,
  `type` enum('access','modify','other') NOT NULL DEFAULT 'other',
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_cash_handover` (
  `handover_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `from_user_id` int(11) NOT NULL,
  `to_user_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `handover_time` datetime NOT NULL,
  `notes` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_session` (
  `session_id` int(11) NOT NULL,
  `session_number` varchar(50) NOT NULL,
  `terminal_id` int(11) NOT NULL,
  `cashier_id` int(11) NOT NULL,
  `shift_id` int(11) DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','closed','suspended','cancelled') NOT NULL DEFAULT 'active',
  `opening_cash` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `closing_cash` decimal(15,4) DEFAULT NULL,
  `expected_cash` decimal(15,4) DEFAULT NULL,
  `cash_difference` decimal(15,4) DEFAULT NULL,
  `total_sales` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_transactions` int(11) NOT NULL DEFAULT 0,
  `total_items` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_shift` (
  `shift_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `terminal_id` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `starting_cash` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `ending_cash` decimal(15,4) DEFAULT NULL,
  `expected_cash` decimal(15,4) DEFAULT NULL,
  `cash_difference` decimal(15,4) DEFAULT NULL,
  `status` enum('active','closed','balanced') NOT NULL DEFAULT 'active',
  `notes` mediumtext DEFAULT NULL,
  `total_sales` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_cogs` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `last_order_id` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_terminal` (
  `terminal_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `printer_type` varchar(32) DEFAULT NULL,
  `printer_name` varchar(64) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_transaction` (
  `transaction_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `type` enum('sale','cash_in','cash_out','refund','void','drop') NOT NULL,
  `payment_method` varchar(32) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `reference` varchar(64) DEFAULT NULL,
  `notes` mediumtext DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product` (
  `product_id` int(11) NOT NULL,
  `eta_item_code` varchar(100) DEFAULT NULL,
  `eta_item_type` varchar(10) DEFAULT 'GS1',
  `eta_unit_type` varchar(10) DEFAULT 'EA',
  `gpc_code` varchar(20) DEFAULT NULL,
  `egs_code` varchar(20) DEFAULT NULL,
  `eta_tax_type` varchar(10) DEFAULT 'T1',
  `eta_tax_subtype` varchar(10) DEFAULT 'V009',
  `model` varchar(64) NOT NULL,
  `type` enum('product','consu','service') NOT NULL DEFAULT 'product',
  `sku` varchar(64) NOT NULL,
  `upc` varchar(12) NOT NULL,
  `ean` varchar(14) NOT NULL,
  `jan` varchar(13) NOT NULL,
  `isbn` varchar(17) NOT NULL,
  `mpn` varchar(64) NOT NULL,
  `location` varchar(128) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `average_cost` decimal(15,4) DEFAULT 0.0000,
  `stock_status_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `manufacturer_id` int(11) NOT NULL,
  `shipping` tinyint(1) NOT NULL DEFAULT 1,
  `price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `points` int(11) NOT NULL DEFAULT 0,
  `tax_class_id` int(11) NOT NULL,
  `date_available` date NOT NULL DEFAULT '1970-01-01',
  `weight` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `weight_class_id` int(11) NOT NULL DEFAULT 0,
  `length` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `width` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `height` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `length_class_id` int(11) NOT NULL DEFAULT 0,
  `subtract` tinyint(1) NOT NULL DEFAULT 1,
  `minimum` int(11) NOT NULL DEFAULT 1,
  `track_expiry` tinyint(1) NOT NULL DEFAULT 0,
  `expiry_alert_days` int(11) DEFAULT NULL,
  `track_batch` tinyint(1) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `viewed` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_attribute` (
  `product_id` int(11) NOT NULL,
  `attribute_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `text` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_barcode` (
  `product_barcode_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `product_option_id` int(11) DEFAULT NULL,
  `product_option_value_id` int(11) DEFAULT NULL,
  `barcode` varchar(255) NOT NULL,
  `type` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_batch` (
  `batch_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `batch_number` varchar(100) NOT NULL,
  `manufacturing_date` date DEFAULT NULL,
  `expiry_date` date NOT NULL,
  `initial_quantity` decimal(15,4) NOT NULL,
  `remaining_quantity` decimal(15,4) NOT NULL,
  `cost` decimal(15,4) NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_bundle` (
  `bundle_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `discount_type` enum('percentage','fixed','product') NOT NULL,
  `discount_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `min_quantity` int(11) NOT NULL DEFAULT 1 COMMENT 'أقل كمية للباقة',
  `max_quantity` int(11) DEFAULT NULL COMMENT 'أقصى كمية للباقة',
  `valid_from` datetime DEFAULT NULL COMMENT 'تاريخ بداية صلاحية الباقة',
  `valid_to` datetime DEFAULT NULL COMMENT 'تاريخ انتهاء صلاحية الباقة',
  `usage_limit` int(11) DEFAULT NULL COMMENT 'حد الاستخدام الإجمالي',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT 'عدد مرات الاستخدام',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT 'أولوية العرض'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_bundle_item` (
  `bundle_item_id` int(11) NOT NULL,
  `bundle_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_id` int(11) NOT NULL,
  `is_free` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_description` (
  `product_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `tag` mediumtext NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` varchar(255) NOT NULL,
  `meta_keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_dynamic_pricing` (
  `product_id` int(11) NOT NULL,
  `rule_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_egs` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `egs_code` varchar(60) NOT NULL,
  `gpc_code` varchar(30) DEFAULT NULL,
  `eta_status` varchar(100) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_filter` (
  `product_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_image` (
  `product_image_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_inventory` (
  `product_inventory_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_available` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `is_consignment` tinyint(1) NOT NULL DEFAULT 0,
  `consignment_supplier_id` int(11) DEFAULT NULL,
  `average_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `reserved_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية المحجوزة للطلبات',
  `last_sync_at` datetime DEFAULT NULL COMMENT 'آخر مزامنة بين الوهمي والفعلي',
  `sync_status` enum('synced','pending','error') NOT NULL DEFAULT 'synced',
  `auto_sync_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `quantity_maintenance` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مخزون الصيانة',
  `quantity_quality_check` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'قيد الفحص',
  `quantity_damaged` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'تالف',
  `quantity_expired` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'منتهي الصلاحية',
  `quantity_quarantine` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'حجر صحي',
  `last_status_update` datetime DEFAULT NULL COMMENT 'آخر تحديث للحالة',
  `status_updated_by` int(11) DEFAULT NULL COMMENT 'محدث الحالة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_inventory_history` (
  `history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `transaction_date` datetime NOT NULL,
  `transaction_type` varchar(20) NOT NULL,
  `reference_id` int(11) NOT NULL DEFAULT 0,
  `reference_type` varchar(30) NOT NULL DEFAULT '',
  `quantity_before` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_after` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_change` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cost_before` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cost_after` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `created_by` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_movement` (
  `product_movement_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `product_id` int(11) NOT NULL,
  `type` enum('purchase','sale','adjustment','transfer','consignment','import','receipt') NOT NULL,
  `movement_reference_type` varchar(50) DEFAULT NULL,
  `movement_reference_id` int(11) DEFAULT NULL,
  `source_document_type` varchar(50) DEFAULT NULL COMMENT 'نوع المستند المصدر (فاتورة، أمر شراء، إلخ)',
  `source_document_id` int(11) DEFAULT NULL COMMENT 'معرف المستند المصدر',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `quantity` decimal(15,4) NOT NULL,
  `unit_cost` decimal(15,4) DEFAULT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL DEFAULT 0,
  `reference` varchar(50) DEFAULT NULL,
  `old_average_cost` decimal(15,4) DEFAULT NULL,
  `new_average_cost` decimal(15,4) DEFAULT NULL,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `cost_before_movement` decimal(15,4) DEFAULT NULL COMMENT 'التكلفة قبل الحركة',
  `cost_after_movement` decimal(15,4) DEFAULT NULL COMMENT 'التكلفة بعد الحركة',
  `movement_value` decimal(15,4) DEFAULT NULL COMMENT 'قيمة الحركة بالتكلفة',
  `effect_on_cost` enum('increase','decrease','no_change') DEFAULT NULL,
  `detailed_calculation` text DEFAULT NULL,
  `related_costs` decimal(15,4) DEFAULT NULL,
  `is_cost_finalized` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_option` (
  `product_option_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `value` mediumtext NOT NULL,
  `required` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_option_value` (
  `product_option_value_id` int(11) NOT NULL,
  `product_option_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `option_value_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `subtract` tinyint(1) NOT NULL,
  `price` decimal(15,4) NOT NULL,
  `price_prefix` varchar(1) NOT NULL,
  `points` int(11) NOT NULL,
  `points_prefix` varchar(1) NOT NULL,
  `weight` decimal(15,8) NOT NULL,
  `weight_prefix` varchar(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_price_history` (
  `history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `old_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `new_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `change_date` datetime NOT NULL DEFAULT current_timestamp(),
  `change_type` enum('manual','cost_update','import','system') NOT NULL DEFAULT 'manual',
  `changed_by` int(11) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_pricing` (
  `product_pricing_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `base_price` decimal(15,2) NOT NULL DEFAULT 0.00,
  `special_price` decimal(15,2) DEFAULT NULL,
  `wholesale_price` decimal(15,2) DEFAULT NULL,
  `half_wholesale_price` decimal(15,2) DEFAULT NULL,
  `last_updated` datetime DEFAULT NULL,
  `custom_price` decimal(15,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_quantity_discounts` (
  `discount_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('buy_x_get_y','buy_x_get_discount') NOT NULL,
  `buy_quantity` int(11) NOT NULL,
  `get_quantity` int(11) NOT NULL,
  `discount_type` enum('percentage','fixed') NOT NULL,
  `discount_value` decimal(15,4) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `unit_id` int(11) DEFAULT 37,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `notes` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_recommendation` (
  `recommendation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `related_product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `customer_group_id` int(11) DEFAULT NULL,
  `discount_type` enum('percentage','fixed') DEFAULT NULL,
  `discount_value` decimal(15,4) DEFAULT NULL,
  `type` enum('upsell','cross_sell') NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_recurring` (
  `product_id` int(11) NOT NULL,
  `recurring_id` int(11) NOT NULL,
  `customer_group_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_related` (
  `product_id` int(11) NOT NULL,
  `related_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_reward` (
  `product_reward_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL DEFAULT 0,
  `customer_group_id` int(11) NOT NULL DEFAULT 0,
  `points` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_to_category` (
  `product_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_to_layout` (
  `product_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_to_store` (
  `product_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_unit` (
  `product_unit_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `unit_type` enum('base','additional') NOT NULL DEFAULT 'base',
  `conversion_factor` decimal(15,4) NOT NULL DEFAULT 1.0000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pt_transactions` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `payment_method` varchar(32) NOT NULL,
  `transaction_ref` varchar(64) NOT NULL,
  `parent_ref` varchar(64) DEFAULT NULL,
  `transaction_type` varchar(32) NOT NULL,
  `transaction_status` tinyint(1) NOT NULL,
  `transaction_amount` decimal(15,4) NOT NULL,
  `transaction_currency` varchar(8) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_document` (
  `document_id` int(11) NOT NULL,
  `reference_type` enum('requisition','rfq','purchase_order','goods_receipt','supplier_invoice','vendor_payment','purchase_return') NOT NULL,
  `reference_id` int(11) NOT NULL,
  `document_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'حجم الملف بالبايت',
  `document_type` varchar(100) DEFAULT NULL COMMENT 'نوع محتوى الملف',
  `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل الملف متاح للعامة',
  `uploaded_by` int(11) NOT NULL,
  `upload_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_log` (
  `log_id` int(11) NOT NULL,
  `reference_id` int(11) NOT NULL,
  `reference_type` enum('requisition','quote','purchase_order','goods_receipt','invoice','payment') NOT NULL,
  `action` varchar(255) NOT NULL,
  `user_id` int(11) NOT NULL,
  `details` text DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_matching` (
  `matching_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL COMMENT 'Purchase Order ID',
  `receipt_id` int(11) DEFAULT NULL COMMENT 'Goods Receipt ID',
  `invoice_id` int(11) DEFAULT NULL COMMENT 'Supplier Invoice ID',
  `status` enum('pending','matched','partial','mismatch') NOT NULL DEFAULT 'pending',
  `matched_by` int(11) DEFAULT NULL COMMENT 'User who performed matching',
  `matched_at` datetime DEFAULT NULL COMMENT 'Matching date/time',
  `notes` text DEFAULT NULL COMMENT 'Matching notes',
  `total_variance_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل يتطلب موافقة على المطابقة',
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_matching_item` (
  `matching_item_id` int(11) NOT NULL,
  `matching_id` int(11) NOT NULL,
  `po_item_id` int(11) NOT NULL COMMENT 'PO Item ID',
  `receipt_item_id` int(11) DEFAULT NULL COMMENT 'Receipt Item ID',
  `invoice_item_id` int(11) DEFAULT NULL COMMENT 'Invoice Item ID',
  `quantity_ordered` decimal(15,4) NOT NULL COMMENT 'Ordered quantity',
  `quantity_received` decimal(15,4) DEFAULT NULL COMMENT 'Received quantity',
  `quantity_invoiced` decimal(15,4) DEFAULT NULL COMMENT 'Invoiced quantity',
  `unit_price_ordered` decimal(15,4) NOT NULL COMMENT 'Price in PO',
  `unit_price_invoiced` decimal(15,4) DEFAULT NULL COMMENT 'Price in Invoice',
  `status` enum('pending','matched','mismatch') NOT NULL DEFAULT 'pending',
  `variance_amount` decimal(15,4) DEFAULT NULL COMMENT 'Price variance amount',
  `variance_notes` text DEFAULT NULL COMMENT 'Variance explanation',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order` (
  `po_id` int(11) NOT NULL,
  `po_number` varchar(50) NOT NULL,
  `quotation_id` int(11) DEFAULT NULL,
  `supplier_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` enum('draft','pending_review','approved','rejected','cancelled','completed') NOT NULL DEFAULT 'draft',
  `order_date` date NOT NULL,
  `expected_delivery_date` date DEFAULT NULL,
  `subtotal` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` int(11) NOT NULL,
  `source_type` enum('direct','requisition','quotation') NOT NULL DEFAULT 'direct' COMMENT 'مصدر أمر الشراء',
  `source_id` int(11) DEFAULT NULL COMMENT 'معرف المصدر (رقم الطلب أو العرض)',
  `payment_terms` text DEFAULT NULL COMMENT 'شروط الدفع',
  `delivery_terms` text DEFAULT NULL COMMENT 'شروط التسليم',
  `currency_id` int(11) NOT NULL DEFAULT 1 COMMENT 'العملة',
  `exchange_rate` decimal(15,6) NOT NULL DEFAULT 1.000000 COMMENT 'سعر الصرف',
  `is_cost_updated` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم تحديث التكلفة',
  `financial_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تمت الموافقة المالية',
  `financial_approved_by` int(11) DEFAULT NULL COMMENT 'من اعتمد مالياً',
  `financial_approval_date` datetime DEFAULT NULL COMMENT 'تاريخ الاعتماد المالي',
  `budget_allocated` decimal(15,4) DEFAULT NULL COMMENT 'المبلغ المخصص من الموازنة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order_history` (
  `history_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order_item` (
  `po_item_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(15,4) NOT NULL,
  `description` text DEFAULT NULL,
  `original_unit_price` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'السعر الأصلي بعملة المورد',
  `exchange_rate` decimal(15,6) NOT NULL DEFAULT 1.000000 COMMENT 'سعر الصرف عند الإنشاء',
  `is_received` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم الاستلام',
  `received_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية المستلمة',
  `source_item_id` int(11) DEFAULT NULL COMMENT 'معرف البند المصدر (من الطلب أو العرض)',
  `average_cost` decimal(15,4) DEFAULT NULL COMMENT 'متوسط التكلفة المحسوب',
  `original_cost` decimal(15,4) DEFAULT NULL COMMENT 'التكلفة الأصلية قبل التعديل',
  `cost_updated_at` datetime DEFAULT NULL COMMENT 'تاريخ تحديث التكلفة',
  `cost_updated_by` int(11) DEFAULT NULL COMMENT 'من قام بتحديث التكلفة',
  `budget_line_id` int(11) DEFAULT NULL COMMENT 'بند الموازنة المرتبط'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order_tracking` (
  `tracking_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `status_change` enum('created','sent_to_vendor','confirmed_by_vendor','partially_received','fully_received','cancelled','closed') NOT NULL,
  `status_date` datetime NOT NULL DEFAULT current_timestamp(),
  `expected_delivery_date` date DEFAULT NULL,
  `actual_delivery_date` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_price_variance` (
  `variance_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `receipt_id` int(11) DEFAULT NULL COMMENT 'معرف استلام البضاعة',
  `invoice_id` int(11) DEFAULT NULL COMMENT 'معرف فاتورة المورد',
  `po_id` int(11) DEFAULT NULL COMMENT 'معرف أمر الشراء',
  `receipt_price` decimal(15,4) NOT NULL COMMENT 'سعر الاستلام الأولي',
  `invoice_price` decimal(15,4) NOT NULL COMMENT 'سعر الفاتورة النهائي',
  `quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المستلمة',
  `remaining_quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المتبقية عند وصول الفاتورة',
  `variance_amount` decimal(15,4) NOT NULL COMMENT 'مبلغ الفرق الإجمالي',
  `adjustment_to_inventory` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ تعديل المخزون',
  `adjustment_to_cogs` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ تعديل تكلفة المبيعات',
  `adjustment_date` datetime NOT NULL,
  `adjustment_type` enum('cost_update','variance_account') NOT NULL COMMENT 'نوع التعديل',
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_quotation` (
  `quotation_id` int(11) NOT NULL,
  `quotation_number` varchar(50) NOT NULL,
  `requisition_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `total_amount` decimal(15,4) NOT NULL,
  `is_expired` tinyint(1) NOT NULL DEFAULT 0,
  `exchange_rate` decimal(15,6) NOT NULL DEFAULT 1.000000,
  `status` enum('draft','pending','approved','rejected','converted') NOT NULL DEFAULT 'draft',
  `rejection_reason` text DEFAULT NULL,
  `validity_date` date DEFAULT NULL,
  `delivery_terms` text DEFAULT NULL,
  `payment_terms` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `comparison_notes` text DEFAULT NULL,
  `technical_approval` tinyint(1) NOT NULL DEFAULT 0,
  `technical_approved_by` int(11) DEFAULT NULL,
  `technical_approval_date` datetime DEFAULT NULL,
  `financial_approval` tinyint(1) NOT NULL DEFAULT 0,
  `financial_approved_by` int(11) DEFAULT NULL,
  `financial_approval_date` datetime DEFAULT NULL,
  `attachment_count` int(11) NOT NULL DEFAULT 0,
  `is_best_offer` tinyint(1) NOT NULL DEFAULT 0,
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `tax_included` tinyint(1) NOT NULL DEFAULT 0,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_type` varchar(10) NOT NULL DEFAULT 'fixed',
  `has_discount` tinyint(1) NOT NULL DEFAULT 0,
  `discount_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_quotation_history` (
  `history_id` int(11) NOT NULL,
  `quotation_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_quotation_item` (
  `quotation_item_id` int(11) NOT NULL,
  `quotation_id` int(11) NOT NULL,
  `requisition_item_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `alternative_product_id` int(11) DEFAULT NULL,
  `is_alternative` tinyint(1) NOT NULL DEFAULT 0,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `unit_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `line_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_type` enum('percentage','fixed') NOT NULL DEFAULT 'fixed',
  `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `delivery_time` varchar(50) DEFAULT NULL,
  `warranty_period` varchar(50) DEFAULT NULL,
  `is_best_price` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_requisition` (
  `requisition_id` int(11) NOT NULL,
  `req_number` varchar(50) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `user_group_id` int(11) NOT NULL,
  `status` enum('draft','pending','approved','rejected','cancelled','processing','completed') NOT NULL DEFAULT 'draft',
  `rejection_reason` text DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `required_date` date DEFAULT NULL COMMENT 'التاريخ المتوقع للاستلام',
  `notes` mediumtext DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `date_modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_requisition_history` (
  `history_id` int(11) NOT NULL,
  `requisition_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_requisition_item` (
  `requisition_item_id` int(11) NOT NULL,
  `requisition_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_return` (
  `return_id` int(11) NOT NULL,
  `return_number` varchar(50) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `purchase_order_id` int(11) DEFAULT NULL,
  `goods_receipt_id` int(11) DEFAULT NULL,
  `return_date` date NOT NULL,
  `status` enum('pending','approved','completed','cancelled') NOT NULL DEFAULT 'pending',
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `journal_ref` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_return_history` (
  `history_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_return_item` (
  `return_item_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `price` decimal(15,4) NOT NULL,
  `total` decimal(15,4) NOT NULL,
  `reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quality_inspection` (
  `inspection_id` int(11) NOT NULL,
  `inspection_number` varchar(50) NOT NULL,
  `receipt_id` int(11) NOT NULL,
  `inspector_id` int(11) NOT NULL,
  `status` enum('pending','passed','failed','partially_passed') NOT NULL DEFAULT 'pending',
  `inspection_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quality_inspection_history` (
  `history_id` int(11) NOT NULL,
  `inspection_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quality_inspection_result` (
  `result_id` int(11) NOT NULL,
  `goods_receipt_id` int(11) NOT NULL,
  `receipt_item_id` int(11) NOT NULL,
  `checked_by` int(11) NOT NULL,
  `check_date` datetime NOT NULL,
  `result` enum('passed','failed') NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_queue_jobs` (
  `id` int(11) NOT NULL,
  `job` mediumtext NOT NULL,
  `status` enum('pending','processing','done','failed') NOT NULL DEFAULT 'pending',
  `attempts` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quotation_signatures` (
  `signature_id` int(11) NOT NULL,
  `reference_type` enum('quotation','comparison','purchase_order') NOT NULL,
  `reference_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `position` varchar(100) NOT NULL,
  `signature_date` datetime NOT NULL DEFAULT current_timestamp(),
  `signature_image` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `order_num` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_receipts` (
  `receipt_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `seller_rin` varchar(30) DEFAULT NULL,
  `seller_name` varchar(200) DEFAULT NULL,
  `buyer_type` varchar(1) DEFAULT NULL,
  `buyer_id` varchar(30) DEFAULT NULL,
  `buyer_name` varchar(100) DEFAULT NULL,
  `total_sales` decimal(13,5) DEFAULT NULL,
  `total_commercial_discount` decimal(13,5) DEFAULT NULL,
  `total_items_discount` decimal(13,5) DEFAULT NULL,
  `extra_discount` decimal(13,5) DEFAULT NULL,
  `net_amount` decimal(13,5) DEFAULT NULL,
  `fees_amount` decimal(13,5) DEFAULT NULL,
  `total_amount` decimal(13,5) DEFAULT NULL,
  `tax_totals` text DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `date_time_issued` datetime DEFAULT NULL,
  `submission_uuid` varchar(36) DEFAULT NULL,
  `status` enum('pending','submitted','accepted','rejected') DEFAULT 'pending',
  `rejection_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_recommendation_rule` (
  `rule_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `condition_type` enum('category','price_range','customer_group','purchase_history') NOT NULL,
  `condition_value` text NOT NULL,
  `recommendation_type` enum('upsell','cross_sell') NOT NULL,
  `product_ids` text NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_report_execution_log` (
  `execution_id` int(11) NOT NULL,
  `report_id` int(11) NOT NULL,
  `execution_start` datetime NOT NULL,
  `execution_end` datetime DEFAULT NULL,
  `status` enum('started','completed','failed') NOT NULL DEFAULT 'started',
  `file_path` varchar(255) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `sent_to` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return` (
  `return_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) NOT NULL,
  `email` varchar(96) NOT NULL,
  `telephone` varchar(32) NOT NULL,
  `product` varchar(255) NOT NULL,
  `model` varchar(64) NOT NULL,
  `quantity` int(11) NOT NULL,
  `opened` tinyint(1) NOT NULL,
  `return_reason_id` int(11) NOT NULL,
  `return_action_id` int(11) NOT NULL,
  `return_status_id` int(11) NOT NULL,
  `comment` mediumtext DEFAULT NULL,
  `date_ordered` date DEFAULT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_action` (
  `return_action_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_history` (
  `return_history_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `return_status_id` int(11) NOT NULL,
  `notify` tinyint(1) NOT NULL,
  `comment` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_reason` (
  `return_reason_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_status` (
  `return_status_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_review` (
  `review_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `author` varchar(64) NOT NULL,
  `text` mediumtext NOT NULL,
  `rating` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_risk_register` (
  `risk_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL COMMENT 'عنوان الخطر',
  `description` text DEFAULT NULL COMMENT 'تفاصيل الخطر',
  `risk_category` varchar(50) DEFAULT NULL COMMENT 'تصنيف الخطر: مالي، قانوني، تشغيل...',
  `likelihood` enum('low','medium','high') DEFAULT 'medium' COMMENT 'احتمالية الحدوث',
  `impact` enum('low','medium','high') DEFAULT 'medium' COMMENT 'تأثير الخطر',
  `owner_user_id` int(11) DEFAULT NULL COMMENT 'من المسؤول عن مراقبة هذا الخطر',
  `mitigation_plan` text DEFAULT NULL COMMENT 'خطة الحد من الخطر',
  `status` enum('open','mitigated','closed') DEFAULT 'open',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `owner_group_id` int(11) NOT NULL DEFAULT 0,
  `nature_of_risk` enum('ongoing','one_time') NOT NULL DEFAULT 'ongoing',
  `risk_start_date` date DEFAULT NULL,
  `risk_end_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_sales_forecast` (
  `forecast_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `forecast_date` date NOT NULL,
  `forecast_quantity` decimal(15,4) NOT NULL,
  `forecast_amount` decimal(15,4) NOT NULL,
  `confidence_level` decimal(5,2) NOT NULL DEFAULT 90.00,
  `method` varchar(50) NOT NULL DEFAULT 'moving_average',
  `actual_quantity` decimal(15,4) DEFAULT NULL,
  `variance_percentage` decimal(5,2) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_sales_quotation` (
  `quotation_id` int(11) NOT NULL,
  `quotation_number` varchar(50) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `quotation_date` date NOT NULL,
  `valid_until` date NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL,
  `converted_to_order` tinyint(1) NOT NULL DEFAULT 0,
  `order_id` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_sales_quotation_item` (
  `item_id` int(11) NOT NULL,
  `quotation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `price` decimal(15,4) NOT NULL,
  `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total` decimal(15,4) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_scheduled_report` (
  `report_id` int(11) NOT NULL,
  `report_name` varchar(255) NOT NULL,
  `report_type` varchar(100) NOT NULL,
  `parameters` text NOT NULL,
  `format` enum('pdf','excel','csv','html') NOT NULL DEFAULT 'pdf',
  `frequency` enum('daily','weekly','monthly','quarterly','yearly') NOT NULL DEFAULT 'monthly',
  `next_run` datetime NOT NULL,
  `last_run` datetime DEFAULT NULL,
  `recipients` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_dashboard_widget` tinyint(1) NOT NULL DEFAULT 0,
  `widget_title` varchar(100) DEFAULT NULL,
  `widget_type` varchar(20) DEFAULT 'table',
  `access_groups` text DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_internal_link` (
  `link_id` int(11) NOT NULL,
  `source_page` varchar(255) NOT NULL,
  `target_page` varchar(255) NOT NULL,
  `anchor_text` varchar(255) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_keyword_tracking` (
  `tracking_id` int(11) NOT NULL,
  `keyword` varchar(255) NOT NULL,
  `search_engine` varchar(50) NOT NULL DEFAULT 'google',
  `position` int(11) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `last_checked` datetime NOT NULL,
  `previous_position` int(11) DEFAULT NULL,
  `status` enum('improved','declined','unchanged','new') NOT NULL DEFAULT 'new'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_page_analysis` (
  `analysis_id` int(11) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `target_keyword` varchar(255) NOT NULL,
  `title_score` int(11) NOT NULL DEFAULT 0,
  `meta_score` int(11) NOT NULL DEFAULT 0,
  `content_score` int(11) NOT NULL DEFAULT 0,
  `technical_score` int(11) NOT NULL DEFAULT 0,
  `overall_score` int(11) NOT NULL DEFAULT 0,
  `suggestions` text DEFAULT NULL,
  `date_analysis` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_settings` (
  `setting_id` int(11) NOT NULL,
  `code` varchar(128) NOT NULL,
  `key` varchar(128) NOT NULL,
  `value` text NOT NULL,
  `serialized` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_url` (
  `seo_url_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `query` varchar(255) NOT NULL,
  `keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_session` (
  `session_id` varchar(32) NOT NULL,
  `data` mediumtext NOT NULL,
  `expire` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_setting` (
  `setting_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `code` varchar(128) NOT NULL,
  `key` varchar(128) NOT NULL,
  `value` mediumtext NOT NULL,
  `serialized` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_company` (
  `company_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `website` varchar(255) DEFAULT NULL,
  `contact_name` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(50) DEFAULT NULL,
  `contact_email` varchar(100) DEFAULT NULL,
  `api_url` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `logo` varchar(255) DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `tracking_url_template` varchar(255) DEFAULT NULL COMMENT 'قالب URL لتتبع الشحنة: {tracking_number}',
  `supports_cod` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'يدعم الدفع عند الاستلام',
  `supports_api` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'يدعم الربط عبر API',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_company_config` (
  `config_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `key` varchar(100) NOT NULL,
  `value` text NOT NULL,
  `is_sensitive` tinyint(1) NOT NULL DEFAULT 0,
  `environment` enum('live','test','both') NOT NULL DEFAULT 'both',
  `last_updated` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_courier` (
  `shipping_courier_id` int(11) NOT NULL,
  `shipping_courier_code` varchar(255) NOT NULL DEFAULT '',
  `shipping_courier_name` varchar(255) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_coverage` (
  `coverage_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `zone_id` int(11) DEFAULT NULL COMMENT 'المنطقة من جدول zone',
  `country_id` int(11) DEFAULT NULL COMMENT 'الدولة من جدول country',
  `geo_zone_id` int(11) DEFAULT NULL COMMENT 'المنطقة الجغرافية',
  `city` varchar(100) DEFAULT NULL COMMENT 'مدينة محددة',
  `area` varchar(100) DEFAULT NULL COMMENT 'منطقة محددة',
  `zip_code` varchar(20) DEFAULT NULL COMMENT 'الرمز البريدي',
  `priority` enum('primary','secondary','backup') NOT NULL DEFAULT 'primary',
  `delivery_days` int(11) NOT NULL DEFAULT 1 COMMENT 'أيام التوصيل المتوقعة',
  `delivery_days_max` int(11) DEFAULT NULL COMMENT 'الحد الأقصى لأيام التوصيل',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_order` (
  `shipping_order_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `tracking_number` varchar(100) DEFAULT NULL,
  `shipment_date` datetime DEFAULT NULL,
  `scheduled_delivery_date` date DEFAULT NULL,
  `actual_delivery_date` datetime DEFAULT NULL,
  `shipping_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cod_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ الدفع عند الاستلام',
  `status` enum('pending','processed','shipped','in_transit','delivered','returned','failed','cancelled') NOT NULL DEFAULT 'pending',
  `package_weight` decimal(10,2) DEFAULT NULL,
  `package_dimensions` varchar(50) DEFAULT NULL COMMENT 'الأبعاد بالسنتيمتر: طول×عرض×ارتفاع',
  `shipping_label_url` varchar(255) DEFAULT NULL,
  `invoice_url` varchar(255) DEFAULT NULL,
  `awb_number` varchar(50) DEFAULT NULL COMMENT 'رقم بوليصة الشحن',
  `api_response` mediumtext DEFAULT NULL COMMENT 'استجابة API للشحن',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_rate` (
  `rate_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `coverage_id` int(11) DEFAULT NULL,
  `weight_from` decimal(10,2) NOT NULL DEFAULT 0.00,
  `weight_to` decimal(10,2) DEFAULT NULL,
  `price` decimal(15,4) NOT NULL,
  `price_type` enum('fixed','per_kg','percentage') NOT NULL DEFAULT 'fixed',
  `min_price` decimal(15,4) DEFAULT NULL,
  `max_price` decimal(15,4) DEFAULT NULL,
  `additional_kg_price` decimal(15,4) DEFAULT NULL COMMENT 'سعر الكيلو الإضافي',
  `cod_fee` decimal(15,4) DEFAULT NULL COMMENT 'رسوم الدفع عند الاستلام',
  `cod_fee_type` enum('fixed','percentage') DEFAULT NULL,
  `effective_from` date NOT NULL,
  `effective_to` date DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_settlement` (
  `settlement_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `settlement_date` date NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `total_orders` int(11) NOT NULL DEFAULT 0,
  `total_cod_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `shipping_fees` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cod_fees` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ الصافي للتسوية',
  `payment_direction` enum('to_company','from_company') NOT NULL COMMENT 'اتجاه الدفع: لنا أو علينا',
  `status` enum('pending','reconciled','disputed') NOT NULL DEFAULT 'pending',
  `bank_transaction_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `reconciled_by` int(11) DEFAULT NULL,
  `reconciled_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_settlement_order` (
  `settlement_order_id` int(11) NOT NULL,
  `settlement_id` int(11) NOT NULL,
  `shipping_order_id` int(11) NOT NULL,
  `cod_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `shipping_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cod_fee` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` enum('matched','unmatched','disputed') NOT NULL DEFAULT 'matched',
  `discrepancy_amount` decimal(15,4) DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_tracking` (
  `tracking_id` int(11) NOT NULL,
  `shipping_order_id` int(11) NOT NULL,
  `status` varchar(100) NOT NULL,
  `status_details` text DEFAULT NULL,
  `tracking_date` datetime NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `agent_name` varchar(100) DEFAULT NULL,
  `source` enum('api','manual','customer') NOT NULL DEFAULT 'api',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL COMMENT 'NULL إذا كان من API'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_statistics` (
  `statistics_id` int(11) NOT NULL,
  `code` varchar(64) NOT NULL,
  `value` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_adjustment` (
  `adjustment_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `adjustment_number` varchar(50) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `type` enum('increase','decrease') NOT NULL,
  `status` enum('draft','approved','cancelled') NOT NULL DEFAULT 'draft',
  `adjustment_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_adjustment_history` (
  `history_id` int(11) NOT NULL,
  `adjustment_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_adjustment_item` (
  `adjustment_item_id` int(11) NOT NULL,
  `adjustment_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_count` (
  `stock_count_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL COMMENT 'الفرع/المخزن الذي يتم الجرد فيه',
  `reference_code` varchar(50) DEFAULT NULL COMMENT 'يمكن تخزين رقم الجرد أو رمز معين لاستخدامه في الطباعة',
  `count_date` date NOT NULL COMMENT 'تاريخ الجرد الفعلي',
  `status` enum('draft','in_progress','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT 'الحالة: تحت الإنشاء، قيد الجرد، منتهية، ملغاة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات عامة حول الجرد',
  `created_by` int(11) NOT NULL COMMENT 'منشئ جلسة الجرد',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_count_item` (
  `count_item_id` int(11) NOT NULL,
  `stock_count_id` int(11) NOT NULL COMMENT 'FK إلى رأس الجرد',
  `product_id` int(11) NOT NULL COMMENT 'رقم المنتج',
  `unit_id` int(11) NOT NULL COMMENT 'الوحدة التي يتم الجرد بها',
  `system_qty` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية المسجلة في النظام قبل الجرد',
  `counted_qty` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية التي وجدها الموظف فعليًا',
  `difference` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT '(counted_qty - system_qty)',
  `barcode` varchar(255) DEFAULT NULL COMMENT 'في حال تم إدخال الباركود أثناء عملية الجرد، للبحث السريع',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_status` (
  `stock_status_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_transfer` (
  `transfer_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `transfer_number` varchar(50) NOT NULL,
  `from_branch_id` int(11) NOT NULL,
  `to_branch_id` int(11) NOT NULL,
  `status` enum('pending','in_transit','completed','cancelled') NOT NULL DEFAULT 'pending',
  `transfer_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_transfer_history` (
  `history_id` int(11) NOT NULL,
  `transfer_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_transfer_item` (
  `transfer_item_id` int(11) NOT NULL,
  `transfer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_store` (
  `store_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `url` varchar(255) NOT NULL,
  `ssl` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier` (
  `supplier_id` int(11) NOT NULL,
  `account_code` bigint(20) DEFAULT NULL,
  `supplier_group_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `language_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `password` varchar(40) NOT NULL,
  `salt` varchar(9) NOT NULL,
  `cart` mediumtext DEFAULT NULL,
  `wishlist` mediumtext DEFAULT NULL,
  `newsletter` tinyint(1) NOT NULL DEFAULT 0,
  `address_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL,
  `ip` varchar(40) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `safe` tinyint(1) NOT NULL,
  `token` mediumtext NOT NULL,
  `code` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_address` (
  `address_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `company` varchar(40) DEFAULT NULL,
  `address_1` varchar(128) NOT NULL,
  `address_2` varchar(128) NOT NULL,
  `city` varchar(128) NOT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `country_id` int(11) NOT NULL DEFAULT 63,
  `zone_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_evaluation` (
  `evaluation_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `evaluator_id` int(11) NOT NULL,
  `evaluation_date` date NOT NULL,
  `quality_score` decimal(3,2) NOT NULL,
  `delivery_score` decimal(3,2) NOT NULL,
  `price_score` decimal(3,2) NOT NULL,
  `service_score` decimal(3,2) NOT NULL,
  `overall_score` decimal(3,2) NOT NULL,
  `comments` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_group` (
  `supplier_group_id` int(11) NOT NULL,
  `approval` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_group_description` (
  `supplier_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_invoice` (
  `invoice_id` int(11) NOT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `po_id` int(11) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  `invoice_date` date NOT NULL,
  `due_date` date NOT NULL,
  `subtotal` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) NOT NULL,
  `discount_amount` decimal(15,4) NOT NULL,
  `total_amount` decimal(15,4) NOT NULL,
  `status` enum('pending','approved','paid','cancelled') NOT NULL DEFAULT 'pending',
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` int(11) NOT NULL,
  `journal_ref` int(11) DEFAULT NULL,
  `matching_status` enum('pending','matched','partial','mismatch') NOT NULL DEFAULT 'pending' COMMENT 'حالة المطابقة مع الاستلام وأمر الشراء',
  `payment_status` enum('unpaid','partially_paid','paid') NOT NULL DEFAULT 'unpaid' COMMENT 'حالة السداد',
  `paid_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'المبلغ المسدد',
  `last_payment_date` datetime DEFAULT NULL COMMENT 'تاريخ آخر سداد'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_invoice_history` (
  `history_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_invoice_item` (
  `invoice_item_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `po_item_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL,
  `discount_rate` decimal(5,2) NOT NULL,
  `total_price` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_product_price` (
  `price_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL DEFAULT 1,
  `price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `min_quantity` decimal(15,4) DEFAULT NULL COMMENT 'الحد الأدنى للكمية بهذا السعر',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل هذا هو السعر الافتراضي',
  `start_date` date DEFAULT NULL COMMENT 'تاريخ بداية السعر',
  `end_date` date DEFAULT NULL COMMENT 'تاريخ نهاية السعر',
  `last_purchase_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_system_events` (
  `event_id` int(11) NOT NULL,
  `event_type` varchar(50) NOT NULL COMMENT 'نوع الحدث',
  `event_action` varchar(50) NOT NULL COMMENT 'الإجراء المتخذ',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `event_data` text DEFAULT NULL COMMENT 'بيانات الحدث بتنسيق JSON',
  `user_id` int(11) DEFAULT NULL COMMENT 'المستخدم المسبب للحدث',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'عنوان IP',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_system_notifications` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_group_id` int(11) DEFAULT NULL,
  `type` enum('info','warning','error','success','alert') NOT NULL DEFAULT 'info',
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expiry_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_task` (
  `task_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `assigned_to` int(11) NOT NULL COMMENT 'User ID the task is assigned to',
  `assigned_by` int(11) NOT NULL COMMENT 'User ID who assigned the task',
  `due_date` date DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `completed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = pending, 1 = completed',
  `completed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'Optional: Module related to the task (e.g., order, customer)',
  `reference_id` int(11) DEFAULT NULL COMMENT 'Optional: ID related to the module'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores user tasks';

CREATE TABLE `cod_tax_class` (
  `tax_class_id` int(11) NOT NULL,
  `eta_tax_type` varchar(10) DEFAULT 'T1',
  `eta_tax_subtype` varchar(10) DEFAULT 'V009',
  `title` varchar(32) NOT NULL,
  `description` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_tax_rate` (
  `tax_rate_id` int(11) NOT NULL,
  `geo_zone_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(32) NOT NULL,
  `rate` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `type` char(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_tax_rate_to_customer_group` (
  `tax_rate_id` int(11) NOT NULL,
  `customer_group_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_tax_rule` (
  `tax_rule_id` int(11) NOT NULL,
  `tax_class_id` int(11) NOT NULL,
  `tax_rate_id` int(11) NOT NULL,
  `based` varchar(10) NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_theme` (
  `theme_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `theme` varchar(64) NOT NULL,
  `route` varchar(64) NOT NULL,
  `code` longtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_translation` (
  `translation_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `route` varchar(64) NOT NULL,
  `key` varchar(64) NOT NULL,
  `value` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unavailability_reasons` (
  `reason_id` int(11) NOT NULL,
  `reason_code` varchar(20) NOT NULL COMMENT 'كود السبب',
  `reason_name_ar` varchar(100) NOT NULL COMMENT 'اسم السبب بالعربية',
  `reason_name_en` varchar(100) NOT NULL COMMENT 'اسم السبب بالإنجليزية',
  `status_type` enum('maintenance','quality_check','damaged','expired','quarantine') NOT NULL,
  `description_ar` text DEFAULT NULL COMMENT 'وصف بالعربية',
  `description_en` text DEFAULT NULL COMMENT 'وصف بالإنجليزية',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط',
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'يحتاج موافقة',
  `auto_expire_days` int(11) DEFAULT NULL COMMENT 'انتهاء تلقائي بالأيام',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'ترتيب العرض',
  `created_by` int(11) NOT NULL COMMENT 'المنشئ',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unified_document` (
  `document_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `document_type` enum('contract','invoice','report','proposal','policy','other') NOT NULL DEFAULT 'other',
  `file_path` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_size` int(10) UNSIGNED NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `version` varchar(20) NOT NULL DEFAULT '1.0',
  `status` enum('draft','pending_approval','approved','rejected','archived') NOT NULL DEFAULT 'draft',
  `tags` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `creator_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `parent_document_id` int(11) DEFAULT NULL COMMENT 'المستند الأب في حالة الإصدارات',
  `expiry_date` date DEFAULT NULL COMMENT 'تاريخ انتهاء الصلاحية إن وجد',
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, customer',
  `reference_id` int(11) DEFAULT NULL COMMENT 'المعرف المرتبط بالمودل',
  `is_template` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل المستند قالب',
  `approval_workflow_id` int(11) DEFAULT NULL COMMENT 'معرف سير عمل الموافقة إن وجد'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unified_notification` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` enum('info','success','warning','error','approval','reminder','system') NOT NULL DEFAULT 'info',
  `module` varchar(50) NOT NULL COMMENT 'المودل المُنشئ للإشعار',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع مثل message, order, approval',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `read_at` datetime DEFAULT NULL,
  `expiry_at` datetime DEFAULT NULL,
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `channels` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'قنوات الإرسال' CHECK (json_valid(`channels`)),
  `scheduled_at` datetime DEFAULT NULL COMMENT 'موعد الإرسال المجدول',
  `sent_at` datetime DEFAULT NULL COMMENT 'وقت الإرسال الفعلي',
  `delivery_status` enum('pending','sent','delivered','failed') NOT NULL DEFAULT 'pending',
  `action_url` varchar(255) DEFAULT NULL COMMENT 'الرابط للعمل المطلوب',
  `created_by` int(11) DEFAULT NULL COMMENT 'المستخدم المنشئ أو NULL للنظام'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول الإشعارات الموحدة المحدث';

CREATE TABLE `cod_unified_workflow` (
  `workflow_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `workflow_type` enum('document_approval','purchase_approval','leave_request','expense_claim','payment_approval','other') NOT NULL,
  `status` enum('active','inactive','archived') NOT NULL DEFAULT 'active',
  `creator_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_template` tinyint(1) NOT NULL DEFAULT 0,
  `department_id` int(11) DEFAULT NULL,
  `escalation_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل تصعيد الطلبات',
  `escalation_after_days` int(11) DEFAULT NULL COMMENT 'التصعيد بعد عدد أيام',
  `notify_creator` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'إشعار المنشئ بالتغييرات'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unit` (
  `unit_id` int(11) NOT NULL,
  `code` varchar(10) NOT NULL,
  `desc_en` varchar(255) NOT NULL,
  `desc_ar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unit_conversion_history` (
  `conversion_history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `from_unit_id` int(11) NOT NULL,
  `to_unit_id` int(11) NOT NULL,
  `from_quantity` decimal(15,4) NOT NULL,
  `to_quantity` decimal(15,4) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unit_conversion_log` (
  `conversion_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `from_unit_id` int(11) NOT NULL,
  `to_unit_id` int(11) NOT NULL,
  `from_quantity` decimal(15,4) NOT NULL,
  `to_quantity` decimal(15,4) NOT NULL,
  `conversion_factor` decimal(15,4) NOT NULL,
  `conversion_type` enum('manual','auto_sale','auto_bundle','auto_sync') NOT NULL,
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف العملية المرجعية',
  `converted_by` int(11) DEFAULT NULL,
  `converted_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='سجل تحويلات الوحدات';

CREATE TABLE `cod_upload` (
  `upload_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user` (
  `user_id` int(11) NOT NULL,
  `user_group_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL DEFAULT 2,
  `username` varchar(20) NOT NULL,
  `password` varchar(40) NOT NULL,
  `salt` varchar(9) NOT NULL,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) NOT NULL,
  `email` varchar(96) NOT NULL,
  `image` varchar(255) NOT NULL,
  `code` varchar(40) NOT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `ip` varchar(40) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT 0,
  `two_factor_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل المصادقة الثنائية',
  `two_factor_secret` varchar(32) DEFAULT NULL COMMENT 'مفتاح المصادقة الثنائية',
  `two_factor_backup_codes` text DEFAULT NULL COMMENT 'رموز النسخ الاحتياطي',
  `two_factor_last_used` datetime DEFAULT NULL COMMENT 'آخر استخدام للمصادقة الثنائية',
  `phone_number` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف للرسائل النصية',
  `phone_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تأكيد رقم الهاتف'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_2fa_attempts` (
  `attempt_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `attempt_type` enum('totp','sms','email','backup') NOT NULL COMMENT 'نوع المحاولة',
  `code_entered` varchar(10) NOT NULL COMMENT 'الرمز المدخل',
  `is_successful` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'نجح أم فشل',
  `ip_address` varchar(45) NOT NULL COMMENT 'عنوان IP',
  `user_agent` varchar(255) DEFAULT NULL COMMENT 'معلومات المتصفح',
  `attempt_time` datetime NOT NULL DEFAULT current_timestamp(),
  `failure_reason` varchar(100) DEFAULT NULL COMMENT 'سبب الفشل'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `cod_user_activity_log` (
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `ip` varchar(40) NOT NULL,
  `date_activity` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_group` (
  `user_group_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `permission` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_group_permission` (
  `user_group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_kpi_assignment` (
  `assignment_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT 'FK referencing cod_user.user_id',
  `kpi_code` varchar(50) NOT NULL COMMENT 'Matches kpi_code in cod_dashboard_kpi',
  `assigned_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Links users to specific KPIs they can view or manage';

CREATE TABLE `cod_user_login_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `login_time` datetime NOT NULL DEFAULT current_timestamp(),
  `logout_time` datetime DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `failure_reason` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_notification_preferences` (
  `preference_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'تفضيلات الإشعارات للمستخدم' CHECK (json_valid(`preferences`)),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول إعدادات الإشعارات للمستخدمين';

CREATE TABLE `cod_user_permission` (
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_session` (
  `session_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `user_agent` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_trusted_devices` (
  `device_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `device_fingerprint` varchar(64) NOT NULL COMMENT 'بصمة الجهاز',
  `device_name` varchar(100) NOT NULL COMMENT 'اسم الجهاز',
  `ip_address` varchar(45) NOT NULL COMMENT 'عنوان IP',
  `user_agent` varchar(255) NOT NULL COMMENT 'معلومات المتصفح',
  `location` varchar(100) DEFAULT NULL COMMENT 'الموقع الجغرافي',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط أم لا',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `last_used_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `expires_at` datetime NOT NULL COMMENT 'تاريخ انتهاء الثقة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='الأجهزة الموثوقة للمستخدمين';

CREATE TABLE `cod_user_verification_codes` (
  `code_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `code` varchar(10) NOT NULL COMMENT 'رمز التحقق',
  `code_type` enum('sms','email','phone_verification') NOT NULL COMMENT 'نوع الرمز',
  `purpose` enum('2fa','phone_verify','password_reset','login') NOT NULL COMMENT 'الغرض من الرمز',
  `is_used` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تم استخدامه أم لا',
  `attempts_count` int(11) NOT NULL DEFAULT 0 COMMENT 'عدد المحاولات',
  `max_attempts` int(11) NOT NULL DEFAULT 3 COMMENT 'الحد الأقصى للمحاولات',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL COMMENT 'تاريخ انتهاء الصلاحية',
  `used_at` datetime DEFAULT NULL COMMENT 'تاريخ الاستخدام',
  `ip_address` varchar(45) NOT NULL COMMENT 'عنوان IP'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `cod_vendor_payment` (
  `payment_id` int(11) NOT NULL,
  `payment_number` varchar(50) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  `payment_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `payment_method` enum('cash','bank_transfer','cheque','credit_card') NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` int(11) NOT NULL,
  `invoice_id` int(11) DEFAULT NULL COMMENT 'معرف فاتورة المورد',
  `po_id` int(11) DEFAULT NULL COMMENT 'معرف أمر الشراء',
  `currency_id` int(11) DEFAULT NULL COMMENT 'العملة',
  `exchange_rate` decimal(15,6) DEFAULT NULL COMMENT 'سعر الصرف',
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_virtual_inventory_log` (
  `log_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `old_quantity_available` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `new_quantity_available` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `change_reason` enum('manual','auto_sync','bundle_update','promotion','order_reserve','order_cancel') NOT NULL,
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف مرجعي (طلب، عرض، إلخ)',
  `notes` text DEFAULT NULL,
  `changed_by` int(11) NOT NULL,
  `changed_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='سجل تغييرات المخزون الوهمي';

CREATE TABLE `cod_visitors_stats` (
  `visit_date` date NOT NULL,
  `visits` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher` (
  `voucher_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `code` varchar(10) NOT NULL,
  `from_name` varchar(64) NOT NULL,
  `from_email` varchar(96) NOT NULL,
  `to_name` varchar(64) NOT NULL,
  `to_email` varchar(96) NOT NULL,
  `voucher_theme_id` int(11) NOT NULL,
  `message` mediumtext NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher_history` (
  `voucher_history_id` int(11) NOT NULL,
  `voucher_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher_theme` (
  `voucher_theme_id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher_theme_description` (
  `voucher_theme_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_warranty` (
  `warranty_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `warranty_status` enum('active','expired','claimed','void') NOT NULL DEFAULT 'active',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_weight_class` (
  `weight_class_id` int(11) NOT NULL,
  `value` decimal(15,8) NOT NULL DEFAULT 0.00000000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_weight_class_description` (
  `weight_class_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(32) NOT NULL,
  `unit` varchar(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_workflow_approval` (
  `approval_id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `step_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` enum('approved','rejected','delegated','commented') NOT NULL,
  `comment` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `delegated_to` int(11) DEFAULT NULL COMMENT 'تفويض الموافقة لمستخدم آخر'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_workflow_request` (
  `request_id` int(11) NOT NULL,
  `workflow_id` int(11) NOT NULL,
  `current_step_id` int(11) DEFAULT NULL,
  `requester_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('draft','pending','in_progress','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `reference_module` varchar(50) NOT NULL COMMENT 'المودل المرتبط مثل document, purchase',
  `reference_id` int(11) NOT NULL COMMENT 'معرف المرجع',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `completed_at` datetime DEFAULT NULL,
  `due_date` datetime DEFAULT NULL COMMENT 'الموعد النهائي للطلب'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_workflow_step` (
  `step_id` int(11) NOT NULL,
  `workflow_id` int(11) NOT NULL,
  `step_name` varchar(255) NOT NULL,
  `step_order` int(11) NOT NULL,
  `approver_user_id` int(11) DEFAULT NULL COMMENT 'المستخدم المعتمد أو NULL لمجموعة',
  `approver_group_id` int(11) DEFAULT NULL COMMENT 'المجموعة المعتمدة أو NULL لمستخدم',
  `approval_type` enum('any_one','all','percentage','sequential') NOT NULL DEFAULT 'any_one',
  `approval_percentage` int(11) DEFAULT NULL,
  `is_final_step` tinyint(1) NOT NULL DEFAULT 0,
  `on_reject_goto_step` int(11) DEFAULT NULL COMMENT 'الخطوة التالية عند الرفض',
  `instructions` text DEFAULT NULL,
  `deadline_days` int(11) DEFAULT NULL COMMENT 'مهلة الموافقة بالأيام',
  `reminder_days` int(11) DEFAULT NULL COMMENT 'تذكير قبل الموعد النهائي بأيام'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_zone` (
  `zone_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL,
  `code` varchar(32) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_zone_distance` (
  `zone_distance_id` int(11) NOT NULL,
  `from_zone_id` int(11) NOT NULL COMMENT 'المحافظة المرسلة',
  `to_zone_id` int(11) NOT NULL COMMENT 'المحافظة المستقبلة',
  `distance_km` decimal(8,2) NOT NULL COMMENT 'المسافة بالكيلومتر',
  `travel_time_hours` decimal(5,2) NOT NULL COMMENT 'وقت السفر بالساعات',
  `road_quality` enum('excellent','good','fair','poor') NOT NULL DEFAULT 'good',
  `toll_cost` decimal(8,2) NOT NULL DEFAULT 0.00,
  `fuel_cost_estimate` decimal(8,2) NOT NULL DEFAULT 0.00,
  `is_direct_route` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_zone_to_geo_zone` (
  `zone_to_geo_zone_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `zone_id` int(11) NOT NULL DEFAULT 0,
  `geo_zone_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
