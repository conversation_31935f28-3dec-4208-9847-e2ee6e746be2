# 1️⃣2️⃣ إدارة الاشتراكات SaaS

## 🎯 الهدف من نظام الاشتراكات
تحويل AYM ERP إلى منصة SaaS قابلة للتوسع مع نظام اشتراكات مرن يدعم خطط متنوعة ونمو الأعمال.

## 💳 نظام الاشتراكات المتقدم

### **📋 1. خطط الاشتراك (Subscription Plans)**

#### **🥉 الخطة الأساسية (Basic Plan) - 299 ج.م/شهر**
- **المستخدمين:** حتى 3 مستخدمين
- **الفروع:** فرع واحد
- **المنتجات:** حتى 1,000 منتج
- **المعاملات:** حتى 500 معاملة شهرياً
- **التخزين:** 5 جيجابايت
- **الدعم:** دعم بريد إلكتروني
- **الميزات:**
  - المحاسبة الأساسية
  - إدارة المخزون البسيطة
  - نقطة بيع واحدة
  - تقارير أساسية

#### **🥈 الخطة المهنية (Professional Plan) - 599 ج.م/شهر**
- **المستخدمين:** حتى 10 مستخدمين
- **الفروع:** حتى 3 فروع
- **المنتجات:** حتى 10,000 منتج
- **المعاملات:** حتى 2,000 معاملة شهرياً
- **التخزين:** 25 جيجابايت
- **الدعم:** دعم هاتفي + بريد إلكتروني
- **الميزات:**
  - جميع ميزات الخطة الأساسية
  - المحاسبة المتقدمة
  - إدارة المخزون المتقدمة
  - نقاط بيع متعددة
  - التجارة الإلكترونية
  - تطبيق الموبايل
  - تقارير متقدمة

#### **🥇 الخطة المؤسسية (Enterprise Plan) - 1,199 ج.م/شهر**
- **المستخدمين:** مستخدمين غير محدودين
- **الفروع:** فروع غير محدودة
- **المنتجات:** منتجات غير محدودة
- **المعاملات:** معاملات غير محدودة
- **التخزين:** 100 جيجابايت
- **الدعم:** دعم مخصص 24/7
- **الميزات:**
  - جميع ميزات الخطة المهنية
  - الذكاء الاصطناعي
  - سير العمل المتقدم
  - التكامل مع ETA
  - APIs مفتوحة
  - تخصيص كامل
  - تدريب مخصص

#### **💎 الخطة المخصصة (Custom Plan) - حسب الطلب**
- **مصممة خصيصاً:** حسب احتياجات العميل
- **تكامل خاص:** مع أنظمة العميل الحالية
- **دعم مخصص:** فريق دعم مخصص
- **SLA مضمون:** اتفاقية مستوى خدمة
- **تطوير مخصص:** ميزات خاصة بالعميل

### **💰 2. نظام الفوترة المرن**

#### **🔄 دورات الفوترة:**
- **شهرية:** دفع كل شهر
- **ربع سنوية:** خصم 5%
- **نصف سنوية:** خصم 10%
- **سنوية:** خصم 15%

#### **📊 الفوترة حسب الاستخدام (Usage-Based Billing):**
```php
<?php
/**
 * نظام الفوترة حسب الاستخدام
 */
class UsageBasedBilling {
    
    public function calculateMonthlyBill($tenant_id, $month, $year) {
        $base_plan = $this->getTenantPlan($tenant_id);
        $usage = $this->getMonthlyUsage($tenant_id, $month, $year);
        
        $bill = [
            'base_amount' => $base_plan['monthly_price'],
            'overages' => [],
            'total_overages' => 0,
            'total_amount' => $base_plan['monthly_price']
        ];
        
        // حساب التجاوزات
        if ($usage['users'] > $base_plan['max_users']) {
            $extra_users = $usage['users'] - $base_plan['max_users'];
            $overage_cost = $extra_users * 50; // 50 ج.م لكل مستخدم إضافي
            $bill['overages']['users'] = $overage_cost;
            $bill['total_overages'] += $overage_cost;
        }
        
        if ($usage['transactions'] > $base_plan['max_transactions']) {
            $extra_transactions = $usage['transactions'] - $base_plan['max_transactions'];
            $overage_cost = ceil($extra_transactions / 100) * 10; // 10 ج.م لكل 100 معاملة إضافية
            $bill['overages']['transactions'] = $overage_cost;
            $bill['total_overages'] += $overage_cost;
        }
        
        if ($usage['storage_gb'] > $base_plan['max_storage_gb']) {
            $extra_storage = $usage['storage_gb'] - $base_plan['max_storage_gb'];
            $overage_cost = $extra_storage * 5; // 5 ج.م لكل جيجابايت إضافي
            $bill['overages']['storage'] = $overage_cost;
            $bill['total_overages'] += $overage_cost;
        }
        
        $bill['total_amount'] += $bill['total_overages'];
        
        return $bill;
    }
}
```

### **💳 3. طرق الدفع المتعددة**

#### **البطاقات الائتمانية:**
- **فيزا (Visa)**
- **ماستركارد (Mastercard)**
- **أمريكان إكسبريس (American Express)**

#### **المحافظ الإلكترونية:**
- **فوري (Fawry)**
- **فودافون كاش (Vodafone Cash)**
- **أورانج موني (Orange Money)**
- **إتصالات كاش (Etisalat Cash)**

#### **التحويل البنكي:**
- **التحويل المباشر**
- **الإيداع البنكي**
- **الشيكات**

#### **تكامل بوابات الدفع:**
```php
<?php
/**
 * تكامل بوابات الدفع المصرية
 */
class PaymentGatewayIntegration {
    
    public function processFawryPayment($amount, $customer_data, $subscription_id) {
        $fawry_api = new FawryAPI($this->fawry_merchant_code, $this->fawry_security_key);
        
        $payment_request = [
            'merchantRefNum' => 'SUB_' . $subscription_id . '_' . time(),
            'customerMobile' => $customer_data['mobile'],
            'customerEmail' => $customer_data['email'],
            'customerName' => $customer_data['name'],
            'chargeItems' => [
                [
                    'itemId' => 'subscription_' . $subscription_id,
                    'description' => 'AYM ERP Subscription',
                    'price' => $amount,
                    'quantity' => 1
                ]
            ],
            'returnUrl' => $this->base_url . '/payment/fawry/return',
            'authCaptureModePayment' => false
        ];
        
        return $fawry_api->createPayment($payment_request);
    }
    
    public function processCardPayment($amount, $card_data, $subscription_id) {
        $paymob_api = new PaymobAPI($this->paymob_api_key);
        
        $payment_request = [
            'amount_cents' => $amount * 100, // تحويل إلى قروش
            'currency' => 'EGP',
            'billing_data' => [
                'first_name' => $card_data['first_name'],
                'last_name' => $card_data['last_name'],
                'email' => $card_data['email'],
                'phone_number' => $card_data['phone']
            ],
            'items' => [
                [
                    'name' => 'AYM ERP Subscription',
                    'amount_cents' => $amount * 100,
                    'description' => 'Monthly subscription',
                    'quantity' => 1
                ]
            ]
        ];
        
        return $paymob_api->createPayment($payment_request);
    }
}
```

## 🏢 إدارة المستأجرين (Tenant Management)

### **🏗️ 1. بنية متعددة المستأجرين**

#### **عزل البيانات:**
```php
<?php
/**
 * نظام عزل البيانات للمستأجرين
 */
class TenantDataIsolation {
    
    private $current_tenant_id;
    
    public function setCurrentTenant($tenant_id) {
        $this->current_tenant_id = $tenant_id;
        
        // تعيين قاعدة البيانات الخاصة بالمستأجر
        $this->switchDatabase($tenant_id);
        
        // تعيين متغيرات الجلسة
        $_SESSION['tenant_id'] = $tenant_id;
        $_SESSION['tenant_domain'] = $this->getTenantDomain($tenant_id);
    }
    
    private function switchDatabase($tenant_id) {
        $tenant_db = 'aym_erp_tenant_' . $tenant_id;
        
        // التحقق من وجود قاعدة البيانات
        if (!$this->databaseExists($tenant_db)) {
            $this->createTenantDatabase($tenant_id);
        }
        
        // تبديل الاتصال
        $this->db->switchDatabase($tenant_db);
    }
    
    private function createTenantDatabase($tenant_id) {
        $tenant_db = 'aym_erp_tenant_' . $tenant_id;
        
        // إنشاء قاعدة البيانات
        $this->db->query("CREATE DATABASE `{$tenant_db}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // نسخ الهيكل من القالب
        $this->copyDatabaseStructure('aym_erp_template', $tenant_db);
        
        // إدراج البيانات الأساسية
        $this->insertDefaultData($tenant_db, $tenant_id);
    }
}
```

### **📊 2. مراقبة الاستخدام**

#### **تتبع الموارد:**
```php
<?php
/**
 * مراقبة استخدام الموارد
 */
class ResourceMonitoring {
    
    public function trackUsage($tenant_id, $resource_type, $amount = 1) {
        $usage_data = [
            'tenant_id' => $tenant_id,
            'resource_type' => $resource_type, // users, transactions, storage, api_calls
            'amount' => $amount,
            'date' => date('Y-m-d'),
            'hour' => date('H'),
            'timestamp' => time()
        ];
        
        $this->db->query("INSERT INTO cod_usage_tracking SET " . $this->buildInsertQuery($usage_data));
        
        // فحص الحدود
        $this->checkLimits($tenant_id, $resource_type);
    }
    
    public function getMonthlyUsage($tenant_id, $month, $year) {
        $start_date = "{$year}-{$month}-01";
        $end_date = date('Y-m-t', strtotime($start_date));
        
        $query = "
            SELECT 
                resource_type,
                SUM(amount) as total_usage
            FROM cod_usage_tracking 
            WHERE tenant_id = '{$tenant_id}' 
            AND date BETWEEN '{$start_date}' AND '{$end_date}'
            GROUP BY resource_type
        ";
        
        $results = $this->db->query($query);
        
        $usage = [
            'users' => 0,
            'transactions' => 0,
            'storage_gb' => 0,
            'api_calls' => 0
        ];
        
        foreach ($results->rows as $row) {
            $usage[$row['resource_type']] = intval($row['total_usage']);
        }
        
        return $usage;
    }
    
    private function checkLimits($tenant_id, $resource_type) {
        $tenant_plan = $this->getTenantPlan($tenant_id);
        $current_usage = $this->getCurrentUsage($tenant_id, $resource_type);
        
        $limit_key = "max_{$resource_type}";
        $limit = $tenant_plan[$limit_key] ?? 0;
        
        if ($current_usage >= $limit) {
            $this->handleLimitExceeded($tenant_id, $resource_type, $current_usage, $limit);
        } elseif ($current_usage >= ($limit * 0.8)) {
            $this->sendUsageWarning($tenant_id, $resource_type, $current_usage, $limit);
        }
    }
}
```

### **🔧 3. إدارة الميزات الديناميكية**

#### **تفعيل/إلغاء الميزات:**
```php
<?php
/**
 * إدارة الميزات حسب خطة الاشتراك
 */
class FeatureManager {
    
    public function isFeatureEnabled($tenant_id, $feature_name) {
        $tenant_plan = $this->getTenantPlan($tenant_id);
        $plan_features = $this->getPlanFeatures($tenant_plan['plan_id']);
        
        return in_array($feature_name, $plan_features);
    }
    
    public function enforceFeatureAccess($tenant_id, $feature_name) {
        if (!$this->isFeatureEnabled($tenant_id, $feature_name)) {
            throw new FeatureNotAvailableException(
                "Feature '{$feature_name}' is not available in your current plan. Please upgrade to access this feature."
            );
        }
    }
    
    public function getPlanFeatures($plan_id) {
        $features = [
            'basic' => [
                'accounting_basic',
                'inventory_basic',
                'pos_single',
                'reports_basic'
            ],
            'professional' => [
                'accounting_basic',
                'accounting_advanced',
                'inventory_basic',
                'inventory_advanced',
                'pos_multiple',
                'ecommerce',
                'mobile_app',
                'reports_basic',
                'reports_advanced'
            ],
            'enterprise' => [
                'accounting_basic',
                'accounting_advanced',
                'inventory_basic',
                'inventory_advanced',
                'pos_multiple',
                'ecommerce',
                'mobile_app',
                'reports_basic',
                'reports_advanced',
                'ai_features',
                'workflow_advanced',
                'eta_integration',
                'api_access',
                'custom_development'
            ]
        ];
        
        return $features[$plan_id] ?? [];
    }
}
```

## 🔄 التجديد التلقائي والإشعارات

### **⏰ 1. نظام التجديد التلقائي**

#### **معالج التجديد:**
```php
<?php
/**
 * معالج التجديد التلقائي للاشتراكات
 */
class AutoRenewalProcessor {
    
    public function processRenewals() {
        // البحث عن الاشتراكات المستحقة للتجديد
        $due_subscriptions = $this->getDueSubscriptions();
        
        foreach ($due_subscriptions as $subscription) {
            try {
                $this->processRenewal($subscription);
            } catch (Exception $e) {
                $this->handleRenewalFailure($subscription, $e);
            }
        }
    }
    
    private function processRenewal($subscription) {
        $tenant_id = $subscription['tenant_id'];
        $amount = $subscription['amount'];
        
        // محاولة الدفع
        $payment_result = $this->processPayment($subscription);
        
        if ($payment_result['success']) {
            // تجديد الاشتراك
            $this->renewSubscription($subscription);
            
            // إرسال إشعار النجاح
            $this->sendRenewalSuccessNotification($tenant_id);
            
            // تسجيل العملية
            $this->logRenewal($subscription, 'success');
        } else {
            throw new PaymentFailedException($payment_result['error']);
        }
    }
    
    private function handleRenewalFailure($subscription, $exception) {
        $tenant_id = $subscription['tenant_id'];
        
        // إرسال إشعار الفشل
        $this->sendRenewalFailureNotification($tenant_id, $exception->getMessage());
        
        // تسجيل الفشل
        $this->logRenewal($subscription, 'failed', $exception->getMessage());
        
        // بدء فترة السماح
        $this->startGracePeriod($subscription);
    }
}
```

### **📧 2. نظام الإشعارات المتقدم**

#### **إشعارات الاشتراك:**
```php
<?php
/**
 * نظام إشعارات الاشتراك
 */
class SubscriptionNotifications {
    
    public function sendExpiryWarning($tenant_id, $days_remaining) {
        $tenant = $this->getTenant($tenant_id);
        
        $message = "تنبيه: اشتراكك في AYM ERP سينتهي خلال {$days_remaining} أيام. يرجى تجديد اشتراكك لتجنب انقطاع الخدمة.";
        
        $this->sendEmail($tenant['email'], 'تنبيه انتهاء الاشتراك', $message);
        $this->sendSMS($tenant['mobile'], $message);
        $this->sendInAppNotification($tenant_id, $message);
    }
    
    public function sendUsageLimitWarning($tenant_id, $resource_type, $usage_percentage) {
        $tenant = $this->getTenant($tenant_id);
        
        $resource_names = [
            'users' => 'المستخدمين',
            'transactions' => 'المعاملات',
            'storage' => 'التخزين'
        ];
        
        $resource_name = $resource_names[$resource_type] ?? $resource_type;
        
        $message = "تحذير: لقد استخدمت {$usage_percentage}% من حد {$resource_name} المسموح في خطتك الحالية.";
        
        $this->sendInAppNotification($tenant_id, $message);
        
        if ($usage_percentage >= 90) {
            $this->sendEmail($tenant['email'], 'تحذير: اقتراب من حد الاستخدام', $message);
        }
    }
    
    public function sendUpgradeRecommendation($tenant_id, $recommended_plan) {
        $tenant = $this->getTenant($tenant_id);
        
        $message = "بناءً على استخدامك، نوصي بالترقية إلى خطة {$recommended_plan} للحصول على أفضل تجربة.";
        
        $this->sendInAppNotification($tenant_id, $message);
    }
}
```

## 📊 لوحة إدارة SaaS

### **🎛️ 1. لوحة التحكم الرئيسية**

#### **مؤشرات الأداء الرئيسية:**
- **إجمالي المستأجرين:** العدد الكلي للعملاء
- **الإيرادات الشهرية:** MRR (Monthly Recurring Revenue)
- **معدل النمو:** نمو الإيرادات والعملاء
- **معدل الاستبقاء:** Customer Retention Rate
- **معدل الإلغاء:** Churn Rate
- **متوسط قيمة العميل:** ARPU (Average Revenue Per User)

#### **تقارير الاستخدام:**
```php
<?php
/**
 * تقارير استخدام المنصة
 */
class SaaSReporting {
    
    public function getDashboardMetrics() {
        return [
            'total_tenants' => $this->getTotalTenants(),
            'active_tenants' => $this->getActiveTenants(),
            'mrr' => $this->getMonthlyRecurringRevenue(),
            'growth_rate' => $this->getGrowthRate(),
            'churn_rate' => $this->getChurnRate(),
            'arpu' => $this->getAverageRevenuePerUser(),
            'top_plans' => $this->getTopPlans(),
            'usage_trends' => $this->getUsageTrends()
        ];
    }
    
    public function getMonthlyRecurringRevenue() {
        $query = "
            SELECT SUM(amount) as mrr
            FROM cod_subscriptions 
            WHERE status = 'active'
            AND billing_cycle = 'monthly'
        ";
        
        $monthly_mrr = $this->db->query($query)->row['mrr'] ?? 0;
        
        // تحويل الاشتراكات السنوية إلى شهرية
        $query = "
            SELECT SUM(amount/12) as annual_mrr
            FROM cod_subscriptions 
            WHERE status = 'active'
            AND billing_cycle = 'annual'
        ";
        
        $annual_mrr = $this->db->query($query)->row['annual_mrr'] ?? 0;
        
        return $monthly_mrr + $annual_mrr;
    }
    
    public function getChurnRate($period = 'monthly') {
        $start_date = date('Y-m-01', strtotime('-1 month'));
        $end_date = date('Y-m-t', strtotime('-1 month'));
        
        $total_at_start = $this->getActiveTenants($start_date);
        $churned = $this->getChurnedTenants($start_date, $end_date);
        
        return $total_at_start > 0 ? ($churned / $total_at_start) * 100 : 0;
    }
}
```

### **👥 2. إدارة العملاء**

#### **ملفات العملاء:**
- **معلومات الاشتراك:** الخطة الحالية وتاريخ التجديد
- **تاريخ الدفع:** جميع المدفوعات والفواتير
- **استخدام الموارد:** تفاصيل الاستخدام الشهري
- **تذاكر الدعم:** جميع طلبات الدعم
- **سجل النشاط:** تسجيل الدخول والأنشطة

#### **أدوات إدارة العملاء:**
- **ترقية/تخفيض الخطة:** تغيير خطة العميل
- **تجميد الحساب:** إيقاف مؤقت للخدمة
- **إنهاء الاشتراك:** إلغاء الخدمة
- **إعادة تعيين كلمة المرور:** للدعم الفني
- **إرسال إشعارات:** تواصل مباشر مع العميل

## 🎯 خطة تطوير نظام SaaS

### **📅 المرحلة الأولى (الشهر الأول):**
1. **تطوير نظام المستأجرين:** عزل البيانات والموارد
2. **إنشاء خطط الاشتراك:** الأساسية والمهنية والمؤسسية
3. **تكامل بوابات الدفع:** فوري وPaymob
4. **نظام الفوترة:** التلقائية والتجديد

### **📅 المرحلة الثانية (الشهر الثاني):**
1. **لوحة إدارة SaaS:** مؤشرات الأداء والتقارير
2. **نظام مراقبة الاستخدام:** تتبع الموارد والحدود
3. **نظام الإشعارات:** التنبيهات والتحذيرات
4. **أدوات إدارة العملاء:** ترقية وإدارة الحسابات

### **📅 المرحلة الثالثة (الشهر الثالث):**
1. **تحسين الأداء:** تحسين قواعد البيانات والخوادم
2. **أمان متقدم:** حماية البيانات والخصوصية
3. **نظام النسخ الاحتياطي:** حماية بيانات العملاء
4. **اختبار الضغط:** اختبار قدرة التحمل

### **📊 معايير النجاح:**
- **وقت التشغيل:** 99.9%+ uptime
- **رضا العملاء:** 4.5/5+ تقييم
- **نمو الإيرادات:** 20%+ شهرياً
- **معدل الاستبقاء:** 95%+ سنوياً
- **سرعة الاستجابة:** أقل من 2 ثانية
