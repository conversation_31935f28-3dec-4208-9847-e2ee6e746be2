📄 Route: extension/module/html
📂 Controller: controller\extension\module\html.php
🧱 Models used (2):
   ✅ setting/module (7 functions)
   ✅ localisation/language (7 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\html.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\module\html.php (10 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\html.php (10 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (23):
   - action
   - button_cancel
   - button_save
   - column_left
   - entry_description
   - entry_name
   - entry_status
   - entry_title
   - error_name
   - error_permission
   - error_warning
   - footer
   - header
   - name
   - summernote
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_success
   ... و 3 متغير آخر

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - name
   - summernote
   - text_disabled
   - text_enabled
   - text_home

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - name
   - summernote
   - text_disabled
   - text_enabled
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - summernote
      - button_save
      - error_warning
      - column_left
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - summernote
      - button_save
      - error_warning
      - column_left
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:23
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.