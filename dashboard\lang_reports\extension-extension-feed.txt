📄 Route: extension/extension/feed
📂 Controller: controller\extension\extension\feed.php
🧱 Models used (2):
   - setting/extension
   - user/user_group
🎨 Twig templates (1):
   - view\template\extension\extension\feed.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\extension\feed.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\extension\feed.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - extension
   - text_disabled
   - text_enabled
   - text_success

❌ Missing in Arabic:
   - error_permission
   - extension
   - text_disabled
   - text_enabled
   - text_success

❌ Missing in English:
   - error_permission
   - extension
   - text_disabled
   - text_enabled
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - extension = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - extension = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
