📄 Route: sale/installment_plan
📂 Controller: controller\sale\installment_plan.php
🧱 Models used (4):
   - customer/customer
   - sale/installment_plan
   - sale/installment_template
   - tool/activity_log
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_amount
   - error_customer
   - error_not_found
   - error_permission
   - error_template
   - heading_title
   - heading_title_add
   - heading_title_edit
   - heading_title_schedule
   - heading_title_view
   - text_home
   - text_pagination
   - text_status_active
   - text_status_cancelled
   - text_status_completed
   - text_status_defaulted
   - text_status_overdue
   - text_status_pending
   - text_success_add
   - text_success_edit

❌ Missing in Arabic:
   - date_format_short
   - error_amount
   - error_customer
   - error_not_found
   - error_permission
   - error_template
   - heading_title
   - heading_title_add
   - heading_title_edit
   - heading_title_schedule
   - heading_title_view
   - text_home
   - text_pagination
   - text_status_active
   - text_status_cancelled
   - text_status_completed
   - text_status_defaulted
   - text_status_overdue
   - text_status_pending
   - text_success_add
   - text_success_edit

❌ Missing in English:
   - date_format_short
   - error_amount
   - error_customer
   - error_not_found
   - error_permission
   - error_template
   - heading_title
   - heading_title_add
   - heading_title_edit
   - heading_title_schedule
   - heading_title_view
   - text_home
   - text_pagination
   - text_status_active
   - text_status_cancelled
   - text_status_completed
   - text_status_defaulted
   - text_status_overdue
   - text_status_pending
   - text_success_add
   - text_success_edit

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_amount = ""  # TODO: ترجمة عربية
   - error_customer = ""  # TODO: ترجمة عربية
   - error_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_template = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_title_add = ""  # TODO: ترجمة عربية
   - heading_title_edit = ""  # TODO: ترجمة عربية
   - heading_title_schedule = ""  # TODO: ترجمة عربية
   - heading_title_view = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_status_active = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_completed = ""  # TODO: ترجمة عربية
   - text_status_defaulted = ""  # TODO: ترجمة عربية
   - text_status_overdue = ""  # TODO: ترجمة عربية
   - text_status_pending = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_amount = ""  # TODO: English translation
   - error_customer = ""  # TODO: English translation
   - error_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_template = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_title_add = ""  # TODO: English translation
   - heading_title_edit = ""  # TODO: English translation
   - heading_title_schedule = ""  # TODO: English translation
   - heading_title_view = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_status_active = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_completed = ""  # TODO: English translation
   - text_status_defaulted = ""  # TODO: English translation
   - text_status_overdue = ""  # TODO: English translation
   - text_status_pending = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
