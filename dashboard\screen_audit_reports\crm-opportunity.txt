📄 Route: crm/opportunity
📂 Controller: controller\crm\opportunity.php
🧱 Models used (2):
   ✅ crm/opportunity (6 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\crm\opportunity.php (48 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\crm\opportunity.php (48 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (48):
   - button_add_opportunity
   - button_close
   - button_filter
   - column_name
   - error_not_found
   - error_permission
   - text_ajax_error
   - text_enter_opportunity_name
   - text_filter
   - text_name
   - text_opportunity_list
   - text_opportunity_name
   - text_probability
   - text_select_user
   - text_stage_closed_lost
   - text_stage_closed_won
   - text_stage_negotiation
   - text_stage_qualification
   - text_status
   - text_status_on_hold
   ... و 28 متغير آخر

🗄️ Database Tables Used (1):
   ✅ cod_crm_opportunity

📈 Screen Health Score: ✅ 100%
📅 Analysis Date: 2025-07-21 18:32:58
🔧 Total Issues: 0

🎉 ممتاز! هذه الشاشة في حالة جيدة جداً.