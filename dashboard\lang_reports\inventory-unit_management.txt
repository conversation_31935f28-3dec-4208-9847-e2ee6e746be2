📄 Route: inventory/unit_management
📂 Controller: controller\inventory\unit_management.php
🧱 Models used (1):
   - inventory/unit_management
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\inventory\unit_management.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_conversion_factor
   - error_name
   - error_permission
   - error_symbol
   - error_symbol_exists
   - error_unit_not_found
   - error_unit_type
   - heading_title
   - text_add
   - text_all
   - text_conversion_calculator
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_no
   - text_none
   - text_pagination
   - text_success
   - text_usage_report
   - text_yes

❌ Missing in Arabic:
   - date_format_short
   - error_conversion_factor
   - error_name
   - error_permission
   - error_symbol
   - error_symbol_exists
   - error_unit_not_found
   - error_unit_type
   - heading_title
   - text_add
   - text_all
   - text_conversion_calculator
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_no
   - text_none
   - text_pagination
   - text_success
   - text_usage_report
   - text_yes

❌ Missing in English:
   - date_format_short
   - error_conversion_factor
   - error_name
   - error_permission
   - error_symbol
   - error_symbol_exists
   - error_unit_not_found
   - error_unit_type
   - heading_title
   - text_add
   - text_all
   - text_conversion_calculator
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_no
   - text_none
   - text_pagination
   - text_success
   - text_usage_report
   - text_yes

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_conversion_factor = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_symbol = ""  # TODO: ترجمة عربية
   - error_symbol_exists = ""  # TODO: ترجمة عربية
   - error_unit_not_found = ""  # TODO: ترجمة عربية
   - error_unit_type = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_conversion_calculator = ""  # TODO: ترجمة عربية
   - text_defaults_created = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_no = ""  # TODO: ترجمة عربية
   - text_none = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_usage_report = ""  # TODO: ترجمة عربية
   - text_yes = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_conversion_factor = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_symbol = ""  # TODO: English translation
   - error_symbol_exists = ""  # TODO: English translation
   - error_unit_not_found = ""  # TODO: English translation
   - error_unit_type = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_conversion_calculator = ""  # TODO: English translation
   - text_defaults_created = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_no = ""  # TODO: English translation
   - text_none = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_usage_report = ""  # TODO: English translation
   - text_yes = ""  # TODO: English translation
