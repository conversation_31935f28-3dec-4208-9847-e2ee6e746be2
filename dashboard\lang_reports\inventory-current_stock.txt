📄 Route: inventory/current_stock
📂 Controller: controller\inventory\current_stock.php
🧱 Models used (9):
   - catalog/category
   - catalog/manufacturer
   - catalog/product
   - core/central_service_manager
   - inventory/current_stock
   - inventory/current_stock_enhanced
   - inventory/warehouse
   - setting/setting
   - user/user_group
🎨 Twig templates (1):
   - view\template\inventory\current_stock.twig
🈯 Arabic Language Files (2):
   - language\ar\common\header.php
   - language\ar\inventory\current_stock.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_abc_class
   - column_available_stock
   - column_category
   - column_current_stock
   - column_last_movement
   - column_location
   - column_manufacturer
   - column_max_level
   - column_product_name
   - column_reorder_level
   - column_reserved_stock
   - column_sku
   - column_status
   - column_total_value
   - column_turnover_rate
   - column_unit_cost
   - column_warehouse
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_export_failed
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_invalid_request
   - error_items_required
   - error_movement_failed_for_product
   - error_no_data
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_analytics
   - text_home
   - text_pagination
   - text_success_reorder_update

❌ Missing in Arabic:
   - column_abc_class
   - column_available_stock
   - column_category
   - column_current_stock
   - column_last_movement
   - column_location
   - column_manufacturer
   - column_max_level
   - column_product_name
   - column_reorder_level
   - column_reserved_stock
   - column_sku
   - column_status
   - column_total_value
   - column_turnover_rate
   - column_unit_cost
   - column_warehouse
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_export_failed
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_invalid_request
   - error_items_required
   - error_movement_failed_for_product
   - error_no_data
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_analytics
   - text_home
   - text_pagination
   - text_success_reorder_update

❌ Missing in English:
   - column_abc_class
   - column_available_stock
   - column_category
   - column_current_stock
   - column_last_movement
   - column_location
   - column_manufacturer
   - column_max_level
   - column_product_name
   - column_reorder_level
   - column_reserved_stock
   - column_sku
   - column_status
   - column_total_value
   - column_turnover_rate
   - column_unit_cost
   - column_warehouse
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_export_failed
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_invalid_request
   - error_items_required
   - error_movement_failed_for_product
   - error_no_data
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_analytics
   - text_home
   - text_pagination
   - text_success_reorder_update

💡 Suggested Arabic Additions:
   - column_abc_class = ""  # TODO: ترجمة عربية
   - column_available_stock = ""  # TODO: ترجمة عربية
   - column_category = ""  # TODO: ترجمة عربية
   - column_current_stock = ""  # TODO: ترجمة عربية
   - column_last_movement = ""  # TODO: ترجمة عربية
   - column_location = ""  # TODO: ترجمة عربية
   - column_manufacturer = ""  # TODO: ترجمة عربية
   - column_max_level = ""  # TODO: ترجمة عربية
   - column_product_name = ""  # TODO: ترجمة عربية
   - column_reorder_level = ""  # TODO: ترجمة عربية
   - column_reserved_stock = ""  # TODO: ترجمة عربية
   - column_sku = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_total_value = ""  # TODO: ترجمة عربية
   - column_turnover_rate = ""  # TODO: ترجمة عربية
   - column_unit_cost = ""  # TODO: ترجمة عربية
   - column_warehouse = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_export_failed = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_analytics = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success_reorder_update = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_abc_class = ""  # TODO: English translation
   - column_available_stock = ""  # TODO: English translation
   - column_category = ""  # TODO: English translation
   - column_current_stock = ""  # TODO: English translation
   - column_last_movement = ""  # TODO: English translation
   - column_location = ""  # TODO: English translation
   - column_manufacturer = ""  # TODO: English translation
   - column_max_level = ""  # TODO: English translation
   - column_product_name = ""  # TODO: English translation
   - column_reorder_level = ""  # TODO: English translation
   - column_reserved_stock = ""  # TODO: English translation
   - column_sku = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_total_value = ""  # TODO: English translation
   - column_turnover_rate = ""  # TODO: English translation
   - column_unit_cost = ""  # TODO: English translation
   - column_warehouse = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_export_failed = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_analytics = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success_reorder_update = ""  # TODO: English translation
