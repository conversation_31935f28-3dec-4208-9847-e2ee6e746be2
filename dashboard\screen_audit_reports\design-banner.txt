📄 Route: design/banner
📂 Controller: controller\design\banner.php
🧱 Models used (3):
   ✅ design/banner (7 functions)
   ✅ localisation/language (7 functions)
   ✅ tool/image (1 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\design\banner.php (18 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\design\banner.php (18 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - error_name
   - error_permission
   - error_title
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (4):
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

❌ Missing in English (4):
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - text_enabled
      - text_home
      - text_disabled
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 4 items
      - text_enabled
      - text_home
      - text_disabled
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 4 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:00
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.