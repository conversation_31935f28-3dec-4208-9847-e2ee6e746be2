📄 Route: user/permission
📂 Controller: controller\user\permission.php
🧱 Models used (3):
   - user/permission
   - user/user
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\user\permission.php
🇬🇧 English Language Files (1):
   - language\en-gb\user\permission.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_name
   - error_permission
   - heading_title
   - text_no_results
   - text_success

❌ Missing in Arabic:
   - error_name
   - error_permission
   - heading_title
   - text_no_results
   - text_success

❌ Missing in English:
   - error_name
   - error_permission
   - heading_title
   - text_no_results
   - text_success

💡 Suggested Arabic Additions:
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
