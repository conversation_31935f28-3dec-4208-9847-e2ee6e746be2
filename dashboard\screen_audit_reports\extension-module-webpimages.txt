📄 Route: extension/module/webpimages
📂 Controller: controller\extension\module\webpimages.php
🧱 Models used (1):
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\webpimages.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\extension\module\webpimages.php (0 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\webpimages.php (14 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (27):
   - action
   - button_save
   - cancel
   - column_left
   - entry_quality
   - entry_status
   - error_permission
   - footer
   - header
   - help_cookie
   - help_gd
   - help_quality
   - module_webpimages_quality
   - text_alert
   - text_extension
   - text_gd
   - text_home
   - text_success
   - text_version
   - value
   ... و 7 متغير آخر

❌ Missing in Arabic (27):
   - action
   - column_left
   - entry_status
   - footer
   - header
   - help_cookie
   - help_gd
   - help_quality
   - module_webpimages_quality
   - text_alert
   - text_extension
   - text_gd
   - text_home
   - text_success
   - text_version
   ... و 12 متغير آخر

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - key
   - module_webpimages_quality
   - text_alert
   - text_home
   - value

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 27 items
      - text_gd
      - help_quality
      - text_success
      - text_extension
      - column_left
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - error_warning
      - column_left
      - action
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 27 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:24
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.