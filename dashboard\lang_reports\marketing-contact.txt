📄 Route: marketing/contact
📂 Controller: controller\marketing\contact.php
🧱 Models used (5):
   - customer/customer
   - customer/customer_group
   - sale/order
   - setting/setting
   - setting/store
🎨 Twig templates (1):
   - view\template\marketing\contact.twig
🈯 Arabic Language Files (1):
   - language\ar\marketing\contact.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketing\contact.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - code
   - error_email
   - error_message
   - error_permission
   - error_subject
   - heading_title
   - text_home
   - text_sent
   - text_success

❌ Missing in Arabic:
   - code
   - error_email
   - error_message
   - error_permission
   - error_subject
   - heading_title
   - text_home
   - text_sent
   - text_success

❌ Missing in English:
   - code
   - error_email
   - error_message
   - error_permission
   - error_subject
   - heading_title
   - text_home
   - text_sent
   - text_success

💡 Suggested Arabic Additions:
   - code = ""  # TODO: ترجمة عربية
   - error_email = ""  # TODO: ترجمة عربية
   - error_message = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_subject = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_sent = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - code = ""  # TODO: English translation
   - error_email = ""  # TODO: English translation
   - error_message = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_subject = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_sent = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
