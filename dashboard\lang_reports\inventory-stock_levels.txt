📄 Route: inventory/stock_levels
📂 Controller: controller\inventory\stock_levels.php
🧱 Models used (4):
   - inventory/branch
   - inventory/category
   - inventory/manufacturer
   - inventory/stock_levels
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\inventory\stock_levels.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_average_cost
   - column_branch
   - column_category
   - column_last_movement
   - column_manufacturer
   - column_model
   - column_product_name
   - column_quantity
   - column_sku
   - column_stock_status
   - column_total_value
   - column_unit
   - date_format_long
   - date_format_short
   - heading_title
   - text_all
   - text_branch_type_store
   - text_branch_type_warehouse
   - text_disabled
   - text_enabled
   - text_home
   - text_never
   - text_pagination
   - text_stock_status_low_stock
   - text_stock_status_normal
   - text_stock_status_out_of_stock
   - text_stock_status_overstock

❌ Missing in Arabic:
   - column_average_cost
   - column_branch
   - column_category
   - column_last_movement
   - column_manufacturer
   - column_model
   - column_product_name
   - column_quantity
   - column_sku
   - column_stock_status
   - column_total_value
   - column_unit
   - date_format_long
   - date_format_short
   - heading_title
   - text_all
   - text_branch_type_store
   - text_branch_type_warehouse
   - text_disabled
   - text_enabled
   - text_home
   - text_never
   - text_pagination
   - text_stock_status_low_stock
   - text_stock_status_normal
   - text_stock_status_out_of_stock
   - text_stock_status_overstock

❌ Missing in English:
   - column_average_cost
   - column_branch
   - column_category
   - column_last_movement
   - column_manufacturer
   - column_model
   - column_product_name
   - column_quantity
   - column_sku
   - column_stock_status
   - column_total_value
   - column_unit
   - date_format_long
   - date_format_short
   - heading_title
   - text_all
   - text_branch_type_store
   - text_branch_type_warehouse
   - text_disabled
   - text_enabled
   - text_home
   - text_never
   - text_pagination
   - text_stock_status_low_stock
   - text_stock_status_normal
   - text_stock_status_out_of_stock
   - text_stock_status_overstock

💡 Suggested Arabic Additions:
   - column_average_cost = ""  # TODO: ترجمة عربية
   - column_branch = ""  # TODO: ترجمة عربية
   - column_category = ""  # TODO: ترجمة عربية
   - column_last_movement = ""  # TODO: ترجمة عربية
   - column_manufacturer = ""  # TODO: ترجمة عربية
   - column_model = ""  # TODO: ترجمة عربية
   - column_product_name = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_sku = ""  # TODO: ترجمة عربية
   - column_stock_status = ""  # TODO: ترجمة عربية
   - column_total_value = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - date_format_long = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_branch_type_store = ""  # TODO: ترجمة عربية
   - text_branch_type_warehouse = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_never = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_stock_status_low_stock = ""  # TODO: ترجمة عربية
   - text_stock_status_normal = ""  # TODO: ترجمة عربية
   - text_stock_status_out_of_stock = ""  # TODO: ترجمة عربية
   - text_stock_status_overstock = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_average_cost = ""  # TODO: English translation
   - column_branch = ""  # TODO: English translation
   - column_category = ""  # TODO: English translation
   - column_last_movement = ""  # TODO: English translation
   - column_manufacturer = ""  # TODO: English translation
   - column_model = ""  # TODO: English translation
   - column_product_name = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_sku = ""  # TODO: English translation
   - column_stock_status = ""  # TODO: English translation
   - column_total_value = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - date_format_long = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_branch_type_store = ""  # TODO: English translation
   - text_branch_type_warehouse = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_never = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_stock_status_low_stock = ""  # TODO: English translation
   - text_stock_status_normal = ""  # TODO: English translation
   - text_stock_status_out_of_stock = ""  # TODO: English translation
   - text_stock_status_overstock = ""  # TODO: English translation
