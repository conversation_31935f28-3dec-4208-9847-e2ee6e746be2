📄 Route: accounts/balance_sheet
📂 Controller: controller\accounts\balance_sheet.php
🧱 Models used (4):
   - accounts/balance_sheet
   - accounts/trial_balance
   - branch/branch
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\balance_sheet.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\balance_sheet.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - code
   - date_format_short
   - direction
   - error_comparative_date
   - error_date_end
   - error_date_required
   - error_invalid_request
   - error_no_analysis_data
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_account
   - text_advanced_analysis
   - text_amount
   - text_analysis_view
   - text_as_of
   - text_assets
   - text_balance_correct
   - text_balance_error
   - text_balance_unverified
   - text_balance_verified
   - text_equity
   - text_home
   - text_liabilities
   - text_no_results
   - text_success_analysis
   - text_success_generate
   - text_total_assets
   - text_total_equity
   - text_total_liabilities

❌ Missing in Arabic:
   - code
   - date_format_short
   - direction
   - error_comparative_date
   - error_date_end
   - error_date_required
   - error_invalid_request
   - error_no_analysis_data
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_account
   - text_advanced_analysis
   - text_amount
   - text_analysis_view
   - text_as_of
   - text_assets
   - text_balance_correct
   - text_balance_error
   - text_balance_unverified
   - text_balance_verified
   - text_equity
   - text_home
   - text_liabilities
   - text_no_results
   - text_success_analysis
   - text_success_generate
   - text_total_assets
   - text_total_equity
   - text_total_liabilities

❌ Missing in English:
   - code
   - date_format_short
   - direction
   - error_comparative_date
   - error_date_end
   - error_date_required
   - error_invalid_request
   - error_no_analysis_data
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_account
   - text_advanced_analysis
   - text_amount
   - text_analysis_view
   - text_as_of
   - text_assets
   - text_balance_correct
   - text_balance_error
   - text_balance_unverified
   - text_balance_verified
   - text_equity
   - text_home
   - text_liabilities
   - text_no_results
   - text_success_analysis
   - text_success_generate
   - text_total_assets
   - text_total_equity
   - text_total_liabilities

💡 Suggested Arabic Additions:
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - error_comparative_date = ""  # TODO: ترجمة عربية
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_date_required = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_no_analysis_data = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_account = ""  # TODO: ترجمة عربية
   - text_advanced_analysis = ""  # TODO: ترجمة عربية
   - text_amount = ""  # TODO: ترجمة عربية
   - text_analysis_view = ""  # TODO: ترجمة عربية
   - text_as_of = ""  # TODO: ترجمة عربية
   - text_assets = ""  # TODO: ترجمة عربية
   - text_balance_correct = ""  # TODO: ترجمة عربية
   - text_balance_error = ""  # TODO: ترجمة عربية
   - text_balance_unverified = ""  # TODO: ترجمة عربية
   - text_balance_verified = ""  # TODO: ترجمة عربية
   - text_equity = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_liabilities = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_success_analysis = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_total_assets = ""  # TODO: ترجمة عربية
   - text_total_equity = ""  # TODO: ترجمة عربية
   - text_total_liabilities = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - error_comparative_date = ""  # TODO: English translation
   - error_date_end = ""  # TODO: English translation
   - error_date_required = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_no_analysis_data = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_account = ""  # TODO: English translation
   - text_advanced_analysis = ""  # TODO: English translation
   - text_amount = ""  # TODO: English translation
   - text_analysis_view = ""  # TODO: English translation
   - text_as_of = ""  # TODO: English translation
   - text_assets = ""  # TODO: English translation
   - text_balance_correct = ""  # TODO: English translation
   - text_balance_error = ""  # TODO: English translation
   - text_balance_unverified = ""  # TODO: English translation
   - text_balance_verified = ""  # TODO: English translation
   - text_equity = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_liabilities = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_success_analysis = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_total_assets = ""  # TODO: English translation
   - text_total_equity = ""  # TODO: English translation
   - text_total_liabilities = ""  # TODO: English translation
