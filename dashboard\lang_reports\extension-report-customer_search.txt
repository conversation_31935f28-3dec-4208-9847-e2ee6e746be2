📄 Route: extension/report/customer_search
📂 Controller: controller\extension\report\customer_search.php
🧱 Models used (3):
   - catalog/category
   - extension/report/customer
   - setting/setting
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\extension\report\customer_search.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\report\customer_search.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - error_permission
   - heading_title
   - text_customer
   - text_extension
   - text_guest
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - datetime_format
   - error_permission
   - heading_title
   - text_customer
   - text_extension
   - text_guest
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - datetime_format
   - error_permission
   - heading_title
   - text_customer
   - text_extension
   - text_guest
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_customer = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_guest = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_customer = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_guest = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
