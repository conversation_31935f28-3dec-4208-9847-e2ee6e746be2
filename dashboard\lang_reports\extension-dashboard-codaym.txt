📄 Route: extension/dashboard/codaym
📂 Controller: controller\extension\dashboard\codaym.php
🧱 Models used (2):
   - extension/dashboard/codaym
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\dashboard\codaym.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\dashboard\codaym.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\dashboard\codaym.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - heading_title
   - text_home

❌ Missing in Arabic:
   - date_format_short
   - heading_title
   - text_home

❌ Missing in English:
   - date_format_short
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
