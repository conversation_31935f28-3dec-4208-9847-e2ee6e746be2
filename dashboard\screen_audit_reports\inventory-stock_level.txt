📄 Route: inventory/stock_level
📂 Controller: controller\inventory\stock_level.php
🧱 Models used (9):
   ✅ core/central_service_manager (60 functions)
   ✅ inventory/stock_level_enhanced (2 functions)
   ✅ inventory/warehouse (47 functions)
   ✅ catalog/product (128 functions)
   ✅ branch/branch (7 functions)
   ✅ setting/setting (5 functions)
   ✅ user/user_group (9 functions)
   ❌ inventory/unit (0 functions)
   ✅ inventory/stock_level (11 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\stock_level.php (99 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\stock_level.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (33):
   - error_exception
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_maximum_logic
   - error_maximum_stock
   - error_movement_failed_for_product
   - error_permission
   - error_product
   - error_quantity_must_be_positive
   - error_reorder_logic
   - error_reorder_point
   - error_transfer_already_completed
   - error_transfer_not_found
   - error_unit
   - text_add
   - text_disabled
   - text_enabled
   - text_home
   - text_success
   ... و 13 متغير آخر

❌ Missing in Arabic (17):
   - error_advanced_permission
   - error_exception
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_items_required
   - error_maximum_logic
   - error_movement_failed_for_product
   - error_quantity_must_be_positive
   - error_reorder_logic
   - error_same_branch
   - error_transfer_no_items
   - error_transfer_not_found
   - text_auto_calculate_success
   - text_home
   ... و 2 متغير آخر

❌ Missing in English (33):
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_maximum_logic
   - error_movement_failed_for_product
   - error_permission
   - error_product
   - error_reorder_logic
   - error_reorder_point
   - error_transfer_already_completed
   - error_transfer_not_found
   - error_unit
   - text_add
   - text_disabled
   - text_success
   ... و 18 متغير آخر

🗄️ Database Tables Used (5):
   ❌ existing
   ❌ statements
   ❌ stock
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 17 items
      - error_items_required
      - error_insufficient_stock_for_product
      - error_same_branch
      - error_advanced_permission
      - error_insufficient_stock_for_transfer_item
   🟡 MISSING_ENGLISH_VARIABLES: 33 items
      - error_maximum_logic
      - error_unit
      - error_movement_failed_for_product
      - error_transfer_not_found
      - error_reorder_logic
   🔴 INVALID_DATABASE_TABLES: 5 items
      - workflow
      - template
      - existing
      - stock
      - statements
   🟢 MISSING_MODEL_FILES: 1 items
      - inventory/unit

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 17 متغير عربي و 33 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 5 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:07
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.