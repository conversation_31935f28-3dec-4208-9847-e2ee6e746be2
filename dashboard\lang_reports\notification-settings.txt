📄 Route: notification/settings
📂 Controller: controller\notification\settings.php
🧱 Models used (3):
   - notification/settings
   - notification/templates
   - user/user_group
🎨 Twig templates (1):
   - view\template\notification\settings.twig
🈯 Arabic Language Files (1):
   - language\ar\notification\settings.php
🇬🇧 English Language Files (1):
   - language\en-gb\notification\settings.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_batch_interval
   - error_invalid_file
   - error_no_file
   - error_permission
   - heading_title
   - text_home
   - text_import_success
   - text_success
   - text_test_failed
   - text_test_success

❌ Missing in Arabic:
   - error_batch_interval
   - error_invalid_file
   - error_no_file
   - error_permission
   - heading_title
   - text_home
   - text_import_success
   - text_success
   - text_test_failed
   - text_test_success

❌ Missing in English:
   - error_batch_interval
   - error_invalid_file
   - error_no_file
   - error_permission
   - heading_title
   - text_home
   - text_import_success
   - text_success
   - text_test_failed
   - text_test_success

💡 Suggested Arabic Additions:
   - error_batch_interval = ""  # TODO: ترجمة عربية
   - error_invalid_file = ""  # TODO: ترجمة عربية
   - error_no_file = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_import_success = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_test_failed = ""  # TODO: ترجمة عربية
   - text_test_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_batch_interval = ""  # TODO: English translation
   - error_invalid_file = ""  # TODO: English translation
   - error_no_file = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_import_success = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_test_failed = ""  # TODO: English translation
   - text_test_success = ""  # TODO: English translation
