📄 Route: marketplace/installer
📂 Controller: controller\marketplace\installer.php
🧱 Models used (1):
   - setting/extension
🎨 Twig templates (1):
   - view\template\marketplace\installer.twig
🈯 Arabic Language Files (1):
   - language\ar\marketplace\installer.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketplace\installer.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_file
   - error_filetype
   - error_install
   - error_permission
   - error_upload
   - heading_title
   - text_home
   - text_install
   - text_pagination

❌ Missing in Arabic:
   - date_format_short
   - error_file
   - error_filetype
   - error_install
   - error_permission
   - error_upload
   - heading_title
   - text_home
   - text_install
   - text_pagination

❌ Missing in English:
   - date_format_short
   - error_file
   - error_filetype
   - error_install
   - error_permission
   - error_upload
   - heading_title
   - text_home
   - text_install
   - text_pagination

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_file = ""  # TODO: ترجمة عربية
   - error_filetype = ""  # TODO: ترجمة عربية
   - error_install = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_upload = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_install = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_file = ""  # TODO: English translation
   - error_filetype = ""  # TODO: English translation
   - error_install = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_upload = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_install = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
