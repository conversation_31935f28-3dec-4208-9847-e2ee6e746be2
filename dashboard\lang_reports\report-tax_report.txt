📄 Route: report/tax_report
📂 Controller: controller\report\tax_report.php
🧱 Models used (1):
   - report/tax_report
🎨 Twig templates (1):
   - view\template\report\tax_report.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_filing_generation_failed
   - error_filing_not_found
   - error_invalid_request
   - heading_title
   - text_filing_generated_success
   - text_home
   - text_reports

❌ Missing in Arabic:
   - error_filing_generation_failed
   - error_filing_not_found
   - error_invalid_request
   - heading_title
   - text_filing_generated_success
   - text_home
   - text_reports

❌ Missing in English:
   - error_filing_generation_failed
   - error_filing_not_found
   - error_invalid_request
   - heading_title
   - text_filing_generated_success
   - text_home
   - text_reports

💡 Suggested Arabic Additions:
   - error_filing_generation_failed = ""  # TODO: ترجمة عربية
   - error_filing_not_found = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_filing_generated_success = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_reports = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_filing_generation_failed = ""  # TODO: English translation
   - error_filing_not_found = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_filing_generated_success = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_reports = ""  # TODO: English translation
