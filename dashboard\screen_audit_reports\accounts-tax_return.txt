📄 Route: accounts/tax_return
📂 Controller: controller\accounts\tax_return.php
🧱 Models used (2):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/tax_return (1 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\tax_return.php (65 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\tax_return.php (65 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (27):
   - button_filter
   - code
   - date_format_short
   - entry_date_end
   - entry_date_start
   - error_financial_year_required
   - error_no_data
   - error_no_data_submit
   - print_title
   - text_accounting_profit
   - text_exempt_income
   - text_from
   - text_home
   - text_no_results
   - text_period
   - text_success_eta_submit
   - text_success_generate
   - text_tax_rate
   - text_tax_return
   - text_taxable_profit
   ... و 7 متغير آخر

❌ Missing in Arabic (3):
   - code
   - date_format_short
   - direction

❌ Missing in English (3):
   - code
   - date_format_short
   - direction

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - code
      - direction
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - code
      - direction
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:46
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.