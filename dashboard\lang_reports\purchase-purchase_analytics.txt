📄 Route: purchase/purchase_analytics
📂 Controller: controller\purchase\purchase_analytics.php
🧱 Models used (1):
   - purchase/purchase_analytics
🎨 Twig templates (1):
   - view\template\purchase\purchase_analytics.twig
🈯 Arabic Language Files (2):
   - language\ar\common\column_left.php
   - language\ar\purchase\purchase_analytics.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\column_left.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_export
   - button_view
   - error_date_range_required
   - error_invalid_type
   - error_permission
   - heading_title
   - text_amount
   - text_apply
   - text_category
   - text_date_range
   - text_export
   - text_filter
   - text_home
   - text_invoice_matching
   - text_lead_time
   - text_lead_time_days
   - text_loading
   - text_on_time_rate
   - text_orders
   - text_percentage
   - text_period
   - text_po_status
   - text_price_variance
   - text_quality_rate
   - text_spending_by_category
   - text_spending_trend
   - text_supplier
   - text_supplier_performance
   - text_top_suppliers

❌ Missing in Arabic:
   - button_export
   - button_view
   - error_date_range_required
   - error_invalid_type
   - error_permission
   - heading_title
   - text_amount
   - text_apply
   - text_category
   - text_date_range
   - text_export
   - text_filter
   - text_home
   - text_invoice_matching
   - text_lead_time
   - text_lead_time_days
   - text_loading
   - text_on_time_rate
   - text_orders
   - text_percentage
   - text_period
   - text_po_status
   - text_price_variance
   - text_quality_rate
   - text_spending_by_category
   - text_spending_trend
   - text_supplier
   - text_supplier_performance
   - text_top_suppliers

❌ Missing in English:
   - button_export
   - button_view
   - error_date_range_required
   - error_invalid_type
   - error_permission
   - heading_title
   - text_amount
   - text_apply
   - text_category
   - text_date_range
   - text_export
   - text_filter
   - text_home
   - text_invoice_matching
   - text_lead_time
   - text_lead_time_days
   - text_loading
   - text_on_time_rate
   - text_orders
   - text_percentage
   - text_period
   - text_po_status
   - text_price_variance
   - text_quality_rate
   - text_spending_by_category
   - text_spending_trend
   - text_supplier
   - text_supplier_performance
   - text_top_suppliers

💡 Suggested Arabic Additions:
   - button_export = ""  # TODO: ترجمة عربية
   - button_view = ""  # TODO: ترجمة عربية
   - error_date_range_required = ""  # TODO: ترجمة عربية
   - error_invalid_type = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_amount = ""  # TODO: ترجمة عربية
   - text_apply = ""  # TODO: ترجمة عربية
   - text_category = ""  # TODO: ترجمة عربية
   - text_date_range = ""  # TODO: ترجمة عربية
   - text_export = ""  # TODO: ترجمة عربية
   - text_filter = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_invoice_matching = ""  # TODO: ترجمة عربية
   - text_lead_time = ""  # TODO: ترجمة عربية
   - text_lead_time_days = ""  # TODO: ترجمة عربية
   - text_loading = ""  # TODO: ترجمة عربية
   - text_on_time_rate = ""  # TODO: ترجمة عربية
   - text_orders = ""  # TODO: ترجمة عربية
   - text_percentage = ""  # TODO: ترجمة عربية
   - text_period = ""  # TODO: ترجمة عربية
   - text_po_status = ""  # TODO: ترجمة عربية
   - text_price_variance = ""  # TODO: ترجمة عربية
   - text_quality_rate = ""  # TODO: ترجمة عربية
   - text_spending_by_category = ""  # TODO: ترجمة عربية
   - text_spending_trend = ""  # TODO: ترجمة عربية
   - text_supplier = ""  # TODO: ترجمة عربية
   - text_supplier_performance = ""  # TODO: ترجمة عربية
   - text_top_suppliers = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_export = ""  # TODO: English translation
   - button_view = ""  # TODO: English translation
   - error_date_range_required = ""  # TODO: English translation
   - error_invalid_type = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_amount = ""  # TODO: English translation
   - text_apply = ""  # TODO: English translation
   - text_category = ""  # TODO: English translation
   - text_date_range = ""  # TODO: English translation
   - text_export = ""  # TODO: English translation
   - text_filter = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_invoice_matching = ""  # TODO: English translation
   - text_lead_time = ""  # TODO: English translation
   - text_lead_time_days = ""  # TODO: English translation
   - text_loading = ""  # TODO: English translation
   - text_on_time_rate = ""  # TODO: English translation
   - text_orders = ""  # TODO: English translation
   - text_percentage = ""  # TODO: English translation
   - text_period = ""  # TODO: English translation
   - text_po_status = ""  # TODO: English translation
   - text_price_variance = ""  # TODO: English translation
   - text_quality_rate = ""  # TODO: English translation
   - text_spending_by_category = ""  # TODO: English translation
   - text_spending_trend = ""  # TODO: English translation
   - text_supplier = ""  # TODO: English translation
   - text_supplier_performance = ""  # TODO: English translation
   - text_top_suppliers = ""  # TODO: English translation
