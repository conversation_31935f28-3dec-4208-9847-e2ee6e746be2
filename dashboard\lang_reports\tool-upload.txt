📄 Route: tool/upload
📂 Controller: controller\tool\upload.php
🧱 Models used (1):
   - tool/upload
🎨 Twig templates (1):
   - view\template\tool\upload.twig
🈯 Arabic Language Files (2):
   - language\ar\sale\order.php
   - language\ar\tool\upload.php
🇬🇧 English Language Files (2):
   - language\en-gb\sale\order.php
   - language\en-gb\tool\upload.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_file
   - error_filename
   - error_filetype
   - error_permission
   - error_upload
   - heading_title
   - text_home
   - text_pagination
   - text_success
   - text_upload

❌ Missing in Arabic:
   - date_format_short
   - error_file
   - error_filename
   - error_filetype
   - error_permission
   - error_upload
   - heading_title
   - text_home
   - text_pagination
   - text_success
   - text_upload

❌ Missing in English:
   - date_format_short
   - error_file
   - error_filename
   - error_filetype
   - error_permission
   - error_upload
   - heading_title
   - text_home
   - text_pagination
   - text_success
   - text_upload

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_file = ""  # TODO: ترجمة عربية
   - error_filename = ""  # TODO: ترجمة عربية
   - error_filetype = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_upload = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_upload = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_file = ""  # TODO: English translation
   - error_filename = ""  # TODO: English translation
   - error_filetype = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_upload = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_upload = ""  # TODO: English translation
