📄 Route: purchase/quotation_comparison
📂 Controller: controller\purchase\quotation_comparison.php
🧱 Models used (8):
   ✅ purchase/quotation (73 functions)
   ✅ purchase/requisition (17 functions)
   ✅ purchase/order (45 functions)
   ❌ tool/notification (0 functions)
   ✅ user/user (47 functions)
   ✅ setting/setting (5 functions)
   ❌ user/activity (0 functions)
   ✅ purchase/quotation_comparison (46 functions)
🎨 Twig templates (1):
   ✅ view\template\purchase\quotation_comparison.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\purchase\quotation_comparison.php (50 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\purchase\quotation_comparison.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (138):
   - column_attribute
   - error_expired_quotation
   - error_permission
   - error_quotation_not_found
   - text_best_price_recommendation_text
   - text_best_price_supplier
   - text_comparison_title
   - text_create_purchase_order
   - text_date
   - text_date_required
   - text_discount
   - text_document_uploaded
   - text_no_quotations_selected
   - text_price_difference
   - text_quantity
   - text_quotation_approved_notification_message
   - text_quotation_number
   - text_recommendation
   - text_status_partially_received
   - text_validity_date
   ... و 118 متغير آخر

❌ Missing in Arabic (93):
   - column_attribute
   - error_expired_quotation
   - error_file_move
   - error_quotation_not_found
   - text_best_price_recommendation_text
   - text_comparison_title
   - text_create_purchase_order
   - text_date
   - text_discount
   - text_document_uploaded
   - text_no_quotations_selected
   - text_quotation_approved_notification_message
   - text_recommendation
   - text_status_partially_received
   - text_validity
   ... و 78 متغير آخر

❌ Missing in English (138):
   - column_attribute
   - error_permission
   - error_quotation_not_found
   - text_best_price_recommendation_text
   - text_best_price_supplier
   - text_comparison_title
   - text_create_purchase_order
   - text_date
   - text_date_required
   - text_discount
   - text_no_quotations_selected
   - text_quantity
   - text_quotation_approved_notification_message
   - text_recommendation
   - text_status_partially_received
   ... و 123 متغير آخر

🗄️ Database Tables Used (12):
   ❌ SET
   ❌ approved
   ❌ database
   ❌ filter
   ❌ from
   ❌ goods
   ❌ main
   ❌ quotation
   ❌ rejection_reason
   ❌ requisition
   ❌ status
   ❌ the

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 93 items
      - column_attribute
      - text_best_price_recommendation_text
      - text_discount
      - text_create_purchase_order
      - text_comparison_title
   🟡 MISSING_ENGLISH_VARIABLES: 138 items
      - column_attribute
      - text_best_price_recommendation_text
      - text_discount
      - text_create_purchase_order
      - text_quantity
   🔴 INVALID_DATABASE_TABLES: 12 items
      - filter
      - from
      - SET
      - rejection_reason
      - approved
   🟢 MISSING_MODEL_FILES: 2 items
      - tool/notification
      - user/activity

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 93 متغير عربي و 138 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 12 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:15
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.