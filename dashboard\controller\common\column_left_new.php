<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP SYSTEM - Professional Sidebar Controller v5.0 (Enterprise Grade)
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * @version 5.0.0 Enterprise Build
 * <AUTHOR> Development Team
 * @copyright 2025 AYM ERP Systems
 * @description Professional sidebar controller with zero direct text strings.
 * All text content is managed through language files for proper internationalization.
 * 
 * Key Principles:
 * - Zero direct text strings in code
 * - Proper permission checking with hasPermission() and hasKey()
 * - Modular structure with separate build functions
 * - Enterprise-grade security and performance
 * - Full RTL/LTR support through language files
 * - Comprehensive error handling and logging
 * ═══════════════════════════════════════════════════════════════════════════════
 */
 
class ControllerCommonColumnLeft extends Controller {
    
    /**
     * Main index method - builds the complete sidebar menu
     */
    public function index() {
        // 1) Security check - user token and login validation
        if (!$this->validateUserAccess()) {
            $this->response->redirect($this->url->link('common/login'));
            return;
        }
        
        // 2) Load language files
        $this->load->language('common/column_left');
        
        // 3) Load central services for logging
        $this->load->model('core/central_service_manager');
        
        // 4) Initialize main menus array
        $data['menus'] = [];
        
        // 5) Build comprehensive menu system in logical order
        try {
            $this->buildCoreNavigation($data);
            $this->buildSystemMenus($data);
            
            // 6) Filter menu based on user permissions
            $data['menus'] = $this->filterPermittedMenus($data['menus']);
            
            // 7) Log activity
            $this->logMenuAccess();
            
        } catch (Exception $e) {
            $this->model_core_central_service_manager->logError(
                'column_left', 
                'Error building sidebar menu: ' . $e->getMessage()
            );
            
            // Fallback to basic menu
            $data['menus'] = $this->buildBasicMenu();
        }
        
        // 8) Return view with data
        return $this->load->view('common/column_left', $data);
    }
    
    /**
     * Validate user access and permissions
     */
    private function validateUserAccess() {
        // Check if user is logged in
        if (!$this->user->isLogged()) {
            return false;
        }
        
        // Check user token
        if (!isset($this->request->get['user_token']) || 
            !isset($this->session->data['user_token']) || 
            ($this->request->get['user_token'] != $this->session->data['user_token'])) {
            return false;
        }
        
        // Check basic dashboard permission
        if (!$this->user->hasPermission('access', 'common/dashboard')) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Build core navigation elements
     */
    private function buildCoreNavigation(&$data) {
        // Website link
        $data['menus'][] = [
            'id' => 'menu-webstore-link',
            'icon' => 'fa-globe',
            'name' => $this->language->get('text_show_website'),
            'href' => HTTPS_CATALOG,
            'target' => '_blank',
            'children' => []
        ];
        
        // Dashboards section
        $dashboards = [];
        
        if ($this->user->hasPermission('access', 'common/dashboard')) {
            $dashboards[] = [
                'name' => $this->language->get('text_main_dashboard'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-tachometer'
            ];
        }
        
        if ($this->user->hasPermission('access', 'dashboard/kpi')) {
            $dashboards[] = [
                'name' => $this->language->get('text_kpi_dashboard'),
                'href' => $this->url->link('dashboard/kpi', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-line-chart'
            ];
        }
        
        if (!empty($dashboards)) {
            $data['menus'][] = [
                'id' => 'menu-dashboards',
                'icon' => 'fa-dashboard',
                'name' => $this->language->get('text_dashboards'),
                'href' => '',
                'children' => $dashboards
            ];
        }
        
        // Quick operations section
        $this->buildQuickOperations($data);
    }
    
    /**
     * Build quick operations menu
     */
    private function buildQuickOperations(&$data) {
        $quick_ops = [];
        
        // Quick sales tasks
        $sales_tasks = [];
        if ($this->user->hasPermission('access', 'sale/order')) {
            $sales_tasks[] = [
                'name' => $this->language->get('text_quick_add_order'),
                'href' => $this->url->link('sale/order/add', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-plus-circle'
            ];
        }
        
        if (!empty($sales_tasks)) {
            $quick_ops[] = [
                'name' => $this->language->get('text_quick_sales_tasks'),
                'href' => '',
                'icon' => 'fa-shopping-cart',
                'children' => $sales_tasks
            ];
        }
        
        // Quick inventory tasks
        $inventory_tasks = [];
        if ($this->user->hasPermission('access', 'inventory/stock_adjustment')) {
            $inventory_tasks[] = [
                'name' => $this->language->get('text_quick_add_adjustment'),
                'href' => $this->url->link('inventory/stock_adjustment/add', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-plus-circle'
            ];
        }
        
        if (!empty($inventory_tasks)) {
            $quick_ops[] = [
                'name' => $this->language->get('text_quick_inventory_tasks'),
                'href' => '',
                'icon' => 'fa-cubes',
                'children' => $inventory_tasks
            ];
        }
        
        if (!empty($quick_ops)) {
            $data['menus'][] = [
                'id' => 'menu-quick-operations',
                'icon' => 'fa-bolt',
                'name' => $this->language->get('text_daily_operations'),
                'href' => '',
                'children' => $quick_ops
            ];
        }
    }
    
    /**
     * Build main system menus
     */
    private function buildSystemMenus(&$data) {
        // Build menus in logical order
        $this->buildSalesAndCrmSystem($data);
        $this->buildPurchasingSystem($data);
        $this->buildInventorySystem($data);
        $this->buildAccountingSystem($data);
        $this->buildFinanceSystem($data);
        $this->buildHrSystem($data);
        $this->buildReportsSystem($data);
        $this->buildSystemAndSettingsMenu($data);
    }
    
    /**
     * Build Sales & CRM system menu
     */
    private function buildSalesAndCrmSystem(&$data) {
        $sales_menu = [];
        
        // POS Section
        $pos_children = [];
        if ($this->user->hasPermission('access', 'pos/pos')) {
            $pos_children[] = [
                'name' => $this->language->get('text_pos_interface'),
                'href' => $this->url->link('pos/pos', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-calculator'
            ];
        }
        
        if ($this->user->hasPermission('access', 'pos/shift')) {
            $pos_children[] = [
                'name' => $this->language->get('text_pos_shifts'),
                'href' => $this->url->link('pos/shift', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-clock-o'
            ];
        }
        
        if (!empty($pos_children)) {
            $sales_menu[] = [
                'name' => $this->language->get('text_pos_management_section'),
                'href' => '',
                'icon' => 'fa-calculator',
                'children' => $pos_children
            ];
        }
        
        // Sales Operations
        $sales_ops = [];
        if ($this->user->hasPermission('access', 'sale/order')) {
            $sales_ops[] = [
                'name' => $this->language->get('text_sales_orders'),
                'href' => $this->url->link('sale/order', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-shopping-cart'
            ];
        }
        
        if ($this->user->hasPermission('access', 'sale/return')) {
            $sales_ops[] = [
                'name' => $this->language->get('text_sales_returns'),
                'href' => $this->url->link('sale/return', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-undo'
            ];
        }
        
        if (!empty($sales_ops)) {
            $sales_menu[] = [
                'name' => $this->language->get('text_sales_operations_section'),
                'href' => '',
                'icon' => 'fa-shopping-cart',
                'children' => $sales_ops
            ];
        }
        
        // Customers
        $customer_children = [];
        if ($this->user->hasPermission('access', 'customer/customer')) {
            $customer_children[] = [
                'name' => $this->language->get('text_customers'),
                'href' => $this->url->link('customer/customer', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-users'
            ];
        }
        
        if (!empty($customer_children)) {
            $sales_menu[] = [
                'name' => $this->language->get('text_customers_section'),
                'href' => '',
                'icon' => 'fa-users',
                'children' => $customer_children
            ];
        }
        
        if (!empty($sales_menu)) {
            $data['menus'][] = [
                'id' => 'menu-sales-crm',
                'icon' => 'fa-shopping-cart',
                'name' => $this->language->get('text_sales_and_crm'),
                'href' => '',
                'children' => $sales_menu
            ];
        }
    }

    /**
     * Build Purchasing system menu
     */
    private function buildPurchasingSystem(&$data) {
        $purchase_menu = [];

        if ($this->user->hasPermission('access', 'purchase/order')) {
            $purchase_menu[] = [
                'name' => $this->language->get('text_purchase_orders'),
                'href' => $this->url->link('purchase/order', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-shopping-bag'
            ];
        }

        if ($this->user->hasPermission('access', 'supplier/supplier')) {
            $purchase_menu[] = [
                'name' => $this->language->get('text_suppliers'),
                'href' => $this->url->link('supplier/supplier', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-truck'
            ];
        }

        if (!empty($purchase_menu)) {
            $data['menus'][] = [
                'id' => 'menu-purchasing',
                'icon' => 'fa-shopping-bag',
                'name' => $this->language->get('text_purchasing_and_suppliers'),
                'href' => '',
                'children' => $purchase_menu
            ];
        }
    }

    /**
     * Build Inventory system menu
     */
    private function buildInventorySystem(&$data) {
        $inventory_menu = [];

        if ($this->user->hasPermission('access', 'inventory/dashboard')) {
            $inventory_menu[] = [
                'name' => $this->language->get('text_inventory_dashboard'),
                'href' => $this->url->link('inventory/dashboard', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-tachometer'
            ];
        }

        if ($this->user->hasPermission('access', 'inventory/stock_levels')) {
            $inventory_menu[] = [
                'name' => $this->language->get('text_current_stock_levels'),
                'href' => $this->url->link('inventory/stock_levels', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-cubes'
            ];
        }

        if ($this->user->hasPermission('access', 'inventory/stock_count')) {
            $inventory_menu[] = [
                'name' => $this->language->get('text_stock_counting'),
                'href' => $this->url->link('inventory/stock_count', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-list-ol'
            ];
        }

        if (!empty($inventory_menu)) {
            $data['menus'][] = [
                'id' => 'menu-inventory',
                'icon' => 'fa-cubes',
                'name' => $this->language->get('text_inventory_and_warehouse'),
                'href' => '',
                'children' => $inventory_menu
            ];
        }
    }

    /**
     * Build Accounting system menu
     */
    private function buildAccountingSystem(&$data) {
        $accounting_menu = [];

        if ($this->user->hasPermission('access', 'accounts/chart_of_accounts')) {
            $accounting_menu[] = [
                'name' => $this->language->get('text_chart_of_accounts'),
                'href' => $this->url->link('accounts/chart_of_accounts', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-sitemap'
            ];
        }

        if ($this->user->hasPermission('access', 'accounts/journal_entry')) {
            $accounting_menu[] = [
                'name' => $this->language->get('text_journal_entries'),
                'href' => $this->url->link('accounts/journal_entry', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-book'
            ];
        }

        if (!empty($accounting_menu)) {
            $data['menus'][] = [
                'id' => 'menu-accounting',
                'icon' => 'fa-calculator',
                'name' => $this->language->get('text_accounting_system'),
                'href' => '',
                'children' => $accounting_menu
            ];
        }
    }

    /**
     * Build Finance system menu
     */
    private function buildFinanceSystem(&$data) {
        $finance_menu = [];

        if ($this->user->hasPermission('access', 'finance/cash_flow')) {
            $finance_menu[] = [
                'name' => $this->language->get('text_cash_flow'),
                'href' => $this->url->link('finance/cash_flow', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-money'
            ];
        }

        if (!empty($finance_menu)) {
            $data['menus'][] = [
                'id' => 'menu-finance',
                'icon' => 'fa-money',
                'name' => $this->language->get('text_finance_system'),
                'href' => '',
                'children' => $finance_menu
            ];
        }
    }

    /**
     * Build HR system menu
     */
    private function buildHrSystem(&$data) {
        $hr_menu = [];

        if ($this->user->hasPermission('access', 'hr/employee')) {
            $hr_menu[] = [
                'name' => $this->language->get('text_employees'),
                'href' => $this->url->link('hr/employee', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-users'
            ];
        }

        if (!empty($hr_menu)) {
            $data['menus'][] = [
                'id' => 'menu-hr',
                'icon' => 'fa-users',
                'name' => $this->language->get('text_human_resources'),
                'href' => '',
                'children' => $hr_menu
            ];
        }
    }

    /**
     * Build Reports system menu
     */
    private function buildReportsSystem(&$data) {
        $reports_menu = [];

        if ($this->user->hasPermission('access', 'report/sales')) {
            $reports_menu[] = [
                'name' => $this->language->get('text_sales_reports'),
                'href' => $this->url->link('report/sales', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-bar-chart'
            ];
        }

        if (!empty($reports_menu)) {
            $data['menus'][] = [
                'id' => 'menu-reports',
                'icon' => 'fa-bar-chart',
                'name' => $this->language->get('text_reports_and_analytics'),
                'href' => '',
                'children' => $reports_menu
            ];
        }
    }

    /**
     * Build System and Settings menu
     */
    private function buildSystemAndSettingsMenu(&$data) {
        $settings_menu = [];

        if ($this->user->hasPermission('access', 'setting/setting')) {
            $settings_menu[] = [
                'name' => $this->language->get('text_general_settings'),
                'href' => $this->url->link('setting/setting', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-cog'
            ];
        }

        if ($this->user->hasPermission('access', 'user/user')) {
            $settings_menu[] = [
                'name' => $this->language->get('text_users'),
                'href' => $this->url->link('user/user', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-users'
            ];
        }

        if (!empty($settings_menu)) {
            $data['menus'][] = [
                'id' => 'menu-system-settings',
                'icon' => 'fa-cogs',
                'name' => $this->language->get('text_system_and_settings'),
                'href' => '',
                'children' => $settings_menu
            ];
        }
    }

    /**
     * Filter menus based on user permissions
     */
    private function filterPermittedMenus($menus) {
        $filtered = [];

        foreach ($menus as $menu) {
            // Check if menu has children
            if (!empty($menu['children'])) {
                $menu['children'] = $this->filterPermittedMenus($menu['children']);

                // Only include menu if it has children after filtering
                if (!empty($menu['children'])) {
                    $filtered[] = $menu;
                }
            } else {
                // For leaf items, include them (permission already checked during build)
                $filtered[] = $menu;
            }
        }

        return $filtered;
    }

    /**
     * Build basic fallback menu in case of errors
     */
    private function buildBasicMenu() {
        return [
            [
                'id' => 'menu-dashboard',
                'icon' => 'fa-tachometer',
                'name' => $this->language->get('text_main_dashboard'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true),
                'children' => []
            ]
        ];
    }

    /**
     * Log menu access for audit purposes
     */
    private function logMenuAccess() {
        try {
            $this->model_core_central_service_manager->logActivity(
                'menu_access',
                'column_left',
                'User accessed sidebar menu'
            );
        } catch (Exception $e) {
            // Silent fail for logging - don't break the menu
            error_log('Column left logging failed: ' . $e->getMessage());
        }
    }
}
