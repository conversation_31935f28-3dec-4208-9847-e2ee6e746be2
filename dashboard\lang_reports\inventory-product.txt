📄 Route: inventory/product
📂 Controller: controller\inventory\product.php
🧱 Models used (10):
   - accounts/journal
   - catalog/option
   - communication/unified_notification
   - core/central_service_manager
   - inventory/category
   - inventory/manufacturer
   - inventory/product
   - inventory/units
   - localisation/language
   - tool/image
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\inventory\product.php
🇬🇧 English Language Files (1):
   - language\en-gb\inventory\product.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_account_create
   - error_barcode_add
   - error_barcode_data_invalid
   - error_barcode_exists
   - error_barcode_generate
   - error_barcode_image
   - error_barcode_invalid
   - error_barcode_not_found
   - error_batch_add
   - error_batch_data_invalid
   - error_batch_edit
   - error_batch_not_found
   - error_batch_number_exists
   - error_bundle_create
   - error_bundle_delete
   - error_bundle_edit
   - error_bundle_id_invalid
   - error_bundle_name_required
   - error_bundle_not_found
   - error_bundle_products_minimum
   - error_color_add
   - error_color_data_invalid
   - error_color_delete
   - error_color_edit
   - error_color_id_invalid
   - error_color_in_use
   - error_color_name_required
   - error_cost_data_invalid
   - error_delete_image
   - error_file_type_invalid
   - error_file_upload
   - error_image_not_found
   - error_image_size
   - error_image_type
   - error_image_upload
   - error_invalid_data
   - error_journal_entry
   - error_model
   - error_name
   - error_no_file
   - error_no_images
   - error_permission
   - error_price_tier_add
   - error_price_tier_data_invalid
   - error_price_tier_delete
   - error_price_tier_edit
   - error_price_tier_id_invalid
   - error_price_tier_not_found
   - error_price_tier_overlap
   - error_product_clone
   - error_product_data_required
   - error_product_id_invalid
   - error_product_not_found
   - error_reorder_images
   - error_size_add
   - error_size_data_invalid
   - error_size_delete
   - error_size_edit
   - error_size_id_invalid
   - error_size_in_use
   - error_size_name_required
   - error_variant_add
   - error_variant_data_invalid
   - error_variant_exists
   - heading_title
   - text_add
   - text_bundle_available
   - text_bundle_unavailable
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_import_success
   - text_no_suggestions
   - text_pagination
   - text_pricing_invalid
   - text_pricing_valid
   - text_success

❌ Missing in Arabic:
   - error_account_create
   - error_barcode_add
   - error_barcode_data_invalid
   - error_barcode_exists
   - error_barcode_generate
   - error_barcode_image
   - error_barcode_invalid
   - error_barcode_not_found
   - error_batch_add
   - error_batch_data_invalid
   - error_batch_edit
   - error_batch_not_found
   - error_batch_number_exists
   - error_bundle_create
   - error_bundle_delete
   - error_bundle_edit
   - error_bundle_id_invalid
   - error_bundle_name_required
   - error_bundle_not_found
   - error_bundle_products_minimum
   - error_color_add
   - error_color_data_invalid
   - error_color_delete
   - error_color_edit
   - error_color_id_invalid
   - error_color_in_use
   - error_color_name_required
   - error_cost_data_invalid
   - error_delete_image
   - error_file_type_invalid
   - error_file_upload
   - error_image_not_found
   - error_image_size
   - error_image_type
   - error_image_upload
   - error_invalid_data
   - error_journal_entry
   - error_model
   - error_name
   - error_no_file
   - error_no_images
   - error_permission
   - error_price_tier_add
   - error_price_tier_data_invalid
   - error_price_tier_delete
   - error_price_tier_edit
   - error_price_tier_id_invalid
   - error_price_tier_not_found
   - error_price_tier_overlap
   - error_product_clone
   - error_product_data_required
   - error_product_id_invalid
   - error_product_not_found
   - error_reorder_images
   - error_size_add
   - error_size_data_invalid
   - error_size_delete
   - error_size_edit
   - error_size_id_invalid
   - error_size_in_use
   - error_size_name_required
   - error_variant_add
   - error_variant_data_invalid
   - error_variant_exists
   - heading_title
   - text_add
   - text_bundle_available
   - text_bundle_unavailable
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_import_success
   - text_no_suggestions
   - text_pagination
   - text_pricing_invalid
   - text_pricing_valid
   - text_success

❌ Missing in English:
   - error_account_create
   - error_barcode_add
   - error_barcode_data_invalid
   - error_barcode_exists
   - error_barcode_generate
   - error_barcode_image
   - error_barcode_invalid
   - error_barcode_not_found
   - error_batch_add
   - error_batch_data_invalid
   - error_batch_edit
   - error_batch_not_found
   - error_batch_number_exists
   - error_bundle_create
   - error_bundle_delete
   - error_bundle_edit
   - error_bundle_id_invalid
   - error_bundle_name_required
   - error_bundle_not_found
   - error_bundle_products_minimum
   - error_color_add
   - error_color_data_invalid
   - error_color_delete
   - error_color_edit
   - error_color_id_invalid
   - error_color_in_use
   - error_color_name_required
   - error_cost_data_invalid
   - error_delete_image
   - error_file_type_invalid
   - error_file_upload
   - error_image_not_found
   - error_image_size
   - error_image_type
   - error_image_upload
   - error_invalid_data
   - error_journal_entry
   - error_model
   - error_name
   - error_no_file
   - error_no_images
   - error_permission
   - error_price_tier_add
   - error_price_tier_data_invalid
   - error_price_tier_delete
   - error_price_tier_edit
   - error_price_tier_id_invalid
   - error_price_tier_not_found
   - error_price_tier_overlap
   - error_product_clone
   - error_product_data_required
   - error_product_id_invalid
   - error_product_not_found
   - error_reorder_images
   - error_size_add
   - error_size_data_invalid
   - error_size_delete
   - error_size_edit
   - error_size_id_invalid
   - error_size_in_use
   - error_size_name_required
   - error_variant_add
   - error_variant_data_invalid
   - error_variant_exists
   - heading_title
   - text_add
   - text_bundle_available
   - text_bundle_unavailable
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_import_success
   - text_no_suggestions
   - text_pagination
   - text_pricing_invalid
   - text_pricing_valid
   - text_success

💡 Suggested Arabic Additions:
   - error_account_create = ""  # TODO: ترجمة عربية
   - error_barcode_add = ""  # TODO: ترجمة عربية
   - error_barcode_data_invalid = ""  # TODO: ترجمة عربية
   - error_barcode_exists = ""  # TODO: ترجمة عربية
   - error_barcode_generate = ""  # TODO: ترجمة عربية
   - error_barcode_image = ""  # TODO: ترجمة عربية
   - error_barcode_invalid = ""  # TODO: ترجمة عربية
   - error_barcode_not_found = ""  # TODO: ترجمة عربية
   - error_batch_add = ""  # TODO: ترجمة عربية
   - error_batch_data_invalid = ""  # TODO: ترجمة عربية
   - error_batch_edit = ""  # TODO: ترجمة عربية
   - error_batch_not_found = ""  # TODO: ترجمة عربية
   - error_batch_number_exists = ""  # TODO: ترجمة عربية
   - error_bundle_create = ""  # TODO: ترجمة عربية
   - error_bundle_delete = ""  # TODO: ترجمة عربية
   - error_bundle_edit = ""  # TODO: ترجمة عربية
   - error_bundle_id_invalid = ""  # TODO: ترجمة عربية
   - error_bundle_name_required = ""  # TODO: ترجمة عربية
   - error_bundle_not_found = ""  # TODO: ترجمة عربية
   - error_bundle_products_minimum = ""  # TODO: ترجمة عربية
   - error_color_add = ""  # TODO: ترجمة عربية
   - error_color_data_invalid = ""  # TODO: ترجمة عربية
   - error_color_delete = ""  # TODO: ترجمة عربية
   - error_color_edit = ""  # TODO: ترجمة عربية
   - error_color_id_invalid = ""  # TODO: ترجمة عربية
   - error_color_in_use = ""  # TODO: ترجمة عربية
   - error_color_name_required = ""  # TODO: ترجمة عربية
   - error_cost_data_invalid = ""  # TODO: ترجمة عربية
   - error_delete_image = ""  # TODO: ترجمة عربية
   - error_file_type_invalid = ""  # TODO: ترجمة عربية
   - error_file_upload = ""  # TODO: ترجمة عربية
   - error_image_not_found = ""  # TODO: ترجمة عربية
   - error_image_size = ""  # TODO: ترجمة عربية
   - error_image_type = ""  # TODO: ترجمة عربية
   - error_image_upload = ""  # TODO: ترجمة عربية
   - error_invalid_data = ""  # TODO: ترجمة عربية
   - error_journal_entry = ""  # TODO: ترجمة عربية
   - error_model = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_no_file = ""  # TODO: ترجمة عربية
   - error_no_images = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_price_tier_add = ""  # TODO: ترجمة عربية
   - error_price_tier_data_invalid = ""  # TODO: ترجمة عربية
   - error_price_tier_delete = ""  # TODO: ترجمة عربية
   - error_price_tier_edit = ""  # TODO: ترجمة عربية
   - error_price_tier_id_invalid = ""  # TODO: ترجمة عربية
   - error_price_tier_not_found = ""  # TODO: ترجمة عربية
   - error_price_tier_overlap = ""  # TODO: ترجمة عربية
   - error_product_clone = ""  # TODO: ترجمة عربية
   - error_product_data_required = ""  # TODO: ترجمة عربية
   - error_product_id_invalid = ""  # TODO: ترجمة عربية
   - error_product_not_found = ""  # TODO: ترجمة عربية
   - error_reorder_images = ""  # TODO: ترجمة عربية
   - error_size_add = ""  # TODO: ترجمة عربية
   - error_size_data_invalid = ""  # TODO: ترجمة عربية
   - error_size_delete = ""  # TODO: ترجمة عربية
   - error_size_edit = ""  # TODO: ترجمة عربية
   - error_size_id_invalid = ""  # TODO: ترجمة عربية
   - error_size_in_use = ""  # TODO: ترجمة عربية
   - error_size_name_required = ""  # TODO: ترجمة عربية
   - error_variant_add = ""  # TODO: ترجمة عربية
   - error_variant_data_invalid = ""  # TODO: ترجمة عربية
   - error_variant_exists = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_bundle_available = ""  # TODO: ترجمة عربية
   - text_bundle_unavailable = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_import_success = ""  # TODO: ترجمة عربية
   - text_no_suggestions = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_pricing_invalid = ""  # TODO: ترجمة عربية
   - text_pricing_valid = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_account_create = ""  # TODO: English translation
   - error_barcode_add = ""  # TODO: English translation
   - error_barcode_data_invalid = ""  # TODO: English translation
   - error_barcode_exists = ""  # TODO: English translation
   - error_barcode_generate = ""  # TODO: English translation
   - error_barcode_image = ""  # TODO: English translation
   - error_barcode_invalid = ""  # TODO: English translation
   - error_barcode_not_found = ""  # TODO: English translation
   - error_batch_add = ""  # TODO: English translation
   - error_batch_data_invalid = ""  # TODO: English translation
   - error_batch_edit = ""  # TODO: English translation
   - error_batch_not_found = ""  # TODO: English translation
   - error_batch_number_exists = ""  # TODO: English translation
   - error_bundle_create = ""  # TODO: English translation
   - error_bundle_delete = ""  # TODO: English translation
   - error_bundle_edit = ""  # TODO: English translation
   - error_bundle_id_invalid = ""  # TODO: English translation
   - error_bundle_name_required = ""  # TODO: English translation
   - error_bundle_not_found = ""  # TODO: English translation
   - error_bundle_products_minimum = ""  # TODO: English translation
   - error_color_add = ""  # TODO: English translation
   - error_color_data_invalid = ""  # TODO: English translation
   - error_color_delete = ""  # TODO: English translation
   - error_color_edit = ""  # TODO: English translation
   - error_color_id_invalid = ""  # TODO: English translation
   - error_color_in_use = ""  # TODO: English translation
   - error_color_name_required = ""  # TODO: English translation
   - error_cost_data_invalid = ""  # TODO: English translation
   - error_delete_image = ""  # TODO: English translation
   - error_file_type_invalid = ""  # TODO: English translation
   - error_file_upload = ""  # TODO: English translation
   - error_image_not_found = ""  # TODO: English translation
   - error_image_size = ""  # TODO: English translation
   - error_image_type = ""  # TODO: English translation
   - error_image_upload = ""  # TODO: English translation
   - error_invalid_data = ""  # TODO: English translation
   - error_journal_entry = ""  # TODO: English translation
   - error_model = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_no_file = ""  # TODO: English translation
   - error_no_images = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_price_tier_add = ""  # TODO: English translation
   - error_price_tier_data_invalid = ""  # TODO: English translation
   - error_price_tier_delete = ""  # TODO: English translation
   - error_price_tier_edit = ""  # TODO: English translation
   - error_price_tier_id_invalid = ""  # TODO: English translation
   - error_price_tier_not_found = ""  # TODO: English translation
   - error_price_tier_overlap = ""  # TODO: English translation
   - error_product_clone = ""  # TODO: English translation
   - error_product_data_required = ""  # TODO: English translation
   - error_product_id_invalid = ""  # TODO: English translation
   - error_product_not_found = ""  # TODO: English translation
   - error_reorder_images = ""  # TODO: English translation
   - error_size_add = ""  # TODO: English translation
   - error_size_data_invalid = ""  # TODO: English translation
   - error_size_delete = ""  # TODO: English translation
   - error_size_edit = ""  # TODO: English translation
   - error_size_id_invalid = ""  # TODO: English translation
   - error_size_in_use = ""  # TODO: English translation
   - error_size_name_required = ""  # TODO: English translation
   - error_variant_add = ""  # TODO: English translation
   - error_variant_data_invalid = ""  # TODO: English translation
   - error_variant_exists = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_bundle_available = ""  # TODO: English translation
   - text_bundle_unavailable = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_import_success = ""  # TODO: English translation
   - text_no_suggestions = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_pricing_invalid = ""  # TODO: English translation
   - text_pricing_valid = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
