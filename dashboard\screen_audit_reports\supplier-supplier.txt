📄 Route: supplier/supplier
📂 Controller: controller\supplier\supplier.php
🧱 Models used (5):
   ✅ supplier/supplier (21 functions)
   ✅ setting/store (14 functions)
   ✅ customer/customer_group (7 functions)
   ✅ localisation/country (6 functions)
   ❌ supplier/custom_field (0 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\supplier\supplier.php (106 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\supplier\supplier.php (105 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (28):
   - date_format_short
   - error_bank_account_name
   - error_bank_account_number
   - error_city
   - error_country
   - error_custom_field
   - error_exists
   - error_password
   - error_paypal
   - error_permission
   - error_postcode
   - error_telephone
   - error_tracking
   - error_tracking_exists
   - error_zone
   - text_add
   - text_disabled
   - text_enabled
   - text_home
   - text_success
   ... و 8 متغير آخر

❌ Missing in Arabic (5):
   - date_format_short
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

❌ Missing in English (5):
   - date_format_short
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - text_disabled
      - text_home
      - text_pagination
      - text_enabled
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 5 items
      - text_disabled
      - text_home
      - text_pagination
      - text_enabled
      - date_format_short
   🟢 MISSING_MODEL_FILES: 1 items
      - supplier/custom_field

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 5 متغير إنجليزي
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ⚠️ 75%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 3

⚠️ جيد، لكن يحتاج بعض التحسينات.