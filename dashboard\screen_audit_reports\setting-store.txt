📄 Route: setting/store
📂 Controller: controller\setting\store.php
🧱 Models used (13):
   ✅ setting/store (14 functions)
   ✅ setting/setting (5 functions)
   ✅ setting/extension (11 functions)
   ✅ design/layout (8 functions)
   ✅ tool/image (1 functions)
   ✅ localisation/location (6 functions)
   ✅ localisation/country (6 functions)
   ✅ localisation/language (7 functions)
   ✅ localisation/currency (8 functions)
   ✅ customer/customer_group (7 functions)
   ✅ catalog/information (12 functions)
   ✅ localisation/order_status (7 functions)
   ✅ sale/order (27 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\setting\store.php (86 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\setting\store.php (86 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (20):
   - error_address
   - error_customer_group_display
   - error_default
   - error_email
   - error_meta_title
   - error_name
   - error_owner
   - error_permission
   - error_store
   - error_telephone
   - error_url
   - error_warning
   - extension
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_settings
   - text_success

❌ Missing in Arabic (3):
   - extension
   - text_default
   - text_home

❌ Missing in English (3):
   - extension
   - text_default
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - extension
      - text_home
      - text_default
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - extension
      - text_home
      - text_default

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:18
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.