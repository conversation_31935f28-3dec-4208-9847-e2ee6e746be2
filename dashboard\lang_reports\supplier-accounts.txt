📄 Route: supplier/accounts
📂 Controller: controller\supplier\accounts.php
🧱 Models used (2):
   - supplier/accounts
   - supplier/supplier
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\supplier\accounts.php
🇬🇧 English Language Files (1):
   - language\en-gb\supplier\accounts.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_account_number
   - column_account_status
   - column_credit_limit
   - column_current_balance
   - column_last_transaction
   - column_payment_terms
   - column_supplier
   - date_format_short
   - error_amount
   - error_credit_limit
   - error_missing_data
   - error_payment_amount
   - error_payment_date
   - error_payment_method
   - error_permission
   - error_supplier
   - error_transaction_date
   - error_transaction_type
   - error_update_status
   - heading_title
   - text_account_details
   - text_aging_report
   - text_credit_limit_updated
   - text_home
   - text_pagination
   - text_payment_success
   - text_statement
   - text_status_updated
   - text_transaction_success

❌ Missing in Arabic:
   - column_account_number
   - column_account_status
   - column_credit_limit
   - column_current_balance
   - column_last_transaction
   - column_payment_terms
   - column_supplier
   - date_format_short
   - error_amount
   - error_credit_limit
   - error_missing_data
   - error_payment_amount
   - error_payment_date
   - error_payment_method
   - error_permission
   - error_supplier
   - error_transaction_date
   - error_transaction_type
   - error_update_status
   - heading_title
   - text_account_details
   - text_aging_report
   - text_credit_limit_updated
   - text_home
   - text_pagination
   - text_payment_success
   - text_statement
   - text_status_updated
   - text_transaction_success

❌ Missing in English:
   - column_account_number
   - column_account_status
   - column_credit_limit
   - column_current_balance
   - column_last_transaction
   - column_payment_terms
   - column_supplier
   - date_format_short
   - error_amount
   - error_credit_limit
   - error_missing_data
   - error_payment_amount
   - error_payment_date
   - error_payment_method
   - error_permission
   - error_supplier
   - error_transaction_date
   - error_transaction_type
   - error_update_status
   - heading_title
   - text_account_details
   - text_aging_report
   - text_credit_limit_updated
   - text_home
   - text_pagination
   - text_payment_success
   - text_statement
   - text_status_updated
   - text_transaction_success

💡 Suggested Arabic Additions:
   - column_account_number = ""  # TODO: ترجمة عربية
   - column_account_status = ""  # TODO: ترجمة عربية
   - column_credit_limit = ""  # TODO: ترجمة عربية
   - column_current_balance = ""  # TODO: ترجمة عربية
   - column_last_transaction = ""  # TODO: ترجمة عربية
   - column_payment_terms = ""  # TODO: ترجمة عربية
   - column_supplier = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_amount = ""  # TODO: ترجمة عربية
   - error_credit_limit = ""  # TODO: ترجمة عربية
   - error_missing_data = ""  # TODO: ترجمة عربية
   - error_payment_amount = ""  # TODO: ترجمة عربية
   - error_payment_date = ""  # TODO: ترجمة عربية
   - error_payment_method = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_supplier = ""  # TODO: ترجمة عربية
   - error_transaction_date = ""  # TODO: ترجمة عربية
   - error_transaction_type = ""  # TODO: ترجمة عربية
   - error_update_status = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_account_details = ""  # TODO: ترجمة عربية
   - text_aging_report = ""  # TODO: ترجمة عربية
   - text_credit_limit_updated = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_payment_success = ""  # TODO: ترجمة عربية
   - text_statement = ""  # TODO: ترجمة عربية
   - text_status_updated = ""  # TODO: ترجمة عربية
   - text_transaction_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_account_number = ""  # TODO: English translation
   - column_account_status = ""  # TODO: English translation
   - column_credit_limit = ""  # TODO: English translation
   - column_current_balance = ""  # TODO: English translation
   - column_last_transaction = ""  # TODO: English translation
   - column_payment_terms = ""  # TODO: English translation
   - column_supplier = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_amount = ""  # TODO: English translation
   - error_credit_limit = ""  # TODO: English translation
   - error_missing_data = ""  # TODO: English translation
   - error_payment_amount = ""  # TODO: English translation
   - error_payment_date = ""  # TODO: English translation
   - error_payment_method = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_supplier = ""  # TODO: English translation
   - error_transaction_date = ""  # TODO: English translation
   - error_transaction_type = ""  # TODO: English translation
   - error_update_status = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_account_details = ""  # TODO: English translation
   - text_aging_report = ""  # TODO: English translation
   - text_credit_limit_updated = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_payment_success = ""  # TODO: English translation
   - text_statement = ""  # TODO: English translation
   - text_status_updated = ""  # TODO: English translation
   - text_transaction_success = ""  # TODO: English translation
