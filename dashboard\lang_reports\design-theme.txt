📄 Route: design/theme
📂 Controller: controller\design\theme.php
🧱 Models used (3):
   - design/theme
   - setting/setting
   - setting/store
🎨 Twig templates (1):
   - view\template\design\theme.twig
🈯 Arabic Language Files (1):
   - language\ar\design\theme.php
🇬🇧 English Language Files (1):
   - language\en-gb\design\theme.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_back
   - date_format_short
   - error_permission
   - error_twig
   - heading_title
   - text_default
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - button_back
   - date_format_short
   - error_permission
   - error_twig
   - heading_title
   - text_default
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - button_back
   - date_format_short
   - error_permission
   - error_twig
   - heading_title
   - text_default
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - button_back = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_twig = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_default = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_back = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_twig = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_default = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
