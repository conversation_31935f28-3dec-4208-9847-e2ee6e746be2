📄 Route: inventory/stock_alerts
📂 Controller: controller\inventory\stock_alerts.php
🧱 Models used (4):
   - common/central_service_manager
   - inventory/product
   - inventory/stock_alerts
   - inventory/warehouse
🎨 Twig templates (1):
   - view\template\inventory\stock_alerts.twig
🈯 Arabic Language Files (1):
   - language\ar\inventory\stock_alerts.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_resolve
   - button_view
   - date_format_short
   - error_advanced_permission
   - error_alert_id
   - error_exception
   - error_permission
   - error_refresh_failed
   - error_resolve_failed
   - heading_title
   - text_active
   - text_alert_resolved
   - text_home
   - text_pagination
   - text_refresh_success
   - text_resolved
   - text_settings
   - text_settings_saved

❌ Missing in Arabic:
   - button_resolve
   - button_view
   - date_format_short
   - error_advanced_permission
   - error_alert_id
   - error_exception
   - error_permission
   - error_refresh_failed
   - error_resolve_failed
   - heading_title
   - text_active
   - text_alert_resolved
   - text_home
   - text_pagination
   - text_refresh_success
   - text_resolved
   - text_settings
   - text_settings_saved

❌ Missing in English:
   - button_resolve
   - button_view
   - date_format_short
   - error_advanced_permission
   - error_alert_id
   - error_exception
   - error_permission
   - error_refresh_failed
   - error_resolve_failed
   - heading_title
   - text_active
   - text_alert_resolved
   - text_home
   - text_pagination
   - text_refresh_success
   - text_resolved
   - text_settings
   - text_settings_saved

💡 Suggested Arabic Additions:
   - button_resolve = ""  # TODO: ترجمة عربية
   - button_view = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_alert_id = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_refresh_failed = ""  # TODO: ترجمة عربية
   - error_resolve_failed = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_active = ""  # TODO: ترجمة عربية
   - text_alert_resolved = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_refresh_success = ""  # TODO: ترجمة عربية
   - text_resolved = ""  # TODO: ترجمة عربية
   - text_settings = ""  # TODO: ترجمة عربية
   - text_settings_saved = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_resolve = ""  # TODO: English translation
   - button_view = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_alert_id = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_refresh_failed = ""  # TODO: English translation
   - error_resolve_failed = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_active = ""  # TODO: English translation
   - text_alert_resolved = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_refresh_success = ""  # TODO: English translation
   - text_resolved = ""  # TODO: English translation
   - text_settings = ""  # TODO: English translation
   - text_settings_saved = ""  # TODO: English translation
