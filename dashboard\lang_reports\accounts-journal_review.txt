📄 Route: accounts/journal_review
📂 Controller: controller\accounts\journal_review.php
🧱 Models used (1):
   - accounts/journal_review
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - datetime_format
   - error_journal_id
   - error_permission
   - error_rejection_reason
   - heading_title
   - text_home
   - text_pagination
   - text_success_approved
   - text_success_rejected

❌ Missing in Arabic:
   - date_format_short
   - datetime_format
   - error_journal_id
   - error_permission
   - error_rejection_reason
   - heading_title
   - text_home
   - text_pagination
   - text_success_approved
   - text_success_rejected

❌ Missing in English:
   - date_format_short
   - datetime_format
   - error_journal_id
   - error_permission
   - error_rejection_reason
   - heading_title
   - text_home
   - text_pagination
   - text_success_approved
   - text_success_rejected

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_journal_id = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_rejection_reason = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success_approved = ""  # TODO: ترجمة عربية
   - text_success_rejected = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_journal_id = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_rejection_reason = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success_approved = ""  # TODO: English translation
   - text_success_rejected = ""  # TODO: English translation
