📄 Route: inventory/stocktake
📂 Controller: controller\inventory\stocktake.php
🧱 Models used (5):
   - branch/branch
   - catalog/category
   - inventory/stocktake
   - notification/notification
   - setting/setting
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\inventory\stocktake.php
🇬🇧 English Language Files (1):
   - language\en-gb\inventory\stocktake.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - code
   - column_branch
   - column_counted_quantity
   - column_created_by
   - column_date
   - column_date_added
   - column_expected_quantity
   - column_model
   - column_notes
   - column_product
   - column_reference
   - column_sku
   - column_status
   - column_total_items
   - column_type
   - column_unit
   - column_variance_percentage
   - column_variance_quantity
   - date_format_short
   - direction
   - entry_branch
   - entry_notes
   - entry_reference
   - entry_status
   - entry_stocktake_date
   - entry_type
   - error_branch
   - error_counted_quantity_required
   - error_expected_quantity
   - error_import_file
   - error_import_format
   - error_permission
   - error_product
   - error_products
   - error_reference
   - error_stocktake_cancelled
   - error_stocktake_completed
   - error_stocktake_date
   - error_stocktake_id
   - error_stocktake_status
   - error_type
   - error_unit
   - heading_stocktake_view
   - heading_title
   - text_add
   - text_all_status
   - text_all_types
   - text_cancel_success
   - text_complete_success
   - text_completed_by
   - text_created_by
   - text_date_completed
   - text_date_created
   - text_edit
   - text_home
   - text_import_from_excel
   - text_import_instructions
   - text_import_success
   - text_pagination
   - text_status_cancelled
   - text_status_completed
   - text_status_draft
   - text_status_in_progress
   - text_stocktake_cancelled
   - text_stocktake_cancelled_message
   - text_stocktake_completed
   - text_stocktake_completed_message
   - text_stocktake_created
   - text_stocktake_created_message
   - text_stocktake_deleted
   - text_stocktake_deleted_message
   - text_stocktake_details
   - text_stocktake_products
   - text_stocktake_summary
   - text_stocktake_updated
   - text_stocktake_updated_message
   - text_success
   - text_total
   - text_total_counted
   - text_total_expected
   - text_total_products
   - text_total_variance
   - text_type_cycle
   - text_type_full
   - text_type_partial
   - text_type_spot
   - text_variance_percentage
   - text_variance_value

❌ Missing in Arabic:
   - code
   - column_branch
   - column_counted_quantity
   - column_created_by
   - column_date
   - column_date_added
   - column_expected_quantity
   - column_model
   - column_notes
   - column_product
   - column_reference
   - column_sku
   - column_status
   - column_total_items
   - column_type
   - column_unit
   - column_variance_percentage
   - column_variance_quantity
   - date_format_short
   - direction
   - entry_branch
   - entry_notes
   - entry_reference
   - entry_status
   - entry_stocktake_date
   - entry_type
   - error_branch
   - error_counted_quantity_required
   - error_expected_quantity
   - error_import_file
   - error_import_format
   - error_permission
   - error_product
   - error_products
   - error_reference
   - error_stocktake_cancelled
   - error_stocktake_completed
   - error_stocktake_date
   - error_stocktake_id
   - error_stocktake_status
   - error_type
   - error_unit
   - heading_stocktake_view
   - heading_title
   - text_add
   - text_all_status
   - text_all_types
   - text_cancel_success
   - text_complete_success
   - text_completed_by
   - text_created_by
   - text_date_completed
   - text_date_created
   - text_edit
   - text_home
   - text_import_from_excel
   - text_import_instructions
   - text_import_success
   - text_pagination
   - text_status_cancelled
   - text_status_completed
   - text_status_draft
   - text_status_in_progress
   - text_stocktake_cancelled
   - text_stocktake_cancelled_message
   - text_stocktake_completed
   - text_stocktake_completed_message
   - text_stocktake_created
   - text_stocktake_created_message
   - text_stocktake_deleted
   - text_stocktake_deleted_message
   - text_stocktake_details
   - text_stocktake_products
   - text_stocktake_summary
   - text_stocktake_updated
   - text_stocktake_updated_message
   - text_success
   - text_total
   - text_total_counted
   - text_total_expected
   - text_total_products
   - text_total_variance
   - text_type_cycle
   - text_type_full
   - text_type_partial
   - text_type_spot
   - text_variance_percentage
   - text_variance_value

❌ Missing in English:
   - code
   - column_branch
   - column_counted_quantity
   - column_created_by
   - column_date
   - column_date_added
   - column_expected_quantity
   - column_model
   - column_notes
   - column_product
   - column_reference
   - column_sku
   - column_status
   - column_total_items
   - column_type
   - column_unit
   - column_variance_percentage
   - column_variance_quantity
   - date_format_short
   - direction
   - entry_branch
   - entry_notes
   - entry_reference
   - entry_status
   - entry_stocktake_date
   - entry_type
   - error_branch
   - error_counted_quantity_required
   - error_expected_quantity
   - error_import_file
   - error_import_format
   - error_permission
   - error_product
   - error_products
   - error_reference
   - error_stocktake_cancelled
   - error_stocktake_completed
   - error_stocktake_date
   - error_stocktake_id
   - error_stocktake_status
   - error_type
   - error_unit
   - heading_stocktake_view
   - heading_title
   - text_add
   - text_all_status
   - text_all_types
   - text_cancel_success
   - text_complete_success
   - text_completed_by
   - text_created_by
   - text_date_completed
   - text_date_created
   - text_edit
   - text_home
   - text_import_from_excel
   - text_import_instructions
   - text_import_success
   - text_pagination
   - text_status_cancelled
   - text_status_completed
   - text_status_draft
   - text_status_in_progress
   - text_stocktake_cancelled
   - text_stocktake_cancelled_message
   - text_stocktake_completed
   - text_stocktake_completed_message
   - text_stocktake_created
   - text_stocktake_created_message
   - text_stocktake_deleted
   - text_stocktake_deleted_message
   - text_stocktake_details
   - text_stocktake_products
   - text_stocktake_summary
   - text_stocktake_updated
   - text_stocktake_updated_message
   - text_success
   - text_total
   - text_total_counted
   - text_total_expected
   - text_total_products
   - text_total_variance
   - text_type_cycle
   - text_type_full
   - text_type_partial
   - text_type_spot
   - text_variance_percentage
   - text_variance_value

💡 Suggested Arabic Additions:
   - code = ""  # TODO: ترجمة عربية
   - column_branch = ""  # TODO: ترجمة عربية
   - column_counted_quantity = ""  # TODO: ترجمة عربية
   - column_created_by = ""  # TODO: ترجمة عربية
   - column_date = ""  # TODO: ترجمة عربية
   - column_date_added = ""  # TODO: ترجمة عربية
   - column_expected_quantity = ""  # TODO: ترجمة عربية
   - column_model = ""  # TODO: ترجمة عربية
   - column_notes = ""  # TODO: ترجمة عربية
   - column_product = ""  # TODO: ترجمة عربية
   - column_reference = ""  # TODO: ترجمة عربية
   - column_sku = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_total_items = ""  # TODO: ترجمة عربية
   - column_type = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - column_variance_percentage = ""  # TODO: ترجمة عربية
   - column_variance_quantity = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_branch = ""  # TODO: ترجمة عربية
   - entry_notes = ""  # TODO: ترجمة عربية
   - entry_reference = ""  # TODO: ترجمة عربية
   - entry_status = ""  # TODO: ترجمة عربية
   - entry_stocktake_date = ""  # TODO: ترجمة عربية
   - entry_type = ""  # TODO: ترجمة عربية
   - error_branch = ""  # TODO: ترجمة عربية
   - error_counted_quantity_required = ""  # TODO: ترجمة عربية
   - error_expected_quantity = ""  # TODO: ترجمة عربية
   - error_import_file = ""  # TODO: ترجمة عربية
   - error_import_format = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_product = ""  # TODO: ترجمة عربية
   - error_products = ""  # TODO: ترجمة عربية
   - error_reference = ""  # TODO: ترجمة عربية
   - error_stocktake_cancelled = ""  # TODO: ترجمة عربية
   - error_stocktake_completed = ""  # TODO: ترجمة عربية
   - error_stocktake_date = ""  # TODO: ترجمة عربية
   - error_stocktake_id = ""  # TODO: ترجمة عربية
   - error_stocktake_status = ""  # TODO: ترجمة عربية
   - error_type = ""  # TODO: ترجمة عربية
   - error_unit = ""  # TODO: ترجمة عربية
   - heading_stocktake_view = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_all_status = ""  # TODO: ترجمة عربية
   - text_all_types = ""  # TODO: ترجمة عربية
   - text_cancel_success = ""  # TODO: ترجمة عربية
   - text_complete_success = ""  # TODO: ترجمة عربية
   - text_completed_by = ""  # TODO: ترجمة عربية
   - text_created_by = ""  # TODO: ترجمة عربية
   - text_date_completed = ""  # TODO: ترجمة عربية
   - text_date_created = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_import_from_excel = ""  # TODO: ترجمة عربية
   - text_import_instructions = ""  # TODO: ترجمة عربية
   - text_import_success = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_completed = ""  # TODO: ترجمة عربية
   - text_status_draft = ""  # TODO: ترجمة عربية
   - text_status_in_progress = ""  # TODO: ترجمة عربية
   - text_stocktake_cancelled = ""  # TODO: ترجمة عربية
   - text_stocktake_cancelled_message = ""  # TODO: ترجمة عربية
   - text_stocktake_completed = ""  # TODO: ترجمة عربية
   - text_stocktake_completed_message = ""  # TODO: ترجمة عربية
   - text_stocktake_created = ""  # TODO: ترجمة عربية
   - text_stocktake_created_message = ""  # TODO: ترجمة عربية
   - text_stocktake_deleted = ""  # TODO: ترجمة عربية
   - text_stocktake_deleted_message = ""  # TODO: ترجمة عربية
   - text_stocktake_details = ""  # TODO: ترجمة عربية
   - text_stocktake_products = ""  # TODO: ترجمة عربية
   - text_stocktake_summary = ""  # TODO: ترجمة عربية
   - text_stocktake_updated = ""  # TODO: ترجمة عربية
   - text_stocktake_updated_message = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_total = ""  # TODO: ترجمة عربية
   - text_total_counted = ""  # TODO: ترجمة عربية
   - text_total_expected = ""  # TODO: ترجمة عربية
   - text_total_products = ""  # TODO: ترجمة عربية
   - text_total_variance = ""  # TODO: ترجمة عربية
   - text_type_cycle = ""  # TODO: ترجمة عربية
   - text_type_full = ""  # TODO: ترجمة عربية
   - text_type_partial = ""  # TODO: ترجمة عربية
   - text_type_spot = ""  # TODO: ترجمة عربية
   - text_variance_percentage = ""  # TODO: ترجمة عربية
   - text_variance_value = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - code = ""  # TODO: English translation
   - column_branch = ""  # TODO: English translation
   - column_counted_quantity = ""  # TODO: English translation
   - column_created_by = ""  # TODO: English translation
   - column_date = ""  # TODO: English translation
   - column_date_added = ""  # TODO: English translation
   - column_expected_quantity = ""  # TODO: English translation
   - column_model = ""  # TODO: English translation
   - column_notes = ""  # TODO: English translation
   - column_product = ""  # TODO: English translation
   - column_reference = ""  # TODO: English translation
   - column_sku = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_total_items = ""  # TODO: English translation
   - column_type = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - column_variance_percentage = ""  # TODO: English translation
   - column_variance_quantity = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_branch = ""  # TODO: English translation
   - entry_notes = ""  # TODO: English translation
   - entry_reference = ""  # TODO: English translation
   - entry_status = ""  # TODO: English translation
   - entry_stocktake_date = ""  # TODO: English translation
   - entry_type = ""  # TODO: English translation
   - error_branch = ""  # TODO: English translation
   - error_counted_quantity_required = ""  # TODO: English translation
   - error_expected_quantity = ""  # TODO: English translation
   - error_import_file = ""  # TODO: English translation
   - error_import_format = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_product = ""  # TODO: English translation
   - error_products = ""  # TODO: English translation
   - error_reference = ""  # TODO: English translation
   - error_stocktake_cancelled = ""  # TODO: English translation
   - error_stocktake_completed = ""  # TODO: English translation
   - error_stocktake_date = ""  # TODO: English translation
   - error_stocktake_id = ""  # TODO: English translation
   - error_stocktake_status = ""  # TODO: English translation
   - error_type = ""  # TODO: English translation
   - error_unit = ""  # TODO: English translation
   - heading_stocktake_view = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_all_status = ""  # TODO: English translation
   - text_all_types = ""  # TODO: English translation
   - text_cancel_success = ""  # TODO: English translation
   - text_complete_success = ""  # TODO: English translation
   - text_completed_by = ""  # TODO: English translation
   - text_created_by = ""  # TODO: English translation
   - text_date_completed = ""  # TODO: English translation
   - text_date_created = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_import_from_excel = ""  # TODO: English translation
   - text_import_instructions = ""  # TODO: English translation
   - text_import_success = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_completed = ""  # TODO: English translation
   - text_status_draft = ""  # TODO: English translation
   - text_status_in_progress = ""  # TODO: English translation
   - text_stocktake_cancelled = ""  # TODO: English translation
   - text_stocktake_cancelled_message = ""  # TODO: English translation
   - text_stocktake_completed = ""  # TODO: English translation
   - text_stocktake_completed_message = ""  # TODO: English translation
   - text_stocktake_created = ""  # TODO: English translation
   - text_stocktake_created_message = ""  # TODO: English translation
   - text_stocktake_deleted = ""  # TODO: English translation
   - text_stocktake_deleted_message = ""  # TODO: English translation
   - text_stocktake_details = ""  # TODO: English translation
   - text_stocktake_products = ""  # TODO: English translation
   - text_stocktake_summary = ""  # TODO: English translation
   - text_stocktake_updated = ""  # TODO: English translation
   - text_stocktake_updated_message = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_total = ""  # TODO: English translation
   - text_total_counted = ""  # TODO: English translation
   - text_total_expected = ""  # TODO: English translation
   - text_total_products = ""  # TODO: English translation
   - text_total_variance = ""  # TODO: English translation
   - text_type_cycle = ""  # TODO: English translation
   - text_type_full = ""  # TODO: English translation
   - text_type_partial = ""  # TODO: English translation
   - text_type_spot = ""  # TODO: English translation
   - text_variance_percentage = ""  # TODO: English translation
   - text_variance_value = ""  # TODO: English translation
