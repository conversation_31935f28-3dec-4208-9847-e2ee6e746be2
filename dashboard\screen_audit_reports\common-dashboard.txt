📄 Route: common/dashboard
📂 Controller: controller\common\dashboard.php
🧱 Models used (6):
   ✅ common/dashboard (323 functions)
   ✅ setting/setting (5 functions)
   ✅ core/central_service_manager (60 functions)
   ✅ communication/unified_notification (16 functions)
   ✅ workflow/visual_workflow_engine (18 functions)
   ✅ workflow/approval (15 functions)
🎨 Twig templates (1):
   ✅ view\template\common\dashboard.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\common\dashboard.php (131 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\common\dashboard.php (259 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (52):
   - header
   - text_advanced_filters
   - text_all_branches
   - text_all_categories
   - text_category
   - text_currency
   - text_dashboard_settings
   - text_data_updated_successfully
   - text_direct_sales
   - text_error_updating_data
   - text_export
   - text_order_status
   - text_quick_filters
   - text_save
   - text_settings_saved
   - text_smart_dashboard
   - text_smart_filters
   - text_this_month
   - text_today
   - text_toggle_filters
   ... و 32 متغير آخر

❌ Missing in Arabic (43):
   - header
   - text_advanced_filters
   - text_all_branches
   - text_all_categories
   - text_category
   - text_data_updated_successfully
   - text_direct_sales
   - text_error_updating_data
   - text_order_status
   - text_quick_filters
   - text_save
   - text_settings_saved
   - text_smart_dashboard
   - text_smart_filters
   - text_toggle_filters
   ... و 28 متغير آخر

❌ Missing in English (15):
   - column_left
   - csrf_token
   - csrf_token_name
   - direction
   - footer
   - header
   - language_code
   - text_auto_refresh
   - text_data_updated_successfully
   - text_error_updating_data
   - text_export_feature
   - text_home
   - text_last_update
   - text_settings_saved
   - user_token

🗄️ Database Tables Used (12):
   ❌ Rate
   ❌ Request
   ✅ cod_unified_workflow
   ❌ cod_workflow_activity_log
   ✅ cod_workflow_approval
   ❌ cod_workflow_connection
   ❌ cod_workflow_execution
   ❌ cod_workflow_node
   ❌ cod_workflow_node_execution
   ❌ request
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 43 items
      - text_error_updating_data
      - text_toggle_filters
      - text_order_status
      - text_advanced_filters
      - text_all_categories
   🟡 MISSING_ENGLISH_VARIABLES: 15 items
      - text_error_updating_data
      - text_settings_saved
      - user_token
      - column_left
      - text_home
   🔴 INVALID_DATABASE_TABLES: 10 items
      - workflow
      - request
      - cod_workflow_execution
      - template
      - cod_workflow_node_execution

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 43 متغير عربي و 15 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 10 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:51
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.