📄 Route: accounts/statement_account
📂 Controller: controller\accounts\statement_account.php
🧱 Models used (2):
   - accounts/chartaccount
   - accounts/statement_account
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_account_not_found
   - heading_title
   - text_home
   - text_view_statement

❌ Missing in Arabic:
   - date_format_short
   - error_account_not_found
   - heading_title
   - text_home
   - text_view_statement

❌ Missing in English:
   - date_format_short
   - error_account_not_found
   - heading_title
   - text_home
   - text_view_statement

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_account_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_view_statement = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_account_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_view_statement = ""  # TODO: English translation
