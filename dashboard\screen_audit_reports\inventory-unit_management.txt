📄 Route: inventory/unit_management
📂 Controller: controller\inventory\unit_management.php
🧱 Models used (1):
   ✅ inventory/unit_management (21 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\unit_management.php (237 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\unit_management.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (23):
   - date_format_short
   - error_conversion_factor
   - error_name
   - error_permission
   - error_symbol
   - error_symbol_exists
   - error_unit_not_found
   - heading_title
   - text_add
   - text_all
   - text_defaults_created
   - text_disabled
   - text_enabled
   - text_home
   - text_no
   - text_none
   - text_pagination
   - text_success
   - text_usage_report
   - text_yes
   ... و 3 متغير آخر

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (23):
   - date_format_short
   - error_conversion_factor
   - error_name
   - error_permission
   - error_symbol_exists
   - text_add
   - text_disabled
   - text_enabled
   - text_home
   - text_no
   - text_none
   - text_pagination
   - text_success
   - text_usage_report
   - text_yes
   ... و 8 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 23 items
      - text_no
      - text_success
      - text_add
      - text_home
      - text_usage_report

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 23 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:07
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.