📄 Route: sale/voucher_theme
📂 Controller: controller\sale\voucher_theme.php
🧱 Models used (4):
   - localisation/language
   - sale/voucher
   - sale/voucher_theme
   - tool/image
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\sale\voucher_theme.php
🇬🇧 English Language Files (1):
   - language\en-gb\sale\voucher_theme.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_image
   - error_name
   - error_permission
   - error_voucher
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_image
   - error_name
   - error_permission
   - error_voucher
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_image
   - error_name
   - error_permission
   - error_voucher
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_image = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_voucher = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_image = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_voucher = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
