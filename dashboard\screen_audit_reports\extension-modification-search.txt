📄 Route: extension/modification/search
📂 Controller: controller\extension\modification\search.php
🧱 Models used (1):
   ✅ extension/modification/editor (8 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\modification\search.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\extension\modification\search.php (0 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\modification\search.php (16 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (24):
   - button_edit
   - button_return
   - button_search_query
   - column_left
   - column_name
   - datetime_format
   - entry_name
   - entry_search_name
   - footer
   - header
   - pagination
   - results
   - return
   - search_query
   - text_home
   - text_list
   - text_modifications
   - text_no_results
   - text_pagination
   - user_token
   ... و 4 متغير آخر

❌ Missing in Arabic (24):
   - button_return
   - column_left
   - column_name
   - entry_search_name
   - footer
   - header
   - pagination
   - results
   - return
   - search_query
   - text_home
   - text_list
   - text_modifications
   - text_no_results
   - user_token
   ... و 9 متغير آخر

❌ Missing in English (12):
   - button_edit
   - column_left
   - datetime_format
   - footer
   - header
   - pagination
   - results
   - return
   - search_query
   - text_home
   - text_pagination
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 24 items
      - user_token
      - column_left
      - text_home
      - pagination
      - column_name
   🟡 MISSING_ENGLISH_VARIABLES: 12 items
      - search_query
      - user_token
      - column_left
      - text_home
      - pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 24 متغير عربي و 12 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:23
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.