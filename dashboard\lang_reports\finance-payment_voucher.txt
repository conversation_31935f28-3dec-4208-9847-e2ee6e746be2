📄 Route: finance/payment_voucher
📂 Controller: controller\finance\payment_voucher.php
🧱 Models used (6):
   - accounts/audit_trail
   - accounts/chartaccount
   - accounts/journal_security_advanced
   - finance/payment_voucher
   - hr/employee
   - supplier/supplier
🎨 Twig templates (1):
   - view\template\finance\payment_voucher.twig
🈯 Arabic Language Files (1):
   - language\ar\finance\payment_voucher.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination

❌ Missing in Arabic:
   - date_format_short
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination

❌ Missing in English:
   - date_format_short
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
