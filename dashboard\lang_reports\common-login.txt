📄 Route: common/login
📂 Controller: controller\common\login.php
🧱 Models used (3):
   - core/central_service_manager
   - user/two_factor_auth
   - user/user
🎨 Twig templates (1):
   - view\template\common\login.twig
🈯 Arabic Language Files (1):
   - language\ar\common\login.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\login.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_login
   - entry_password
   - entry_username
   - error_attempts
   - error_login
   - error_password
   - error_username
   - heading_title
   - text_2fa_support
   - text_authenticating
   - text_forgotten
   - text_logging_in
   - text_login
   - text_login_subtitle
   - text_powered_by
   - text_remember_me
   - text_secure
   - text_secure_connection
   - text_secure_login
   - text_session_timeout
   - text_version
   - text_welcome

❌ Missing in Arabic:
   - button_login
   - entry_password
   - entry_username
   - error_attempts
   - error_login
   - error_password
   - error_username
   - heading_title
   - text_2fa_support
   - text_authenticating
   - text_forgotten
   - text_logging_in
   - text_login
   - text_login_subtitle
   - text_powered_by
   - text_remember_me
   - text_secure
   - text_secure_connection
   - text_secure_login
   - text_session_timeout
   - text_version
   - text_welcome

❌ Missing in English:
   - button_login
   - entry_password
   - entry_username
   - error_attempts
   - error_login
   - error_password
   - error_username
   - heading_title
   - text_2fa_support
   - text_authenticating
   - text_forgotten
   - text_logging_in
   - text_login
   - text_login_subtitle
   - text_powered_by
   - text_remember_me
   - text_secure
   - text_secure_connection
   - text_secure_login
   - text_session_timeout
   - text_version
   - text_welcome

💡 Suggested Arabic Additions:
   - button_login = ""  # TODO: ترجمة عربية
   - entry_password = ""  # TODO: ترجمة عربية
   - entry_username = ""  # TODO: ترجمة عربية
   - error_attempts = ""  # TODO: ترجمة عربية
   - error_login = ""  # TODO: ترجمة عربية
   - error_password = ""  # TODO: ترجمة عربية
   - error_username = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_2fa_support = ""  # TODO: ترجمة عربية
   - text_authenticating = ""  # TODO: ترجمة عربية
   - text_forgotten = ""  # TODO: ترجمة عربية
   - text_logging_in = ""  # TODO: ترجمة عربية
   - text_login = ""  # TODO: ترجمة عربية
   - text_login_subtitle = ""  # TODO: ترجمة عربية
   - text_powered_by = ""  # TODO: ترجمة عربية
   - text_remember_me = ""  # TODO: ترجمة عربية
   - text_secure = ""  # TODO: ترجمة عربية
   - text_secure_connection = ""  # TODO: ترجمة عربية
   - text_secure_login = ""  # TODO: ترجمة عربية
   - text_session_timeout = ""  # TODO: ترجمة عربية
   - text_version = ""  # TODO: ترجمة عربية
   - text_welcome = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_login = ""  # TODO: English translation
   - entry_password = ""  # TODO: English translation
   - entry_username = ""  # TODO: English translation
   - error_attempts = ""  # TODO: English translation
   - error_login = ""  # TODO: English translation
   - error_password = ""  # TODO: English translation
   - error_username = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_2fa_support = ""  # TODO: English translation
   - text_authenticating = ""  # TODO: English translation
   - text_forgotten = ""  # TODO: English translation
   - text_logging_in = ""  # TODO: English translation
   - text_login = ""  # TODO: English translation
   - text_login_subtitle = ""  # TODO: English translation
   - text_powered_by = ""  # TODO: English translation
   - text_remember_me = ""  # TODO: English translation
   - text_secure = ""  # TODO: English translation
   - text_secure_connection = ""  # TODO: English translation
   - text_secure_login = ""  # TODO: English translation
   - text_session_timeout = ""  # TODO: English translation
   - text_version = ""  # TODO: English translation
   - text_welcome = ""  # TODO: English translation
