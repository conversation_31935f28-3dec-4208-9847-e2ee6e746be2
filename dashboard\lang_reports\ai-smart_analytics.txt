📄 Route: ai/smart_analytics
📂 Controller: controller\ai\smart_analytics.php
🧱 Models used (1):
   - ai/smart_analytics
🎨 Twig templates (1):
   - view\template\ai\smart_analytics.twig
🈯 Arabic Language Files (1):
   - language\ar\ai\smart_analytics.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_analysis_type_required
   - error_model_type_required
   - error_optimization_target_required
   - error_prediction_failed
   - error_prediction_type_required
   - error_report_generation
   - heading_title
   - text_customer_analysis
   - text_home
   - text_inventory_optimization
   - text_market_trends
   - text_model_trained
   - text_optimization_completed
   - text_prediction_generated
   - text_report_generated
   - text_risk_assessment
   - text_sales_forecast

❌ Missing in Arabic:
   - error_analysis_type_required
   - error_model_type_required
   - error_optimization_target_required
   - error_prediction_failed
   - error_prediction_type_required
   - error_report_generation
   - heading_title
   - text_customer_analysis
   - text_home
   - text_inventory_optimization
   - text_market_trends
   - text_model_trained
   - text_optimization_completed
   - text_prediction_generated
   - text_report_generated
   - text_risk_assessment
   - text_sales_forecast

❌ Missing in English:
   - error_analysis_type_required
   - error_model_type_required
   - error_optimization_target_required
   - error_prediction_failed
   - error_prediction_type_required
   - error_report_generation
   - heading_title
   - text_customer_analysis
   - text_home
   - text_inventory_optimization
   - text_market_trends
   - text_model_trained
   - text_optimization_completed
   - text_prediction_generated
   - text_report_generated
   - text_risk_assessment
   - text_sales_forecast

💡 Suggested Arabic Additions:
   - error_analysis_type_required = ""  # TODO: ترجمة عربية
   - error_model_type_required = ""  # TODO: ترجمة عربية
   - error_optimization_target_required = ""  # TODO: ترجمة عربية
   - error_prediction_failed = ""  # TODO: ترجمة عربية
   - error_prediction_type_required = ""  # TODO: ترجمة عربية
   - error_report_generation = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_customer_analysis = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_inventory_optimization = ""  # TODO: ترجمة عربية
   - text_market_trends = ""  # TODO: ترجمة عربية
   - text_model_trained = ""  # TODO: ترجمة عربية
   - text_optimization_completed = ""  # TODO: ترجمة عربية
   - text_prediction_generated = ""  # TODO: ترجمة عربية
   - text_report_generated = ""  # TODO: ترجمة عربية
   - text_risk_assessment = ""  # TODO: ترجمة عربية
   - text_sales_forecast = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_analysis_type_required = ""  # TODO: English translation
   - error_model_type_required = ""  # TODO: English translation
   - error_optimization_target_required = ""  # TODO: English translation
   - error_prediction_failed = ""  # TODO: English translation
   - error_prediction_type_required = ""  # TODO: English translation
   - error_report_generation = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_customer_analysis = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_inventory_optimization = ""  # TODO: English translation
   - text_market_trends = ""  # TODO: English translation
   - text_model_trained = ""  # TODO: English translation
   - text_optimization_completed = ""  # TODO: English translation
   - text_prediction_generated = ""  # TODO: English translation
   - text_report_generated = ""  # TODO: English translation
   - text_risk_assessment = ""  # TODO: English translation
   - text_sales_forecast = ""  # TODO: English translation
