📄 Route: purchase/supplier_invoice
📂 Controller: controller\purchase\supplier_invoice.php
🧱 Models used (5):
   ✅ purchase/supplier_invoice (37 functions)
   ✅ purchase/order (45 functions)
   ✅ localisation/currency (8 functions)
   ❌ purchase/supplier (0 functions)
   ❌ common/notification (0 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\purchase\supplier_invoice.php (205 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\purchase\supplier_invoice.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (155):
   - button_add_item
   - button_approve
   - column_document_type
   - column_product
   - column_upload_date
   - datetime_format
   - error_delete_status
   - error_missing_data
   - error_permission
   - tab_general
   - text_add
   - text_history_created
   - text_history_partially_paid
   - text_invoice_added_notification_title
   - text_invoice_approved_notification_message
   - text_invoice_view
   - text_journal_entry
   - text_notes
   - text_status_partially_received
   - text_upload_success
   ... و 135 متغير آخر

❌ Missing in Arabic (83):
   - button_upload
   - column_document_type
   - column_upload_date
   - datetime_format
   - error_delete_journal_linked
   - error_delete_status
   - error_missing_data
   - text_document_uploaded
   - text_history_created
   - text_history_partially_paid
   - text_invoice_approved_notification_message
   - text_invoice_view
   - text_journal_entry
   - text_status_partially_received
   - text_upload_success
   ... و 68 متغير آخر

❌ Missing in English (155):
   - button_add_item
   - button_approve
   - column_document_type
   - column_product
   - column_upload_date
   - datetime_format
   - error_missing_data
   - error_permission
   - tab_general
   - text_invoice_added_notification_title
   - text_invoice_approved_notification_message
   - text_invoice_view
   - text_journal_entry
   - text_notes
   - text_status_partially_received
   ... و 140 متغير آخر

🗄️ Database Tables Used (15):
   ❌ DIR_UPLOAD
   ❌ PO
   ❌ SET
   ❌ both
   ❌ from
   ❌ goods
   ❌ invoice
   ❌ matching
   ❌ non
   ❌ other
   ❌ payment
   ❌ settings
   ❌ status
   ❌ supplier_invoice_payment
   ❌ vendor_payment_invoice

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 83 items
      - text_invoice_approved_notification_message
      - text_journal_entry
      - text_invoice_view
      - text_status_partially_received
      - datetime_format
   🟡 MISSING_ENGLISH_VARIABLES: 155 items
      - text_invoice_approved_notification_message
      - tab_general
      - button_add_item
      - text_journal_entry
      - text_invoice_view
   🔴 INVALID_DATABASE_TABLES: 15 items
      - matching
      - from
      - DIR_UPLOAD
      - vendor_payment_invoice
      - SET
   🟢 MISSING_MODEL_FILES: 2 items
      - purchase/supplier
      - common/notification

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 83 متغير عربي و 155 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 15 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.