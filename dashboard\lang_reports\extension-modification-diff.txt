📄 Route: extension/modification/diff
📂 Controller: controller\extension\modification\diff.php
🧱 Models used (0):
🎨 Twig templates (1):
   - view\template\extension\modification\diff.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (1):
   - language\en-gb\extension\modification\diff.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_code
   - error_file
   - error_filewrite
   - error_permission
   - heading_title
   - text_home
   - text_modifications
   - text_modified_files
   - text_success_edit

❌ Missing in Arabic:
   - error_code
   - error_file
   - error_filewrite
   - error_permission
   - heading_title
   - text_home
   - text_modifications
   - text_modified_files
   - text_success_edit

❌ Missing in English:
   - error_code
   - error_file
   - error_filewrite
   - error_permission
   - heading_title
   - text_home
   - text_modifications
   - text_modified_files
   - text_success_edit

💡 Suggested Arabic Additions:
   - error_code = ""  # TODO: ترجمة عربية
   - error_file = ""  # TODO: ترجمة عربية
   - error_filewrite = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_modifications = ""  # TODO: ترجمة عربية
   - text_modified_files = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_code = ""  # TODO: English translation
   - error_file = ""  # TODO: English translation
   - error_filewrite = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_modifications = ""  # TODO: English translation
   - text_modified_files = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
