📄 Route: inventory/movement_history
📂 Controller: controller\inventory\movement_history.php
🧱 Models used (6):
   - catalog/inventory_manager
   - catalog/product
   - common/central_service_manager
   - inventory/movement_history
   - inventory/product
   - inventory/warehouse
🎨 Twig templates (1):
   - view\template\inventory\movement_history.twig
🈯 Arabic Language Files (1):
   - language\ar\inventory\movement_history.php
🇬🇧 English Language Files (1):
   - language\en-gb\inventory\movement_history.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_cost
   - column_date
   - column_movement_type
   - column_notes
   - column_product
   - column_quantity
   - column_reference
   - column_unit
   - column_user
   - column_warehouse
   - datetime_format
   - error_advanced_permission
   - error_exception
   - error_export_failed
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_movement_id
   - error_movement_not_found
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_search_failed
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_adjustment
   - text_all_movement_history
   - text_all_references
   - text_all_types
   - text_analytics
   - text_date_range
   - text_home
   - text_in
   - text_movement_history
   - text_out
   - text_pagination
   - text_production
   - text_purchase
   - text_return
   - text_sale
   - text_transfer

❌ Missing in Arabic:
   - column_cost
   - column_date
   - column_movement_type
   - column_notes
   - column_product
   - column_quantity
   - column_reference
   - column_unit
   - column_user
   - column_warehouse
   - datetime_format
   - error_advanced_permission
   - error_exception
   - error_export_failed
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_movement_id
   - error_movement_not_found
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_search_failed
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_adjustment
   - text_all_movement_history
   - text_all_references
   - text_all_types
   - text_analytics
   - text_date_range
   - text_home
   - text_in
   - text_movement_history
   - text_out
   - text_pagination
   - text_production
   - text_purchase
   - text_return
   - text_sale
   - text_transfer

❌ Missing in English:
   - column_cost
   - column_date
   - column_movement_type
   - column_notes
   - column_product
   - column_quantity
   - column_reference
   - column_unit
   - column_user
   - column_warehouse
   - datetime_format
   - error_advanced_permission
   - error_exception
   - error_export_failed
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_movement_id
   - error_movement_not_found
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_search_failed
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_adjustment
   - text_all_movement_history
   - text_all_references
   - text_all_types
   - text_analytics
   - text_date_range
   - text_home
   - text_in
   - text_movement_history
   - text_out
   - text_pagination
   - text_production
   - text_purchase
   - text_return
   - text_sale
   - text_transfer

💡 Suggested Arabic Additions:
   - column_cost = ""  # TODO: ترجمة عربية
   - column_date = ""  # TODO: ترجمة عربية
   - column_movement_type = ""  # TODO: ترجمة عربية
   - column_notes = ""  # TODO: ترجمة عربية
   - column_product = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_reference = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - column_user = ""  # TODO: ترجمة عربية
   - column_warehouse = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_export_failed = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_movement_id = ""  # TODO: ترجمة عربية
   - error_movement_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_search_failed = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_adjustment = ""  # TODO: ترجمة عربية
   - text_all_movement_history = ""  # TODO: ترجمة عربية
   - text_all_references = ""  # TODO: ترجمة عربية
   - text_all_types = ""  # TODO: ترجمة عربية
   - text_analytics = ""  # TODO: ترجمة عربية
   - text_date_range = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_in = ""  # TODO: ترجمة عربية
   - text_movement_history = ""  # TODO: ترجمة عربية
   - text_out = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_production = ""  # TODO: ترجمة عربية
   - text_purchase = ""  # TODO: ترجمة عربية
   - text_return = ""  # TODO: ترجمة عربية
   - text_sale = ""  # TODO: ترجمة عربية
   - text_transfer = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_cost = ""  # TODO: English translation
   - column_date = ""  # TODO: English translation
   - column_movement_type = ""  # TODO: English translation
   - column_notes = ""  # TODO: English translation
   - column_product = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_reference = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - column_user = ""  # TODO: English translation
   - column_warehouse = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_export_failed = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_movement_id = ""  # TODO: English translation
   - error_movement_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_search_failed = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_adjustment = ""  # TODO: English translation
   - text_all_movement_history = ""  # TODO: English translation
   - text_all_references = ""  # TODO: English translation
   - text_all_types = ""  # TODO: English translation
   - text_analytics = ""  # TODO: English translation
   - text_date_range = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_in = ""  # TODO: English translation
   - text_movement_history = ""  # TODO: English translation
   - text_out = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_production = ""  # TODO: English translation
   - text_purchase = ""  # TODO: English translation
   - text_return = ""  # TODO: English translation
   - text_sale = ""  # TODO: English translation
   - text_transfer = ""  # TODO: English translation
