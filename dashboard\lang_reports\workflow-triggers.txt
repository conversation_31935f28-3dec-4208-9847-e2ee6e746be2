📄 Route: workflow/triggers
📂 Controller: controller\workflow\triggers.php
🧱 Models used (3):
   - core/central_service_manager
   - logging/user_activity
   - workflow/triggers
🎨 Twig templates (1):
   - view\template\workflow\triggers.twig
🈯 Arabic Language Files (1):
   - language\ar\workflow\triggers.php
🇬🇧 English Language Files (1):
   - language\en-gb\workflow\triggers.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_configuration_required
   - error_name_required
   - error_permission
   - error_test_validation
   - error_toggle_validation
   - error_trigger_creation_failed
   - error_trigger_type_required
   - error_workflow_data_required
   - error_workflow_id_required
   - error_workflow_not_found
   - error_workflow_save_failed
   - heading_title
   - text_add_trigger
   - text_ai_desc
   - text_ai_prediction_trigger
   - text_ai_triggers
   - text_anomaly_trigger
   - text_api_trigger
   - text_business_desc
   - text_business_triggers
   - text_cron_trigger
   - text_customer_trigger
   - text_database_change_desc
   - text_database_trigger
   - text_db_example_add_product
   - text_db_example_delete_customer
   - text_db_example_update_inventory
   - text_delay_example_10min
   - text_delay_example_day
   - text_delay_example_hour
   - text_delay_trigger
   - text_delay_trigger_desc
   - text_edit_trigger
   - text_email_trigger
   - text_event_based_desc
   - text_event_based_triggers
   - text_external_desc
   - text_external_triggers
   - text_file_trigger
   - text_google_sheets_trigger
   - text_home
   - text_integration_desc
   - text_integration_triggers
   - text_interval_example_5min
   - text_interval_example_day
   - text_interval_example_hour
   - text_interval_trigger
   - text_interval_trigger_desc
   - text_inventory_trigger
   - text_pattern_trigger
   - text_sales_trigger
   - text_schedule_trigger_desc
   - text_slack_trigger
   - text_success
   - text_system_event_desc
   - text_system_event_trigger
   - text_system_example_error
   - text_system_example_exceed_limit
   - text_system_example_out_of_stock
   - text_test_successful
   - text_time_based_desc
   - text_time_based_triggers
   - text_trigger_monitoring
   - text_user_action_desc
   - text_user_action_trigger
   - text_user_example_approve_document
   - text_user_example_create_order
   - text_user_example_login
   - text_visual_editor
   - text_webhook_trigger
   - text_whatsapp_trigger
   - text_workflow_saved

❌ Missing in Arabic:
   - error_configuration_required
   - error_name_required
   - error_permission
   - error_test_validation
   - error_toggle_validation
   - error_trigger_creation_failed
   - error_trigger_type_required
   - error_workflow_data_required
   - error_workflow_id_required
   - error_workflow_not_found
   - error_workflow_save_failed
   - heading_title
   - text_add_trigger
   - text_ai_desc
   - text_ai_prediction_trigger
   - text_ai_triggers
   - text_anomaly_trigger
   - text_api_trigger
   - text_business_desc
   - text_business_triggers
   - text_cron_trigger
   - text_customer_trigger
   - text_database_change_desc
   - text_database_trigger
   - text_db_example_add_product
   - text_db_example_delete_customer
   - text_db_example_update_inventory
   - text_delay_example_10min
   - text_delay_example_day
   - text_delay_example_hour
   - text_delay_trigger
   - text_delay_trigger_desc
   - text_edit_trigger
   - text_email_trigger
   - text_event_based_desc
   - text_event_based_triggers
   - text_external_desc
   - text_external_triggers
   - text_file_trigger
   - text_google_sheets_trigger
   - text_home
   - text_integration_desc
   - text_integration_triggers
   - text_interval_example_5min
   - text_interval_example_day
   - text_interval_example_hour
   - text_interval_trigger
   - text_interval_trigger_desc
   - text_inventory_trigger
   - text_pattern_trigger
   - text_sales_trigger
   - text_schedule_trigger_desc
   - text_slack_trigger
   - text_success
   - text_system_event_desc
   - text_system_event_trigger
   - text_system_example_error
   - text_system_example_exceed_limit
   - text_system_example_out_of_stock
   - text_test_successful
   - text_time_based_desc
   - text_time_based_triggers
   - text_trigger_monitoring
   - text_user_action_desc
   - text_user_action_trigger
   - text_user_example_approve_document
   - text_user_example_create_order
   - text_user_example_login
   - text_visual_editor
   - text_webhook_trigger
   - text_whatsapp_trigger
   - text_workflow_saved

❌ Missing in English:
   - error_configuration_required
   - error_name_required
   - error_permission
   - error_test_validation
   - error_toggle_validation
   - error_trigger_creation_failed
   - error_trigger_type_required
   - error_workflow_data_required
   - error_workflow_id_required
   - error_workflow_not_found
   - error_workflow_save_failed
   - heading_title
   - text_add_trigger
   - text_ai_desc
   - text_ai_prediction_trigger
   - text_ai_triggers
   - text_anomaly_trigger
   - text_api_trigger
   - text_business_desc
   - text_business_triggers
   - text_cron_trigger
   - text_customer_trigger
   - text_database_change_desc
   - text_database_trigger
   - text_db_example_add_product
   - text_db_example_delete_customer
   - text_db_example_update_inventory
   - text_delay_example_10min
   - text_delay_example_day
   - text_delay_example_hour
   - text_delay_trigger
   - text_delay_trigger_desc
   - text_edit_trigger
   - text_email_trigger
   - text_event_based_desc
   - text_event_based_triggers
   - text_external_desc
   - text_external_triggers
   - text_file_trigger
   - text_google_sheets_trigger
   - text_home
   - text_integration_desc
   - text_integration_triggers
   - text_interval_example_5min
   - text_interval_example_day
   - text_interval_example_hour
   - text_interval_trigger
   - text_interval_trigger_desc
   - text_inventory_trigger
   - text_pattern_trigger
   - text_sales_trigger
   - text_schedule_trigger_desc
   - text_slack_trigger
   - text_success
   - text_system_event_desc
   - text_system_event_trigger
   - text_system_example_error
   - text_system_example_exceed_limit
   - text_system_example_out_of_stock
   - text_test_successful
   - text_time_based_desc
   - text_time_based_triggers
   - text_trigger_monitoring
   - text_user_action_desc
   - text_user_action_trigger
   - text_user_example_approve_document
   - text_user_example_create_order
   - text_user_example_login
   - text_visual_editor
   - text_webhook_trigger
   - text_whatsapp_trigger
   - text_workflow_saved

💡 Suggested Arabic Additions:
   - error_configuration_required = ""  # TODO: ترجمة عربية
   - error_name_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_test_validation = ""  # TODO: ترجمة عربية
   - error_toggle_validation = ""  # TODO: ترجمة عربية
   - error_trigger_creation_failed = ""  # TODO: ترجمة عربية
   - error_trigger_type_required = ""  # TODO: ترجمة عربية
   - error_workflow_data_required = ""  # TODO: ترجمة عربية
   - error_workflow_id_required = ""  # TODO: ترجمة عربية
   - error_workflow_not_found = ""  # TODO: ترجمة عربية
   - error_workflow_save_failed = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add_trigger = ""  # TODO: ترجمة عربية
   - text_ai_desc = ""  # TODO: ترجمة عربية
   - text_ai_prediction_trigger = ""  # TODO: ترجمة عربية
   - text_ai_triggers = ""  # TODO: ترجمة عربية
   - text_anomaly_trigger = ""  # TODO: ترجمة عربية
   - text_api_trigger = ""  # TODO: ترجمة عربية
   - text_business_desc = ""  # TODO: ترجمة عربية
   - text_business_triggers = ""  # TODO: ترجمة عربية
   - text_cron_trigger = ""  # TODO: ترجمة عربية
   - text_customer_trigger = ""  # TODO: ترجمة عربية
   - text_database_change_desc = ""  # TODO: ترجمة عربية
   - text_database_trigger = ""  # TODO: ترجمة عربية
   - text_db_example_add_product = ""  # TODO: ترجمة عربية
   - text_db_example_delete_customer = ""  # TODO: ترجمة عربية
   - text_db_example_update_inventory = ""  # TODO: ترجمة عربية
   - text_delay_example_10min = ""  # TODO: ترجمة عربية
   - text_delay_example_day = ""  # TODO: ترجمة عربية
   - text_delay_example_hour = ""  # TODO: ترجمة عربية
   - text_delay_trigger = ""  # TODO: ترجمة عربية
   - text_delay_trigger_desc = ""  # TODO: ترجمة عربية
   - text_edit_trigger = ""  # TODO: ترجمة عربية
   - text_email_trigger = ""  # TODO: ترجمة عربية
   - text_event_based_desc = ""  # TODO: ترجمة عربية
   - text_event_based_triggers = ""  # TODO: ترجمة عربية
   - text_external_desc = ""  # TODO: ترجمة عربية
   - text_external_triggers = ""  # TODO: ترجمة عربية
   - text_file_trigger = ""  # TODO: ترجمة عربية
   - text_google_sheets_trigger = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_integration_desc = ""  # TODO: ترجمة عربية
   - text_integration_triggers = ""  # TODO: ترجمة عربية
   - text_interval_example_5min = ""  # TODO: ترجمة عربية
   - text_interval_example_day = ""  # TODO: ترجمة عربية
   - text_interval_example_hour = ""  # TODO: ترجمة عربية
   - text_interval_trigger = ""  # TODO: ترجمة عربية
   - text_interval_trigger_desc = ""  # TODO: ترجمة عربية
   - text_inventory_trigger = ""  # TODO: ترجمة عربية
   - text_pattern_trigger = ""  # TODO: ترجمة عربية
   - text_sales_trigger = ""  # TODO: ترجمة عربية
   - text_schedule_trigger_desc = ""  # TODO: ترجمة عربية
   - text_slack_trigger = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_system_event_desc = ""  # TODO: ترجمة عربية
   - text_system_event_trigger = ""  # TODO: ترجمة عربية
   - text_system_example_error = ""  # TODO: ترجمة عربية
   - text_system_example_exceed_limit = ""  # TODO: ترجمة عربية
   - text_system_example_out_of_stock = ""  # TODO: ترجمة عربية
   - text_test_successful = ""  # TODO: ترجمة عربية
   - text_time_based_desc = ""  # TODO: ترجمة عربية
   - text_time_based_triggers = ""  # TODO: ترجمة عربية
   - text_trigger_monitoring = ""  # TODO: ترجمة عربية
   - text_user_action_desc = ""  # TODO: ترجمة عربية
   - text_user_action_trigger = ""  # TODO: ترجمة عربية
   - text_user_example_approve_document = ""  # TODO: ترجمة عربية
   - text_user_example_create_order = ""  # TODO: ترجمة عربية
   - text_user_example_login = ""  # TODO: ترجمة عربية
   - text_visual_editor = ""  # TODO: ترجمة عربية
   - text_webhook_trigger = ""  # TODO: ترجمة عربية
   - text_whatsapp_trigger = ""  # TODO: ترجمة عربية
   - text_workflow_saved = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_configuration_required = ""  # TODO: English translation
   - error_name_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_test_validation = ""  # TODO: English translation
   - error_toggle_validation = ""  # TODO: English translation
   - error_trigger_creation_failed = ""  # TODO: English translation
   - error_trigger_type_required = ""  # TODO: English translation
   - error_workflow_data_required = ""  # TODO: English translation
   - error_workflow_id_required = ""  # TODO: English translation
   - error_workflow_not_found = ""  # TODO: English translation
   - error_workflow_save_failed = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add_trigger = ""  # TODO: English translation
   - text_ai_desc = ""  # TODO: English translation
   - text_ai_prediction_trigger = ""  # TODO: English translation
   - text_ai_triggers = ""  # TODO: English translation
   - text_anomaly_trigger = ""  # TODO: English translation
   - text_api_trigger = ""  # TODO: English translation
   - text_business_desc = ""  # TODO: English translation
   - text_business_triggers = ""  # TODO: English translation
   - text_cron_trigger = ""  # TODO: English translation
   - text_customer_trigger = ""  # TODO: English translation
   - text_database_change_desc = ""  # TODO: English translation
   - text_database_trigger = ""  # TODO: English translation
   - text_db_example_add_product = ""  # TODO: English translation
   - text_db_example_delete_customer = ""  # TODO: English translation
   - text_db_example_update_inventory = ""  # TODO: English translation
   - text_delay_example_10min = ""  # TODO: English translation
   - text_delay_example_day = ""  # TODO: English translation
   - text_delay_example_hour = ""  # TODO: English translation
   - text_delay_trigger = ""  # TODO: English translation
   - text_delay_trigger_desc = ""  # TODO: English translation
   - text_edit_trigger = ""  # TODO: English translation
   - text_email_trigger = ""  # TODO: English translation
   - text_event_based_desc = ""  # TODO: English translation
   - text_event_based_triggers = ""  # TODO: English translation
   - text_external_desc = ""  # TODO: English translation
   - text_external_triggers = ""  # TODO: English translation
   - text_file_trigger = ""  # TODO: English translation
   - text_google_sheets_trigger = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_integration_desc = ""  # TODO: English translation
   - text_integration_triggers = ""  # TODO: English translation
   - text_interval_example_5min = ""  # TODO: English translation
   - text_interval_example_day = ""  # TODO: English translation
   - text_interval_example_hour = ""  # TODO: English translation
   - text_interval_trigger = ""  # TODO: English translation
   - text_interval_trigger_desc = ""  # TODO: English translation
   - text_inventory_trigger = ""  # TODO: English translation
   - text_pattern_trigger = ""  # TODO: English translation
   - text_sales_trigger = ""  # TODO: English translation
   - text_schedule_trigger_desc = ""  # TODO: English translation
   - text_slack_trigger = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_system_event_desc = ""  # TODO: English translation
   - text_system_event_trigger = ""  # TODO: English translation
   - text_system_example_error = ""  # TODO: English translation
   - text_system_example_exceed_limit = ""  # TODO: English translation
   - text_system_example_out_of_stock = ""  # TODO: English translation
   - text_test_successful = ""  # TODO: English translation
   - text_time_based_desc = ""  # TODO: English translation
   - text_time_based_triggers = ""  # TODO: English translation
   - text_trigger_monitoring = ""  # TODO: English translation
   - text_user_action_desc = ""  # TODO: English translation
   - text_user_action_trigger = ""  # TODO: English translation
   - text_user_example_approve_document = ""  # TODO: English translation
   - text_user_example_create_order = ""  # TODO: English translation
   - text_user_example_login = ""  # TODO: English translation
   - text_visual_editor = ""  # TODO: English translation
   - text_webhook_trigger = ""  # TODO: English translation
   - text_whatsapp_trigger = ""  # TODO: English translation
   - text_workflow_saved = ""  # TODO: English translation
