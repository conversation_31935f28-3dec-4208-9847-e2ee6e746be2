📄 Route: extension/feed/google_base
📂 Controller: controller\extension\feed\google_base.php
🧱 Models used (2):
   ✅ setting/setting (5 functions)
   ✅ extension/feed/google_base (8 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\feed\google_base.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\feed\google_base.php (15 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\feed\google_base.php (15 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (28):
   - action
   - button_category_add
   - button_import
   - button_save
   - column_left
   - data_feed
   - entry_category
   - entry_google_category
   - entry_status
   - error_permission
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_pagination
   - text_success
   - user_token
   ... و 8 متغير آخر

❌ Missing in Arabic (16):
   - action
   - button_cancel
   - button_category_add
   - button_import
   - button_save
   - column_left
   - data_feed
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - user_token
   ... و 1 متغير آخر

❌ Missing in English (16):
   - action
   - button_cancel
   - button_category_add
   - button_import
   - button_save
   - column_left
   - data_feed
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - user_token
   ... و 1 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 16 items
      - button_category_add
      - button_save
      - error_warning
      - user_token
      - column_left
   🟡 MISSING_ENGLISH_VARIABLES: 16 items
      - button_category_add
      - button_save
      - error_warning
      - user_token
      - column_left

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 16 متغير عربي و 16 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:22
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.