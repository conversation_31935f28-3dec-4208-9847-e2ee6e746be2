📄 Route: inventory/stock_movement
📂 Controller: controller\inventory\stock_movement.php
🧱 Models used (8):
   - core/central_service_manager
   - inventory/branch
   - inventory/category
   - inventory/manufacturer
   - inventory/stock_movement
   - inventory/stock_movement_enhanced
   - inventory/warehouse
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (2):
   - language\ar\common\header.php
   - language\ar\inventory\stock_movement.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_branch
   - column_date
   - column_lot_number
   - column_movement_type
   - column_notes
   - column_product_name
   - column_quantity_in
   - column_quantity_out
   - column_reference
   - column_running_balance
   - column_total_cost
   - column_unit_cost
   - column_user
   - date_format_short
   - datetime_format
   - error_permission
   - error_product_required
   - heading_title
   - text_all
   - text_branch_type_store
   - text_branch_type_warehouse
   - text_home
   - text_pagination
   - text_with_expiry
   - text_without_expiry

❌ Missing in Arabic:
   - column_branch
   - column_date
   - column_lot_number
   - column_movement_type
   - column_notes
   - column_product_name
   - column_quantity_in
   - column_quantity_out
   - column_reference
   - column_running_balance
   - column_total_cost
   - column_unit_cost
   - column_user
   - date_format_short
   - datetime_format
   - error_permission
   - error_product_required
   - heading_title
   - text_all
   - text_branch_type_store
   - text_branch_type_warehouse
   - text_home
   - text_pagination
   - text_with_expiry
   - text_without_expiry

❌ Missing in English:
   - column_branch
   - column_date
   - column_lot_number
   - column_movement_type
   - column_notes
   - column_product_name
   - column_quantity_in
   - column_quantity_out
   - column_reference
   - column_running_balance
   - column_total_cost
   - column_unit_cost
   - column_user
   - date_format_short
   - datetime_format
   - error_permission
   - error_product_required
   - heading_title
   - text_all
   - text_branch_type_store
   - text_branch_type_warehouse
   - text_home
   - text_pagination
   - text_with_expiry
   - text_without_expiry

💡 Suggested Arabic Additions:
   - column_branch = ""  # TODO: ترجمة عربية
   - column_date = ""  # TODO: ترجمة عربية
   - column_lot_number = ""  # TODO: ترجمة عربية
   - column_movement_type = ""  # TODO: ترجمة عربية
   - column_notes = ""  # TODO: ترجمة عربية
   - column_product_name = ""  # TODO: ترجمة عربية
   - column_quantity_in = ""  # TODO: ترجمة عربية
   - column_quantity_out = ""  # TODO: ترجمة عربية
   - column_reference = ""  # TODO: ترجمة عربية
   - column_running_balance = ""  # TODO: ترجمة عربية
   - column_total_cost = ""  # TODO: ترجمة عربية
   - column_unit_cost = ""  # TODO: ترجمة عربية
   - column_user = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_product_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_branch_type_store = ""  # TODO: ترجمة عربية
   - text_branch_type_warehouse = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_with_expiry = ""  # TODO: ترجمة عربية
   - text_without_expiry = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_branch = ""  # TODO: English translation
   - column_date = ""  # TODO: English translation
   - column_lot_number = ""  # TODO: English translation
   - column_movement_type = ""  # TODO: English translation
   - column_notes = ""  # TODO: English translation
   - column_product_name = ""  # TODO: English translation
   - column_quantity_in = ""  # TODO: English translation
   - column_quantity_out = ""  # TODO: English translation
   - column_reference = ""  # TODO: English translation
   - column_running_balance = ""  # TODO: English translation
   - column_total_cost = ""  # TODO: English translation
   - column_unit_cost = ""  # TODO: English translation
   - column_user = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_product_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_branch_type_store = ""  # TODO: English translation
   - text_branch_type_warehouse = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_with_expiry = ""  # TODO: English translation
   - text_without_expiry = ""  # TODO: English translation
