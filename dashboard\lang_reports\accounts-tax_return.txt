📄 Route: accounts/tax_return
📂 Controller: controller\accounts\tax_return.php
🧱 Models used (2):
   - accounts/tax_return
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\tax_return.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\tax_return.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_range
   - error_financial_year_required
   - error_no_data
   - error_no_data_submit
   - heading_title
   - print_title
   - text_accounting_profit
   - text_exempt_income
   - text_form
   - text_from
   - text_home
   - text_no_results
   - text_non_deductible
   - text_period
   - text_success_eta_submit
   - text_success_generate
   - text_tax_due
   - text_tax_rate
   - text_tax_return
   - text_taxable_profit
   - text_to

❌ Missing in Arabic:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_range
   - error_financial_year_required
   - error_no_data
   - error_no_data_submit
   - heading_title
   - print_title
   - text_accounting_profit
   - text_exempt_income
   - text_form
   - text_from
   - text_home
   - text_no_results
   - text_non_deductible
   - text_period
   - text_success_eta_submit
   - text_success_generate
   - text_tax_due
   - text_tax_rate
   - text_tax_return
   - text_taxable_profit
   - text_to

❌ Missing in English:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_range
   - error_financial_year_required
   - error_no_data
   - error_no_data_submit
   - heading_title
   - print_title
   - text_accounting_profit
   - text_exempt_income
   - text_form
   - text_from
   - text_home
   - text_no_results
   - text_non_deductible
   - text_period
   - text_success_eta_submit
   - text_success_generate
   - text_tax_due
   - text_tax_rate
   - text_tax_return
   - text_taxable_profit
   - text_to

💡 Suggested Arabic Additions:
   - button_filter = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - entry_date_start = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_financial_year_required = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_no_data_submit = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_accounting_profit = ""  # TODO: ترجمة عربية
   - text_exempt_income = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_from = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_non_deductible = ""  # TODO: ترجمة عربية
   - text_period = ""  # TODO: ترجمة عربية
   - text_success_eta_submit = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_tax_due = ""  # TODO: ترجمة عربية
   - text_tax_rate = ""  # TODO: ترجمة عربية
   - text_tax_return = ""  # TODO: ترجمة عربية
   - text_taxable_profit = ""  # TODO: ترجمة عربية
   - text_to = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_filter = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - entry_date_start = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_financial_year_required = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_no_data_submit = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_accounting_profit = ""  # TODO: English translation
   - text_exempt_income = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_from = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_non_deductible = ""  # TODO: English translation
   - text_period = ""  # TODO: English translation
   - text_success_eta_submit = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_tax_due = ""  # TODO: English translation
   - text_tax_rate = ""  # TODO: English translation
   - text_tax_return = ""  # TODO: English translation
   - text_taxable_profit = ""  # TODO: English translation
   - text_to = ""  # TODO: English translation
