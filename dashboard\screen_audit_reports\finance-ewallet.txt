📄 Route: finance/ewallet
📂 Controller: controller\finance\ewallet.php
🧱 Models used (2):
   ✅ finance/ewallet (16 functions)
   ✅ accounts/chartaccount (19 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\finance\ewallet.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\finance\ewallet.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (19):
   - error_account
   - error_account_number
   - error_amount
   - error_description
   - error_ewallet
   - error_name
   - error_opening_balance
   - error_permission
   - error_provider
   - error_transaction_date
   - error_transaction_type
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success
   - text_success_transaction

❌ Missing in Arabic (19):
   - error_account
   - error_account_number
   - error_amount
   - error_description
   - error_ewallet
   - error_name
   - error_opening_balance
   - error_permission
   - heading_title
   - text_add
   - text_disabled
   - text_enabled
   - text_home
   - text_success
   - text_success_transaction
   ... و 4 متغير آخر

❌ Missing in English (19):
   - error_account
   - error_account_number
   - error_amount
   - error_description
   - error_ewallet
   - error_name
   - error_opening_balance
   - error_permission
   - heading_title
   - text_add
   - text_disabled
   - text_enabled
   - text_home
   - text_success
   - text_success_transaction
   ... و 4 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 19 items
      - text_success
      - text_add
      - error_amount
      - text_home
      - text_enabled
   🟡 MISSING_ENGLISH_VARIABLES: 19 items
      - text_success
      - text_add
      - error_amount
      - text_home
      - text_enabled

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 19 متغير عربي و 19 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:03
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.