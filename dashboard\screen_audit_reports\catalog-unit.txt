📄 Route: catalog/unit
📂 Controller: controller\catalog\unit.php
🧱 Models used (1):
   ✅ catalog/unit (8 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\catalog\unit.php (34 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\catalog\unit.php (34 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - error_code
   - error_code_exists
   - error_desc_en
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_home
   - text_pagination
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (1):
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 1 items
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 1 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:49
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.