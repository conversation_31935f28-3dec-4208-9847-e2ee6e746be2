📄 Route: accounts/statement_account
📂 Controller: controller\accounts\statement_account.php
🧱 Models used (2):
   ✅ accounts/statement_account (6 functions)
   ✅ accounts/chartaccount (19 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\accounts\statement_account.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\accounts\statement_account.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (5):
   - date_format_short
   - error_account_not_found
   - heading_title
   - text_home
   - text_view_statement

❌ Missing in Arabic (5):
   - date_format_short
   - error_account_not_found
   - heading_title
   - text_home
   - text_view_statement

❌ Missing in English (5):
   - date_format_short
   - error_account_not_found
   - heading_title
   - text_home
   - text_view_statement

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - text_view_statement
      - heading_title
      - text_home
      - error_account_not_found
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 5 items
      - text_view_statement
      - heading_title
      - text_home
      - error_account_not_found
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 5 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:45
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.