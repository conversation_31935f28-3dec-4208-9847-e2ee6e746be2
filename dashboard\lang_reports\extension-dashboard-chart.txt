📄 Route: extension/dashboard/chart
📂 Controller: controller\extension\dashboard\chart.php
🧱 Models used (2):
   - extension/dashboard/chart
   - setting/setting
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\extension\dashboard\chart.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\dashboard\chart.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_customer
   - text_extension
   - text_home
   - text_order
   - text_success

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_customer
   - text_extension
   - text_home
   - text_order
   - text_success

❌ Missing in English:
   - error_permission
   - heading_title
   - text_customer
   - text_extension
   - text_home
   - text_order
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_customer = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_order = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_customer = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_order = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
