📄 Route: documents/archive
📂 Controller: controller\documents\archive.php
🧱 Models used (8):
   - ai/pattern_recognition
   - catalog/category
   - catalog/product
   - communication/unified_notification
   - core/central_service_manager
   - documents/archive
   - logging/user_activity
   - purchase/supplier
🎨 Twig templates (1):
   - view\template\documents\archive.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_category_required
   - error_file_required
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_title_required
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_adjustment_documents
   - text_approval_documents
   - text_audit_documents
   - text_audit_period
   - text_audit_reports
   - text_category_catalog
   - text_category_catalog_desc
   - text_category_compliance
   - text_category_compliance_desc
   - text_category_documents
   - text_category_id
   - text_category_inventory
   - text_category_inventory_desc
   - text_category_purchase
   - text_category_purchase_desc
   - text_category_sales
   - text_category_sales_desc
   - text_category_workflow
   - text_category_workflow_desc
   - text_certifications
   - text_compliance_reports
   - text_contract_type
   - text_contracts
   - text_customer_documents
   - text_delivery_notes
   - text_home
   - text_invoice_number
   - text_invoices
   - text_legal_documents
   - text_movement_documents
   - text_movement_type
   - text_po_number
   - text_price_lists
   - text_process_documents
   - text_product_id
   - text_product_images
   - text_product_specifications
   - text_purchase_orders
   - text_quotations
   - text_reference_number
   - text_sales_orders
   - text_specification_type
   - text_stock_reports
   - text_supplier_documents
   - text_supplier_id
   - text_upload_document
   - text_upload_success
   - text_warehouse_id
   - text_workflow_logs
   - text_workflow_templates

❌ Missing in Arabic:
   - error_category_required
   - error_file_required
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_title_required
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_adjustment_documents
   - text_approval_documents
   - text_audit_documents
   - text_audit_period
   - text_audit_reports
   - text_category_catalog
   - text_category_catalog_desc
   - text_category_compliance
   - text_category_compliance_desc
   - text_category_documents
   - text_category_id
   - text_category_inventory
   - text_category_inventory_desc
   - text_category_purchase
   - text_category_purchase_desc
   - text_category_sales
   - text_category_sales_desc
   - text_category_workflow
   - text_category_workflow_desc
   - text_certifications
   - text_compliance_reports
   - text_contract_type
   - text_contracts
   - text_customer_documents
   - text_delivery_notes
   - text_home
   - text_invoice_number
   - text_invoices
   - text_legal_documents
   - text_movement_documents
   - text_movement_type
   - text_po_number
   - text_price_lists
   - text_process_documents
   - text_product_id
   - text_product_images
   - text_product_specifications
   - text_purchase_orders
   - text_quotations
   - text_reference_number
   - text_sales_orders
   - text_specification_type
   - text_stock_reports
   - text_supplier_documents
   - text_supplier_id
   - text_upload_document
   - text_upload_success
   - text_warehouse_id
   - text_workflow_logs
   - text_workflow_templates

❌ Missing in English:
   - error_category_required
   - error_file_required
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_title_required
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_adjustment_documents
   - text_approval_documents
   - text_audit_documents
   - text_audit_period
   - text_audit_reports
   - text_category_catalog
   - text_category_catalog_desc
   - text_category_compliance
   - text_category_compliance_desc
   - text_category_documents
   - text_category_id
   - text_category_inventory
   - text_category_inventory_desc
   - text_category_purchase
   - text_category_purchase_desc
   - text_category_sales
   - text_category_sales_desc
   - text_category_workflow
   - text_category_workflow_desc
   - text_certifications
   - text_compliance_reports
   - text_contract_type
   - text_contracts
   - text_customer_documents
   - text_delivery_notes
   - text_home
   - text_invoice_number
   - text_invoices
   - text_legal_documents
   - text_movement_documents
   - text_movement_type
   - text_po_number
   - text_price_lists
   - text_process_documents
   - text_product_id
   - text_product_images
   - text_product_specifications
   - text_purchase_orders
   - text_quotations
   - text_reference_number
   - text_sales_orders
   - text_specification_type
   - text_stock_reports
   - text_supplier_documents
   - text_supplier_id
   - text_upload_document
   - text_upload_success
   - text_warehouse_id
   - text_workflow_logs
   - text_workflow_templates

💡 Suggested Arabic Additions:
   - error_category_required = ""  # TODO: ترجمة عربية
   - error_file_required = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_title_required = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_adjustment_documents = ""  # TODO: ترجمة عربية
   - text_approval_documents = ""  # TODO: ترجمة عربية
   - text_audit_documents = ""  # TODO: ترجمة عربية
   - text_audit_period = ""  # TODO: ترجمة عربية
   - text_audit_reports = ""  # TODO: ترجمة عربية
   - text_category_catalog = ""  # TODO: ترجمة عربية
   - text_category_catalog_desc = ""  # TODO: ترجمة عربية
   - text_category_compliance = ""  # TODO: ترجمة عربية
   - text_category_compliance_desc = ""  # TODO: ترجمة عربية
   - text_category_documents = ""  # TODO: ترجمة عربية
   - text_category_id = ""  # TODO: ترجمة عربية
   - text_category_inventory = ""  # TODO: ترجمة عربية
   - text_category_inventory_desc = ""  # TODO: ترجمة عربية
   - text_category_purchase = ""  # TODO: ترجمة عربية
   - text_category_purchase_desc = ""  # TODO: ترجمة عربية
   - text_category_sales = ""  # TODO: ترجمة عربية
   - text_category_sales_desc = ""  # TODO: ترجمة عربية
   - text_category_workflow = ""  # TODO: ترجمة عربية
   - text_category_workflow_desc = ""  # TODO: ترجمة عربية
   - text_certifications = ""  # TODO: ترجمة عربية
   - text_compliance_reports = ""  # TODO: ترجمة عربية
   - text_contract_type = ""  # TODO: ترجمة عربية
   - text_contracts = ""  # TODO: ترجمة عربية
   - text_customer_documents = ""  # TODO: ترجمة عربية
   - text_delivery_notes = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_invoice_number = ""  # TODO: ترجمة عربية
   - text_invoices = ""  # TODO: ترجمة عربية
   - text_legal_documents = ""  # TODO: ترجمة عربية
   - text_movement_documents = ""  # TODO: ترجمة عربية
   - text_movement_type = ""  # TODO: ترجمة عربية
   - text_po_number = ""  # TODO: ترجمة عربية
   - text_price_lists = ""  # TODO: ترجمة عربية
   - text_process_documents = ""  # TODO: ترجمة عربية
   - text_product_id = ""  # TODO: ترجمة عربية
   - text_product_images = ""  # TODO: ترجمة عربية
   - text_product_specifications = ""  # TODO: ترجمة عربية
   - text_purchase_orders = ""  # TODO: ترجمة عربية
   - text_quotations = ""  # TODO: ترجمة عربية
   - text_reference_number = ""  # TODO: ترجمة عربية
   - text_sales_orders = ""  # TODO: ترجمة عربية
   - text_specification_type = ""  # TODO: ترجمة عربية
   - text_stock_reports = ""  # TODO: ترجمة عربية
   - text_supplier_documents = ""  # TODO: ترجمة عربية
   - text_supplier_id = ""  # TODO: ترجمة عربية
   - text_upload_document = ""  # TODO: ترجمة عربية
   - text_upload_success = ""  # TODO: ترجمة عربية
   - text_warehouse_id = ""  # TODO: ترجمة عربية
   - text_workflow_logs = ""  # TODO: ترجمة عربية
   - text_workflow_templates = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_category_required = ""  # TODO: English translation
   - error_file_required = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_title_required = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_adjustment_documents = ""  # TODO: English translation
   - text_approval_documents = ""  # TODO: English translation
   - text_audit_documents = ""  # TODO: English translation
   - text_audit_period = ""  # TODO: English translation
   - text_audit_reports = ""  # TODO: English translation
   - text_category_catalog = ""  # TODO: English translation
   - text_category_catalog_desc = ""  # TODO: English translation
   - text_category_compliance = ""  # TODO: English translation
   - text_category_compliance_desc = ""  # TODO: English translation
   - text_category_documents = ""  # TODO: English translation
   - text_category_id = ""  # TODO: English translation
   - text_category_inventory = ""  # TODO: English translation
   - text_category_inventory_desc = ""  # TODO: English translation
   - text_category_purchase = ""  # TODO: English translation
   - text_category_purchase_desc = ""  # TODO: English translation
   - text_category_sales = ""  # TODO: English translation
   - text_category_sales_desc = ""  # TODO: English translation
   - text_category_workflow = ""  # TODO: English translation
   - text_category_workflow_desc = ""  # TODO: English translation
   - text_certifications = ""  # TODO: English translation
   - text_compliance_reports = ""  # TODO: English translation
   - text_contract_type = ""  # TODO: English translation
   - text_contracts = ""  # TODO: English translation
   - text_customer_documents = ""  # TODO: English translation
   - text_delivery_notes = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_invoice_number = ""  # TODO: English translation
   - text_invoices = ""  # TODO: English translation
   - text_legal_documents = ""  # TODO: English translation
   - text_movement_documents = ""  # TODO: English translation
   - text_movement_type = ""  # TODO: English translation
   - text_po_number = ""  # TODO: English translation
   - text_price_lists = ""  # TODO: English translation
   - text_process_documents = ""  # TODO: English translation
   - text_product_id = ""  # TODO: English translation
   - text_product_images = ""  # TODO: English translation
   - text_product_specifications = ""  # TODO: English translation
   - text_purchase_orders = ""  # TODO: English translation
   - text_quotations = ""  # TODO: English translation
   - text_reference_number = ""  # TODO: English translation
   - text_sales_orders = ""  # TODO: English translation
   - text_specification_type = ""  # TODO: English translation
   - text_stock_reports = ""  # TODO: English translation
   - text_supplier_documents = ""  # TODO: English translation
   - text_supplier_id = ""  # TODO: English translation
   - text_upload_document = ""  # TODO: English translation
   - text_upload_success = ""  # TODO: English translation
   - text_warehouse_id = ""  # TODO: English translation
   - text_workflow_logs = ""  # TODO: English translation
   - text_workflow_templates = ""  # TODO: English translation
