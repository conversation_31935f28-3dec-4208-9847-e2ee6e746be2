📄 Route: marketplace/api
📂 Controller: controller\marketplace\api.php
🧱 Models used (1):
   - setting/setting
🎨 Twig templates (1):
   - view\template\marketplace\api.twig
🈯 Arabic Language Files (1):
   - language\ar\marketplace\api.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketplace\api.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - error_secret
   - error_username
   - text_success

❌ Missing in Arabic:
   - error_permission
   - error_secret
   - error_username
   - text_success

❌ Missing in English:
   - error_permission
   - error_secret
   - error_username
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - error_secret = ""  # TODO: ترجمة عربية
   - error_username = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - error_secret = ""  # TODO: English translation
   - error_username = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
