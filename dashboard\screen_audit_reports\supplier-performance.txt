📄 Route: supplier/performance
📂 Controller: controller\supplier\performance.php
🧱 Models used (2):
   ✅ supplier/performance (15 functions)
   ✅ supplier/supplier (21 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\supplier\performance.php (128 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\supplier\performance.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - date_format_short
   - error_period
   - error_permission
   - error_scores
   - error_supplier
   - heading_title
   - text_evaluate
   - text_home
   - text_never
   - text_pagination
   - text_success

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (11):
   - date_format_short
   - error_period
   - error_permission
   - error_scores
   - error_supplier
   - heading_title
   - text_evaluate
   - text_home
   - text_never
   - text_pagination
   - text_success

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 11 items
      - error_period
      - text_success
      - text_home
      - error_permission
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 11 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.