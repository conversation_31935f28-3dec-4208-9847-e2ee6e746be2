📄 Route: notification/settings
📂 Controller: controller\notification\settings.php
🧱 Models used (3):
   ✅ notification/settings (9 functions)
   ✅ user/user_group (9 functions)
   ✅ notification/templates (13 functions)
🎨 Twig templates (1):
   ✅ view\template\notification\settings.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\notification\settings.php (173 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\notification\settings.php (173 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (23):
   - action
   - button_cancel
   - button_save
   - column_left
   - error_batch_interval
   - error_invalid_file
   - error_no_file
   - error_permission
   - error_warning
   - footer
   - header
   - tab_advanced
   - tab_general
   - tab_option
   - text_home
   - text_import_success
   - text_success
   - text_test_failed
   - text_test_success
   - user_token
   ... و 3 متغير آخر

❌ Missing in Arabic (17):
   - action
   - cancel
   - column_left
   - error_invalid_file
   - error_no_file
   - error_warning
   - footer
   - success
   - tab_advanced
   - tab_option
   - text_home
   - text_import_success
   - text_test_failed
   - text_test_success
   - user_token
   ... و 2 متغير آخر

❌ Missing in English (17):
   - action
   - cancel
   - column_left
   - error_invalid_file
   - error_no_file
   - error_warning
   - footer
   - success
   - tab_advanced
   - tab_option
   - text_home
   - text_import_success
   - text_test_failed
   - text_test_success
   - user_token
   ... و 2 متغير آخر

🗄️ Database Tables Used (2):
   ❌ setting_value
   ❌ via

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 17 items
      - error_invalid_file
      - error_warning
      - user_token
      - column_left
      - cancel
   🟡 MISSING_ENGLISH_VARIABLES: 17 items
      - error_invalid_file
      - error_warning
      - user_token
      - column_left
      - cancel
   🔴 INVALID_DATABASE_TABLES: 2 items
      - setting_value
      - via

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 17 متغير عربي و 17 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.