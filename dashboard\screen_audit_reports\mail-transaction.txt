📄 Route: mail/transaction
📂 Controller: controller\mail\transaction.php
🧱 Models used (2):
   ✅ customer/customer (51 functions)
   ✅ setting/store (14 functions)
🎨 Twig templates (1):
   ✅ view\template\mail\transaction.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\mail\transaction.php (4 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\mail\transaction.php (4 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (4):
   - text_credit
   - text_received
   - text_subject
   - text_total

🗄️ Database Tables Used (1):
   ❌ credit_limit

🚨 Issues Found (1):
   🔴 INVALID_DATABASE_TABLES: 1 items
      - credit_limit

💡 Recommendations (1):
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ⚠️ 85%
📅 Analysis Date: 2025-07-21 18:33:10
🔧 Total Issues: 1

⚠️ جيد، لكن يحتاج بعض التحسينات.