📄 Route: inventory/stock_movement
📂 Controller: controller\inventory\stock_movement.php
🧱 Models used (8):
   ✅ core/central_service_manager (60 functions)
   ✅ inventory/stock_movement_enhanced (0 functions)
   ✅ inventory/warehouse (47 functions)
   ✅ inventory/category (15 functions)
   ✅ inventory/manufacturer (14 functions)
   ✅ user/user (47 functions)
   ✅ inventory/stock_movement (14 functions)
   ❌ inventory/branch (0 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\stock_movement.php (241 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\stock_movement.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (25):
   - column_branch
   - column_date
   - column_lot_number
   - column_movement_type
   - column_product_name
   - column_quantity_in
   - column_quantity_out
   - column_reference
   - column_running_balance
   - column_total_cost
   - column_user
   - date_format_short
   - datetime_format
   - error_permission
   - error_product_required
   - text_branch_type_store
   - text_branch_type_warehouse
   - text_home
   - text_with_expiry
   - text_without_expiry
   ... و 5 متغير آخر

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (25):
   - column_branch
   - column_lot_number
   - column_movement_type
   - column_product_name
   - column_quantity_in
   - column_quantity_out
   - column_reference
   - column_running_balance
   - column_user
   - date_format_short
   - error_permission
   - error_product_required
   - text_branch_type_store
   - text_home
   - text_without_expiry
   ... و 10 متغير آخر

🗄️ Database Tables Used (4):
   ❌ existing
   ❌ stock
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 25 items
      - column_quantity_out
      - text_home
      - column_movement_type
      - text_branch_type_store
      - column_product_name
   🔴 INVALID_DATABASE_TABLES: 4 items
      - workflow
      - stock
      - template
      - existing
   🟢 MISSING_MODEL_FILES: 1 items
      - inventory/branch

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 25 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 4 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:07
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.