📄 Route: inventory/stocktake
📂 Controller: controller\inventory\stocktake.php
🧱 Models used (5):
   ✅ inventory/stocktake (14 functions)
   ✅ branch/branch (7 functions)
   ✅ notification/notification (8 functions)
   ✅ setting/setting (5 functions)
   ✅ catalog/category (15 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\stocktake.php (133 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\inventory\stocktake.php (133 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (88):
   - column_created_by
   - column_notes
   - column_product
   - column_status
   - column_variance_percentage
   - direction
   - entry_branch
   - error_import_file
   - error_permission
   - error_products
   - text_add
   - text_edit
   - text_status_in_progress
   - text_stocktake_cancelled
   - text_stocktake_cancelled_message
   - text_stocktake_completed_message
   - text_stocktake_created
   - text_stocktake_deleted
   - text_stocktake_deleted_message
   - text_type_partial
   ... و 68 متغير آخر

❌ Missing in Arabic (5):
   - code
   - date_format_short
   - direction
   - error_stocktake_status
   - text_home

❌ Missing in English (5):
   - code
   - date_format_short
   - direction
   - error_stocktake_status
   - text_home

🗄️ Database Tables Used (1):
   ❌ quantity

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - code
      - text_home
      - direction
      - error_stocktake_status
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 5 items
      - code
      - text_home
      - direction
      - error_stocktake_status
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 1 items
      - quantity

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 5 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:06
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.