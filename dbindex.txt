
ALTER TABLE `cod_2fa_message_templates`
  ADD PRIMARY KEY (`template_id`),
  ADD UNIQUE KEY `unique_template` (`template_type`,`language_code`,`purpose`);

ALTER TABLE `cod_2fa_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `unique_setting_key` (`setting_key`);

ALTER TABLE `cod_abandoned_cart`
  ADD PRIMARY KEY (`cart_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_abandoned_cart_recovery`
  ADD PRIMARY KEY (`recovery_id`),
  ADD KEY `cart_id` (`cart_id`);

ALTER TABLE `cod_abandoned_cart_template`
  ADD PRIMARY KEY (`template_id`);

ALTER TABLE `cod_accounts`
  ADD PRIMARY KEY (`account_id`),
  ADD UNIQUE KEY `account_code` (`account_code`),
  ADD KEY `parent_id` (`parent_id`),
  ADD KEY `idx_account_parent` (`parent_id`,`account_code`);

ALTER TABLE `cod_account_description`
  ADD PRIMARY KEY (`account_id`,`language_id`),
  ADD KEY `language_id` (`language_id`);

ALTER TABLE `cod_activity_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `action_type` (`action_type`),
  ADD KEY `module` (`module`),
  ADD KEY `reference_type_id` (`reference_type`,`reference_id`),
  ADD KEY `created_at` (`created_at`),
  ADD KEY `idx_user_action_created` (`user_id`,`action_type`,`created_at`);

ALTER TABLE `cod_address`
  ADD PRIMARY KEY (`address_id`),
  ADD KEY `customer_id` (`customer_id`);

ALTER TABLE `cod_announcement`
  ADD PRIMARY KEY (`announcement_id`),
  ADD KEY `idx_announcement_type` (`type`),
  ADD KEY `idx_announcement_status` (`status`),
  ADD KEY `idx_announcement_priority` (`priority`),
  ADD KEY `idx_announcement_dates` (`start_date`,`end_date`),
  ADD KEY `fk_announcement_creator` (`created_by`),
  ADD KEY `idx_announcement_search` (`title`,`content`(100));

ALTER TABLE `cod_announcement_attachment`
  ADD PRIMARY KEY (`attachment_id`),
  ADD KEY `idx_attachment_announcement` (`announcement_id`),
  ADD KEY `idx_attachment_sort` (`sort_order`),
  ADD KEY `fk_attachment_uploader` (`uploaded_by`);

ALTER TABLE `cod_announcement_comment`
  ADD PRIMARY KEY (`comment_id`),
  ADD KEY `idx_comment_announcement` (`announcement_id`),
  ADD KEY `idx_comment_user` (`user_id`),
  ADD KEY `idx_comment_parent` (`parent_comment_id`),
  ADD KEY `idx_comment_status` (`status`);

ALTER TABLE `cod_announcement_view`
  ADD PRIMARY KEY (`view_id`),
  ADD UNIQUE KEY `unique_announcement_user_view` (`announcement_id`,`user_id`),
  ADD KEY `idx_view_date` (`viewed_at`),
  ADD KEY `fk_view_user` (`user_id`);

ALTER TABLE `cod_api`
  ADD PRIMARY KEY (`api_id`);

ALTER TABLE `cod_api_ip`
  ADD PRIMARY KEY (`api_ip_id`);

ALTER TABLE `cod_api_session`
  ADD PRIMARY KEY (`api_session_id`);

ALTER TABLE `cod_asset_types`
  ADD PRIMARY KEY (`asset_type_id`);

ALTER TABLE `cod_attendance`
  ADD PRIMARY KEY (`attendance_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_attribute`
  ADD PRIMARY KEY (`attribute_id`);

ALTER TABLE `cod_attribute_description`
  ADD PRIMARY KEY (`attribute_id`,`language_id`),
  ADD KEY `language_id` (`language_id`);

ALTER TABLE `cod_attribute_group`
  ADD PRIMARY KEY (`attribute_group_id`);

ALTER TABLE `cod_attribute_group_description`
  ADD PRIMARY KEY (`attribute_group_id`,`language_id`),
  ADD KEY `language_id` (`language_id`);

ALTER TABLE `cod_audit_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `reference_type` (`reference_type`);

ALTER TABLE `cod_audit_plan`
  ADD PRIMARY KEY (`plan_id`),
  ADD KEY `year` (`year`),
  ADD KEY `status` (`status`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `approved_by` (`approved_by`);

ALTER TABLE `cod_audit_task`
  ADD PRIMARY KEY (`task_id`),
  ADD KEY `plan_id` (`plan_id`),
  ADD KEY `status` (`status`),
  ADD KEY `assigned_to` (`assigned_to`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_bank`
  ADD PRIMARY KEY (`bank_id`),
  ADD KEY `account_code` (`account_code`);

ALTER TABLE `cod_bank_account`
  ADD PRIMARY KEY (`account_id`);

ALTER TABLE `cod_bank_reconciliation`
  ADD PRIMARY KEY (`reconciliation_id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`);

ALTER TABLE `cod_bank_transaction`
  ADD PRIMARY KEY (`bank_transaction_id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`);

ALTER TABLE `cod_banner`
  ADD PRIMARY KEY (`banner_id`);

ALTER TABLE `cod_banner_image`
  ADD PRIMARY KEY (`banner_image_id`);

ALTER TABLE `cod_blog_category`
  ADD PRIMARY KEY (`category_id`);

ALTER TABLE `cod_blog_comment`
  ADD PRIMARY KEY (`comment_id`);

ALTER TABLE `cod_blog_post`
  ADD PRIMARY KEY (`post_id`);

ALTER TABLE `cod_blog_post_to_category`
  ADD PRIMARY KEY (`post_id`,`category_id`);

ALTER TABLE `cod_blog_post_to_tag`
  ADD PRIMARY KEY (`post_id`,`tag_id`);

ALTER TABLE `cod_blog_tag`
  ADD PRIMARY KEY (`tag_id`);

ALTER TABLE `cod_branch`
  ADD PRIMARY KEY (`branch_id`),
  ADD KEY `eta_branch_id` (`eta_branch_id`,`manager_id`);

ALTER TABLE `cod_branch_address`
  ADD PRIMARY KEY (`address_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `cod_branch_distance`
  ADD PRIMARY KEY (`distance_id`),
  ADD UNIQUE KEY `unique_branch_zone` (`from_branch_id`,`to_zone_id`),
  ADD KEY `idx_branch_distance_from` (`from_branch_id`),
  ADD KEY `idx_branch_distance_to` (`to_zone_id`),
  ADD KEY `idx_branch_distance_priority` (`priority`),
  ADD KEY `idx_branch_distance_active` (`is_active`);

ALTER TABLE `cod_branch_inventory_snapshot`
  ADD PRIMARY KEY (`snapshot_id`),
  ADD KEY `idx_branch_product` (`branch_id`,`product_id`),
  ADD KEY `idx_date` (`snapshot_date`);

ALTER TABLE `cod_budget`
  ADD PRIMARY KEY (`budget_id`),
  ADD KEY `idx_period_status` (`period_start`,`period_end`,`status`),
  ADD KEY `fk_budget_creator` (`created_by`),
  ADD KEY `fk_budget_approver` (`approved_by`);

ALTER TABLE `cod_budget_line`
  ADD PRIMARY KEY (`line_id`),
  ADD KEY `idx_budget_account` (`budget_id`,`account_code`),
  ADD KEY `fk_budget_line_account` (`account_code`);

ALTER TABLE `cod_bundle_performance_analysis`
  ADD PRIMARY KEY (`analysis_id`),
  ADD UNIQUE KEY `idx_bundle_date` (`bundle_id`,`analysis_date`),
  ADD KEY `idx_analysis_date` (`analysis_date`),
  ADD KEY `idx_performance` (`conversion_rate`,`profit_margin`);

ALTER TABLE `cod_bundle_usage_log`
  ADD PRIMARY KEY (`usage_id`),
  ADD KEY `idx_bundle_date` (`bundle_id`,`used_at`),
  ADD KEY `idx_order_bundle` (`order_id`,`bundle_id`),
  ADD KEY `idx_customer_bundle` (`customer_id`,`bundle_id`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_cart`
  ADD PRIMARY KEY (`cart_id`),
  ADD KEY `cart_id` (`api_id`,`customer_id`,`session_id`,`product_id`,`recurring_id`),
  ADD KEY `is_free` (`is_free`,`bundle_id`,`product_quantity_discount_id`,`group_id`);

ALTER TABLE `cod_cash`
  ADD PRIMARY KEY (`cash_id`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `responsible_user_id` (`responsible_user_id`);

ALTER TABLE `cod_cash_transaction`
  ADD PRIMARY KEY (`cash_transaction_id`),
  ADD KEY `idx_cash_id` (`cash_id`);

ALTER TABLE `cod_category`
  ADD PRIMARY KEY (`category_id`),
  ADD KEY `parent_id` (`parent_id`);

ALTER TABLE `cod_category_description`
  ADD PRIMARY KEY (`category_id`,`language_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_category_filter`
  ADD PRIMARY KEY (`category_id`,`filter_id`);

ALTER TABLE `cod_category_path`
  ADD PRIMARY KEY (`category_id`,`path_id`);

ALTER TABLE `cod_category_to_layout`
  ADD PRIMARY KEY (`category_id`,`store_id`);

ALTER TABLE `cod_category_to_store`
  ADD PRIMARY KEY (`category_id`,`store_id`);

ALTER TABLE `cod_checks`
  ADD PRIMARY KEY (`check_id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`),
  ADD KEY `idx_currency_id` (`currency_id`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_updated_by` (`updated_by`);

ALTER TABLE `cod_compliance_record`
  ADD PRIMARY KEY (`compliance_id`),
  ADD KEY `idx_compliance_type` (`compliance_type`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `cod_compliance_record_fk_user` (`responsible_user_id`);

ALTER TABLE `cod_consignment_inventory`
  ADD PRIMARY KEY (`consignment_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `supplier_id` (`supplier_id`);

ALTER TABLE `cod_cost_calculation_settings`
  ADD PRIMARY KEY (`setting_id`);

ALTER TABLE `cod_country`
  ADD PRIMARY KEY (`country_id`);

ALTER TABLE `cod_coupon`
  ADD PRIMARY KEY (`coupon_id`);

ALTER TABLE `cod_coupon_category`
  ADD PRIMARY KEY (`coupon_id`,`category_id`);

ALTER TABLE `cod_coupon_history`
  ADD PRIMARY KEY (`coupon_history_id`);

ALTER TABLE `cod_coupon_product`
  ADD PRIMARY KEY (`coupon_product_id`);

ALTER TABLE `cod_crm_campaign`
  ADD PRIMARY KEY (`campaign_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_contact`
  ADD PRIMARY KEY (`contact_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_deal`
  ADD PRIMARY KEY (`deal_id`),
  ADD KEY `idx_opportunity_id` (`opportunity_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_lead`
  ADD PRIMARY KEY (`lead_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_opportunity`
  ADD PRIMARY KEY (`opportunity_id`),
  ADD KEY `idx_lead_id` (`lead_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_currency`
  ADD PRIMARY KEY (`currency_id`);

ALTER TABLE `cod_currency_rate_history`
  ADD PRIMARY KEY (`rate_history_id`),
  ADD KEY `idx_currency_id` (`currency_id`),
  ADD KEY `idx_rate_date` (`rate_date`),
  ADD KEY `idx_changed_by` (`changed_by`);

ALTER TABLE `cod_customer`
  ADD PRIMARY KEY (`customer_id`),
  ADD UNIQUE KEY `email` (`email`,`telephone`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `idx_customer_name_email` (`firstname`,`email`),
  ADD KEY `idx_eta_tax_id` (`eta_tax_id`),
  ADD KEY `idx_eta_commercial_registration` (`eta_commercial_registration`);

ALTER TABLE `cod_customer_activity`
  ADD PRIMARY KEY (`customer_activity_id`);

ALTER TABLE `cod_customer_affiliate`
  ADD PRIMARY KEY (`customer_id`);

ALTER TABLE `cod_customer_approval`
  ADD PRIMARY KEY (`customer_approval_id`);

ALTER TABLE `cod_customer_credit_limit`
  ADD PRIMARY KEY (`limit_id`),
  ADD UNIQUE KEY `customer_id` (`customer_id`),
  ADD KEY `approved_by` (`approved_by`);

ALTER TABLE `cod_customer_feedback`
  ADD PRIMARY KEY (`feedback_id`),
  ADD KEY `fk_feedback_customer` (`customer_id`),
  ADD KEY `fk_feedback_assigned_to` (`assigned_to`),
  ADD KEY `idx_feedback_reference` (`reference_module`,`reference_id`),
  ADD KEY `idx_feedback_status` (`status`,`created_at`);

ALTER TABLE `cod_customer_group`
  ADD PRIMARY KEY (`customer_group_id`);

ALTER TABLE `cod_customer_group_description`
  ADD PRIMARY KEY (`customer_group_id`,`language_id`);

ALTER TABLE `cod_customer_history`
  ADD PRIMARY KEY (`customer_history_id`);

ALTER TABLE `cod_customer_ip`
  ADD PRIMARY KEY (`customer_ip_id`),
  ADD KEY `ip` (`ip`);

ALTER TABLE `cod_customer_login`
  ADD PRIMARY KEY (`customer_login_id`),
  ADD KEY `email` (`email`),
  ADD KEY `ip` (`ip`);

ALTER TABLE `cod_customer_note`
  ADD PRIMARY KEY (`note_id`);

ALTER TABLE `cod_customer_online`
  ADD PRIMARY KEY (`ip`);

ALTER TABLE `cod_customer_return_inventory`
  ADD PRIMARY KEY (`return_inventory_id`),
  ADD KEY `return_id` (`return_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `movement_id` (`movement_id`),
  ADD KEY `journal_id` (`journal_id`);

ALTER TABLE `cod_customer_reward`
  ADD PRIMARY KEY (`customer_reward_id`);

ALTER TABLE `cod_customer_search`
  ADD PRIMARY KEY (`customer_search_id`);

ALTER TABLE `cod_customer_transaction`
  ADD PRIMARY KEY (`customer_transaction_id`);

ALTER TABLE `cod_customer_wishlist`
  ADD PRIMARY KEY (`customer_id`,`product_id`);

ALTER TABLE `cod_custom_field`
  ADD PRIMARY KEY (`custom_field_id`);

ALTER TABLE `cod_custom_field_customer_group`
  ADD PRIMARY KEY (`custom_field_id`,`customer_group_id`);

ALTER TABLE `cod_custom_field_description`
  ADD PRIMARY KEY (`custom_field_id`,`language_id`);

ALTER TABLE `cod_custom_field_value`
  ADD PRIMARY KEY (`custom_field_value_id`);

ALTER TABLE `cod_custom_field_value_description`
  ADD PRIMARY KEY (`custom_field_value_id`,`language_id`);

ALTER TABLE `cod_custom_report`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `idx_creator_public` (`created_by`,`is_public`);

ALTER TABLE `cod_data_access_control`
  ADD PRIMARY KEY (`access_id`),
  ADD UNIQUE KEY `idx_access_unique` (`user_id`,`user_group_id`,`resource_type`,`resource_id`),
  ADD KEY `idx_resource` (`resource_type`,`resource_id`),
  ADD KEY `fk_access_group` (`user_group_id`),
  ADD KEY `fk_access_grantor` (`granted_by`);

ALTER TABLE `cod_document_permission`
  ADD PRIMARY KEY (`permission_id`),
  ADD KEY `fk_doc_permission_document` (`document_id`),
  ADD KEY `fk_doc_permission_user` (`user_id`),
  ADD KEY `fk_doc_permission_group` (`user_group_id`),
  ADD KEY `fk_doc_permission_grantor` (`granted_by`);

ALTER TABLE `cod_dynamic_pricing_rule`
  ADD PRIMARY KEY (`rule_id`);

ALTER TABLE `cod_employee_advance`
  ADD PRIMARY KEY (`advance_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_updated_by` (`updated_by`);

ALTER TABLE `cod_employee_advance_installment`
  ADD PRIMARY KEY (`installment_id`),
  ADD KEY `idx_advance_id` (`advance_id`),
  ADD KEY `idx_due_date` (`due_date`);

ALTER TABLE `cod_employee_documents`
  ADD PRIMARY KEY (`document_id`),
  ADD KEY `idx_employee_id` (`employee_id`);

ALTER TABLE `cod_employee_profile`
  ADD PRIMARY KEY (`employee_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_eta_activity_codes`
  ADD PRIMARY KEY (`activity_id`),
  ADD UNIQUE KEY `uk_activity_code` (`activity_code`),
  ADD KEY `idx_parent_code` (`parent_code`),
  ADD KEY `idx_level` (`level`),
  ADD KEY `idx_is_active` (`is_active`);

ALTER TABLE `cod_eta_activity_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_activity_type` (`activity_type`),
  ADD KEY `idx_entity` (`entity_type`,`entity_id`),
  ADD KEY `idx_document_id` (`document_id`),
  ADD KEY `idx_queue_id` (`queue_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_eta_activity_log_type_date` (`activity_type`,`created_at`);

ALTER TABLE `cod_eta_documents`
  ADD PRIMARY KEY (`document_id`),
  ADD UNIQUE KEY `uk_entity` (`entity_type`,`entity_id`),
  ADD UNIQUE KEY `uk_eta_uuid` (`eta_uuid`),
  ADD KEY `idx_internal_id` (`internal_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_eta_status` (`eta_status`),
  ADD KEY `idx_document_type` (`document_type`),
  ADD KEY `idx_submitted_at` (`submitted_at`),
  ADD KEY `idx_eta_documents_status_date` (`status`,`submitted_at`);

ALTER TABLE `cod_eta_document_lines`
  ADD PRIMARY KEY (`line_id`),
  ADD KEY `idx_document_id` (`document_id`),
  ADD KEY `idx_product_id` (`product_id`),
  ADD KEY `idx_item_code` (`item_code`),
  ADD KEY `idx_gpc_code` (`gpc_code`);

ALTER TABLE `cod_eta_queue`
  ADD PRIMARY KEY (`queue_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_entity` (`entity_type`,`entity_id`),
  ADD KEY `idx_priority_status` (`priority`,`status`),
  ADD KEY `idx_scheduled_at` (`scheduled_at`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_eta_queue_priority_status` (`priority`,`status`,`scheduled_at`);

ALTER TABLE `cod_eta_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `uk_setting_key` (`setting_key`),
  ADD KEY `idx_is_active` (`is_active`);

ALTER TABLE `cod_eta_statistics`
  ADD PRIMARY KEY (`stat_id`),
  ADD UNIQUE KEY `uk_stat_date_hour` (`stat_date`,`stat_hour`),
  ADD KEY `idx_stat_date` (`stat_date`),
  ADD KEY `idx_eta_statistics_date_success` (`stat_date`,`success_rate`);

ALTER TABLE `cod_eta_tax_codes`
  ADD PRIMARY KEY (`tax_code_id`),
  ADD UNIQUE KEY `uk_tax_code` (`tax_type`,`tax_subtype`),
  ADD KEY `idx_tax_rate` (`tax_rate`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_effective_dates` (`effective_from`,`effective_to`);

ALTER TABLE `cod_eta_tokens`
  ADD PRIMARY KEY (`token_id`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_environment` (`environment`);

ALTER TABLE `cod_eta_unit_types`
  ADD PRIMARY KEY (`unit_id`),
  ADD UNIQUE KEY `uk_unit_code` (`unit_code`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_is_active` (`is_active`);

ALTER TABLE `cod_event`
  ADD PRIMARY KEY (`event_id`);

ALTER TABLE `cod_extension`
  ADD PRIMARY KEY (`extension_id`);

ALTER TABLE `cod_extension_install`
  ADD PRIMARY KEY (`extension_install_id`);

ALTER TABLE `cod_extension_path`
  ADD PRIMARY KEY (`extension_path_id`);

ALTER TABLE `cod_feedback_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `fk_feedback_history_feedback` (`feedback_id`),
  ADD KEY `fk_feedback_history_user` (`user_id`);

ALTER TABLE `cod_feedback_message`
  ADD PRIMARY KEY (`message_id`),
  ADD KEY `fk_feedback_message_feedback` (`feedback_id`);

ALTER TABLE `cod_feedback_template`
  ADD PRIMARY KEY (`template_id`),
  ADD KEY `fk_feedback_template_creator` (`created_by`);

ALTER TABLE `cod_filter`
  ADD PRIMARY KEY (`filter_id`);

ALTER TABLE `cod_filter_description`
  ADD PRIMARY KEY (`filter_id`,`language_id`);

ALTER TABLE `cod_filter_group`
  ADD PRIMARY KEY (`filter_group_id`);

ALTER TABLE `cod_filter_group_description`
  ADD PRIMARY KEY (`filter_group_id`,`language_id`);

ALTER TABLE `cod_financial_forecast`
  ADD PRIMARY KEY (`forecast_id`),
  ADD KEY `idx_type_period` (`forecast_type`,`period_start`,`period_end`),
  ADD KEY `fk_financial_forecast_user` (`created_by`);

ALTER TABLE `cod_fixed_assets`
  ADD PRIMARY KEY (`asset_id`),
  ADD KEY `asset_type_id` (`asset_type_id`);

ALTER TABLE `cod_fixed_asset_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `asset_id` (`asset_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_geo_zone`
  ADD PRIMARY KEY (`geo_zone_id`);

ALTER TABLE `cod_gift_card`
  ADD PRIMARY KEY (`gift_card_id`);

ALTER TABLE `cod_goods_receipt`
  ADD PRIMARY KEY (`goods_receipt_id`),
  ADD KEY `currency_id` (`currency_id`),
  ADD KEY `idx_receipt_dates` (`receipt_date`,`created_at`),
  ADD KEY `quality_checked_by` (`quality_checked_by`);

ALTER TABLE `cod_goods_receipt_item`
  ADD PRIMARY KEY (`receipt_item_id`),
  ADD KEY `fk_goods_receipt_item_po_item` (`po_item_id`),
  ADD KEY `idx_receipt_item_po_item` (`goods_receipt_id`,`po_item_id`);

ALTER TABLE `cod_governance_issue`
  ADD PRIMARY KEY (`issue_id`),
  ADD KEY `issue_type` (`issue_type`),
  ADD KEY `priority` (`priority`),
  ADD KEY `status` (`status`),
  ADD KEY `responsible_user_id` (`responsible_user_id`),
  ADD KEY `responsible_group_id` (`responsible_group_id`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_governance_meeting`
  ADD PRIMARY KEY (`meeting_id`),
  ADD KEY `idx_meeting_type` (`meeting_type`),
  ADD KEY `idx_meeting_date` (`meeting_date`),
  ADD KEY `cod_governance_meeting_fk_user` (`added_by`),
  ADD KEY `idx_meeting_date_range` (`meeting_date`);

ALTER TABLE `cod_gpc_codes`
  ADD PRIMARY KEY (`gpc_id`),
  ADD KEY `gpc_code` (`gpc_code`);

ALTER TABLE `cod_hitshippo_aramex_details_new`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_hitshippo_aramex_pickup_details`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_hitshippo_fedex_details_new`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_hitshippo_fedex_token`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_import_allocation`
  ADD PRIMARY KEY (`allocation_id`);

ALTER TABLE `cod_import_charge`
  ADD PRIMARY KEY (`charge_id`);

ALTER TABLE `cod_import_shipment`
  ADD PRIMARY KEY (`shipment_id`);

ALTER TABLE `cod_information`
  ADD PRIMARY KEY (`information_id`);

ALTER TABLE `cod_information_description`
  ADD PRIMARY KEY (`information_id`,`language_id`);

ALTER TABLE `cod_information_to_layout`
  ADD PRIMARY KEY (`information_id`,`store_id`);

ALTER TABLE `cod_information_to_store`
  ADD PRIMARY KEY (`information_id`,`store_id`);

ALTER TABLE `cod_installment_payment`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `fk_installment_payment_schedule` (`schedule_id`),
  ADD KEY `fk_installment_payment_receiver` (`received_by`),
  ADD KEY `fk_installment_payment_bank` (`bank_account_id`);

ALTER TABLE `cod_installment_plan`
  ADD PRIMARY KEY (`plan_id`),
  ADD KEY `fk_installment_plan_order` (`order_id`),
  ADD KEY `fk_installment_plan_customer` (`customer_id`),
  ADD KEY `fk_installment_plan_template` (`template_id`),
  ADD KEY `fk_installment_plan_approver` (`approved_by`),
  ADD KEY `fk_installment_plan_guarantor` (`guarantor_id`);

ALTER TABLE `cod_installment_plan_template`
  ADD PRIMARY KEY (`template_id`),
  ADD KEY `fk_installment_template_creator` (`created_by`);

ALTER TABLE `cod_installment_reminder`
  ADD PRIMARY KEY (`reminder_id`),
  ADD KEY `fk_installment_reminder_schedule` (`schedule_id`),
  ADD KEY `fk_installment_reminder_creator` (`created_by`);

ALTER TABLE `cod_installment_schedule`
  ADD PRIMARY KEY (`schedule_id`),
  ADD UNIQUE KEY `unique_installment_number` (`plan_id`,`installment_number`),
  ADD KEY `idx_installment_due_date` (`due_date`,`status`);

ALTER TABLE `cod_internal_attachment`
  ADD PRIMARY KEY (`attachment_id`),
  ADD KEY `fk_attachment_message` (`message_id`);

ALTER TABLE `cod_internal_audit`
  ADD PRIMARY KEY (`audit_id`),
  ADD KEY `idx_auditor_user_id` (`auditor_user_id`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_internal_control`
  ADD PRIMARY KEY (`control_id`),
  ADD KEY `idx_responsible_group_id` (`responsible_group_id`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_internal_conversation`
  ADD PRIMARY KEY (`conversation_id`),
  ADD KEY `fk_conversation_creator` (`creator_id`),
  ADD KEY `idx_conversation_reference` (`associated_module`,`reference_id`);

ALTER TABLE `cod_internal_message`
  ADD PRIMARY KEY (`message_id`),
  ADD KEY `fk_message_conversation` (`conversation_id`),
  ADD KEY `fk_message_sender` (`sender_id`),
  ADD KEY `fk_message_parent` (`parent_message_id`),
  ADD KEY `idx_message_reference` (`reference_module`,`reference_id`);

ALTER TABLE `cod_internal_participant`
  ADD PRIMARY KEY (`participant_id`),
  ADD UNIQUE KEY `unique_conversation_user` (`conversation_id`,`user_id`),
  ADD KEY `fk_participant_user` (`user_id`);

ALTER TABLE `cod_inventory_abc_analysis`
  ADD PRIMARY KEY (`abc_id`),
  ADD KEY `idx_product_branch_period` (`product_id`,`branch_id`,`period_end`),
  ADD KEY `idx_abc_class` (`abc_class`),
  ADD KEY `fk_abc_branch` (`branch_id`),
  ADD KEY `fk_abc_user` (`created_by`);

ALTER TABLE `cod_inventory_accounting_reconciliation`
  ADD PRIMARY KEY (`reconciliation_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `approved_by` (`approved_by`);

ALTER TABLE `cod_inventory_accounting_reconciliation_item`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `reconciliation_id` (`reconciliation_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `adjustment_journal_id` (`adjustment_journal_id`);

ALTER TABLE `cod_inventory_account_mapping`
  ADD PRIMARY KEY (`mapping_id`),
  ADD KEY `branch_category_idx` (`branch_id`,`product_category_id`);

ALTER TABLE `cod_inventory_alert`
  ADD PRIMARY KEY (`alert_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `alert_type` (`alert_type`),
  ADD KEY `status` (`status`),
  ADD KEY `acknowledged_by` (`acknowledged_by`);

ALTER TABLE `cod_inventory_cost_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `date_added` (`date_added`),
  ADD KEY `idx_inventory_cost_history_date` (`date_added`,`product_id`);

ALTER TABLE `cod_inventory_cost_update`
  ADD PRIMARY KEY (`update_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `journal_id` (`journal_id`),
  ADD KEY `idx_inventory_cost` (`product_id`,`update_date`);

ALTER TABLE `cod_inventory_count`
  ADD PRIMARY KEY (`count_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `count_date` (`count_date`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_inventory_reservation`
  ADD PRIMARY KEY (`reservation_id`),
  ADD KEY `idx_product_branch` (`product_id`,`branch_id`),
  ADD KEY `idx_source` (`source_type`,`source_id`),
  ADD KEY `fk_reservation_branch` (`branch_id`),
  ADD KEY `fk_reservation_unit` (`unit_id`),
  ADD KEY `fk_reservation_user` (`created_by`);

ALTER TABLE `cod_inventory_role_permissions`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `idx_user_role` (`user_id`,`role_type`),
  ADD KEY `idx_role_type` (`role_type`),
  ADD KEY `idx_branch_role` (`branch_id`,`role_type`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_inventory_sheet`
  ADD PRIMARY KEY (`sheet_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `sheet_date` (`sheet_date`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_inventory_sheet_item`
  ADD PRIMARY KEY (`sheet_item_id`),
  ADD KEY `sheet_id` (`sheet_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`);

ALTER TABLE `cod_inventory_status_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_product_warehouse` (`product_id`,`warehouse_id`),
  ADD KEY `idx_status_change` (`status_from`,`status_to`),
  ADD KEY `idx_created_date` (`created_at`),
  ADD KEY `idx_batch` (`batch_id`),
  ADD KEY `idx_reason` (`reason_id`),
  ADD KEY `idx_reference` (`reference_type`,`reference_id`);

ALTER TABLE `cod_inventory_sync_rules`
  ADD PRIMARY KEY (`rule_id`),
  ADD UNIQUE KEY `idx_product_branch` (`product_id`,`branch_id`),
  ADD KEY `idx_sync_type` (`sync_type`),
  ADD KEY `idx_active_rules` (`is_active`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_inventory_transfer`
  ADD PRIMARY KEY (`transfer_id`),
  ADD KEY `source_branch_id` (`source_branch_id`),
  ADD KEY `destination_branch_id` (`destination_branch_id`),
  ADD KEY `transfer_date` (`transfer_date`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_inventory_transfer_item`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`);

ALTER TABLE `cod_inventory_turnover`
  ADD PRIMARY KEY (`analysis_id`),
  ADD KEY `idx_product_branch_period` (`product_id`,`branch_id`,`period_end`),
  ADD KEY `fk_turnover_branch` (`branch_id`),
  ADD KEY `fk_turnover_user` (`created_by`);

ALTER TABLE `cod_inventory_turnover_analysis`
  ADD PRIMARY KEY (`analysis_id`),
  ADD UNIQUE KEY `idx_product_branch_period` (`product_id`,`branch_id`,`unit_id`,`analysis_period`,`period_start`),
  ADD KEY `idx_turnover_ratio` (`turnover_ratio`),
  ADD KEY `idx_period` (`analysis_period`,`period_start`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `unit_id` (`unit_id`);

ALTER TABLE `cod_inventory_valuation`
  ADD PRIMARY KEY (`valuation_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `valuation_date` (`valuation_date`),
  ADD KEY `idx_inventory_valuation_date` (`valuation_date`,`product_id`,`branch_id`);

ALTER TABLE `cod_invoices`
  ADD PRIMARY KEY (`invoice_id`);

ALTER TABLE `cod_journals`
  ADD PRIMARY KEY (`journal_id`),
  ADD KEY `thedate` (`thedate`),
  ADD KEY `is_cancelled` (`is_cancelled`),
  ADD KEY `idx_journal_date` (`thedate`,`is_cancelled`),
  ADD KEY `idx_date_cancelled` (`thedate`,`is_cancelled`);

ALTER TABLE `cod_journal_attachments`
  ADD PRIMARY KEY (`attachment_id`),
  ADD KEY `journal_id` (`journal_id`);

ALTER TABLE `cod_journal_entries`
  ADD PRIMARY KEY (`entry_id`),
  ADD KEY `journal_id` (`journal_id`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `idx_journal_entries_account` (`account_code`,`is_debit`),
  ADD KEY `idx_journal_entries_account_date` (`account_code`,`journal_id`);

ALTER TABLE `cod_language`
  ADD PRIMARY KEY (`language_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_layout`
  ADD PRIMARY KEY (`layout_id`);

ALTER TABLE `cod_layout_module`
  ADD PRIMARY KEY (`layout_module_id`);

ALTER TABLE `cod_layout_route`
  ADD PRIMARY KEY (`layout_route_id`);

ALTER TABLE `cod_leave_request`
  ADD PRIMARY KEY (`leave_request_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_leave_type_id` (`leave_type_id`),
  ADD KEY `idx_approved_by` (`approved_by`),
  ADD KEY `idx_user_status_date` (`user_id`,`status`,`start_date`,`end_date`);

ALTER TABLE `cod_leave_type`
  ADD PRIMARY KEY (`leave_type_id`);

ALTER TABLE `cod_legal_contract`
  ADD PRIMARY KEY (`contract_id`),
  ADD KEY `idx_contract_type` (`contract_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_start_end_date` (`start_date`,`end_date`);

ALTER TABLE `cod_length_class`
  ADD PRIMARY KEY (`length_class_id`);

ALTER TABLE `cod_length_class_description`
  ADD PRIMARY KEY (`length_class_id`,`language_id`);

ALTER TABLE `cod_location`
  ADD PRIMARY KEY (`location_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_manufacturer`
  ADD PRIMARY KEY (`manufacturer_id`);

ALTER TABLE `cod_manufacturer_to_store`
  ADD PRIMARY KEY (`manufacturer_id`,`store_id`);

ALTER TABLE `cod_marketing`
  ADD PRIMARY KEY (`marketing_id`);

ALTER TABLE `cod_meeting_attendees`
  ADD PRIMARY KEY (`attendee_id`),
  ADD KEY `idx_meeting_id` (`meeting_id`),
  ADD KEY `idx_user_meeting` (`user_id`,`meeting_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_message_recipient`
  ADD PRIMARY KEY (`message_id`,`user_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_modification`
  ADD PRIMARY KEY (`modification_id`);

ALTER TABLE `cod_module`
  ADD PRIMARY KEY (`module_id`);

ALTER TABLE `cod_notices`
  ADD PRIMARY KEY (`notice_id`);

ALTER TABLE `cod_notification_automation`
  ADD PRIMARY KEY (`rule_id`),
  ADD KEY `idx_automation_event` (`trigger_event`),
  ADD KEY `idx_automation_status` (`status`),
  ADD KEY `fk_automation_template` (`notification_template_id`),
  ADD KEY `fk_automation_creator` (`created_by`),
  ADD KEY `idx_automation_trigger` (`trigger_event`,`status`);

ALTER TABLE `cod_notification_automation_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_log_rule` (`rule_id`),
  ADD KEY `idx_log_date` (`executed_at`),
  ADD KEY `idx_log_status` (`execution_status`);

ALTER TABLE `cod_notification_template`
  ADD PRIMARY KEY (`template_id`),
  ADD UNIQUE KEY `unique_template_name` (`name`),
  ADD KEY `idx_template_category` (`category`),
  ADD KEY `idx_template_type` (`type`),
  ADD KEY `idx_template_status` (`status`),
  ADD KEY `fk_template_creator` (`created_by`),
  ADD KEY `idx_template_search` (`name`,`title`);

ALTER TABLE `cod_notification_user`
  ADD PRIMARY KEY (`notification_id`,`user_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_option`
  ADD PRIMARY KEY (`option_id`);

ALTER TABLE `cod_option_description`
  ADD PRIMARY KEY (`option_id`,`language_id`);

ALTER TABLE `cod_option_value`
  ADD PRIMARY KEY (`option_value_id`);

ALTER TABLE `cod_option_value_description`
  ADD PRIMARY KEY (`option_value_id`,`language_id`);

ALTER TABLE `cod_order`
  ADD PRIMARY KEY (`order_id`),
  ADD KEY `idx_order_status_date` (`order_status_id`,`date_added`),
  ADD KEY `idx_order_customer` (`customer_id`,`date_added`),
  ADD KEY `idx_date_status` (`date_added`,`order_status_id`),
  ADD KEY `idx_eta_status` (`eta_status`),
  ADD KEY `idx_eta_document_id` (`eta_document_id`),
  ADD KEY `idx_eta_uuid` (`eta_uuid`),
  ADD KEY `idx_eta_submission_uuid` (`eta_submission_uuid`);

ALTER TABLE `cod_order_cogs`
  ADD PRIMARY KEY (`order_cogs_id`),
  ADD UNIQUE KEY `order_id` (`order_id`),
  ADD KEY `idx_order_cogs_journal` (`journal_id`);

ALTER TABLE `cod_order_history`
  ADD PRIMARY KEY (`order_history_id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `fk_orderhistory_status` (`order_status_id`);

ALTER TABLE `cod_order_option`
  ADD PRIMARY KEY (`order_option_id`);

ALTER TABLE `cod_order_product`
  ADD PRIMARY KEY (`order_product_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `idx_product_id` (`product_id`);

ALTER TABLE `cod_order_shipment`
  ADD PRIMARY KEY (`order_shipment_id`);

ALTER TABLE `cod_order_shipment_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `idx_order_shipment_id` (`order_shipment_id`),
  ADD KEY `idx_updated_by` (`updated_by`);

ALTER TABLE `cod_order_status`
  ADD PRIMARY KEY (`order_status_id`,`language_id`);

ALTER TABLE `cod_order_total`
  ADD PRIMARY KEY (`order_total_id`),
  ADD KEY `order_id` (`order_id`);

ALTER TABLE `cod_order_voucher`
  ADD PRIMARY KEY (`order_voucher_id`);

ALTER TABLE `cod_paymentlinks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`);

ALTER TABLE `cod_payment_gateway`
  ADD PRIMARY KEY (`gateway_id`),
  ADD UNIQUE KEY `unique_gateway_code` (`code`);

ALTER TABLE `cod_payment_gateway_config`
  ADD PRIMARY KEY (`config_id`),
  ADD UNIQUE KEY `unique_gateway_config` (`gateway_id`,`key`,`environment`),
  ADD KEY `fk_gateway_config_updater` (`updated_by`);

ALTER TABLE `cod_payment_invoice`
  ADD PRIMARY KEY (`payment_invoice_id`),
  ADD KEY `fk_payment_id` (`payment_id`),
  ADD KEY `fk_invoice_id_payment` (`invoice_id`);

ALTER TABLE `cod_payment_settlement`
  ADD PRIMARY KEY (`settlement_id`),
  ADD KEY `fk_payment_settlement_gateway` (`gateway_id`),
  ADD KEY `fk_payment_settlement_bank` (`bank_account_id`),
  ADD KEY `fk_payment_settlement_bank_tx` (`bank_transaction_id`),
  ADD KEY `fk_payment_settlement_user` (`reconciled_by`);

ALTER TABLE `cod_payment_settlement_transaction`
  ADD PRIMARY KEY (`settlement_transaction_id`),
  ADD UNIQUE KEY `unique_settlement_transaction` (`settlement_id`,`transaction_id`),
  ADD KEY `fk_settlement_transaction_tx` (`transaction_id`);

ALTER TABLE `cod_payment_transaction`
  ADD PRIMARY KEY (`transaction_id`),
  ADD KEY `fk_payment_transaction_gateway` (`gateway_id`),
  ADD KEY `fk_payment_transaction_order` (`order_id`),
  ADD KEY `fk_payment_transaction_customer` (`customer_id`),
  ADD KEY `fk_payment_transaction_installment` (`installment_payment_id`),
  ADD KEY `fk_payment_transaction_reference` (`reference_transaction_id`),
  ADD KEY `idx_payment_transaction_gateway_id` (`gateway_transaction_id`),
  ADD KEY `idx_payment_transaction_status` (`status`,`created_at`);

ALTER TABLE `cod_payroll_entry`
  ADD PRIMARY KEY (`payroll_entry_id`),
  ADD KEY `idx_payroll_period_id` (`payroll_period_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_payroll_period`
  ADD PRIMARY KEY (`payroll_period_id`);

ALTER TABLE `cod_performance_criteria`
  ADD PRIMARY KEY (`criteria_id`);

ALTER TABLE `cod_performance_review`
  ADD PRIMARY KEY (`review_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_reviewer_id` (`reviewer_id`);

ALTER TABLE `cod_performance_review_criteria`
  ADD PRIMARY KEY (`review_criteria_id`),
  ADD KEY `idx_review_id` (`review_id`),
  ADD KEY `idx_criteria_id` (`criteria_id`);

ALTER TABLE `cod_permission`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `key` (`key`);

ALTER TABLE `cod_pos_cash_handover`
  ADD PRIMARY KEY (`handover_id`),
  ADD KEY `shift_id` (`shift_id`),
  ADD KEY `from_user_id` (`from_user_id`),
  ADD KEY `to_user_id` (`to_user_id`);

ALTER TABLE `cod_pos_session`
  ADD PRIMARY KEY (`session_id`),
  ADD UNIQUE KEY `unique_session_number` (`session_number`),
  ADD KEY `idx_session_terminal` (`terminal_id`),
  ADD KEY `idx_session_cashier` (`cashier_id`),
  ADD KEY `idx_session_status` (`status`);

ALTER TABLE `cod_pos_shift`
  ADD PRIMARY KEY (`shift_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `terminal_id` (`terminal_id`);

ALTER TABLE `cod_pos_terminal`
  ADD PRIMARY KEY (`terminal_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `cod_pos_transaction`
  ADD PRIMARY KEY (`transaction_id`),
  ADD KEY `shift_id` (`shift_id`),
  ADD KEY `order_id` (`order_id`);

ALTER TABLE `cod_product`
  ADD PRIMARY KEY (`product_id`),
  ADD KEY `idx_model` (`model`),
  ADD KEY `idx_sku` (`sku`),
  ADD KEY `idx_product_search` (`model`,`sku`,`upc`,`ean`,`mpn`),
  ADD KEY `idx_product_price` (`price`,`status`),
  ADD KEY `idx_eta_item_code` (`eta_item_code`),
  ADD KEY `idx_gpc_code` (`gpc_code`),
  ADD KEY `idx_egs_code` (`egs_code`);

ALTER TABLE `cod_product_attribute`
  ADD PRIMARY KEY (`product_id`,`attribute_id`,`language_id`);

ALTER TABLE `cod_product_barcode`
  ADD PRIMARY KEY (`product_barcode_id`),
  ADD UNIQUE KEY `unique_barcode` (`barcode`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `product_option_id` (`product_option_id`),
  ADD KEY `product_option_value_id` (`product_option_value_id`),
  ADD KEY `barcode` (`barcode`);

ALTER TABLE `cod_product_batch`
  ADD PRIMARY KEY (`batch_id`),
  ADD KEY `idx_product_branch_expiry` (`product_id`,`branch_id`,`expiry_date`),
  ADD KEY `fk_batch_branch` (`branch_id`),
  ADD KEY `fk_batch_user` (`created_by`);

ALTER TABLE `cod_product_bundle`
  ADD PRIMARY KEY (`bundle_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_bundle_status` (`status`),
  ADD KEY `idx_discount_type` (`discount_type`),
  ADD KEY `idx_validity` (`valid_from`,`valid_to`),
  ADD KEY `idx_usage` (`usage_count`,`usage_limit`),
  ADD KEY `idx_priority` (`priority`);

ALTER TABLE `cod_product_bundle_item`
  ADD PRIMARY KEY (`bundle_item_id`),
  ADD KEY `bundle_id` (`bundle_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `idx_bundle_product` (`bundle_id`,`product_id`),
  ADD KEY `idx_free_items` (`is_free`);

ALTER TABLE `cod_product_description`
  ADD PRIMARY KEY (`product_id`,`language_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_product_dynamic_pricing`
  ADD PRIMARY KEY (`product_id`,`rule_id`);

ALTER TABLE `cod_product_egs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product` (`product_id`),
  ADD KEY `status` (`eta_status`),
  ADD KEY `gpc_code` (`gpc_code`),
  ADD KEY `egs_code` (`egs_code`);

ALTER TABLE `cod_product_filter`
  ADD PRIMARY KEY (`product_id`,`filter_id`);

ALTER TABLE `cod_product_image`
  ADD PRIMARY KEY (`product_image_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_product_inventory`
  ADD PRIMARY KEY (`product_inventory_id`),
  ADD UNIQUE KEY `idx_product_branch_unit` (`product_id`,`branch_id`,`unit_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_branch` (`branch_id`),
  ADD KEY `idx_inventory_product_branch` (`product_id`,`branch_id`),
  ADD KEY `idx_inventory_branch_quantity` (`branch_id`,`quantity`),
  ADD KEY `idx_branch_product_unit` (`branch_id`,`product_id`,`unit_id`),
  ADD KEY `idx_quantity_available` (`quantity_available`),
  ADD KEY `idx_average_cost` (`average_cost`),
  ADD KEY `idx_consignment` (`is_consignment`,`consignment_supplier_id`),
  ADD KEY `idx_reserved_quantity` (`reserved_quantity`),
  ADD KEY `idx_sync_status` (`sync_status`),
  ADD KEY `idx_last_sync` (`last_sync_at`),
  ADD KEY `idx_maintenance_qty` (`quantity_maintenance`),
  ADD KEY `idx_quality_check_qty` (`quantity_quality_check`),
  ADD KEY `idx_damaged_qty` (`quantity_damaged`),
  ADD KEY `idx_expired_qty` (`quantity_expired`),
  ADD KEY `idx_quarantine_qty` (`quantity_quarantine`),
  ADD KEY `idx_status_update` (`last_status_update`);

ALTER TABLE `cod_product_inventory_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `transaction_date` (`transaction_date`),
  ADD KEY `transaction_type` (`transaction_type`);

ALTER TABLE `cod_product_movement`
  ADD PRIMARY KEY (`product_movement_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_date` (`date_added`),
  ADD KEY `idx_product_movement_reference` (`movement_reference_type`,`movement_reference_id`),
  ADD KEY `idx_source_document` (`source_document_type`,`source_document_id`);

ALTER TABLE `cod_product_option`
  ADD PRIMARY KEY (`product_option_id`);

ALTER TABLE `cod_product_option_value`
  ADD PRIMARY KEY (`product_option_value_id`);

ALTER TABLE `cod_product_price_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `changed_by` (`changed_by`);

ALTER TABLE `cod_product_pricing`
  ADD PRIMARY KEY (`product_pricing_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_product_quantity_discounts`
  ADD PRIMARY KEY (`discount_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_product_recommendation`
  ADD PRIMARY KEY (`recommendation_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `related_product_id` (`related_product_id`);

ALTER TABLE `cod_product_recurring`
  ADD PRIMARY KEY (`product_id`,`recurring_id`,`customer_group_id`);

ALTER TABLE `cod_product_related`
  ADD PRIMARY KEY (`product_id`,`related_id`);

ALTER TABLE `cod_product_reward`
  ADD PRIMARY KEY (`product_reward_id`);

ALTER TABLE `cod_product_to_category`
  ADD PRIMARY KEY (`product_id`,`category_id`),
  ADD KEY `category_id` (`category_id`);

ALTER TABLE `cod_product_to_layout`
  ADD PRIMARY KEY (`product_id`,`store_id`);

ALTER TABLE `cod_product_to_store`
  ADD PRIMARY KEY (`product_id`,`store_id`);

ALTER TABLE `cod_product_unit`
  ADD PRIMARY KEY (`product_unit_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_product_unit_type` (`product_id`,`unit_type`),
  ADD KEY `idx_conversion_factor` (`conversion_factor`);

ALTER TABLE `cod_pt_transactions`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_purchase_document`
  ADD PRIMARY KEY (`document_id`),
  ADD KEY `idx_document_search` (`document_name`,`reference_type`,`reference_id`);

ALTER TABLE `cod_purchase_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_reference` (`reference_type`,`reference_id`),
  ADD KEY `idx_user` (`user_id`),
  ADD KEY `idx_created` (`created_at`);

ALTER TABLE `cod_purchase_matching`
  ADD PRIMARY KEY (`matching_id`),
  ADD KEY `idx_status_dates` (`status`,`matched_at`),
  ADD KEY `idx_documents` (`po_id`,`receipt_id`,`invoice_id`),
  ADD KEY `fk_matching_receipt` (`receipt_id`),
  ADD KEY `fk_matching_invoice` (`invoice_id`),
  ADD KEY `fk_matching_user` (`matched_by`);

ALTER TABLE `cod_purchase_matching_item`
  ADD PRIMARY KEY (`matching_item_id`),
  ADD KEY `idx_matching` (`matching_id`),
  ADD KEY `idx_status_quantities` (`status`,`quantity_ordered`,`quantity_received`,`quantity_invoiced`),
  ADD KEY `idx_items` (`po_item_id`,`receipt_item_id`,`invoice_item_id`),
  ADD KEY `fk_matching_item_receipt` (`receipt_item_id`),
  ADD KEY `fk_matching_item_invoice` (`invoice_item_id`),
  ADD KEY `idx_matching_item_status` (`status`,`variance_amount`);

ALTER TABLE `cod_purchase_order`
  ADD PRIMARY KEY (`po_id`),
  ADD KEY `currency_id` (`currency_id`),
  ADD KEY `idx_po_source` (`source_type`,`source_id`),
  ADD KEY `idx_po_dates` (`order_date`,`expected_delivery_date`),
  ADD KEY `idx_po_status_total` (`status`,`total_amount`),
  ADD KEY `financial_approved_by` (`financial_approved_by`),
  ADD KEY `requisition_id` (`quotation_id`),
  ADD KEY `supplier_id` (`supplier_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `quotation_id` (`quotation_id`);

ALTER TABLE `cod_purchase_order_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `fk_po_history_po` (`po_id`);

ALTER TABLE `cod_purchase_order_item`
  ADD PRIMARY KEY (`po_item_id`),
  ADD KEY `cost_updated_by` (`cost_updated_by`),
  ADD KEY `fk_po_item_product` (`product_id`),
  ADD KEY `fk_po_item_unit` (`unit_id`),
  ADD KEY `idx_po_item_product` (`po_id`,`product_id`);

ALTER TABLE `cod_purchase_order_tracking`
  ADD PRIMARY KEY (`tracking_id`),
  ADD KEY `po_id` (`po_id`),
  ADD KEY `status_change` (`status_change`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_purchase_price_variance`
  ADD PRIMARY KEY (`variance_id`),
  ADD KEY `product_branch_idx` (`product_id`,`branch_id`),
  ADD KEY `receipt_invoice_idx` (`receipt_id`,`invoice_id`),
  ADD KEY `fk_variance_branch` (`branch_id`);

ALTER TABLE `cod_purchase_quotation`
  ADD PRIMARY KEY (`quotation_id`),
  ADD KEY `requisition_id` (`requisition_id`),
  ADD KEY `supplier_id` (`supplier_id`),
  ADD KEY `technical_approved_by` (`technical_approved_by`),
  ADD KEY `financial_approved_by` (`financial_approved_by`),
  ADD KEY `idx_quotation_status_dates` (`status`,`created_at`),
  ADD KEY `idx_quotation_requisition_supplier` (`requisition_id`,`supplier_id`);

ALTER TABLE `cod_purchase_quotation_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `quotation_id` (`quotation_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_quotation_history_dates` (`quotation_id`,`created_at`);

ALTER TABLE `cod_purchase_quotation_item`
  ADD PRIMARY KEY (`quotation_item_id`),
  ADD KEY `quotation_id` (`quotation_id`),
  ADD KEY `requisition_item_id` (`requisition_item_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `alternative_product_id` (`alternative_product_id`),
  ADD KEY `idx_quotation_item_product` (`quotation_id`,`product_id`);

ALTER TABLE `cod_purchase_requisition`
  ADD PRIMARY KEY (`requisition_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `department_id` (`user_group_id`),
  ADD KEY `user_group_id` (`user_group_id`),
  ADD KEY `created_by` (`created_by`,`updated_by`),
  ADD KEY `created_at` (`created_at`,`updated_at`),
  ADD KEY `status` (`status`),
  ADD KEY `req_number` (`req_number`),
  ADD KEY `idx_requisition_status` (`status`);

ALTER TABLE `cod_purchase_requisition_history`
  ADD PRIMARY KEY (`history_id`);

ALTER TABLE `cod_purchase_requisition_item`
  ADD PRIMARY KEY (`requisition_item_id`),
  ADD KEY `requisition_id` (`requisition_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `idx_req_item_requisition_product` (`requisition_id`,`product_id`);

ALTER TABLE `cod_purchase_return`
  ADD PRIMARY KEY (`return_id`),
  ADD KEY `fk_purchase_return_supplier` (`supplier_id`),
  ADD KEY `fk_purchase_return_po` (`purchase_order_id`),
  ADD KEY `fk_purchase_return_receipt` (`goods_receipt_id`),
  ADD KEY `fk_purchase_return_user` (`created_by`);

ALTER TABLE `cod_purchase_return_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `return_id` (`return_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_purchase_return_item`
  ADD PRIMARY KEY (`return_item_id`),
  ADD KEY `fk_return_item_return` (`return_id`),
  ADD KEY `fk_return_item_product` (`product_id`),
  ADD KEY `fk_return_item_unit` (`unit_id`);

ALTER TABLE `cod_quality_inspection`
  ADD PRIMARY KEY (`inspection_id`),
  ADD KEY `receipt_id` (`receipt_id`),
  ADD KEY `inspector_id` (`inspector_id`);

ALTER TABLE `cod_quality_inspection_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `inspection_id` (`inspection_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_quality_inspection_result`
  ADD PRIMARY KEY (`result_id`),
  ADD KEY `goods_receipt_id` (`goods_receipt_id`),
  ADD KEY `receipt_item_id` (`receipt_item_id`);

ALTER TABLE `cod_queue_jobs`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_quotation_signatures`
  ADD PRIMARY KEY (`signature_id`),
  ADD KEY `reference_type_id` (`reference_type`,`reference_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_receipts`
  ADD PRIMARY KEY (`receipt_id`);

ALTER TABLE `cod_recommendation_rule`
  ADD PRIMARY KEY (`rule_id`);

ALTER TABLE `cod_report_execution_log`
  ADD PRIMARY KEY (`execution_id`),
  ADD KEY `report_id` (`report_id`),
  ADD KEY `execution_start` (`execution_start`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_return`
  ADD PRIMARY KEY (`return_id`);

ALTER TABLE `cod_return_action`
  ADD PRIMARY KEY (`return_action_id`,`language_id`);

ALTER TABLE `cod_return_history`
  ADD PRIMARY KEY (`return_history_id`);

ALTER TABLE `cod_return_reason`
  ADD PRIMARY KEY (`return_reason_id`,`language_id`);

ALTER TABLE `cod_return_status`
  ADD PRIMARY KEY (`return_status_id`,`language_id`);

ALTER TABLE `cod_review`
  ADD PRIMARY KEY (`review_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_risk_register`
  ADD PRIMARY KEY (`risk_id`),
  ADD KEY `idx_risk_category` (`risk_category`),
  ADD KEY `cod_risk_register_fk_owner` (`owner_user_id`),
  ADD KEY `status` (`status`),
  ADD KEY `owner_group_id` (`owner_group_id`);

ALTER TABLE `cod_sales_forecast`
  ADD PRIMARY KEY (`forecast_id`),
  ADD KEY `idx_product_branch_date` (`product_id`,`branch_id`,`forecast_date`),
  ADD KEY `fk_forecast_branch` (`branch_id`),
  ADD KEY `fk_forecast_user` (`created_by`);

ALTER TABLE `cod_sales_quotation`
  ADD PRIMARY KEY (`quotation_id`),
  ADD KEY `idx_customer_date` (`customer_id`,`quotation_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `fk_sales_quotation_branch` (`branch_id`),
  ADD KEY `fk_sales_quotation_user` (`created_by`);

ALTER TABLE `cod_sales_quotation_item`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `idx_sales__quotation_product` (`quotation_id`,`product_id`),
  ADD KEY `fk_sales__quotation_item_product` (`product_id`),
  ADD KEY `fk_sales__quotation_item_unit` (`unit_id`);

ALTER TABLE `cod_scheduled_report`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `report_type` (`report_type`),
  ADD KEY `frequency` (`frequency`),
  ADD KEY `next_run` (`next_run`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_seo_internal_link`
  ADD PRIMARY KEY (`link_id`);

ALTER TABLE `cod_seo_keyword_tracking`
  ADD PRIMARY KEY (`tracking_id`);

ALTER TABLE `cod_seo_page_analysis`
  ADD PRIMARY KEY (`analysis_id`);

ALTER TABLE `cod_seo_settings`
  ADD PRIMARY KEY (`setting_id`);

ALTER TABLE `cod_seo_url`
  ADD PRIMARY KEY (`seo_url_id`),
  ADD KEY `query` (`query`),
  ADD KEY `keyword` (`keyword`);

ALTER TABLE `cod_session`
  ADD PRIMARY KEY (`session_id`);

ALTER TABLE `cod_setting`
  ADD PRIMARY KEY (`setting_id`);

ALTER TABLE `cod_shipping_company`
  ADD PRIMARY KEY (`company_id`),
  ADD UNIQUE KEY `unique_shipping_code` (`code`);

ALTER TABLE `cod_shipping_company_config`
  ADD PRIMARY KEY (`config_id`),
  ADD UNIQUE KEY `unique_shipping_config` (`company_id`,`key`,`environment`),
  ADD KEY `fk_shipping_config_updater` (`updated_by`);

ALTER TABLE `cod_shipping_courier`
  ADD PRIMARY KEY (`shipping_courier_id`);

ALTER TABLE `cod_shipping_coverage`
  ADD PRIMARY KEY (`coverage_id`),
  ADD KEY `fk_shipping_coverage_company` (`company_id`),
  ADD KEY `fk_shipping_coverage_zone` (`zone_id`),
  ADD KEY `fk_shipping_coverage_country` (`country_id`),
  ADD KEY `fk_shipping_coverage_geo_zone` (`geo_zone_id`);

ALTER TABLE `cod_shipping_order`
  ADD PRIMARY KEY (`shipping_order_id`),
  ADD UNIQUE KEY `unique_order_company` (`order_id`,`company_id`),
  ADD KEY `fk_shipping_order_company` (`company_id`),
  ADD KEY `fk_shipping_order_creator` (`created_by`),
  ADD KEY `idx_shipping_order_tracking` (`tracking_number`),
  ADD KEY `idx_shipping_order_status` (`status`);

ALTER TABLE `cod_shipping_rate`
  ADD PRIMARY KEY (`rate_id`),
  ADD KEY `fk_shipping_rate_company` (`company_id`),
  ADD KEY `fk_shipping_rate_coverage` (`coverage_id`),
  ADD KEY `fk_shipping_rate_creator` (`created_by`);

ALTER TABLE `cod_shipping_settlement`
  ADD PRIMARY KEY (`settlement_id`),
  ADD KEY `fk_shipping_settlement_company` (`company_id`),
  ADD KEY `fk_shipping_settlement_bank` (`bank_transaction_id`),
  ADD KEY `fk_shipping_settlement_creator` (`created_by`),
  ADD KEY `fk_shipping_settlement_reconciler` (`reconciled_by`);

ALTER TABLE `cod_shipping_settlement_order`
  ADD PRIMARY KEY (`settlement_order_id`),
  ADD UNIQUE KEY `unique_settlement_order` (`settlement_id`,`shipping_order_id`),
  ADD KEY `fk_settlement_order_shipping` (`shipping_order_id`);

ALTER TABLE `cod_shipping_tracking`
  ADD PRIMARY KEY (`tracking_id`),
  ADD KEY `fk_shipping_tracking_order` (`shipping_order_id`),
  ADD KEY `fk_shipping_tracking_user` (`created_by`);

ALTER TABLE `cod_statistics`
  ADD PRIMARY KEY (`statistics_id`);

ALTER TABLE `cod_stock_adjustment`
  ADD PRIMARY KEY (`adjustment_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `cod_stock_adjustment_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `adjustment_id` (`adjustment_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_stock_adjustment_item`
  ADD PRIMARY KEY (`adjustment_item_id`),
  ADD KEY `adjustment_id` (`adjustment_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_stock_count`
  ADD PRIMARY KEY (`stock_count_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_stock_count_item`
  ADD PRIMARY KEY (`count_item_id`),
  ADD KEY `stock_count_id` (`stock_count_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `barcode` (`barcode`(190)) COMMENT 'للبحث السريع بالباركود (قد يكفي فهرس 190-200)';

ALTER TABLE `cod_stock_status`
  ADD PRIMARY KEY (`stock_status_id`,`language_id`);

ALTER TABLE `cod_stock_transfer`
  ADD PRIMARY KEY (`transfer_id`),
  ADD KEY `from_branch_id` (`from_branch_id`),
  ADD KEY `to_branch_id` (`to_branch_id`);

ALTER TABLE `cod_stock_transfer_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_stock_transfer_item`
  ADD PRIMARY KEY (`transfer_item_id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_store`
  ADD PRIMARY KEY (`store_id`);

ALTER TABLE `cod_supplier`
  ADD PRIMARY KEY (`supplier_id`),
  ADD UNIQUE KEY `email` (`email`,`telephone`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `firstname` (`firstname`),
  ADD KEY `idx_supplier_name_email` (`firstname`,`email`);

ALTER TABLE `cod_supplier_address`
  ADD PRIMARY KEY (`address_id`),
  ADD KEY `customer_id` (`supplier_id`);

ALTER TABLE `cod_supplier_evaluation`
  ADD PRIMARY KEY (`evaluation_id`),
  ADD KEY `supplier_id` (`supplier_id`),
  ADD KEY `evaluator_id` (`evaluator_id`);

ALTER TABLE `cod_supplier_group`
  ADD PRIMARY KEY (`supplier_group_id`);

ALTER TABLE `cod_supplier_group_description`
  ADD PRIMARY KEY (`supplier_group_id`,`language_id`);

ALTER TABLE `cod_supplier_invoice`
  ADD PRIMARY KEY (`invoice_id`),
  ADD KEY `fk_po_id_invoice` (`po_id`);

ALTER TABLE `cod_supplier_invoice_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_supplier_invoice_item`
  ADD PRIMARY KEY (`invoice_item_id`),
  ADD KEY `fk_invoice_id` (`invoice_id`);

ALTER TABLE `cod_supplier_product_price`
  ADD PRIMARY KEY (`price_id`),
  ADD UNIQUE KEY `supplier_product_unit` (`supplier_id`,`product_id`,`unit_id`,`currency_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `currency_id` (`currency_id`);

ALTER TABLE `cod_system_events`
  ADD PRIMARY KEY (`event_id`),
  ADD KEY `event_type` (`event_type`,`event_action`),
  ADD KEY `reference_idx` (`reference_type`,`reference_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `created_at` (`created_at`);

ALTER TABLE `cod_system_notifications`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `user_group_id` (`user_group_id`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `reference_type_id` (`reference_type`,`reference_id`),
  ADD KEY `idx_user_expiry_created` (`user_id`,`expiry_date`,`created_at`),
  ADD KEY `idx_group_expiry_created` (`user_group_id`,`expiry_date`,`created_at`);

ALTER TABLE `cod_task`
  ADD PRIMARY KEY (`task_id`),
  ADD KEY `idx_assigned_to_completed` (`assigned_to`,`completed`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `fk_task_assigned_by` (`assigned_by`);

ALTER TABLE `cod_tax_class`
  ADD PRIMARY KEY (`tax_class_id`);

ALTER TABLE `cod_tax_rate`
  ADD PRIMARY KEY (`tax_rate_id`);

ALTER TABLE `cod_tax_rate_to_customer_group`
  ADD PRIMARY KEY (`tax_rate_id`,`customer_group_id`);

ALTER TABLE `cod_tax_rule`
  ADD PRIMARY KEY (`tax_rule_id`);

ALTER TABLE `cod_theme`
  ADD PRIMARY KEY (`theme_id`);

ALTER TABLE `cod_translation`
  ADD PRIMARY KEY (`translation_id`);

ALTER TABLE `cod_unavailability_reasons`
  ADD PRIMARY KEY (`reason_id`),
  ADD UNIQUE KEY `unique_reason_code` (`reason_code`),
  ADD KEY `idx_status_type` (`status_type`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

ALTER TABLE `cod_unified_document`
  ADD PRIMARY KEY (`document_id`),
  ADD KEY `fk_document_creator` (`creator_id`),
  ADD KEY `fk_document_department` (`department_id`),
  ADD KEY `fk_document_parent` (`parent_document_id`),
  ADD KEY `idx_document_reference` (`reference_module`,`reference_id`);

ALTER TABLE `cod_unified_notification`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `fk_notification_user` (`user_id`),
  ADD KEY `idx_notification_reference` (`reference_type`,`reference_id`),
  ADD KEY `idx_notification_status` (`user_id`,`read_at`),
  ADD KEY `fk_notification_creator` (`created_by`),
  ADD KEY `idx_notification_type` (`type`),
  ADD KEY `idx_notification_priority` (`priority`),
  ADD KEY `idx_notification_scheduled` (`scheduled_at`),
  ADD KEY `idx_notification_delivery` (`delivery_status`);

ALTER TABLE `cod_unified_workflow`
  ADD PRIMARY KEY (`workflow_id`),
  ADD KEY `fk_workflow_creator` (`creator_id`),
  ADD KEY `idx_workflow_department` (`department_id`);

ALTER TABLE `cod_unit`
  ADD PRIMARY KEY (`unit_id`);

ALTER TABLE `cod_unit_conversion_history`
  ADD PRIMARY KEY (`conversion_history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `from_unit_id` (`from_unit_id`),
  ADD KEY `to_unit_id` (`to_unit_id`);

ALTER TABLE `cod_unit_conversion_log`
  ADD PRIMARY KEY (`conversion_id`),
  ADD KEY `idx_product_date` (`product_id`,`converted_at`),
  ADD KEY `idx_conversion_type` (`conversion_type`),
  ADD KEY `idx_units` (`from_unit_id`,`to_unit_id`),
  ADD KEY `to_unit_id` (`to_unit_id`),
  ADD KEY `converted_by` (`converted_by`);

ALTER TABLE `cod_upload`
  ADD PRIMARY KEY (`upload_id`);

ALTER TABLE `cod_user`
  ADD PRIMARY KEY (`user_id`),
  ADD KEY `idx_user_2fa_enabled` (`two_factor_enabled`),
  ADD KEY `idx_user_phone_verified` (`phone_verified`);

ALTER TABLE `cod_user_2fa_attempts`
  ADD PRIMARY KEY (`attempt_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_attempt_time` (`attempt_time`),
  ADD KEY `idx_2fa_attempts_user_time` (`user_id`,`attempt_time`);

ALTER TABLE `cod_user_activity_log`
  ADD PRIMARY KEY (`activity_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `date_activity` (`date_activity`);

ALTER TABLE `cod_user_group`
  ADD PRIMARY KEY (`user_group_id`);

ALTER TABLE `cod_user_group_permission`
  ADD PRIMARY KEY (`user_group_id`,`permission_id`);

ALTER TABLE `cod_user_kpi_assignment`
  ADD PRIMARY KEY (`assignment_id`),
  ADD UNIQUE KEY `user_kpi_unique` (`user_id`,`kpi_code`),
  ADD KEY `kpi_code` (`kpi_code`);

ALTER TABLE `cod_user_login_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_user_login` (`user_id`,`login_time`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_user_notification_preferences`
  ADD PRIMARY KEY (`preference_id`),
  ADD UNIQUE KEY `unique_user_preferences` (`user_id`);

ALTER TABLE `cod_user_permission`
  ADD PRIMARY KEY (`user_id`,`permission_id`);

ALTER TABLE `cod_user_session`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `token` (`token`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_user_trusted_devices`
  ADD PRIMARY KEY (`device_id`),
  ADD UNIQUE KEY `unique_user_device` (`user_id`,`device_fingerprint`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_trusted_devices_active` (`user_id`,`is_active`,`expires_at`);

ALTER TABLE `cod_user_verification_codes`
  ADD PRIMARY KEY (`code_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_code_type` (`code_type`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_verification_codes_user_type` (`user_id`,`code_type`,`expires_at`);

ALTER TABLE `cod_vendor_payment`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `po_id` (`po_id`),
  ADD KEY `currency_id` (`currency_id`),
  ADD KEY `journal_id` (`journal_id`),
  ADD KEY `idx_payment_dates` (`payment_date`,`created_at`);

ALTER TABLE `cod_virtual_inventory_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_product_date` (`product_id`,`changed_at`),
  ADD KEY `idx_branch_date` (`branch_id`,`changed_at`),
  ADD KEY `idx_change_reason` (`change_reason`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `changed_by` (`changed_by`);

ALTER TABLE `cod_visitors_stats`
  ADD PRIMARY KEY (`visit_date`);

ALTER TABLE `cod_voucher`
  ADD PRIMARY KEY (`voucher_id`);

ALTER TABLE `cod_voucher_history`
  ADD PRIMARY KEY (`voucher_history_id`);

ALTER TABLE `cod_voucher_theme`
  ADD PRIMARY KEY (`voucher_theme_id`);

ALTER TABLE `cod_voucher_theme_description`
  ADD PRIMARY KEY (`voucher_theme_id`,`language_id`);

ALTER TABLE `cod_warranty`
  ADD PRIMARY KEY (`warranty_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `customer_id` (`customer_id`);

ALTER TABLE `cod_weight_class`
  ADD PRIMARY KEY (`weight_class_id`);

ALTER TABLE `cod_weight_class_description`
  ADD PRIMARY KEY (`weight_class_id`,`language_id`);

ALTER TABLE `cod_workflow_approval`
  ADD PRIMARY KEY (`approval_id`),
  ADD KEY `fk_workflow_approval_request` (`request_id`),
  ADD KEY `fk_workflow_approval_step` (`step_id`),
  ADD KEY `fk_workflow_approval_user` (`user_id`),
  ADD KEY `fk_workflow_approval_delegate` (`delegated_to`),
  ADD KEY `idx_request_step_user` (`request_id`,`step_id`,`user_id`),
  ADD KEY `idx_user_created` (`user_id`,`created_at`),
  ADD KEY `idx_created_action` (`created_at`,`action`);

ALTER TABLE `cod_workflow_request`
  ADD PRIMARY KEY (`request_id`),
  ADD KEY `fk_workflow_request_workflow` (`workflow_id`),
  ADD KEY `fk_workflow_request_step` (`current_step_id`),
  ADD KEY `fk_workflow_request_requester` (`requester_id`),
  ADD KEY `idx_workflow_request_reference` (`reference_module`,`reference_id`),
  ADD KEY `idx_status_current_step` (`status`,`current_step_id`),
  ADD KEY `idx_requester_status` (`requester_id`,`status`,`created_at`),
  ADD KEY `idx_created_status` (`created_at`,`status`),
  ADD KEY `idx_status_priority_created` (`status`,`priority`,`created_at`);

ALTER TABLE `cod_workflow_step`
  ADD PRIMARY KEY (`step_id`),
  ADD KEY `fk_workflow_step_workflow` (`workflow_id`),
  ADD KEY `fk_workflow_step_user` (`approver_user_id`),
  ADD KEY `fk_workflow_step_group` (`approver_group_id`),
  ADD KEY `idx_workflow_step_order` (`workflow_id`,`step_order`),
  ADD KEY `idx_approver_user` (`approver_user_id`),
  ADD KEY `idx_approver_group` (`approver_group_id`);

ALTER TABLE `cod_zone`
  ADD PRIMARY KEY (`zone_id`);

ALTER TABLE `cod_zone_distance`
  ADD PRIMARY KEY (`zone_distance_id`),
  ADD UNIQUE KEY `unique_zone_to_zone` (`from_zone_id`,`to_zone_id`),
  ADD KEY `idx_zone_distance_from` (`from_zone_id`),
  ADD KEY `idx_zone_distance_to` (`to_zone_id`),
  ADD KEY `idx_zone_distance_km` (`distance_km`);

ALTER TABLE `cod_zone_to_geo_zone`
  ADD PRIMARY KEY (`zone_to_geo_zone_id`);


ALTER TABLE `cod_2fa_message_templates`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_2fa_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_abandoned_cart`
  MODIFY `cart_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_abandoned_cart_recovery`
  MODIFY `recovery_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_abandoned_cart_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_accounts`
  MODIFY `account_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_activity_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_address`
  MODIFY `address_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement`
  MODIFY `announcement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement_attachment`
  MODIFY `attachment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement_comment`
  MODIFY `comment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement_view`
  MODIFY `view_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_api`
  MODIFY `api_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_api_ip`
  MODIFY `api_ip_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_api_session`
  MODIFY `api_session_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_asset_types`
  MODIFY `asset_type_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_attendance`
  MODIFY `attendance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_attribute`
  MODIFY `attribute_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_attribute_group`
  MODIFY `attribute_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_audit_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_audit_plan`
  MODIFY `plan_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_audit_task`
  MODIFY `task_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank`
  MODIFY `bank_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank_account`
  MODIFY `account_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank_reconciliation`
  MODIFY `reconciliation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank_transaction`
  MODIFY `bank_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_banner`
  MODIFY `banner_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_banner_image`
  MODIFY `banner_image_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_category`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_comment`
  MODIFY `comment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_post`
  MODIFY `post_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_tag`
  MODIFY `tag_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch`
  MODIFY `branch_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch_address`
  MODIFY `address_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch_distance`
  MODIFY `distance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch_inventory_snapshot`
  MODIFY `snapshot_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_budget`
  MODIFY `budget_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_budget_line`
  MODIFY `line_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bundle_performance_analysis`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bundle_usage_log`
  MODIFY `usage_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cart`
  MODIFY `cart_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cash`
  MODIFY `cash_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cash_transaction`
  MODIFY `cash_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_category`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_checks`
  MODIFY `check_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_compliance_record`
  MODIFY `compliance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_consignment_inventory`
  MODIFY `consignment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cost_calculation_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_country`
  MODIFY `country_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_coupon`
  MODIFY `coupon_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_coupon_history`
  MODIFY `coupon_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_coupon_product`
  MODIFY `coupon_product_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_campaign`
  MODIFY `campaign_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_contact`
  MODIFY `contact_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_deal`
  MODIFY `deal_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_lead`
  MODIFY `lead_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_opportunity`
  MODIFY `opportunity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_currency`
  MODIFY `currency_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_currency_rate_history`
  MODIFY `rate_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_activity`
  MODIFY `customer_activity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_approval`
  MODIFY `customer_approval_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_credit_limit`
  MODIFY `limit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_feedback`
  MODIFY `feedback_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_group`
  MODIFY `customer_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_history`
  MODIFY `customer_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_ip`
  MODIFY `customer_ip_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_login`
  MODIFY `customer_login_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_note`
  MODIFY `note_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_return_inventory`
  MODIFY `return_inventory_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_reward`
  MODIFY `customer_reward_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_search`
  MODIFY `customer_search_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_transaction`
  MODIFY `customer_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_custom_field`
  MODIFY `custom_field_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_custom_field_value`
  MODIFY `custom_field_value_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_custom_report`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_data_access_control`
  MODIFY `access_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_document_permission`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_dynamic_pricing_rule`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_advance`
  MODIFY `advance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_advance_installment`
  MODIFY `installment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_documents`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_profile`
  MODIFY `employee_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_activity_codes`
  MODIFY `activity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_activity_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_documents`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_document_lines`
  MODIFY `line_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_queue`
  MODIFY `queue_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_statistics`
  MODIFY `stat_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_tax_codes`
  MODIFY `tax_code_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_tokens`
  MODIFY `token_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_unit_types`
  MODIFY `unit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_event`
  MODIFY `event_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_extension`
  MODIFY `extension_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_extension_install`
  MODIFY `extension_install_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_extension_path`
  MODIFY `extension_path_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_feedback_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_feedback_message`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_feedback_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_filter`
  MODIFY `filter_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_filter_group`
  MODIFY `filter_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_financial_forecast`
  MODIFY `forecast_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_fixed_assets`
  MODIFY `asset_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_fixed_asset_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_geo_zone`
  MODIFY `geo_zone_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_gift_card`
  MODIFY `gift_card_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_governance_issue`
  MODIFY `issue_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_governance_meeting`
  MODIFY `meeting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_gpc_codes`
  MODIFY `gpc_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_aramex_details_new`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_aramex_pickup_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_fedex_details_new`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_fedex_token`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_import_allocation`
  MODIFY `allocation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_import_charge`
  MODIFY `charge_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_import_shipment`
  MODIFY `shipment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_information`
  MODIFY `information_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_payment`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_plan`
  MODIFY `plan_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_plan_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_reminder`
  MODIFY `reminder_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_schedule`
  MODIFY `schedule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_attachment`
  MODIFY `attachment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_audit`
  MODIFY `audit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_control`
  MODIFY `control_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_conversation`
  MODIFY `conversation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_message`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_participant`
  MODIFY `participant_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_abc_analysis`
  MODIFY `abc_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_accounting_reconciliation`
  MODIFY `reconciliation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_accounting_reconciliation_item`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_account_mapping`
  MODIFY `mapping_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_alert`
  MODIFY `alert_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_cost_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_cost_update`
  MODIFY `update_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_count`
  MODIFY `count_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_reservation`
  MODIFY `reservation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_role_permissions`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_sheet`
  MODIFY `sheet_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_sheet_item`
  MODIFY `sheet_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_status_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_sync_rules`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_transfer`
  MODIFY `transfer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_transfer_item`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_turnover`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_turnover_analysis`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_valuation`
  MODIFY `valuation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_invoices`
  MODIFY `invoice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_journals`
  MODIFY `journal_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_journal_attachments`
  MODIFY `attachment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_journal_entries`
  MODIFY `entry_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_language`
  MODIFY `language_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_layout`
  MODIFY `layout_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_layout_module`
  MODIFY `layout_module_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_layout_route`
  MODIFY `layout_route_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_leave_request`
  MODIFY `leave_request_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_leave_type`
  MODIFY `leave_type_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_legal_contract`
  MODIFY `contract_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_length_class`
  MODIFY `length_class_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_location`
  MODIFY `location_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_manufacturer`
  MODIFY `manufacturer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_marketing`
  MODIFY `marketing_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_meeting_attendees`
  MODIFY `attendee_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_modification`
  MODIFY `modification_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_module`
  MODIFY `module_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notices`
  MODIFY `notice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notification_automation`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notification_automation_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notification_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_option`
  MODIFY `option_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_option_value`
  MODIFY `option_value_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order`
  MODIFY `order_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_cogs`
  MODIFY `order_cogs_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_history`
  MODIFY `order_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_option`
  MODIFY `order_option_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_product`
  MODIFY `order_product_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_shipment`
  MODIFY `order_shipment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_shipment_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_status`
  MODIFY `order_status_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_total`
  MODIFY `order_total_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_voucher`
  MODIFY `order_voucher_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_paymentlinks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_gateway`
  MODIFY `gateway_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_gateway_config`
  MODIFY `config_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_invoice`
  MODIFY `payment_invoice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_settlement`
  MODIFY `settlement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_settlement_transaction`
  MODIFY `settlement_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_transaction`
  MODIFY `transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payroll_entry`
  MODIFY `payroll_entry_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payroll_period`
  MODIFY `payroll_period_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_performance_criteria`
  MODIFY `criteria_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_performance_review`
  MODIFY `review_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_performance_review_criteria`
  MODIFY `review_criteria_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_permission`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_cash_handover`
  MODIFY `handover_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_session`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_shift`
  MODIFY `shift_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_terminal`
  MODIFY `terminal_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_transaction`
  MODIFY `transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product`
  MODIFY `product_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_barcode`
  MODIFY `product_barcode_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_batch`
  MODIFY `batch_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_bundle`
  MODIFY `bundle_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_bundle_item`
  MODIFY `bundle_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_egs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_image`
  MODIFY `product_image_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_inventory`
  MODIFY `product_inventory_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_inventory_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_movement`
  MODIFY `product_movement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_option`
  MODIFY `product_option_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_option_value`
  MODIFY `product_option_value_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_price_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_pricing`
  MODIFY `product_pricing_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_quantity_discounts`
  MODIFY `discount_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_recommendation`
  MODIFY `recommendation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_reward`
  MODIFY `product_reward_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_unit`
  MODIFY `product_unit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pt_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_document`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_matching`
  MODIFY `matching_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_matching_item`
  MODIFY `matching_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order`
  MODIFY `po_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order_item`
  MODIFY `po_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order_tracking`
  MODIFY `tracking_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_price_variance`
  MODIFY `variance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_quotation`
  MODIFY `quotation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_quotation_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_quotation_item`
  MODIFY `quotation_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_requisition`
  MODIFY `requisition_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_requisition_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_requisition_item`
  MODIFY `requisition_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_return`
  MODIFY `return_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_return_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_return_item`
  MODIFY `return_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quality_inspection`
  MODIFY `inspection_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quality_inspection_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quality_inspection_result`
  MODIFY `result_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_queue_jobs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quotation_signatures`
  MODIFY `signature_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_receipts`
  MODIFY `receipt_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_recommendation_rule`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_report_execution_log`
  MODIFY `execution_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return`
  MODIFY `return_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_action`
  MODIFY `return_action_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_history`
  MODIFY `return_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_reason`
  MODIFY `return_reason_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_status`
  MODIFY `return_status_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_review`
  MODIFY `review_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_risk_register`
  MODIFY `risk_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_sales_forecast`
  MODIFY `forecast_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_sales_quotation`
  MODIFY `quotation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_sales_quotation_item`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_scheduled_report`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_internal_link`
  MODIFY `link_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_keyword_tracking`
  MODIFY `tracking_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_page_analysis`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_url`
  MODIFY `seo_url_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_setting`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_company`
  MODIFY `company_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_company_config`
  MODIFY `config_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_coverage`
  MODIFY `coverage_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_order`
  MODIFY `shipping_order_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_rate`
  MODIFY `rate_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_settlement`
  MODIFY `settlement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_settlement_order`
  MODIFY `settlement_order_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_tracking`
  MODIFY `tracking_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_statistics`
  MODIFY `statistics_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_adjustment`
  MODIFY `adjustment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_adjustment_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_adjustment_item`
  MODIFY `adjustment_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_count`
  MODIFY `stock_count_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_count_item`
  MODIFY `count_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_status`
  MODIFY `stock_status_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_transfer`
  MODIFY `transfer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_transfer_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_transfer_item`
  MODIFY `transfer_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_store`
  MODIFY `store_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier`
  MODIFY `supplier_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_address`
  MODIFY `address_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_evaluation`
  MODIFY `evaluation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_group`
  MODIFY `supplier_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_invoice`
  MODIFY `invoice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_invoice_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_invoice_item`
  MODIFY `invoice_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_product_price`
  MODIFY `price_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_system_events`
  MODIFY `event_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_system_notifications`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_task`
  MODIFY `task_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_tax_class`
  MODIFY `tax_class_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_tax_rate`
  MODIFY `tax_rate_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_tax_rule`
  MODIFY `tax_rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_theme`
  MODIFY `theme_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_translation`
  MODIFY `translation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unavailability_reasons`
  MODIFY `reason_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unified_document`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unified_notification`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unified_workflow`
  MODIFY `workflow_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unit`
  MODIFY `unit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unit_conversion_history`
  MODIFY `conversion_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unit_conversion_log`
  MODIFY `conversion_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_upload`
  MODIFY `upload_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_2fa_attempts`
  MODIFY `attempt_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_activity_log`
  MODIFY `activity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_group`
  MODIFY `user_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_kpi_assignment`
  MODIFY `assignment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_login_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_notification_preferences`
  MODIFY `preference_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_session`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_trusted_devices`
  MODIFY `device_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_verification_codes`
  MODIFY `code_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_vendor_payment`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_virtual_inventory_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_voucher`
  MODIFY `voucher_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_voucher_history`
  MODIFY `voucher_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_voucher_theme`
  MODIFY `voucher_theme_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_warranty`
  MODIFY `warranty_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_weight_class`
  MODIFY `weight_class_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_workflow_approval`
  MODIFY `approval_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_workflow_request`
  MODIFY `request_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_workflow_step`
  MODIFY `step_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_zone`
  MODIFY `zone_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_zone_distance`
  MODIFY `zone_distance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_zone_to_geo_zone`
  MODIFY `zone_to_geo_zone_id` int(11) NOT NULL AUTO_INCREMENT;


ALTER TABLE `cod_activity_log`
  ADD CONSTRAINT `fk_activity_log_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_announcement`
  ADD CONSTRAINT `fk_announcement_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_announcement_attachment`
  ADD CONSTRAINT `fk_attachment_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `cod_announcement` (`announcement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_attachment_uploader` FOREIGN KEY (`uploaded_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_announcement_comment`
  ADD CONSTRAINT `fk_comment_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `cod_announcement` (`announcement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_comment_parent` FOREIGN KEY (`parent_comment_id`) REFERENCES `cod_announcement_comment` (`comment_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_comment_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_announcement_view`
  ADD CONSTRAINT `fk_view_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `cod_announcement` (`announcement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_view_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_attendance`
  ADD CONSTRAINT `cod_attendance_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_audit_plan`
  ADD CONSTRAINT `fk_audit_plan_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_audit_plan_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_audit_task`
  ADD CONSTRAINT `fk_audit_task_assigned` FOREIGN KEY (`assigned_to`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_audit_task_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_audit_task_plan` FOREIGN KEY (`plan_id`) REFERENCES `cod_audit_plan` (`plan_id`) ON DELETE SET NULL;

ALTER TABLE `cod_bank_reconciliation`
  ADD CONSTRAINT `fk_cod_bank_reconciliation_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE CASCADE;

ALTER TABLE `cod_bank_transaction`
  ADD CONSTRAINT `fk_cod_bank_transaction_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE CASCADE;

ALTER TABLE `cod_budget`
  ADD CONSTRAINT `fk_budget_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_budget_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_budget_line`
  ADD CONSTRAINT `fk_budget_line_account` FOREIGN KEY (`account_code`) REFERENCES `cod_accounts` (`account_code`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_budget_line_budget` FOREIGN KEY (`budget_id`) REFERENCES `cod_budget` (`budget_id`) ON DELETE CASCADE;

ALTER TABLE `cod_bundle_performance_analysis`
  ADD CONSTRAINT `cod_bundle_performance_analysis_ibfk_1` FOREIGN KEY (`bundle_id`) REFERENCES `cod_product_bundle` (`bundle_id`) ON DELETE CASCADE;

ALTER TABLE `cod_bundle_usage_log`
  ADD CONSTRAINT `cod_bundle_usage_log_ibfk_1` FOREIGN KEY (`bundle_id`) REFERENCES `cod_product_bundle` (`bundle_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_bundle_usage_log_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE SET NULL;

ALTER TABLE `cod_cash_transaction`
  ADD CONSTRAINT `fk_cod_cash_transaction_cash` FOREIGN KEY (`cash_id`) REFERENCES `cod_cash` (`cash_id`) ON DELETE CASCADE;

ALTER TABLE `cod_checks`
  ADD CONSTRAINT `fk_cod_checks_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_checks_created_by` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_checks_currency` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_checks_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `cod_compliance_record`
  ADD CONSTRAINT `cod_compliance_record_fk_user` FOREIGN KEY (`responsible_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_campaign`
  ADD CONSTRAINT `cod_crm_campaign_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_contact`
  ADD CONSTRAINT `cod_crm_contact_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_deal`
  ADD CONSTRAINT `cod_crm_deal_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_lead`
  ADD CONSTRAINT `cod_crm_lead_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_opportunity`
  ADD CONSTRAINT `cod_crm_opportunity_fk_lead` FOREIGN KEY (`lead_id`) REFERENCES `cod_crm_lead` (`lead_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `cod_crm_opportunity_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_currency_rate_history`
  ADD CONSTRAINT `fk_cod_rate_history_currency` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_rate_history_user` FOREIGN KEY (`changed_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `cod_customer_credit_limit`
  ADD CONSTRAINT `fk_credit_limit_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_credit_limit_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE CASCADE;

ALTER TABLE `cod_customer_feedback`
  ADD CONSTRAINT `fk_feedback_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_feedback_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE CASCADE;

ALTER TABLE `cod_customer_return_inventory`
  ADD CONSTRAINT `fk_return_inventory_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`),
  ADD CONSTRAINT `fk_return_inventory_journal` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_return_inventory_movement` FOREIGN KEY (`movement_id`) REFERENCES `cod_product_movement` (`product_movement_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_return_inventory_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `fk_return_inventory_return` FOREIGN KEY (`return_id`) REFERENCES `cod_return` (`return_id`) ON DELETE CASCADE;

ALTER TABLE `cod_custom_report`
  ADD CONSTRAINT `fk_custom_report_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_data_access_control`
  ADD CONSTRAINT `fk_access_grantor` FOREIGN KEY (`granted_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_access_group` FOREIGN KEY (`user_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_access_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_document_permission`
  ADD CONSTRAINT `fk_doc_permission_document` FOREIGN KEY (`document_id`) REFERENCES `cod_unified_document` (`document_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_doc_permission_grantor` FOREIGN KEY (`granted_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_doc_permission_group` FOREIGN KEY (`user_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_doc_permission_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_employee_advance`
  ADD CONSTRAINT `fk_cod_employee_advance_created_by` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_employee_advance_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_employee_advance_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_employee_advance_installment`
  ADD CONSTRAINT `fk_cod_employee_advance_installment_advance` FOREIGN KEY (`advance_id`) REFERENCES `cod_employee_advance` (`advance_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_employee_documents`
  ADD CONSTRAINT `cod_employee_documents_fk_employee` FOREIGN KEY (`employee_id`) REFERENCES `cod_employee_profile` (`employee_id`) ON DELETE CASCADE;

ALTER TABLE `cod_employee_profile`
  ADD CONSTRAINT `cod_employee_profile_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_eta_document_lines`
  ADD CONSTRAINT `fk_eta_line_document` FOREIGN KEY (`document_id`) REFERENCES `cod_eta_documents` (`document_id`) ON DELETE CASCADE;

ALTER TABLE `cod_feedback_history`
  ADD CONSTRAINT `fk_feedback_history_feedback` FOREIGN KEY (`feedback_id`) REFERENCES `cod_customer_feedback` (`feedback_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_feedback_history_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_feedback_message`
  ADD CONSTRAINT `fk_feedback_message_feedback` FOREIGN KEY (`feedback_id`) REFERENCES `cod_customer_feedback` (`feedback_id`) ON DELETE CASCADE;

ALTER TABLE `cod_feedback_template`
  ADD CONSTRAINT `fk_feedback_template_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_financial_forecast`
  ADD CONSTRAINT `fk_financial_forecast_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_fixed_assets`
  ADD CONSTRAINT `cod_fixed_assets_ibfk_1` FOREIGN KEY (`asset_type_id`) REFERENCES `cod_asset_types` (`asset_type_id`);

ALTER TABLE `cod_goods_receipt`
  ADD CONSTRAINT `cod_goods_receipt_ibfk_1` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `cod_goods_receipt_ibfk_2` FOREIGN KEY (`quality_checked_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_goods_receipt_item`
  ADD CONSTRAINT `fk_goods_receipt_item_po_item` FOREIGN KEY (`po_item_id`) REFERENCES `cod_purchase_order_item` (`po_item_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_governance_issue`
  ADD CONSTRAINT `fk_governance_issue_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_governance_issue_group` FOREIGN KEY (`responsible_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_governance_issue_user` FOREIGN KEY (`responsible_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_governance_meeting`
  ADD CONSTRAINT `cod_governance_meeting_fk_user` FOREIGN KEY (`added_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_installment_payment`
  ADD CONSTRAINT `fk_installment_payment_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_payment_receiver` FOREIGN KEY (`received_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_payment_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `cod_installment_schedule` (`schedule_id`) ON DELETE CASCADE;

ALTER TABLE `cod_installment_plan`
  ADD CONSTRAINT `fk_installment_plan_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_plan_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`),
  ADD CONSTRAINT `fk_installment_plan_guarantor` FOREIGN KEY (`guarantor_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_plan_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_installment_plan_template` FOREIGN KEY (`template_id`) REFERENCES `cod_installment_plan_template` (`template_id`) ON DELETE SET NULL;

ALTER TABLE `cod_installment_plan_template`
  ADD CONSTRAINT `fk_installment_template_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_installment_reminder`
  ADD CONSTRAINT `fk_installment_reminder_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_reminder_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `cod_installment_schedule` (`schedule_id`) ON DELETE CASCADE;

ALTER TABLE `cod_installment_schedule`
  ADD CONSTRAINT `fk_installment_schedule_plan` FOREIGN KEY (`plan_id`) REFERENCES `cod_installment_plan` (`plan_id`) ON DELETE CASCADE;

ALTER TABLE `cod_internal_attachment`
  ADD CONSTRAINT `fk_attachment_message` FOREIGN KEY (`message_id`) REFERENCES `cod_internal_message` (`message_id`) ON DELETE CASCADE;

ALTER TABLE `cod_internal_audit`
  ADD CONSTRAINT `fk_internal_audit_auditor` FOREIGN KEY (`auditor_user_id`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_internal_conversation`
  ADD CONSTRAINT `fk_conversation_creator` FOREIGN KEY (`creator_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_internal_message`
  ADD CONSTRAINT `fk_message_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `cod_internal_conversation` (`conversation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_message_parent` FOREIGN KEY (`parent_message_id`) REFERENCES `cod_internal_message` (`message_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_message_sender` FOREIGN KEY (`sender_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_internal_participant`
  ADD CONSTRAINT `fk_participant_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `cod_internal_conversation` (`conversation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_participant_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_abc_analysis`
  ADD CONSTRAINT `fk_abc_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_abc_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_abc_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_accounting_reconciliation`
  ADD CONSTRAINT `fk_inventory_recon_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_inventory_recon_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`),
  ADD CONSTRAINT `fk_inventory_recon_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_accounting_reconciliation_item`
  ADD CONSTRAINT `fk_recon_item_journal` FOREIGN KEY (`adjustment_journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_recon_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `fk_recon_item_reconciliation` FOREIGN KEY (`reconciliation_id`) REFERENCES `cod_inventory_accounting_reconciliation` (`reconciliation_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_account_mapping`
  ADD CONSTRAINT `fk_inventory_mapping_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_alert`
  ADD CONSTRAINT `fk_inventory_alert_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inventory_alert_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inventory_alert_user` FOREIGN KEY (`acknowledged_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_inventory_cost_update`
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`),
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_4` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`);

ALTER TABLE `cod_inventory_reservation`
  ADD CONSTRAINT `fk_reservation_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_reservation_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_reservation_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_reservation_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_role_permissions`
  ADD CONSTRAINT `cod_inventory_role_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_role_permissions_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_role_permissions_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_sync_rules`
  ADD CONSTRAINT `cod_inventory_sync_rules_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_sync_rules_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_sync_rules_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_turnover`
  ADD CONSTRAINT `fk_turnover_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_turnover_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_turnover_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_turnover_analysis`
  ADD CONSTRAINT `cod_inventory_turnover_analysis_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_turnover_analysis_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_turnover_analysis_ibfk_3` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE;

ALTER TABLE `cod_journal_attachments`
  ADD CONSTRAINT `cod_journal_attachments_ibfk_1` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE CASCADE;

ALTER TABLE `cod_journal_entries`
  ADD CONSTRAINT `cod_journal_entries_ibfk_1` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE CASCADE;

ALTER TABLE `cod_leave_request`
  ADD CONSTRAINT `cod_leave_request_fk_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `cod_leave_request_fk_leave_type` FOREIGN KEY (`leave_type_id`) REFERENCES `cod_leave_type` (`leave_type_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_leave_request_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_meeting_attendees`
  ADD CONSTRAINT `cod_meeting_attendees_fk_meeting` FOREIGN KEY (`meeting_id`) REFERENCES `cod_governance_meeting` (`meeting_id`) ON DELETE CASCADE;

ALTER TABLE `cod_notification_automation`
  ADD CONSTRAINT `fk_automation_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_automation_template` FOREIGN KEY (`notification_template_id`) REFERENCES `cod_notification_template` (`template_id`);

ALTER TABLE `cod_notification_automation_log`
  ADD CONSTRAINT `fk_log_automation_rule` FOREIGN KEY (`rule_id`) REFERENCES `cod_notification_automation` (`rule_id`) ON DELETE CASCADE;

ALTER TABLE `cod_notification_template`
  ADD CONSTRAINT `fk_template_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_order_history`
  ADD CONSTRAINT `fk_orderhistory_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_orderhistory_status` FOREIGN KEY (`order_status_id`) REFERENCES `cod_order_status` (`order_status_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_order_product`
  ADD CONSTRAINT `fk_orderproduct_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_orderproduct_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_order_shipment_history`
  ADD CONSTRAINT `fk_shipment_history_shipment` FOREIGN KEY (`order_shipment_id`) REFERENCES `cod_order_shipment` (`order_shipment_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_shipment_history_user` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_payment_gateway_config`
  ADD CONSTRAINT `fk_gateway_config_gateway` FOREIGN KEY (`gateway_id`) REFERENCES `cod_payment_gateway` (`gateway_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_gateway_config_updater` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_payment_invoice`
  ADD CONSTRAINT `fk_invoice_id_payment` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`),
  ADD CONSTRAINT `fk_payment_id` FOREIGN KEY (`payment_id`) REFERENCES `cod_vendor_payment` (`payment_id`) ON DELETE CASCADE;

ALTER TABLE `cod_payment_settlement`
  ADD CONSTRAINT `fk_payment_settlement_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_settlement_bank_tx` FOREIGN KEY (`bank_transaction_id`) REFERENCES `cod_bank_transaction` (`bank_transaction_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_settlement_gateway` FOREIGN KEY (`gateway_id`) REFERENCES `cod_payment_gateway` (`gateway_id`),
  ADD CONSTRAINT `fk_payment_settlement_user` FOREIGN KEY (`reconciled_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_payment_settlement_transaction`
  ADD CONSTRAINT `fk_settlement_transaction_settlement` FOREIGN KEY (`settlement_id`) REFERENCES `cod_payment_settlement` (`settlement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_settlement_transaction_tx` FOREIGN KEY (`transaction_id`) REFERENCES `cod_payment_transaction` (`transaction_id`) ON DELETE CASCADE;

ALTER TABLE `cod_payment_transaction`
  ADD CONSTRAINT `fk_payment_transaction_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`),
  ADD CONSTRAINT `fk_payment_transaction_gateway` FOREIGN KEY (`gateway_id`) REFERENCES `cod_payment_gateway` (`gateway_id`),
  ADD CONSTRAINT `fk_payment_transaction_installment` FOREIGN KEY (`installment_payment_id`) REFERENCES `cod_installment_payment` (`payment_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_transaction_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_transaction_reference` FOREIGN KEY (`reference_transaction_id`) REFERENCES `cod_payment_transaction` (`transaction_id`) ON DELETE SET NULL;

ALTER TABLE `cod_payroll_entry`
  ADD CONSTRAINT `cod_payroll_entry_fk_period` FOREIGN KEY (`payroll_period_id`) REFERENCES `cod_payroll_period` (`payroll_period_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_payroll_entry_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_performance_review`
  ADD CONSTRAINT `cod_performance_review_fk_reviewer` FOREIGN KEY (`reviewer_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_performance_review_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_performance_review_criteria`
  ADD CONSTRAINT `cod_performance_review_criteria_fk_criteria` FOREIGN KEY (`criteria_id`) REFERENCES `cod_performance_criteria` (`criteria_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_performance_review_criteria_fk_review` FOREIGN KEY (`review_id`) REFERENCES `cod_performance_review` (`review_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_batch`
  ADD CONSTRAINT `fk_batch_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_batch_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_batch_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_product_inventory`
  ADD CONSTRAINT `fk_cod_product_inventory_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_movement`
  ADD CONSTRAINT `fk_cod_product_movement_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_price_history`
  ADD CONSTRAINT `fk_price_history_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_price_history_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`),
  ADD CONSTRAINT `fk_price_history_user` FOREIGN KEY (`changed_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_product_pricing`
  ADD CONSTRAINT `fk_cod_product_pricing_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_quantity_discounts`
  ADD CONSTRAINT `cod_product_quantity_discounts_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_unit`
  ADD CONSTRAINT `fk_cod_product_unit_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_purchase_matching`
  ADD CONSTRAINT `fk_matching_invoice` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_matching_po` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`),
  ADD CONSTRAINT `fk_matching_receipt` FOREIGN KEY (`receipt_id`) REFERENCES `cod_goods_receipt` (`goods_receipt_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_matching_user` FOREIGN KEY (`matched_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_purchase_matching_item`
  ADD CONSTRAINT `fk_matching_item_invoice` FOREIGN KEY (`invoice_item_id`) REFERENCES `cod_supplier_invoice_item` (`invoice_item_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_matching_item_matching` FOREIGN KEY (`matching_id`) REFERENCES `cod_purchase_matching` (`matching_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_matching_item_po` FOREIGN KEY (`po_item_id`) REFERENCES `cod_purchase_order_item` (`po_item_id`),
  ADD CONSTRAINT `fk_matching_item_receipt` FOREIGN KEY (`receipt_item_id`) REFERENCES `cod_goods_receipt_item` (`receipt_item_id`) ON DELETE SET NULL;

ALTER TABLE `cod_purchase_order`
  ADD CONSTRAINT `cod_purchase_order_ibfk_2` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `cod_purchase_order_ibfk_3` FOREIGN KEY (`financial_approved_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_po_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `cod_supplier` (`supplier_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_order_history`
  ADD CONSTRAINT `fk_po_history_po` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_order_item`
  ADD CONSTRAINT `cod_purchase_order_item_ibfk_1` FOREIGN KEY (`cost_updated_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_po_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_po_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_order_tracking`
  ADD CONSTRAINT `fk_po_tracking_po` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_po_tracking_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_purchase_price_variance`
  ADD CONSTRAINT `fk_variance_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_variance_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_purchase_quotation`
  ADD CONSTRAINT `fk_quotation_financial_approval` FOREIGN KEY (`financial_approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_quotation_technical_approval` FOREIGN KEY (`technical_approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_purchase_quotation_item`
  ADD CONSTRAINT `cod_purchase_quotation_item_ibfk_1` FOREIGN KEY (`quotation_id`) REFERENCES `cod_purchase_quotation` (`quotation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_purchase_quotation_item_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `cod_purchase_quotation_item_ibfk_3` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`),
  ADD CONSTRAINT `fk_quotation_item_alt_product` FOREIGN KEY (`alternative_product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_quotation_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_quotation_item_quotation` FOREIGN KEY (`quotation_id`) REFERENCES `cod_purchase_quotation` (`quotation_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_quotation_item_requisition_item` FOREIGN KEY (`requisition_item_id`) REFERENCES `cod_purchase_requisition_item` (`requisition_item_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_quotation_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_return`
  ADD CONSTRAINT `fk_purchase_return_po` FOREIGN KEY (`purchase_order_id`) REFERENCES `cod_purchase_order` (`po_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_purchase_return_receipt` FOREIGN KEY (`goods_receipt_id`) REFERENCES `cod_goods_receipt` (`goods_receipt_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_purchase_return_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `cod_supplier` (`supplier_id`),
  ADD CONSTRAINT `fk_purchase_return_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_purchase_return_item`
  ADD CONSTRAINT `fk_return_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `fk_return_item_return` FOREIGN KEY (`return_id`) REFERENCES `cod_purchase_return` (`return_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_return_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`);

ALTER TABLE `cod_quotation_signatures`
  ADD CONSTRAINT `fk_quotation_signatures_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_report_execution_log`
  ADD CONSTRAINT `fk_report_execution_report` FOREIGN KEY (`report_id`) REFERENCES `cod_scheduled_report` (`report_id`) ON DELETE CASCADE;

ALTER TABLE `cod_risk_register`
  ADD CONSTRAINT `cod_risk_register_fk_owner` FOREIGN KEY (`owner_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_sales_forecast`
  ADD CONSTRAINT `fk_forecast_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_forecast_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_forecast_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_sales_quotation`
  ADD CONSTRAINT `fk_sales_quotation_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales_quotation_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales_quotation_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_sales_quotation_item`
  ADD CONSTRAINT `fk_sales__quotation_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales__quotation_item_quotation` FOREIGN KEY (`quotation_id`) REFERENCES `cod_sales_quotation` (`quotation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales__quotation_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE;

ALTER TABLE `cod_scheduled_report`
  ADD CONSTRAINT `fk_scheduled_report_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_shipping_company_config`
  ADD CONSTRAINT `fk_shipping_config_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_config_updater` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_shipping_coverage`
  ADD CONSTRAINT `fk_shipping_coverage_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_coverage_country` FOREIGN KEY (`country_id`) REFERENCES `cod_country` (`country_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_coverage_geo_zone` FOREIGN KEY (`geo_zone_id`) REFERENCES `cod_geo_zone` (`geo_zone_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_coverage_zone` FOREIGN KEY (`zone_id`) REFERENCES `cod_zone` (`zone_id`) ON DELETE SET NULL;

ALTER TABLE `cod_shipping_order`
  ADD CONSTRAINT `fk_shipping_order_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`),
  ADD CONSTRAINT `fk_shipping_order_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_shipping_order_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE;

ALTER TABLE `cod_shipping_rate`
  ADD CONSTRAINT `fk_shipping_rate_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_rate_coverage` FOREIGN KEY (`coverage_id`) REFERENCES `cod_shipping_coverage` (`coverage_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_rate_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_shipping_settlement`
  ADD CONSTRAINT `fk_shipping_settlement_bank` FOREIGN KEY (`bank_transaction_id`) REFERENCES `cod_bank_transaction` (`bank_transaction_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_settlement_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`),
  ADD CONSTRAINT `fk_shipping_settlement_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_shipping_settlement_reconciler` FOREIGN KEY (`reconciled_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_shipping_settlement_order`
  ADD CONSTRAINT `fk_settlement_order_settlement` FOREIGN KEY (`settlement_id`) REFERENCES `cod_shipping_settlement` (`settlement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_settlement_order_shipping` FOREIGN KEY (`shipping_order_id`) REFERENCES `cod_shipping_order` (`shipping_order_id`);

ALTER TABLE `cod_shipping_tracking`
  ADD CONSTRAINT `fk_shipping_tracking_order` FOREIGN KEY (`shipping_order_id`) REFERENCES `cod_shipping_order` (`shipping_order_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_tracking_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_stock_count`
  ADD CONSTRAINT `fk_stock_count_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_stock_count_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_stock_count_item`
  ADD CONSTRAINT `fk_stock_count_item_count` FOREIGN KEY (`stock_count_id`) REFERENCES `cod_stock_count` (`stock_count_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_stock_count_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_stock_count_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_supplier_invoice`
  ADD CONSTRAINT `fk_po_id_invoice` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`);

ALTER TABLE `cod_supplier_invoice_item`
  ADD CONSTRAINT `fk_invoice_id` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`) ON DELETE CASCADE;

ALTER TABLE `cod_supplier_product_price`
  ADD CONSTRAINT `fk_supplier_price_currency` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `fk_supplier_price_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_supplier_price_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `cod_supplier` (`supplier_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_supplier_price_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`);

ALTER TABLE `cod_system_notifications`
  ADD CONSTRAINT `fk_notification_group` FOREIGN KEY (`user_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_notification_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_task`
  ADD CONSTRAINT `fk_task_assigned_by` FOREIGN KEY (`assigned_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_task_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_unified_document`
  ADD CONSTRAINT `fk_document_creator` FOREIGN KEY (`creator_id`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_document_parent` FOREIGN KEY (`parent_document_id`) REFERENCES `cod_unified_document` (`document_id`) ON DELETE SET NULL;

ALTER TABLE `cod_unified_notification`
  ADD CONSTRAINT `fk_notification_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_unified_workflow`
  ADD CONSTRAINT `fk_workflow_creator` FOREIGN KEY (`creator_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_unit_conversion_log`
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_2` FOREIGN KEY (`from_unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_3` FOREIGN KEY (`to_unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_4` FOREIGN KEY (`converted_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_user_2fa_attempts`
  ADD CONSTRAINT `cod_user_2fa_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_activity_log`
  ADD CONSTRAINT `fk_user_activity_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_user_kpi_assignment`
  ADD CONSTRAINT `fk_user_kpi_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_user_login_log`
  ADD CONSTRAINT `fk_login_log_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_notification_preferences`
  ADD CONSTRAINT `fk_preferences_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_session`
  ADD CONSTRAINT `fk_user_session_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_user_trusted_devices`
  ADD CONSTRAINT `cod_user_trusted_devices_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_verification_codes`
  ADD CONSTRAINT `cod_user_verification_codes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_vendor_payment`
  ADD CONSTRAINT `cod_vendor_payment_ibfk_1` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`),
  ADD CONSTRAINT `cod_vendor_payment_ibfk_2` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`),
  ADD CONSTRAINT `cod_vendor_payment_ibfk_3` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `cod_vendor_payment_ibfk_4` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`);

ALTER TABLE `cod_virtual_inventory_log`
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_3` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_4` FOREIGN KEY (`changed_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_workflow_approval`
  ADD CONSTRAINT `fk_approval_request` FOREIGN KEY (`request_id`) REFERENCES `cod_workflow_request` (`request_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_approval_step` FOREIGN KEY (`step_id`) REFERENCES `cod_workflow_step` (`step_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_approval_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_workflow_approval_delegate` FOREIGN KEY (`delegated_to`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_approval_request` FOREIGN KEY (`request_id`) REFERENCES `cod_workflow_request` (`request_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_workflow_approval_step` FOREIGN KEY (`step_id`) REFERENCES `cod_workflow_step` (`step_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_workflow_approval_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_workflow_request`
  ADD CONSTRAINT `fk_workflow_request_requester` FOREIGN KEY (`requester_id`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_workflow_request_step` FOREIGN KEY (`current_step_id`) REFERENCES `cod_workflow_step` (`step_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_request_workflow` FOREIGN KEY (`workflow_id`) REFERENCES `cod_unified_workflow` (`workflow_id`);

ALTER TABLE `cod_workflow_step`
  ADD CONSTRAINT `fk_workflow_step_group` FOREIGN KEY (`approver_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_step_user` FOREIGN KEY (`approver_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_step_workflow` FOREIGN KEY (`workflow_id`) REFERENCES `cod_unified_workflow` (`workflow_id`) ON DELETE CASCADE;
