📄 Route: governance/internal_audit
📂 Controller: controller\governance\internal_audit.php
🧱 Models used (1):
   ✅ governance/internal_audit (5 functions)
🎨 Twig templates (1):
   ✅ view\template\governance\internal_audit.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\governance\internal_audit.php (33 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\governance\internal_audit.php (33 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (28):
   - action
   - button_save
   - can_add
   - column_left
   - error_can_add
   - error_can_delete
   - error_can_edit
   - error_heading_title
   - error_user_token
   - error_warning
   - footer
   - header
   - text_can_delete
   - text_can_edit
   - text_heading_title
   - text_home
   - text_list
   - text_text_list
   - text_user_token
   - user_token
   ... و 8 متغير آخر

❌ Missing in Arabic (24):
   - action
   - can_delete
   - can_edit
   - column_left
   - error_can_add
   - error_can_delete
   - error_can_edit
   - error_heading_title
   - error_user_token
   - header
   - text_can_add
   - text_can_delete
   - text_heading_title
   - text_text_list
   - user_token
   ... و 9 متغير آخر

❌ Missing in English (24):
   - action
   - can_delete
   - can_edit
   - column_left
   - error_can_add
   - error_can_delete
   - error_can_edit
   - error_heading_title
   - error_user_token
   - header
   - text_can_add
   - text_can_delete
   - text_heading_title
   - text_text_list
   - user_token
   ... و 9 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 24 items
      - action
      - error_can_edit
      - text_text_list
      - header
      - text_can_delete
   🟡 MISSING_ENGLISH_VARIABLES: 24 items
      - action
      - error_can_edit
      - text_text_list
      - header
      - text_can_delete

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 24 متغير عربي و 24 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:03
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.