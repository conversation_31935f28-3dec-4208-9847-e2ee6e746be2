📄 Route: inventory/location_management
📂 Controller: controller\inventory\location_management.php
🧱 Models used (7):
   ✅ core/central_service_manager (60 functions)
   ❌ inventory/location_management_enhanced (0 functions)
   ✅ inventory/warehouse (47 functions)
   ✅ branch/branch (7 functions)
   ✅ setting/setting (5 functions)
   ✅ user/user_group (9 functions)
   ✅ inventory/location_management (21 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\location_management.php (279 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\location_management.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (22):
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_location_code
   - error_location_code_exists
   - error_location_has_products
   - error_location_type
   - error_name
   - error_permission
   - text_add
   - text_all
   - text_barcode_scanner
   - text_disabled
   - text_enabled
   - text_home
   - text_location_map
   - text_none
   - text_pagination
   - text_success
   - text_usage_report
   ... و 2 متغير آخر

❌ Missing in Arabic (5):
   - error_advanced_permission
   - error_exception
   - error_location_has_products
   - text_home
   - text_pagination

❌ Missing in English (22):
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_location_type
   - error_name
   - error_permission
   - text_add
   - text_barcode_scanner
   - text_disabled
   - text_enabled
   - text_home
   - text_location_map
   - text_none
   - text_success
   - text_usage_report
   ... و 7 متغير آخر

🗄️ Database Tables Used (4):
   ❌ existing
   ❌ stock
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - error_advanced_permission
      - text_home
      - text_pagination
      - error_exception
      - error_location_has_products
   🟡 MISSING_ENGLISH_VARIABLES: 22 items
      - text_success
      - text_add
      - text_home
      - text_usage_report
      - error_exception
   🔴 INVALID_DATABASE_TABLES: 4 items
      - workflow
      - stock
      - template
      - existing
   🟢 MISSING_MODEL_FILES: 1 items
      - inventory/location_management_enhanced

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 22 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 4 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:05
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.