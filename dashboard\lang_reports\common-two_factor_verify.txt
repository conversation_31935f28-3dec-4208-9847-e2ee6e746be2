📄 Route: common/two_factor_verify
📂 Controller: controller\common\two_factor_verify.php
🧱 Models used (3):
   - activity_log
   - user/two_factor_auth
   - user/user
🎨 Twig templates (1):
   - view\template\common\two_factor_verify.twig
🈯 Arabic Language Files (1):
   - language\ar\common\two_factor_verify.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_cancel
   - button_send_email
   - button_send_sms
   - button_verify
   - entry_backup_code
   - entry_code
   - error_backup_invalid
   - error_backup_required
   - error_email_failed
   - error_email_invalid
   - error_email_required
   - error_method_invalid
   - error_method_required
   - error_rate_limit
   - error_session
   - error_sms_failed
   - error_sms_invalid
   - error_sms_required
   - error_totp_invalid
   - error_totp_required
   - heading_title
   - success_email_sent
   - success_sms_sent
   - text_backup
   - text_email
   - text_sms
   - text_totp
   - text_trust_device
   - text_verification

❌ Missing in Arabic:
   - button_cancel
   - button_send_email
   - button_send_sms
   - button_verify
   - entry_backup_code
   - entry_code
   - error_backup_invalid
   - error_backup_required
   - error_email_failed
   - error_email_invalid
   - error_email_required
   - error_method_invalid
   - error_method_required
   - error_rate_limit
   - error_session
   - error_sms_failed
   - error_sms_invalid
   - error_sms_required
   - error_totp_invalid
   - error_totp_required
   - heading_title
   - success_email_sent
   - success_sms_sent
   - text_backup
   - text_email
   - text_sms
   - text_totp
   - text_trust_device
   - text_verification

❌ Missing in English:
   - button_cancel
   - button_send_email
   - button_send_sms
   - button_verify
   - entry_backup_code
   - entry_code
   - error_backup_invalid
   - error_backup_required
   - error_email_failed
   - error_email_invalid
   - error_email_required
   - error_method_invalid
   - error_method_required
   - error_rate_limit
   - error_session
   - error_sms_failed
   - error_sms_invalid
   - error_sms_required
   - error_totp_invalid
   - error_totp_required
   - heading_title
   - success_email_sent
   - success_sms_sent
   - text_backup
   - text_email
   - text_sms
   - text_totp
   - text_trust_device
   - text_verification

💡 Suggested Arabic Additions:
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_send_email = ""  # TODO: ترجمة عربية
   - button_send_sms = ""  # TODO: ترجمة عربية
   - button_verify = ""  # TODO: ترجمة عربية
   - entry_backup_code = ""  # TODO: ترجمة عربية
   - entry_code = ""  # TODO: ترجمة عربية
   - error_backup_invalid = ""  # TODO: ترجمة عربية
   - error_backup_required = ""  # TODO: ترجمة عربية
   - error_email_failed = ""  # TODO: ترجمة عربية
   - error_email_invalid = ""  # TODO: ترجمة عربية
   - error_email_required = ""  # TODO: ترجمة عربية
   - error_method_invalid = ""  # TODO: ترجمة عربية
   - error_method_required = ""  # TODO: ترجمة عربية
   - error_rate_limit = ""  # TODO: ترجمة عربية
   - error_session = ""  # TODO: ترجمة عربية
   - error_sms_failed = ""  # TODO: ترجمة عربية
   - error_sms_invalid = ""  # TODO: ترجمة عربية
   - error_sms_required = ""  # TODO: ترجمة عربية
   - error_totp_invalid = ""  # TODO: ترجمة عربية
   - error_totp_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - success_email_sent = ""  # TODO: ترجمة عربية
   - success_sms_sent = ""  # TODO: ترجمة عربية
   - text_backup = ""  # TODO: ترجمة عربية
   - text_email = ""  # TODO: ترجمة عربية
   - text_sms = ""  # TODO: ترجمة عربية
   - text_totp = ""  # TODO: ترجمة عربية
   - text_trust_device = ""  # TODO: ترجمة عربية
   - text_verification = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_cancel = ""  # TODO: English translation
   - button_send_email = ""  # TODO: English translation
   - button_send_sms = ""  # TODO: English translation
   - button_verify = ""  # TODO: English translation
   - entry_backup_code = ""  # TODO: English translation
   - entry_code = ""  # TODO: English translation
   - error_backup_invalid = ""  # TODO: English translation
   - error_backup_required = ""  # TODO: English translation
   - error_email_failed = ""  # TODO: English translation
   - error_email_invalid = ""  # TODO: English translation
   - error_email_required = ""  # TODO: English translation
   - error_method_invalid = ""  # TODO: English translation
   - error_method_required = ""  # TODO: English translation
   - error_rate_limit = ""  # TODO: English translation
   - error_session = ""  # TODO: English translation
   - error_sms_failed = ""  # TODO: English translation
   - error_sms_invalid = ""  # TODO: English translation
   - error_sms_required = ""  # TODO: English translation
   - error_totp_invalid = ""  # TODO: English translation
   - error_totp_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - success_email_sent = ""  # TODO: English translation
   - success_sms_sent = ""  # TODO: English translation
   - text_backup = ""  # TODO: English translation
   - text_email = ""  # TODO: English translation
   - text_sms = ""  # TODO: English translation
   - text_totp = ""  # TODO: English translation
   - text_trust_device = ""  # TODO: English translation
   - text_verification = ""  # TODO: English translation
