📄 Route: accounts/chartaccount
📂 Controller: controller\accounts\chartaccount.php
🧱 Models used (3):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/chartaccount (19 functions)
   ✅ localisation/language (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\chartaccount.php (116 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\chartaccount.php (113 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (25):
   - column_account_code
   - column_account_name
   - column_account_nature
   - column_account_type
   - column_current_balance
   - column_parent_account
   - column_status
   - date_format_long
   - error_excel_not_supported
   - error_name
   - error_permission
   - text_add
   - text_disabled
   - text_enabled
   - text_home
   - text_import
   - text_print_date
   - text_success
   - text_tax_accounts_added
   - text_tree_view
   ... و 5 متغير آخر

❌ Missing in Arabic (4):
   - date_format_long
   - text_home
   - text_import
   - text_pagination

❌ Missing in English (6):
   - date_format_long
   - error_name
   - error_parent
   - text_home
   - text_import
   - text_pagination

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - text_home
      - text_import
      - text_pagination
      - date_format_long
   🟡 MISSING_ENGLISH_VARIABLES: 6 items
      - text_home
      - text_pagination
      - error_name
      - error_parent
      - text_import
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 6 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:39
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.