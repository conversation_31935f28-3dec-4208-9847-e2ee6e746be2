📄 Route: extension/payment/paypal
📂 Controller: controller\extension\payment\paypal.php
🧱 Models used (7):
   ✅ extension/payment/paypal (18 functions)
   ✅ setting/setting (5 functions)
   ✅ localisation/country (6 functions)
   ✅ localisation/geo_zone (11 functions)
   ✅ localisation/order_status (7 functions)
   ✅ setting/event (10 functions)
   ❌ sale/recurring (0 functions)
🎨 Twig templates (13):
   ✅ view\template\extension\payment\paypal\applepay_button.twig
   ✅ view\template\extension\payment\paypal\auth.twig
   ✅ view\template\extension\payment\paypal\button.twig
   ✅ view\template\extension\payment\paypal\card.twig
   ✅ view\template\extension\payment\paypal\contact.twig
   ✅ view\template\extension\payment\paypal\dashboard.twig
   ✅ view\template\extension\payment\paypal\general.twig
   ✅ view\template\extension\payment\paypal\googlepay_button.twig
   ✅ view\template\extension\payment\paypal\message_configurator.twig
   ✅ view\template\extension\payment\paypal\message_setting.twig
   ✅ view\template\extension\payment\paypal\order.twig
   ✅ view\template\extension\payment\paypal\order_status.twig
   ✅ view\template\extension\payment\paypal\recurring.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\payment\paypal.php (272 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\payment\paypal.php (314 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (244):
   - action
   - entry_applepay_button_align
   - entry_applepay_button_insert_tag
   - entry_contact_merchant
   - entry_environment
   - entry_vault_status
   - error_agree
   - error_permission
   - error_timeout
   - help_applepay_button_status
   - help_card_currency_value
   - merchant_id
   - paypal_sale_total
   - reauthorize_url
   - text_cart_product_quantity
   - text_cart_product_quantity_value
   - text_cart_total
   - text_checkout
   - text_message_alert
   - text_message_footnote_us
   ... و 224 متغير آخر

❌ Missing in Arabic (104):
   - action
   - applepay_download_host_url
   - column_left
   - entry_applepay_button_insert_tag
   - entry_authorization_type
   - entry_client_secret
   - entry_vault_status
   - help_googlepay_button_status
   - href_message_setting
   - merchant_id
   - paypal_sale_total
   - reauthorize_url
   - sort_order
   - text_automatic
   - text_message_alert
   ... و 89 متغير آخر

❌ Missing in English (59):
   - action
   - connect_url
   - contact_sales
   - decimal_place
   - environment
   - header
   - href_general
   - info_url
   - merchant_id
   - order_recurring_id
   - partner_attribution_id
   - paypal_sale_total
   - reauthorize_url
   - text_disabled
   - text_message_alert
   ... و 44 متغير آخر

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 104 items
      - action
      - merchant_id
      - text_message_alert
      - reauthorize_url
      - entry_applepay_button_insert_tag
   🟡 MISSING_ENGLISH_VARIABLES: 59 items
      - connect_url
      - action
      - environment
      - merchant_id
      - contact_sales
   🟢 MISSING_MODEL_FILES: 1 items
      - sale/recurring

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 104 متغير عربي و 59 متغير إنجليزي
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ⚠️ 75%
📅 Analysis Date: 2025-07-21 18:33:24
🔧 Total Issues: 3

⚠️ جيد، لكن يحتاج بعض التحسينات.