📄 Route: accounts/changes_in_equity
📂 Controller: controller\accounts\changes_in_equity.php
🧱 Models used (4):
   - accounts/changes_in_equity
   - accounts/chartaccount
   - branch/branch
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\changes_in_equity.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\changes_in_equity.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_account_name
   - text_changes_in_equity
   - text_closing_balance
   - text_decrease
   - text_form
   - text_from
   - text_home
   - text_increase
   - text_movement
   - text_no_results
   - text_opening_balance
   - text_period
   - text_success_generate
   - text_to
   - text_total

❌ Missing in Arabic:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_account_name
   - text_changes_in_equity
   - text_closing_balance
   - text_decrease
   - text_form
   - text_from
   - text_home
   - text_increase
   - text_movement
   - text_no_results
   - text_opening_balance
   - text_period
   - text_success_generate
   - text_to
   - text_total

❌ Missing in English:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_account_name
   - text_changes_in_equity
   - text_closing_balance
   - text_decrease
   - text_form
   - text_from
   - text_home
   - text_increase
   - text_movement
   - text_no_results
   - text_opening_balance
   - text_period
   - text_success_generate
   - text_to
   - text_total

💡 Suggested Arabic Additions:
   - button_filter = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - entry_date_start = ""  # TODO: ترجمة عربية
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_date_start = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_account_name = ""  # TODO: ترجمة عربية
   - text_changes_in_equity = ""  # TODO: ترجمة عربية
   - text_closing_balance = ""  # TODO: ترجمة عربية
   - text_decrease = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_from = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_increase = ""  # TODO: ترجمة عربية
   - text_movement = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_opening_balance = ""  # TODO: ترجمة عربية
   - text_period = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_to = ""  # TODO: ترجمة عربية
   - text_total = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_filter = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - entry_date_start = ""  # TODO: English translation
   - error_date_end = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_date_start = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_account_name = ""  # TODO: English translation
   - text_changes_in_equity = ""  # TODO: English translation
   - text_closing_balance = ""  # TODO: English translation
   - text_decrease = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_from = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_increase = ""  # TODO: English translation
   - text_movement = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_opening_balance = ""  # TODO: English translation
   - text_period = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_to = ""  # TODO: English translation
   - text_total = ""  # TODO: English translation
