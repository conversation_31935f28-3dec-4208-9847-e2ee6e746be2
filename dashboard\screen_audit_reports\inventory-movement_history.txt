📄 Route: inventory/movement_history
📂 Controller: controller\inventory\movement_history.php
🧱 Models used (6):
   ❌ common/central_service_manager (0 functions)
   ✅ inventory/movement_history (10 functions)
   ✅ inventory/product (78 functions)
   ✅ inventory/warehouse (47 functions)
   ✅ catalog/inventory_manager (24 functions)
   ✅ catalog/product (128 functions)
🎨 Twig templates (1):
   ✅ view\template\inventory\movement_history.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\movement_history.php (47 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\inventory\movement_history.php (48 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (79):
   - button_filter
   - column_movement_type
   - column_product
   - column_reference
   - column_unit
   - datetime_format
   - entry_movement_type
   - error_movement_id
   - error_movement_not_found
   - error_permission
   - error_transfer_already_completed
   - error_transfer_not_found
   - filter_product
   - pagination
   - text_adjustment
   - text_all_movement_history
   - text_list
   - text_movement_details
   - text_out
   - text_production
   ... و 59 متغير آخر

❌ Missing in Arabic (35):
   - button_close
   - error_export_failed
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_transfer_already_completed
   - error_transfer_not_found
   - filter_date_end
   - filter_product
   - header
   - pagination
   - sort_quantity
   - sort_warehouse
   - user_token
   ... و 20 متغير آخر

❌ Missing in English (34):
   - column_left
   - error_export_failed
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_transfer_already_completed
   - error_transfer_not_found
   - filter_date_end
   - filter_product
   - header
   - pagination
   - sort_quantity
   - sort_warehouse
   - user_token
   ... و 19 متغير آخر

🗄️ Database Tables Used (5):
   ❌ existing
   ❌ name
   ❌ quantity
   ❌ statements
   ❌ stock

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 35 items
      - sort_warehouse
      - button_close
      - filter_date_end
      - pagination
      - error_export_failed
   🟡 MISSING_ENGLISH_VARIABLES: 34 items
      - sort_warehouse
      - filter_date_end
      - pagination
      - error_export_failed
      - sort_quantity
   🔴 INVALID_DATABASE_TABLES: 5 items
      - existing
      - quantity
      - stock
      - name
      - statements
   🟢 MISSING_MODEL_FILES: 1 items
      - common/central_service_manager

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 35 متغير عربي و 34 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 5 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:06
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.