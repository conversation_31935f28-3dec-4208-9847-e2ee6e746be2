📄 Route: accounts/annual_tax
📂 Controller: controller\accounts\annual_tax.php
🧱 Models used (2):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/annual_tax (16 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\annual_tax.php (154 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\annual_tax.php (154 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (16):
   - error_no_data
   - error_permission
   - error_report_type
   - error_year
   - heading_title
   - text_all_taxes
   - text_comparative_report
   - text_detailed_report
   - text_home
   - text_income_tax
   - text_stamp_tax
   - text_success_generate
   - text_summary_report
   - text_vat
   - text_view
   - text_withholding_tax

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (1):
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (1):
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ⚠️ 85%
📅 Analysis Date: 2025-07-21 18:32:32
🔧 Total Issues: 1

⚠️ جيد، لكن يحتاج بعض التحسينات.