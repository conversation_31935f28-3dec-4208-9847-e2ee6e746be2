📄 Route: documents/templates
📂 Controller: controller\documents\templates.php
🧱 Models used (4):
   - communication/unified_notification
   - core/central_service_manager
   - documents/templates
   - logging/user_activity
🎨 Twig templates (1):
   - view\template\documents\templates.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_content_required
   - error_generation_validation
   - error_name_required
   - error_permission
   - error_template_required
   - heading_title
   - text_add_template
   - text_adjustment_form_desc
   - text_adjustment_form_template
   - text_approval_request_desc
   - text_approval_request_template
   - text_catalog_report_desc
   - text_catalog_report_template
   - text_catalog_templates
   - text_catalog_templates_desc
   - text_data_categories
   - text_data_customers
   - text_data_invoices
   - text_data_movements
   - text_data_orders
   - text_data_products
   - text_data_stock_levels
   - text_data_suppliers
   - text_data_warehouses
   - text_edit_template
   - text_element_barcode
   - text_element_chart
   - text_element_image
   - text_element_table
   - text_element_text
   - text_element_variable
   - text_generation_success
   - text_goods_receipt_desc
   - text_goods_receipt_template
   - text_home
   - text_inventory_templates
   - text_inventory_templates_desc
   - text_movement_document_desc
   - text_movement_document_template
   - text_price_list_desc
   - text_price_list_template
   - text_process_documentation_desc
   - text_process_documentation_template
   - text_product_specification_desc
   - text_product_specification_template
   - text_purchase_order_desc
   - text_purchase_order_template
   - text_purchase_templates
   - text_purchase_templates_desc
   - text_stock_report_desc
   - text_stock_report_template
   - text_success
   - text_supplier_evaluation_desc
   - text_supplier_evaluation_template
   - text_template_builder
   - text_type_certificate
   - text_type_certificate_desc
   - text_type_document
   - text_type_document_desc
   - text_type_form
   - text_type_form_desc
   - text_type_report
   - text_type_report_desc
   - text_workflow_report_desc
   - text_workflow_report_template
   - text_workflow_templates
   - text_workflow_templates_desc

❌ Missing in Arabic:
   - error_content_required
   - error_generation_validation
   - error_name_required
   - error_permission
   - error_template_required
   - heading_title
   - text_add_template
   - text_adjustment_form_desc
   - text_adjustment_form_template
   - text_approval_request_desc
   - text_approval_request_template
   - text_catalog_report_desc
   - text_catalog_report_template
   - text_catalog_templates
   - text_catalog_templates_desc
   - text_data_categories
   - text_data_customers
   - text_data_invoices
   - text_data_movements
   - text_data_orders
   - text_data_products
   - text_data_stock_levels
   - text_data_suppliers
   - text_data_warehouses
   - text_edit_template
   - text_element_barcode
   - text_element_chart
   - text_element_image
   - text_element_table
   - text_element_text
   - text_element_variable
   - text_generation_success
   - text_goods_receipt_desc
   - text_goods_receipt_template
   - text_home
   - text_inventory_templates
   - text_inventory_templates_desc
   - text_movement_document_desc
   - text_movement_document_template
   - text_price_list_desc
   - text_price_list_template
   - text_process_documentation_desc
   - text_process_documentation_template
   - text_product_specification_desc
   - text_product_specification_template
   - text_purchase_order_desc
   - text_purchase_order_template
   - text_purchase_templates
   - text_purchase_templates_desc
   - text_stock_report_desc
   - text_stock_report_template
   - text_success
   - text_supplier_evaluation_desc
   - text_supplier_evaluation_template
   - text_template_builder
   - text_type_certificate
   - text_type_certificate_desc
   - text_type_document
   - text_type_document_desc
   - text_type_form
   - text_type_form_desc
   - text_type_report
   - text_type_report_desc
   - text_workflow_report_desc
   - text_workflow_report_template
   - text_workflow_templates
   - text_workflow_templates_desc

❌ Missing in English:
   - error_content_required
   - error_generation_validation
   - error_name_required
   - error_permission
   - error_template_required
   - heading_title
   - text_add_template
   - text_adjustment_form_desc
   - text_adjustment_form_template
   - text_approval_request_desc
   - text_approval_request_template
   - text_catalog_report_desc
   - text_catalog_report_template
   - text_catalog_templates
   - text_catalog_templates_desc
   - text_data_categories
   - text_data_customers
   - text_data_invoices
   - text_data_movements
   - text_data_orders
   - text_data_products
   - text_data_stock_levels
   - text_data_suppliers
   - text_data_warehouses
   - text_edit_template
   - text_element_barcode
   - text_element_chart
   - text_element_image
   - text_element_table
   - text_element_text
   - text_element_variable
   - text_generation_success
   - text_goods_receipt_desc
   - text_goods_receipt_template
   - text_home
   - text_inventory_templates
   - text_inventory_templates_desc
   - text_movement_document_desc
   - text_movement_document_template
   - text_price_list_desc
   - text_price_list_template
   - text_process_documentation_desc
   - text_process_documentation_template
   - text_product_specification_desc
   - text_product_specification_template
   - text_purchase_order_desc
   - text_purchase_order_template
   - text_purchase_templates
   - text_purchase_templates_desc
   - text_stock_report_desc
   - text_stock_report_template
   - text_success
   - text_supplier_evaluation_desc
   - text_supplier_evaluation_template
   - text_template_builder
   - text_type_certificate
   - text_type_certificate_desc
   - text_type_document
   - text_type_document_desc
   - text_type_form
   - text_type_form_desc
   - text_type_report
   - text_type_report_desc
   - text_workflow_report_desc
   - text_workflow_report_template
   - text_workflow_templates
   - text_workflow_templates_desc

💡 Suggested Arabic Additions:
   - error_content_required = ""  # TODO: ترجمة عربية
   - error_generation_validation = ""  # TODO: ترجمة عربية
   - error_name_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_template_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add_template = ""  # TODO: ترجمة عربية
   - text_adjustment_form_desc = ""  # TODO: ترجمة عربية
   - text_adjustment_form_template = ""  # TODO: ترجمة عربية
   - text_approval_request_desc = ""  # TODO: ترجمة عربية
   - text_approval_request_template = ""  # TODO: ترجمة عربية
   - text_catalog_report_desc = ""  # TODO: ترجمة عربية
   - text_catalog_report_template = ""  # TODO: ترجمة عربية
   - text_catalog_templates = ""  # TODO: ترجمة عربية
   - text_catalog_templates_desc = ""  # TODO: ترجمة عربية
   - text_data_categories = ""  # TODO: ترجمة عربية
   - text_data_customers = ""  # TODO: ترجمة عربية
   - text_data_invoices = ""  # TODO: ترجمة عربية
   - text_data_movements = ""  # TODO: ترجمة عربية
   - text_data_orders = ""  # TODO: ترجمة عربية
   - text_data_products = ""  # TODO: ترجمة عربية
   - text_data_stock_levels = ""  # TODO: ترجمة عربية
   - text_data_suppliers = ""  # TODO: ترجمة عربية
   - text_data_warehouses = ""  # TODO: ترجمة عربية
   - text_edit_template = ""  # TODO: ترجمة عربية
   - text_element_barcode = ""  # TODO: ترجمة عربية
   - text_element_chart = ""  # TODO: ترجمة عربية
   - text_element_image = ""  # TODO: ترجمة عربية
   - text_element_table = ""  # TODO: ترجمة عربية
   - text_element_text = ""  # TODO: ترجمة عربية
   - text_element_variable = ""  # TODO: ترجمة عربية
   - text_generation_success = ""  # TODO: ترجمة عربية
   - text_goods_receipt_desc = ""  # TODO: ترجمة عربية
   - text_goods_receipt_template = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_inventory_templates = ""  # TODO: ترجمة عربية
   - text_inventory_templates_desc = ""  # TODO: ترجمة عربية
   - text_movement_document_desc = ""  # TODO: ترجمة عربية
   - text_movement_document_template = ""  # TODO: ترجمة عربية
   - text_price_list_desc = ""  # TODO: ترجمة عربية
   - text_price_list_template = ""  # TODO: ترجمة عربية
   - text_process_documentation_desc = ""  # TODO: ترجمة عربية
   - text_process_documentation_template = ""  # TODO: ترجمة عربية
   - text_product_specification_desc = ""  # TODO: ترجمة عربية
   - text_product_specification_template = ""  # TODO: ترجمة عربية
   - text_purchase_order_desc = ""  # TODO: ترجمة عربية
   - text_purchase_order_template = ""  # TODO: ترجمة عربية
   - text_purchase_templates = ""  # TODO: ترجمة عربية
   - text_purchase_templates_desc = ""  # TODO: ترجمة عربية
   - text_stock_report_desc = ""  # TODO: ترجمة عربية
   - text_stock_report_template = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_supplier_evaluation_desc = ""  # TODO: ترجمة عربية
   - text_supplier_evaluation_template = ""  # TODO: ترجمة عربية
   - text_template_builder = ""  # TODO: ترجمة عربية
   - text_type_certificate = ""  # TODO: ترجمة عربية
   - text_type_certificate_desc = ""  # TODO: ترجمة عربية
   - text_type_document = ""  # TODO: ترجمة عربية
   - text_type_document_desc = ""  # TODO: ترجمة عربية
   - text_type_form = ""  # TODO: ترجمة عربية
   - text_type_form_desc = ""  # TODO: ترجمة عربية
   - text_type_report = ""  # TODO: ترجمة عربية
   - text_type_report_desc = ""  # TODO: ترجمة عربية
   - text_workflow_report_desc = ""  # TODO: ترجمة عربية
   - text_workflow_report_template = ""  # TODO: ترجمة عربية
   - text_workflow_templates = ""  # TODO: ترجمة عربية
   - text_workflow_templates_desc = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_content_required = ""  # TODO: English translation
   - error_generation_validation = ""  # TODO: English translation
   - error_name_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_template_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add_template = ""  # TODO: English translation
   - text_adjustment_form_desc = ""  # TODO: English translation
   - text_adjustment_form_template = ""  # TODO: English translation
   - text_approval_request_desc = ""  # TODO: English translation
   - text_approval_request_template = ""  # TODO: English translation
   - text_catalog_report_desc = ""  # TODO: English translation
   - text_catalog_report_template = ""  # TODO: English translation
   - text_catalog_templates = ""  # TODO: English translation
   - text_catalog_templates_desc = ""  # TODO: English translation
   - text_data_categories = ""  # TODO: English translation
   - text_data_customers = ""  # TODO: English translation
   - text_data_invoices = ""  # TODO: English translation
   - text_data_movements = ""  # TODO: English translation
   - text_data_orders = ""  # TODO: English translation
   - text_data_products = ""  # TODO: English translation
   - text_data_stock_levels = ""  # TODO: English translation
   - text_data_suppliers = ""  # TODO: English translation
   - text_data_warehouses = ""  # TODO: English translation
   - text_edit_template = ""  # TODO: English translation
   - text_element_barcode = ""  # TODO: English translation
   - text_element_chart = ""  # TODO: English translation
   - text_element_image = ""  # TODO: English translation
   - text_element_table = ""  # TODO: English translation
   - text_element_text = ""  # TODO: English translation
   - text_element_variable = ""  # TODO: English translation
   - text_generation_success = ""  # TODO: English translation
   - text_goods_receipt_desc = ""  # TODO: English translation
   - text_goods_receipt_template = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_inventory_templates = ""  # TODO: English translation
   - text_inventory_templates_desc = ""  # TODO: English translation
   - text_movement_document_desc = ""  # TODO: English translation
   - text_movement_document_template = ""  # TODO: English translation
   - text_price_list_desc = ""  # TODO: English translation
   - text_price_list_template = ""  # TODO: English translation
   - text_process_documentation_desc = ""  # TODO: English translation
   - text_process_documentation_template = ""  # TODO: English translation
   - text_product_specification_desc = ""  # TODO: English translation
   - text_product_specification_template = ""  # TODO: English translation
   - text_purchase_order_desc = ""  # TODO: English translation
   - text_purchase_order_template = ""  # TODO: English translation
   - text_purchase_templates = ""  # TODO: English translation
   - text_purchase_templates_desc = ""  # TODO: English translation
   - text_stock_report_desc = ""  # TODO: English translation
   - text_stock_report_template = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_supplier_evaluation_desc = ""  # TODO: English translation
   - text_supplier_evaluation_template = ""  # TODO: English translation
   - text_template_builder = ""  # TODO: English translation
   - text_type_certificate = ""  # TODO: English translation
   - text_type_certificate_desc = ""  # TODO: English translation
   - text_type_document = ""  # TODO: English translation
   - text_type_document_desc = ""  # TODO: English translation
   - text_type_form = ""  # TODO: English translation
   - text_type_form_desc = ""  # TODO: English translation
   - text_type_report = ""  # TODO: English translation
   - text_type_report_desc = ""  # TODO: English translation
   - text_workflow_report_desc = ""  # TODO: English translation
   - text_workflow_report_template = ""  # TODO: English translation
   - text_workflow_templates = ""  # TODO: English translation
   - text_workflow_templates_desc = ""  # TODO: English translation
