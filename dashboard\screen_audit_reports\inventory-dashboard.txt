📄 Route: inventory/dashboard
📂 Controller: controller\inventory\dashboard.php
🧱 Models used (6):
   ✅ core/central_service_manager (60 functions)
   ✅ inventory/dashboard (17 functions)
   ✅ inventory/product (78 functions)
   ✅ inventory/movement (9 functions)
   ❌ accounting/journal (0 functions)
   ✅ purchase/order (45 functions)
🎨 Twig templates (1):
   ✅ view\template\inventory\dashboard.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\dashboard.php (123 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\dashboard.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (68):
   - alert_expiring_message
   - alert_low_stock_title
   - alert_slow_moving_message
   - error_file_type
   - header
   - text_abc_analysis_chart
   - text_current_stock
   - text_dashboard
   - text_date
   - text_inventory_reports
   - text_inventory_value
   - text_low_stock_products
   - text_product_management
   - text_quantity
   - text_recent_movements
   - text_refreshing
   - text_status_approved
   - text_status_cancelled
   - text_status_confirmed_by_vendor
   - text_status_sent_to_vendor
   ... و 48 متغير آخر

❌ Missing in Arabic (26):
   - column_left
   - error_file_size
   - error_file_type
   - header
   - text_costs_updated
   - text_journal_goods_receipt
   - text_order_rejected
   - text_status_approved
   - text_status_confirmed_by_vendor
   - text_status_draft
   - text_status_fully_received
   - text_status_partially_received
   - text_status_rejected
   - text_status_sent_to_vendor
   - user_token
   ... و 11 متغير آخر

❌ Missing in English (68):
   - alert_low_stock_title
   - alert_slow_moving_message
   - error_file_type
   - text_abc_analysis_chart
   - text_current_stock
   - text_dashboard
   - text_date
   - text_inventory_reports
   - text_quantity
   - text_refreshing
   - text_status_approved
   - text_status_completed
   - text_status_partially_received
   - text_status_sent_to_vendor
   - text_view_reports
   ... و 53 متغير آخر

🗄️ Database Tables Used (8):
   ❌ SET
   ❌ from
   ❌ goods
   ❌ quantity
   ❌ temp_product_annual_value
   ❌ template
   ❌ total_value
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 26 items
      - text_status_sent_to_vendor
      - text_status_approved
      - header
      - error_file_type
      - text_status_confirmed_by_vendor
   🟡 MISSING_ENGLISH_VARIABLES: 68 items
      - alert_slow_moving_message
      - text_inventory_reports
      - text_status_sent_to_vendor
      - text_quantity
      - alert_low_stock_title
   🔴 INVALID_DATABASE_TABLES: 8 items
      - workflow
      - from
      - template
      - temp_product_annual_value
      - SET
   🟢 MISSING_MODEL_FILES: 1 items
      - accounting/journal

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 26 متغير عربي و 68 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 8 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:05
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.