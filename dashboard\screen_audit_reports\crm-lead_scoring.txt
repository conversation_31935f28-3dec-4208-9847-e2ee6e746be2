📄 Route: crm/lead_scoring
📂 Controller: controller\crm\lead_scoring.php
🧱 Models used (3):
   ✅ crm/lead_scoring (43 functions)
   ✅ user/user (47 functions)
   ❌ tool/activity_log (0 functions)
🎨 Twig templates (1):
   ✅ view\template\crm\lead_scoring.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\crm\lead_scoring.php (204 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\crm\lead_scoring.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (135):
   - action
   - button_filter
   - button_import
   - column_name
   - column_status
   - error_permission
   - heading_title_rules
   - pagination
   - search
   - sort_score
   - text_all_scores
   - text_category_engagement
   - text_import_leads
   - text_low
   - text_notes
   - text_recalculate_help
   - text_search
   - text_source_ad
   - text_status_lost
   - text_status_proposal
   ... و 115 متغير آخر

❌ Missing in Arabic (63):
   - action
   - column_name
   - filter_date_to
   - filter_name
   - header
   - pagination
   - search
   - sort_score
   - text_all_scores
   - text_customer_status
   - text_filter
   - text_import_leads
   - text_recalculate_help
   - text_score_trend
   - text_update_score
   ... و 48 متغير آخر

❌ Missing in English (135):
   - action
   - button_import
   - column_name
   - error_permission
   - heading_title_rules
   - pagination
   - search
   - sort_score
   - text_all_scores
   - text_import_leads
   - text_low
   - text_recalculate_help
   - text_search
   - text_source_ad
   - text_status_lost
   ... و 120 متغير آخر

🗄️ Database Tables Used (1):
   ❌ rule_value

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 63 items
      - filter_date_to
      - text_customer_status
      - action
      - column_name
      - text_recalculate_help
   🟡 MISSING_ENGLISH_VARIABLES: 135 items
      - action
      - column_name
      - pagination
      - text_recalculate_help
      - text_status_lost
   🔴 INVALID_DATABASE_TABLES: 1 items
      - rule_value
   🟢 MISSING_MODEL_FILES: 1 items
      - tool/activity_log

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 63 متغير عربي و 135 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:58
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.