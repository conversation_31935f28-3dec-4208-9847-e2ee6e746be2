📄 Route: notification/automation
📂 Controller: controller\notification\automation.php
🧱 Models used (8):
   - ai/recommendation_engine
   - catalog/product
   - communication/unified_notification
   - core/central_service_manager
   - inventory/stock
   - notification/automation
   - purchase/requisition
   - workflow/automation
🎨 Twig templates (1):
   - view\template\notification\automation.twig
🈯 Arabic Language Files (1):
   - language\ar\notification\automation.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_quantity_must_be_positive
   - error_rule_id_required
   - error_rule_not_found
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_action_ai_recommendation
   - text_action_create_requisition
   - text_action_create_task
   - text_action_send_email
   - text_action_send_notification
   - text_action_send_sms
   - text_action_trigger_workflow
   - text_action_update_product
   - text_ai_demand_forecast_automation
   - text_ai_demand_forecast_automation_desc
   - text_ai_pricing_automation
   - text_ai_pricing_automation_desc
   - text_batch_expiry_automation
   - text_batch_expiry_automation_desc
   - text_expiry_automation
   - text_expiry_automation_desc
   - text_home
   - text_low_stock_automation
   - text_low_stock_automation_desc
   - text_new_product_automation
   - text_new_product_automation_desc
   - text_price_change_automation
   - text_price_change_automation_desc
   - text_reorder_point_automation
   - text_reorder_point_automation_desc
   - text_stock_movement_automation
   - text_stock_movement_automation_desc
   - text_test_failed
   - text_test_successful
   - text_trigger_ai_analysis
   - text_trigger_daily_check
   - text_trigger_inventory_level
   - text_trigger_price_changed
   - text_trigger_product_created
   - text_trigger_product_updated
   - text_trigger_purchase_order
   - text_trigger_sales_order
   - text_trigger_stock_movement
   - text_trigger_weekly_check

❌ Missing in Arabic:
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_quantity_must_be_positive
   - error_rule_id_required
   - error_rule_not_found
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_action_ai_recommendation
   - text_action_create_requisition
   - text_action_create_task
   - text_action_send_email
   - text_action_send_notification
   - text_action_send_sms
   - text_action_trigger_workflow
   - text_action_update_product
   - text_ai_demand_forecast_automation
   - text_ai_demand_forecast_automation_desc
   - text_ai_pricing_automation
   - text_ai_pricing_automation_desc
   - text_batch_expiry_automation
   - text_batch_expiry_automation_desc
   - text_expiry_automation
   - text_expiry_automation_desc
   - text_home
   - text_low_stock_automation
   - text_low_stock_automation_desc
   - text_new_product_automation
   - text_new_product_automation_desc
   - text_price_change_automation
   - text_price_change_automation_desc
   - text_reorder_point_automation
   - text_reorder_point_automation_desc
   - text_stock_movement_automation
   - text_stock_movement_automation_desc
   - text_test_failed
   - text_test_successful
   - text_trigger_ai_analysis
   - text_trigger_daily_check
   - text_trigger_inventory_level
   - text_trigger_price_changed
   - text_trigger_product_created
   - text_trigger_product_updated
   - text_trigger_purchase_order
   - text_trigger_sales_order
   - text_trigger_stock_movement
   - text_trigger_weekly_check

❌ Missing in English:
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_quantity_must_be_positive
   - error_rule_id_required
   - error_rule_not_found
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_action_ai_recommendation
   - text_action_create_requisition
   - text_action_create_task
   - text_action_send_email
   - text_action_send_notification
   - text_action_send_sms
   - text_action_trigger_workflow
   - text_action_update_product
   - text_ai_demand_forecast_automation
   - text_ai_demand_forecast_automation_desc
   - text_ai_pricing_automation
   - text_ai_pricing_automation_desc
   - text_batch_expiry_automation
   - text_batch_expiry_automation_desc
   - text_expiry_automation
   - text_expiry_automation_desc
   - text_home
   - text_low_stock_automation
   - text_low_stock_automation_desc
   - text_new_product_automation
   - text_new_product_automation_desc
   - text_price_change_automation
   - text_price_change_automation_desc
   - text_reorder_point_automation
   - text_reorder_point_automation_desc
   - text_stock_movement_automation
   - text_stock_movement_automation_desc
   - text_test_failed
   - text_test_successful
   - text_trigger_ai_analysis
   - text_trigger_daily_check
   - text_trigger_inventory_level
   - text_trigger_price_changed
   - text_trigger_product_created
   - text_trigger_product_updated
   - text_trigger_purchase_order
   - text_trigger_sales_order
   - text_trigger_stock_movement
   - text_trigger_weekly_check

💡 Suggested Arabic Additions:
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_rule_id_required = ""  # TODO: ترجمة عربية
   - error_rule_not_found = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_action_ai_recommendation = ""  # TODO: ترجمة عربية
   - text_action_create_requisition = ""  # TODO: ترجمة عربية
   - text_action_create_task = ""  # TODO: ترجمة عربية
   - text_action_send_email = ""  # TODO: ترجمة عربية
   - text_action_send_notification = ""  # TODO: ترجمة عربية
   - text_action_send_sms = ""  # TODO: ترجمة عربية
   - text_action_trigger_workflow = ""  # TODO: ترجمة عربية
   - text_action_update_product = ""  # TODO: ترجمة عربية
   - text_ai_demand_forecast_automation = ""  # TODO: ترجمة عربية
   - text_ai_demand_forecast_automation_desc = ""  # TODO: ترجمة عربية
   - text_ai_pricing_automation = ""  # TODO: ترجمة عربية
   - text_ai_pricing_automation_desc = ""  # TODO: ترجمة عربية
   - text_batch_expiry_automation = ""  # TODO: ترجمة عربية
   - text_batch_expiry_automation_desc = ""  # TODO: ترجمة عربية
   - text_expiry_automation = ""  # TODO: ترجمة عربية
   - text_expiry_automation_desc = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_low_stock_automation = ""  # TODO: ترجمة عربية
   - text_low_stock_automation_desc = ""  # TODO: ترجمة عربية
   - text_new_product_automation = ""  # TODO: ترجمة عربية
   - text_new_product_automation_desc = ""  # TODO: ترجمة عربية
   - text_price_change_automation = ""  # TODO: ترجمة عربية
   - text_price_change_automation_desc = ""  # TODO: ترجمة عربية
   - text_reorder_point_automation = ""  # TODO: ترجمة عربية
   - text_reorder_point_automation_desc = ""  # TODO: ترجمة عربية
   - text_stock_movement_automation = ""  # TODO: ترجمة عربية
   - text_stock_movement_automation_desc = ""  # TODO: ترجمة عربية
   - text_test_failed = ""  # TODO: ترجمة عربية
   - text_test_successful = ""  # TODO: ترجمة عربية
   - text_trigger_ai_analysis = ""  # TODO: ترجمة عربية
   - text_trigger_daily_check = ""  # TODO: ترجمة عربية
   - text_trigger_inventory_level = ""  # TODO: ترجمة عربية
   - text_trigger_price_changed = ""  # TODO: ترجمة عربية
   - text_trigger_product_created = ""  # TODO: ترجمة عربية
   - text_trigger_product_updated = ""  # TODO: ترجمة عربية
   - text_trigger_purchase_order = ""  # TODO: ترجمة عربية
   - text_trigger_sales_order = ""  # TODO: ترجمة عربية
   - text_trigger_stock_movement = ""  # TODO: ترجمة عربية
   - text_trigger_weekly_check = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_rule_id_required = ""  # TODO: English translation
   - error_rule_not_found = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_action_ai_recommendation = ""  # TODO: English translation
   - text_action_create_requisition = ""  # TODO: English translation
   - text_action_create_task = ""  # TODO: English translation
   - text_action_send_email = ""  # TODO: English translation
   - text_action_send_notification = ""  # TODO: English translation
   - text_action_send_sms = ""  # TODO: English translation
   - text_action_trigger_workflow = ""  # TODO: English translation
   - text_action_update_product = ""  # TODO: English translation
   - text_ai_demand_forecast_automation = ""  # TODO: English translation
   - text_ai_demand_forecast_automation_desc = ""  # TODO: English translation
   - text_ai_pricing_automation = ""  # TODO: English translation
   - text_ai_pricing_automation_desc = ""  # TODO: English translation
   - text_batch_expiry_automation = ""  # TODO: English translation
   - text_batch_expiry_automation_desc = ""  # TODO: English translation
   - text_expiry_automation = ""  # TODO: English translation
   - text_expiry_automation_desc = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_low_stock_automation = ""  # TODO: English translation
   - text_low_stock_automation_desc = ""  # TODO: English translation
   - text_new_product_automation = ""  # TODO: English translation
   - text_new_product_automation_desc = ""  # TODO: English translation
   - text_price_change_automation = ""  # TODO: English translation
   - text_price_change_automation_desc = ""  # TODO: English translation
   - text_reorder_point_automation = ""  # TODO: English translation
   - text_reorder_point_automation_desc = ""  # TODO: English translation
   - text_stock_movement_automation = ""  # TODO: English translation
   - text_stock_movement_automation_desc = ""  # TODO: English translation
   - text_test_failed = ""  # TODO: English translation
   - text_test_successful = ""  # TODO: English translation
   - text_trigger_ai_analysis = ""  # TODO: English translation
   - text_trigger_daily_check = ""  # TODO: English translation
   - text_trigger_inventory_level = ""  # TODO: English translation
   - text_trigger_price_changed = ""  # TODO: English translation
   - text_trigger_product_created = ""  # TODO: English translation
   - text_trigger_product_updated = ""  # TODO: English translation
   - text_trigger_purchase_order = ""  # TODO: English translation
   - text_trigger_sales_order = ""  # TODO: English translation
   - text_trigger_stock_movement = ""  # TODO: English translation
   - text_trigger_weekly_check = ""  # TODO: English translation
