📄 Route: logging/audit_trail
📂 Controller: controller\logging\audit_trail.php
🧱 Models used (2):
   - logging/audit_trail
   - user/user
🎨 Twig templates (1):
   - view\template\logging\audit_trail.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_change_details
   - text_compliance_report
   - text_home
   - text_integrity_check
   - text_operation_delete
   - text_operation_insert
   - text_operation_select
   - text_operation_update
   - text_table_categories
   - text_table_categories_desc
   - text_table_inventory
   - text_table_inventory_desc
   - text_table_order_products
   - text_table_order_products_desc
   - text_table_orders
   - text_table_orders_desc
   - text_table_products
   - text_table_products_desc
   - text_table_special_prices
   - text_table_special_prices_desc
   - text_table_stock_movements
   - text_table_stock_movements_desc
   - text_table_user_groups
   - text_table_user_groups_desc
   - text_table_users
   - text_table_users_desc
   - text_table_warehouses
   - text_table_warehouses_desc

❌ Missing in Arabic:
   - heading_title
   - text_change_details
   - text_compliance_report
   - text_home
   - text_integrity_check
   - text_operation_delete
   - text_operation_insert
   - text_operation_select
   - text_operation_update
   - text_table_categories
   - text_table_categories_desc
   - text_table_inventory
   - text_table_inventory_desc
   - text_table_order_products
   - text_table_order_products_desc
   - text_table_orders
   - text_table_orders_desc
   - text_table_products
   - text_table_products_desc
   - text_table_special_prices
   - text_table_special_prices_desc
   - text_table_stock_movements
   - text_table_stock_movements_desc
   - text_table_user_groups
   - text_table_user_groups_desc
   - text_table_users
   - text_table_users_desc
   - text_table_warehouses
   - text_table_warehouses_desc

❌ Missing in English:
   - heading_title
   - text_change_details
   - text_compliance_report
   - text_home
   - text_integrity_check
   - text_operation_delete
   - text_operation_insert
   - text_operation_select
   - text_operation_update
   - text_table_categories
   - text_table_categories_desc
   - text_table_inventory
   - text_table_inventory_desc
   - text_table_order_products
   - text_table_order_products_desc
   - text_table_orders
   - text_table_orders_desc
   - text_table_products
   - text_table_products_desc
   - text_table_special_prices
   - text_table_special_prices_desc
   - text_table_stock_movements
   - text_table_stock_movements_desc
   - text_table_user_groups
   - text_table_user_groups_desc
   - text_table_users
   - text_table_users_desc
   - text_table_warehouses
   - text_table_warehouses_desc

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_change_details = ""  # TODO: ترجمة عربية
   - text_compliance_report = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_integrity_check = ""  # TODO: ترجمة عربية
   - text_operation_delete = ""  # TODO: ترجمة عربية
   - text_operation_insert = ""  # TODO: ترجمة عربية
   - text_operation_select = ""  # TODO: ترجمة عربية
   - text_operation_update = ""  # TODO: ترجمة عربية
   - text_table_categories = ""  # TODO: ترجمة عربية
   - text_table_categories_desc = ""  # TODO: ترجمة عربية
   - text_table_inventory = ""  # TODO: ترجمة عربية
   - text_table_inventory_desc = ""  # TODO: ترجمة عربية
   - text_table_order_products = ""  # TODO: ترجمة عربية
   - text_table_order_products_desc = ""  # TODO: ترجمة عربية
   - text_table_orders = ""  # TODO: ترجمة عربية
   - text_table_orders_desc = ""  # TODO: ترجمة عربية
   - text_table_products = ""  # TODO: ترجمة عربية
   - text_table_products_desc = ""  # TODO: ترجمة عربية
   - text_table_special_prices = ""  # TODO: ترجمة عربية
   - text_table_special_prices_desc = ""  # TODO: ترجمة عربية
   - text_table_stock_movements = ""  # TODO: ترجمة عربية
   - text_table_stock_movements_desc = ""  # TODO: ترجمة عربية
   - text_table_user_groups = ""  # TODO: ترجمة عربية
   - text_table_user_groups_desc = ""  # TODO: ترجمة عربية
   - text_table_users = ""  # TODO: ترجمة عربية
   - text_table_users_desc = ""  # TODO: ترجمة عربية
   - text_table_warehouses = ""  # TODO: ترجمة عربية
   - text_table_warehouses_desc = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_change_details = ""  # TODO: English translation
   - text_compliance_report = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_integrity_check = ""  # TODO: English translation
   - text_operation_delete = ""  # TODO: English translation
   - text_operation_insert = ""  # TODO: English translation
   - text_operation_select = ""  # TODO: English translation
   - text_operation_update = ""  # TODO: English translation
   - text_table_categories = ""  # TODO: English translation
   - text_table_categories_desc = ""  # TODO: English translation
   - text_table_inventory = ""  # TODO: English translation
   - text_table_inventory_desc = ""  # TODO: English translation
   - text_table_order_products = ""  # TODO: English translation
   - text_table_order_products_desc = ""  # TODO: English translation
   - text_table_orders = ""  # TODO: English translation
   - text_table_orders_desc = ""  # TODO: English translation
   - text_table_products = ""  # TODO: English translation
   - text_table_products_desc = ""  # TODO: English translation
   - text_table_special_prices = ""  # TODO: English translation
   - text_table_special_prices_desc = ""  # TODO: English translation
   - text_table_stock_movements = ""  # TODO: English translation
   - text_table_stock_movements_desc = ""  # TODO: English translation
   - text_table_user_groups = ""  # TODO: English translation
   - text_table_user_groups_desc = ""  # TODO: English translation
   - text_table_users = ""  # TODO: English translation
   - text_table_users_desc = ""  # TODO: English translation
   - text_table_warehouses = ""  # TODO: English translation
   - text_table_warehouses_desc = ""  # TODO: English translation
