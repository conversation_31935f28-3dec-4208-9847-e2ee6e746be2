📄 Route: hr/performance
📂 Controller: controller\hr\performance.php
🧱 Models used (2):
   ✅ hr/performance (7 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\hr\performance.php (42 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\hr\performance.php (42 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (43):
   - button_close
   - button_filter
   - column_comments
   - column_criteria_name
   - column_employee
   - column_review_date
   - column_score
   - error_not_found
   - error_permission
   - text_ajax_error
   - text_all_statuses
   - text_comments
   - text_filter
   - text_performance_list
   - text_review_date_start
   - text_select_employee
   - text_select_reviewer
   - text_status
   - text_status_completed
   - text_status_pending
   ... و 23 متغير آخر

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (1):
   - text_home

🗄️ Database Tables Used (4):
   ✅ cod_performance_criteria
   ✅ cod_performance_review
   ✅ cod_performance_review_criteria
   ✅ cod_user

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 1 items
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 1 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:04
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.