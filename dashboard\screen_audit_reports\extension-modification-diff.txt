📄 Route: extension/modification/diff
📂 Controller: controller\extension\modification\diff.php
🧱 Models used (0):
🎨 Twig templates (1):
   ✅ view\template\extension\modification\diff.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\extension\modification\diff.php (0 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\modification\diff.php (11 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (20):
   - button_return
   - button_save
   - code_cache
   - code_original
   - column_left
   - error_code
   - error_file
   - error_filewrite
   - error_permission
   - file_patch
   - footer
   - header
   - heading_title
   - return
   - text_help_diff
   - text_home
   - text_loading
   - text_modifications
   - text_modified_files
   - text_success_edit

❌ Missing in Arabic (20):
   - button_return
   - button_save
   - code_cache
   - column_left
   - error_file
   - error_filewrite
   - file_patch
   - footer
   - header
   - return
   - text_home
   - text_loading
   - text_modifications
   - text_modified_files
   - text_success_edit
   ... و 5 متغير آخر

❌ Missing in English (9):
   - code_cache
   - code_original
   - column_left
   - file_patch
   - footer
   - header
   - return
   - text_home
   - text_loading

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 20 items
      - error_filewrite
      - text_modified_files
      - code_cache
      - column_left
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 9 items
      - code_cache
      - column_left
      - text_loading
      - text_home
      - return

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 20 متغير عربي و 9 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:23
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.