📄 Route: accounts/financial_reports_advanced
📂 Controller: controller\accounts\financial_reports_advanced.php
🧱 Models used (6):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/financial_reports_advanced (38 functions)
   ✅ accounts/audit_trail (13 functions)
   ✅ localisation/currency (8 functions)
   ❌ accounts/cost_centers (0 functions)
   ✅ setting/store (14 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\accounts\financial_reports_advanced.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\accounts\financial_reports_advanced.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (10):
   - date_format_long
   - error_permission
   - heading_title
   - text_generate_report
   - text_home
   - text_net_income
   - text_success
   - text_total_assets
   - text_total_liabilities
   - text_view

❌ Missing in Arabic (10):
   - date_format_long
   - error_permission
   - heading_title
   - text_generate_report
   - text_home
   - text_net_income
   - text_success
   - text_total_assets
   - text_total_liabilities
   - text_view

❌ Missing in English (10):
   - date_format_long
   - error_permission
   - heading_title
   - text_generate_report
   - text_home
   - text_net_income
   - text_success
   - text_total_assets
   - text_total_liabilities
   - text_view

🗄️ Database Tables Used (3):
   ❌ journal_entry
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 10 items
      - text_total_assets
      - text_success
      - text_total_liabilities
      - text_home
      - text_view
   🟡 MISSING_ENGLISH_VARIABLES: 10 items
      - text_total_assets
      - text_success
      - text_total_liabilities
      - text_home
      - text_view
   🔴 INVALID_DATABASE_TABLES: 3 items
      - workflow
      - template
      - journal_entry
   🟢 MISSING_MODEL_FILES: 1 items
      - accounts/cost_centers

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 10 متغير عربي و 10 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 3 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:40
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.