📄 Route: governance/legal_contract
📂 Controller: controller\governance\legal_contract.php
🧱 Models used (1):
   - governance/legal_contract
🎨 Twig templates (1):
   - view\template\governance\legal_contract.twig
🈯 Arabic Language Files (1):
   - language\ar\governance\legal_contract.php
🇬🇧 English Language Files (1):
   - language\en-gb\governance\legal_contract.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_home
   - text_list

❌ Missing in Arabic:
   - heading_title
   - text_home
   - text_list

❌ Missing in English:
   - heading_title
   - text_home
   - text_list

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
