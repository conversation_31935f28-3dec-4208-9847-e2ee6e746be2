📄 Route: common/security
📂 Controller: controller\common\security.php
🧱 Models used (0):
🎨 Twig templates (1):
   - view\template\common\security.twig
🈯 Arabic Language Files (1):
   - language\ar\common\security.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\security.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_directory
   - error_exists
   - error_path
   - error_permission
   - error_writable
   - text_instruction
   - text_success

❌ Missing in Arabic:
   - error_directory
   - error_exists
   - error_path
   - error_permission
   - error_writable
   - text_instruction
   - text_success

❌ Missing in English:
   - error_directory
   - error_exists
   - error_path
   - error_permission
   - error_writable
   - text_instruction
   - text_success

💡 Suggested Arabic Additions:
   - error_directory = ""  # TODO: ترجمة عربية
   - error_exists = ""  # TODO: ترجمة عربية
   - error_path = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_writable = ""  # TODO: ترجمة عربية
   - text_instruction = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_directory = ""  # TODO: English translation
   - error_exists = ""  # TODO: English translation
   - error_path = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_writable = ""  # TODO: English translation
   - text_instruction = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
