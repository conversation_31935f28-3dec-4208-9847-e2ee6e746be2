📄 Route: extension/payment/cod
📂 Controller: controller\extension\payment\cod.php
🧱 Models used (3):
   ✅ setting/setting (5 functions)
   ✅ localisation/order_status (7 functions)
   ✅ localisation/geo_zone (11 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\payment\cod.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\payment\cod.php (11 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\payment\cod.php (11 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (25):
   - action
   - button_save
   - column_left
   - entry_geo_zone
   - entry_sort_order
   - entry_status
   - entry_total
   - error_permission
   - error_warning
   - footer
   - header
   - help_total
   - payment_cod_total
   - text_all_zones
   - text_disabled
   - text_edit
   - text_enabled
   - text_extension
   - text_home
   - text_success
   ... و 5 متغير آخر

❌ Missing in Arabic (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - payment_cod_sort_order
   - payment_cod_total
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home

❌ Missing in English (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - payment_cod_sort_order
   - payment_cod_total
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 14 items
      - button_save
      - error_warning
      - payment_cod_sort_order
      - column_left
      - payment_cod_total
   🟡 MISSING_ENGLISH_VARIABLES: 14 items
      - button_save
      - error_warning
      - payment_cod_sort_order
      - column_left
      - payment_cod_total

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 14 متغير عربي و 14 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:24
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.