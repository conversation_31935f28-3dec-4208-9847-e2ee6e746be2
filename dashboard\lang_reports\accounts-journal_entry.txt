📄 Route: accounts/journal_entry
📂 Controller: controller\accounts\journal_entry.php
🧱 Models used (10):
   - accounts/approval
   - accounts/audit_trail
   - accounts/chartaccount
   - accounts/cost_center
   - accounts/department
   - accounts/journal_entry
   - accounts/journal_template
   - accounts/project
   - core/central_service_manager
   - notification/notification
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_long
   - error_account_not_found
   - error_account_required
   - error_amount_required
   - error_both_amounts
   - error_description
   - error_journal_date
   - error_journal_id
   - error_lines_minimum
   - error_permission
   - error_template_data
   - error_unbalanced
   - heading_title
   - text_add
   - text_approved
   - text_cancelled
   - text_customer_payment
   - text_draft
   - text_edit
   - text_home
   - text_inventory_movement
   - text_manual
   - text_posted
   - text_purchase_order
   - text_sales_order
   - text_select
   - text_success_add
   - text_success_duplicate
   - text_success_edit
   - text_success_post
   - text_success_template_save
   - text_success_unpost
   - text_supplier_payment

❌ Missing in Arabic:
   - date_format_long
   - error_account_not_found
   - error_account_required
   - error_amount_required
   - error_both_amounts
   - error_description
   - error_journal_date
   - error_journal_id
   - error_lines_minimum
   - error_permission
   - error_template_data
   - error_unbalanced
   - heading_title
   - text_add
   - text_approved
   - text_cancelled
   - text_customer_payment
   - text_draft
   - text_edit
   - text_home
   - text_inventory_movement
   - text_manual
   - text_posted
   - text_purchase_order
   - text_sales_order
   - text_select
   - text_success_add
   - text_success_duplicate
   - text_success_edit
   - text_success_post
   - text_success_template_save
   - text_success_unpost
   - text_supplier_payment

❌ Missing in English:
   - date_format_long
   - error_account_not_found
   - error_account_required
   - error_amount_required
   - error_both_amounts
   - error_description
   - error_journal_date
   - error_journal_id
   - error_lines_minimum
   - error_permission
   - error_template_data
   - error_unbalanced
   - heading_title
   - text_add
   - text_approved
   - text_cancelled
   - text_customer_payment
   - text_draft
   - text_edit
   - text_home
   - text_inventory_movement
   - text_manual
   - text_posted
   - text_purchase_order
   - text_sales_order
   - text_select
   - text_success_add
   - text_success_duplicate
   - text_success_edit
   - text_success_post
   - text_success_template_save
   - text_success_unpost
   - text_supplier_payment

💡 Suggested Arabic Additions:
   - date_format_long = ""  # TODO: ترجمة عربية
   - error_account_not_found = ""  # TODO: ترجمة عربية
   - error_account_required = ""  # TODO: ترجمة عربية
   - error_amount_required = ""  # TODO: ترجمة عربية
   - error_both_amounts = ""  # TODO: ترجمة عربية
   - error_description = ""  # TODO: ترجمة عربية
   - error_journal_date = ""  # TODO: ترجمة عربية
   - error_journal_id = ""  # TODO: ترجمة عربية
   - error_lines_minimum = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_template_data = ""  # TODO: ترجمة عربية
   - error_unbalanced = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_approved = ""  # TODO: ترجمة عربية
   - text_cancelled = ""  # TODO: ترجمة عربية
   - text_customer_payment = ""  # TODO: ترجمة عربية
   - text_draft = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_inventory_movement = ""  # TODO: ترجمة عربية
   - text_manual = ""  # TODO: ترجمة عربية
   - text_posted = ""  # TODO: ترجمة عربية
   - text_purchase_order = ""  # TODO: ترجمة عربية
   - text_sales_order = ""  # TODO: ترجمة عربية
   - text_select = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_duplicate = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية
   - text_success_post = ""  # TODO: ترجمة عربية
   - text_success_template_save = ""  # TODO: ترجمة عربية
   - text_success_unpost = ""  # TODO: ترجمة عربية
   - text_supplier_payment = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_long = ""  # TODO: English translation
   - error_account_not_found = ""  # TODO: English translation
   - error_account_required = ""  # TODO: English translation
   - error_amount_required = ""  # TODO: English translation
   - error_both_amounts = ""  # TODO: English translation
   - error_description = ""  # TODO: English translation
   - error_journal_date = ""  # TODO: English translation
   - error_journal_id = ""  # TODO: English translation
   - error_lines_minimum = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_template_data = ""  # TODO: English translation
   - error_unbalanced = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_approved = ""  # TODO: English translation
   - text_cancelled = ""  # TODO: English translation
   - text_customer_payment = ""  # TODO: English translation
   - text_draft = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_inventory_movement = ""  # TODO: English translation
   - text_manual = ""  # TODO: English translation
   - text_posted = ""  # TODO: English translation
   - text_purchase_order = ""  # TODO: English translation
   - text_sales_order = ""  # TODO: English translation
   - text_select = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_duplicate = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
   - text_success_post = ""  # TODO: English translation
   - text_success_template_save = ""  # TODO: English translation
   - text_success_unpost = ""  # TODO: English translation
   - text_supplier_payment = ""  # TODO: English translation
