📄 Route: eta/eta_management
📂 Controller: controller\eta\eta_management.php
🧱 Models used (2):
   - core/central_service_manager
   - eta/eta_client
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_home
   - text_invoice_submit_failed
   - text_invoice_submitted_success
   - text_submit_invoice

❌ Missing in Arabic:
   - heading_title
   - text_home
   - text_invoice_submit_failed
   - text_invoice_submitted_success
   - text_submit_invoice

❌ Missing in English:
   - heading_title
   - text_home
   - text_invoice_submit_failed
   - text_invoice_submitted_success
   - text_submit_invoice

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_invoice_submit_failed = ""  # TODO: ترجمة عربية
   - text_invoice_submitted_success = ""  # TODO: ترجمة عربية
   - text_submit_invoice = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_invoice_submit_failed = ""  # TODO: English translation
   - text_invoice_submitted_success = ""  # TODO: English translation
   - text_submit_invoice = ""  # TODO: English translation
