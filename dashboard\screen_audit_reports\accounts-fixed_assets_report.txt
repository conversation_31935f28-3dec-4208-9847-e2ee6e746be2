📄 Route: accounts/fixed_assets_report
📂 Controller: controller\accounts\fixed_assets_report.php
🧱 Models used (1):
   ✅ accounts/fixed_assets_report (1 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\fixed_assets_report.php (22 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\fixed_assets_report.php (22 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (25):
   - button_filter
   - code
   - date_format_short
   - entry_date_end
   - entry_date_start
   - error_no_data
   - print_title
   - text_asset_code
   - text_asset_name
   - text_current_value
   - text_fixed_assets_report
   - text_form
   - text_from
   - text_method
   - text_new_current_value
   - text_period
   - text_purchase_value
   - text_salvage_value
   - text_to
   - text_useful_life
   ... و 5 متغير آخر

❌ Missing in Arabic (3):
   - code
   - date_format_short
   - direction

❌ Missing in English (3):
   - code
   - date_format_short
   - direction

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - code
      - direction
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - code
      - direction
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:41
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.