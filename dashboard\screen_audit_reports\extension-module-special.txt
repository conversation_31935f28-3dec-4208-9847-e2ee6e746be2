📄 Route: extension/module/special
📂 Controller: controller\extension\module\special.php
🧱 Models used (1):
   ✅ setting/module (7 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\special.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\module\special.php (13 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\special.php (13 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (28):
   - action
   - column_left
   - entry_height
   - entry_limit
   - entry_status
   - entry_width
   - error_height
   - error_name
   - error_permission
   - error_width
   - header
   - height
   - limit
   - name
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_success
   - width
   ... و 8 متغير آخر

❌ Missing in Arabic (15):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - height
   - limit
   - name
   - text_disabled
   - text_enabled
   - text_home
   - width

❌ Missing in English (15):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - height
   - limit
   - name
   - text_disabled
   - text_enabled
   - text_home
   - width

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 15 items
      - width
      - button_save
      - error_warning
      - cancel
      - column_left
   🟡 MISSING_ENGLISH_VARIABLES: 15 items
      - width
      - button_save
      - error_warning
      - cancel
      - column_left

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 15 متغير عربي و 15 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:24
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.