📄 Route: user/api
📂 Controller: controller\user\api.php
🧱 Models used (1):
   - user/api
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\user\api.php
🇬🇧 English Language Files (1):
   - language\en-gb\user\api.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - datetime_format
   - error_ip
   - error_key
   - error_permission
   - error_username
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_ip
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - datetime_format
   - error_ip
   - error_key
   - error_permission
   - error_username
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_ip
   - text_pagination
   - text_success

❌ Missing in English:
   - date_format_short
   - datetime_format
   - error_ip
   - error_key
   - error_permission
   - error_username
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_ip
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_ip = ""  # TODO: ترجمة عربية
   - error_key = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_username = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_ip = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_ip = ""  # TODO: English translation
   - error_key = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_username = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_ip = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
