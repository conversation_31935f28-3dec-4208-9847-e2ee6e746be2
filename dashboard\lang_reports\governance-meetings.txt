📄 Route: governance/meetings
📂 Controller: controller\governance\meetings.php
🧱 Models used (1):
   - governance/meetings
🎨 Twig templates (1):
   - view\template\governance\meetings.twig
🈯 Arabic Language Files (1):
   - language\ar\governance\meetings.php
🇬🇧 English Language Files (1):
   - language\en-gb\governance\meetings.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_home
   - text_list

❌ Missing in Arabic:
   - heading_title
   - text_home
   - text_list

❌ Missing in English:
   - heading_title
   - text_home
   - text_list

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
