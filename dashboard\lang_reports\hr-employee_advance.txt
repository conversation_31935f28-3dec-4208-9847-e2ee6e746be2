📄 Route: hr/employee_advance
📂 Controller: controller\hr\employee_advance.php
🧱 Models used (2):
   - hr/employee
   - hr/employee_advance
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - error_advance_id_required
   - error_advance_not_found
   - error_required_fields
   - heading_title
   - text_add_advance
   - text_advance_added
   - text_advance_approved
   - text_advance_dashboard
   - text_advance_disbursed
   - text_advance_rejected
   - text_advance_updated
   - text_edit_advance
   - text_home
   - text_pending_installments
   - text_view_advance

❌ Missing in Arabic:
   - datetime_format
   - error_advance_id_required
   - error_advance_not_found
   - error_required_fields
   - heading_title
   - text_add_advance
   - text_advance_added
   - text_advance_approved
   - text_advance_dashboard
   - text_advance_disbursed
   - text_advance_rejected
   - text_advance_updated
   - text_edit_advance
   - text_home
   - text_pending_installments
   - text_view_advance

❌ Missing in English:
   - datetime_format
   - error_advance_id_required
   - error_advance_not_found
   - error_required_fields
   - heading_title
   - text_add_advance
   - text_advance_added
   - text_advance_approved
   - text_advance_dashboard
   - text_advance_disbursed
   - text_advance_rejected
   - text_advance_updated
   - text_edit_advance
   - text_home
   - text_pending_installments
   - text_view_advance

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_advance_id_required = ""  # TODO: ترجمة عربية
   - error_advance_not_found = ""  # TODO: ترجمة عربية
   - error_required_fields = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add_advance = ""  # TODO: ترجمة عربية
   - text_advance_added = ""  # TODO: ترجمة عربية
   - text_advance_approved = ""  # TODO: ترجمة عربية
   - text_advance_dashboard = ""  # TODO: ترجمة عربية
   - text_advance_disbursed = ""  # TODO: ترجمة عربية
   - text_advance_rejected = ""  # TODO: ترجمة عربية
   - text_advance_updated = ""  # TODO: ترجمة عربية
   - text_edit_advance = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pending_installments = ""  # TODO: ترجمة عربية
   - text_view_advance = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - error_advance_id_required = ""  # TODO: English translation
   - error_advance_not_found = ""  # TODO: English translation
   - error_required_fields = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add_advance = ""  # TODO: English translation
   - text_advance_added = ""  # TODO: English translation
   - text_advance_approved = ""  # TODO: English translation
   - text_advance_dashboard = ""  # TODO: English translation
   - text_advance_disbursed = ""  # TODO: English translation
   - text_advance_rejected = ""  # TODO: English translation
   - text_advance_updated = ""  # TODO: English translation
   - text_edit_advance = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pending_installments = ""  # TODO: English translation
   - text_view_advance = ""  # TODO: English translation
