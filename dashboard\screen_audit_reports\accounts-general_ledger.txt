📄 Route: accounts/general_ledger
📂 Controller: controller\accounts\general_ledger.php
🧱 Models used (4):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/general_ledger (12 functions)
   ✅ accounts/chartaccount (19 functions)
   ✅ branch/branch (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\general_ledger.php (145 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\general_ledger.php (145 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - error_date_end
   - error_date_range
   - error_date_start
   - error_no_data
   - error_permission
   - heading_title
   - text_account_code
   - text_account_name
   - text_balance
   - text_credit
   - text_debit
   - text_home
   - text_no_results
   - text_success_generate
   - text_visual_analysis

❌ Missing in Arabic (2):
   - text_home
   - text_visual_analysis

❌ Missing in English (2):
   - text_home
   - text_visual_analysis

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_visual_analysis
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - text_visual_analysis
      - text_home
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:41
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.