📄 Route: extension/module/banner
📂 Controller: controller\extension\module\banner.php
🧱 Models used (2):
   ✅ setting/module (7 functions)
   ✅ design/banner (7 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\banner.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\module\banner.php (14 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\banner.php (14 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (27):
   - action
   - button_save
   - column_left
   - entry_height
   - entry_name
   - entry_status
   - entry_width
   - error_name
   - error_permission
   - error_warning
   - footer
   - header
   - height
   - name
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_success
   - width
   ... و 7 متغير آخر

❌ Missing in Arabic (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - height
   - name
   - text_disabled
   - text_enabled
   - text_home
   - width

❌ Missing in English (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - height
   - name
   - text_disabled
   - text_enabled
   - text_home
   - width

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 14 items
      - width
      - button_save
      - error_warning
      - column_left
      - action
   🟡 MISSING_ENGLISH_VARIABLES: 14 items
      - width
      - button_save
      - error_warning
      - column_left
      - action

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 14 متغير عربي و 14 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:23
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.