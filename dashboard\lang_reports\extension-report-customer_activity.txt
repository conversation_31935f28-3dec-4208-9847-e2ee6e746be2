📄 Route: extension/report/customer_activity
📂 Controller: controller\extension\report\customer_activity.php
🧱 Models used (2):
   - extension/report/customer
   - setting/setting
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\extension\report\customer_activity.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\report\customer_activity.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - datetime_format
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - datetime_format
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
