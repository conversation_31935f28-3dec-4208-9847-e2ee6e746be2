📄 Route: extension/total/klarna_fee
📂 Controller: controller\extension\total\klarna_fee.php
🧱 Models used (2):
   - localisation/tax_class
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\total\klarna_fee.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\total\klarna_fee.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\total\klarna_fee.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_denmark
   - text_extension
   - text_finland
   - text_germany
   - text_home
   - text_netherlands
   - text_norway
   - text_success
   - text_sweden

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_denmark
   - text_extension
   - text_finland
   - text_germany
   - text_home
   - text_netherlands
   - text_norway
   - text_success
   - text_sweden

❌ Missing in English:
   - error_permission
   - heading_title
   - text_denmark
   - text_extension
   - text_finland
   - text_germany
   - text_home
   - text_netherlands
   - text_norway
   - text_success
   - text_sweden

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_denmark = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_finland = ""  # TODO: ترجمة عربية
   - text_germany = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_netherlands = ""  # TODO: ترجمة عربية
   - text_norway = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_sweden = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_denmark = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_finland = ""  # TODO: English translation
   - text_germany = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_netherlands = ""  # TODO: English translation
   - text_norway = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_sweden = ""  # TODO: English translation
