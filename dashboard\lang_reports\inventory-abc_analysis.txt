📄 Route: inventory/abc_analysis
📂 Controller: controller\inventory\abc_analysis.php
🧱 Models used (9):
   - branch/branch
   - catalog/category
   - catalog/manufacturer
   - core/central_service_manager
   - inventory/abc_analysis
   - inventory/abc_analysis_enhanced
   - inventory/warehouse
   - setting/setting
   - user/user_group
🎨 Twig templates (1):
   - view\template\inventory\abc_analysis.twig
🈯 Arabic Language Files (2):
   - language\ar\common\header.php
   - language\ar\inventory\abc_analysis.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_abc_class
   - column_category
   - column_cost
   - column_cumulative_items
   - column_cumulative_value
   - column_model
   - column_product
   - column_quantity
   - column_sku
   - column_unit
   - column_value
   - column_value_percentage
   - entry_branch
   - entry_category
   - entry_date
   - error_advanced_permission
   - error_exception
   - heading_title
   - heading_value
   - text_abc_by_profit
   - text_abc_by_profit_desc
   - text_abc_by_sales
   - text_abc_by_sales_desc
   - text_abc_by_value
   - text_abc_by_value_desc
   - text_class_a_items
   - text_class_a_value
   - text_class_b_items
   - text_class_b_value
   - text_class_c_items
   - text_class_c_value
   - text_home
   - text_pagination
   - text_total_value

❌ Missing in Arabic:
   - column_abc_class
   - column_category
   - column_cost
   - column_cumulative_items
   - column_cumulative_value
   - column_model
   - column_product
   - column_quantity
   - column_sku
   - column_unit
   - column_value
   - column_value_percentage
   - entry_branch
   - entry_category
   - entry_date
   - error_advanced_permission
   - error_exception
   - heading_title
   - heading_value
   - text_abc_by_profit
   - text_abc_by_profit_desc
   - text_abc_by_sales
   - text_abc_by_sales_desc
   - text_abc_by_value
   - text_abc_by_value_desc
   - text_class_a_items
   - text_class_a_value
   - text_class_b_items
   - text_class_b_value
   - text_class_c_items
   - text_class_c_value
   - text_home
   - text_pagination
   - text_total_value

❌ Missing in English:
   - column_abc_class
   - column_category
   - column_cost
   - column_cumulative_items
   - column_cumulative_value
   - column_model
   - column_product
   - column_quantity
   - column_sku
   - column_unit
   - column_value
   - column_value_percentage
   - entry_branch
   - entry_category
   - entry_date
   - error_advanced_permission
   - error_exception
   - heading_title
   - heading_value
   - text_abc_by_profit
   - text_abc_by_profit_desc
   - text_abc_by_sales
   - text_abc_by_sales_desc
   - text_abc_by_value
   - text_abc_by_value_desc
   - text_class_a_items
   - text_class_a_value
   - text_class_b_items
   - text_class_b_value
   - text_class_c_items
   - text_class_c_value
   - text_home
   - text_pagination
   - text_total_value

💡 Suggested Arabic Additions:
   - column_abc_class = ""  # TODO: ترجمة عربية
   - column_category = ""  # TODO: ترجمة عربية
   - column_cost = ""  # TODO: ترجمة عربية
   - column_cumulative_items = ""  # TODO: ترجمة عربية
   - column_cumulative_value = ""  # TODO: ترجمة عربية
   - column_model = ""  # TODO: ترجمة عربية
   - column_product = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_sku = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - column_value = ""  # TODO: ترجمة عربية
   - column_value_percentage = ""  # TODO: ترجمة عربية
   - entry_branch = ""  # TODO: ترجمة عربية
   - entry_category = ""  # TODO: ترجمة عربية
   - entry_date = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_value = ""  # TODO: ترجمة عربية
   - text_abc_by_profit = ""  # TODO: ترجمة عربية
   - text_abc_by_profit_desc = ""  # TODO: ترجمة عربية
   - text_abc_by_sales = ""  # TODO: ترجمة عربية
   - text_abc_by_sales_desc = ""  # TODO: ترجمة عربية
   - text_abc_by_value = ""  # TODO: ترجمة عربية
   - text_abc_by_value_desc = ""  # TODO: ترجمة عربية
   - text_class_a_items = ""  # TODO: ترجمة عربية
   - text_class_a_value = ""  # TODO: ترجمة عربية
   - text_class_b_items = ""  # TODO: ترجمة عربية
   - text_class_b_value = ""  # TODO: ترجمة عربية
   - text_class_c_items = ""  # TODO: ترجمة عربية
   - text_class_c_value = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_total_value = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_abc_class = ""  # TODO: English translation
   - column_category = ""  # TODO: English translation
   - column_cost = ""  # TODO: English translation
   - column_cumulative_items = ""  # TODO: English translation
   - column_cumulative_value = ""  # TODO: English translation
   - column_model = ""  # TODO: English translation
   - column_product = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_sku = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - column_value = ""  # TODO: English translation
   - column_value_percentage = ""  # TODO: English translation
   - entry_branch = ""  # TODO: English translation
   - entry_category = ""  # TODO: English translation
   - entry_date = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_value = ""  # TODO: English translation
   - text_abc_by_profit = ""  # TODO: English translation
   - text_abc_by_profit_desc = ""  # TODO: English translation
   - text_abc_by_sales = ""  # TODO: English translation
   - text_abc_by_sales_desc = ""  # TODO: English translation
   - text_abc_by_value = ""  # TODO: English translation
   - text_abc_by_value_desc = ""  # TODO: English translation
   - text_class_a_items = ""  # TODO: English translation
   - text_class_a_value = ""  # TODO: English translation
   - text_class_b_items = ""  # TODO: English translation
   - text_class_b_value = ""  # TODO: English translation
   - text_class_c_items = ""  # TODO: English translation
   - text_class_c_value = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_total_value = ""  # TODO: English translation
