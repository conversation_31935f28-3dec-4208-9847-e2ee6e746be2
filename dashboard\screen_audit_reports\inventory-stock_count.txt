📄 Route: inventory/stock_count
📂 Controller: controller\inventory\stock_count.php
🧱 Models used (2):
   ✅ inventory/stock_count (12 functions)
   ✅ branch/branch (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\stock_count.php (46 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\inventory\stock_count.php (46 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (10):
   - error_branch
   - error_count_date
   - error_no_items
   - error_permission
   - error_reference_code
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_success

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (1):
   - text_home

🗄️ Database Tables Used (10):
   ✅ cod_branch
   ✅ cod_product
   ✅ cod_product_barcode
   ✅ cod_product_description
   ✅ cod_product_inventory
   ✅ cod_product_unit
   ✅ cod_stock_count
   ✅ cod_stock_count_item
   ✅ cod_unit
   ✅ cod_user

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 1 items
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 1 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:07
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.