📄 Route: tool/log
📂 Controller: controller\tool\log.php
🧱 Models used (0):
🎨 Twig templates (1):
   ✅ view\template\tool\log.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\tool\log.php (5 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\tool\log.php (5 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (16):
   - button_clear
   - button_download
   - clear
   - column_left
   - download
   - error_permission
   - error_warning
   - footer
   - header
   - heading_title
   - log
   - success
   - text_confirm
   - text_home
   - text_list
   - text_success

❌ Missing in Arabic (11):
   - button_clear
   - button_download
   - clear
   - column_left
   - download
   - footer
   - header
   - log
   - success
   - text_confirm
   - text_home

❌ Missing in English (11):
   - button_clear
   - button_download
   - clear
   - column_left
   - download
   - footer
   - header
   - log
   - success
   - text_confirm
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 11 items
      - column_left
      - text_home
      - log
      - success
      - button_download
   🟡 MISSING_ENGLISH_VARIABLES: 11 items
      - column_left
      - text_home
      - log
      - success
      - button_download

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 11 متغير عربي و 11 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.