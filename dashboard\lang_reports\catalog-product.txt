📄 Route: catalog/product
📂 Controller: controller\catalog\product.php
🧱 Models used (29):
   - accounts/account
   - accounts/journal
   - ai/ai_assistant
   - catalog/attribute
   - catalog/category
   - catalog/filter
   - catalog/manufacturer
   - catalog/option
   - catalog/pricing_manager
   - catalog/product
   - catalog/unit_manager
   - communication/internal_communication
   - communication/unified_notification
   - core/central_service_manager
   - design/layout
   - inventory/inventory_manager
   - inventory/warehouse
   - localisation/language
   - localisation/length_class
   - localisation/stock_status
   - localisation/tax_class
   - localisation/weight_class
   - logging/user_activity
   - setting/setting
   - tool/export
   - tool/image
   - tool/import
   - unified_document
   - workflow/visual_workflow_engine
🎨 Twig templates (0):
🈯 Arabic Language Files (3):
   - language\ar\catalog\product.php
   - language\ar\common\header.php
   - language\ar\inventory\inventory.php
🇬🇧 English Language Files (3):
   - language\en-gb\catalog\product.php
   - language\en-gb\common\header.php
   - language\en-gb\inventory\inventory.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - audit_duplicate_models
   - audit_negative_stock
   - audit_products_without_categories
   - audit_products_without_images
   - audit_products_without_prices
   - error_add_failed
   - error_edit_failed
   - error_empty_file
   - error_import_row
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_field
   - error_invalid_file_type
   - error_invalid_item
   - error_items_required
   - error_meta_title
   - error_missing_data
   - error_missing_required_data
   - error_model
   - error_model_exists
   - error_movement_failed_for_product
   - error_name
   - error_permission
   - error_permission_advanced
   - error_product_has_dependencies
   - error_product_id_required
   - error_product_not_found
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_abc_recommendation_a
   - text_abc_recommendation_b
   - text_abc_recommendation_c
   - text_add
   - text_auto_saved
   - text_disabled
   - text_edit
   - text_enabled
   - text_field_updated
   - text_home
   - text_import_success
   - text_initial_inventory
   - text_inventory_adjustment
   - text_low_stock_alert
   - text_pagination
   - text_price_adjustment
   - text_product_added_notification
   - text_product_critical_changes
   - text_success_add
   - text_success_copy
   - text_success_delete
   - text_success_edit

❌ Missing in Arabic:
   - audit_duplicate_models
   - audit_negative_stock
   - audit_products_without_categories
   - audit_products_without_images
   - audit_products_without_prices
   - error_add_failed
   - error_edit_failed
   - error_empty_file
   - error_import_row
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_field
   - error_invalid_file_type
   - error_invalid_item
   - error_items_required
   - error_meta_title
   - error_missing_data
   - error_missing_required_data
   - error_model
   - error_model_exists
   - error_movement_failed_for_product
   - error_name
   - error_permission
   - error_permission_advanced
   - error_product_has_dependencies
   - error_product_id_required
   - error_product_not_found
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_abc_recommendation_a
   - text_abc_recommendation_b
   - text_abc_recommendation_c
   - text_add
   - text_auto_saved
   - text_disabled
   - text_edit
   - text_enabled
   - text_field_updated
   - text_home
   - text_import_success
   - text_initial_inventory
   - text_inventory_adjustment
   - text_low_stock_alert
   - text_pagination
   - text_price_adjustment
   - text_product_added_notification
   - text_product_critical_changes
   - text_success_add
   - text_success_copy
   - text_success_delete
   - text_success_edit

❌ Missing in English:
   - audit_duplicate_models
   - audit_negative_stock
   - audit_products_without_categories
   - audit_products_without_images
   - audit_products_without_prices
   - error_add_failed
   - error_edit_failed
   - error_empty_file
   - error_import_row
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_field
   - error_invalid_file_type
   - error_invalid_item
   - error_items_required
   - error_meta_title
   - error_missing_data
   - error_missing_required_data
   - error_model
   - error_model_exists
   - error_movement_failed_for_product
   - error_name
   - error_permission
   - error_permission_advanced
   - error_product_has_dependencies
   - error_product_id_required
   - error_product_not_found
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_abc_recommendation_a
   - text_abc_recommendation_b
   - text_abc_recommendation_c
   - text_add
   - text_auto_saved
   - text_disabled
   - text_edit
   - text_enabled
   - text_field_updated
   - text_home
   - text_import_success
   - text_initial_inventory
   - text_inventory_adjustment
   - text_low_stock_alert
   - text_pagination
   - text_price_adjustment
   - text_product_added_notification
   - text_product_critical_changes
   - text_success_add
   - text_success_copy
   - text_success_delete
   - text_success_edit

💡 Suggested Arabic Additions:
   - audit_duplicate_models = ""  # TODO: ترجمة عربية
   - audit_negative_stock = ""  # TODO: ترجمة عربية
   - audit_products_without_categories = ""  # TODO: ترجمة عربية
   - audit_products_without_images = ""  # TODO: ترجمة عربية
   - audit_products_without_prices = ""  # TODO: ترجمة عربية
   - error_add_failed = ""  # TODO: ترجمة عربية
   - error_edit_failed = ""  # TODO: ترجمة عربية
   - error_empty_file = ""  # TODO: ترجمة عربية
   - error_import_row = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_field = ""  # TODO: ترجمة عربية
   - error_invalid_file_type = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_meta_title = ""  # TODO: ترجمة عربية
   - error_missing_data = ""  # TODO: ترجمة عربية
   - error_missing_required_data = ""  # TODO: ترجمة عربية
   - error_model = ""  # TODO: ترجمة عربية
   - error_model_exists = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_permission_advanced = ""  # TODO: ترجمة عربية
   - error_product_has_dependencies = ""  # TODO: ترجمة عربية
   - error_product_id_required = ""  # TODO: ترجمة عربية
   - error_product_not_found = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_abc_recommendation_a = ""  # TODO: ترجمة عربية
   - text_abc_recommendation_b = ""  # TODO: ترجمة عربية
   - text_abc_recommendation_c = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_auto_saved = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_field_updated = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_import_success = ""  # TODO: ترجمة عربية
   - text_initial_inventory = ""  # TODO: ترجمة عربية
   - text_inventory_adjustment = ""  # TODO: ترجمة عربية
   - text_low_stock_alert = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_price_adjustment = ""  # TODO: ترجمة عربية
   - text_product_added_notification = ""  # TODO: ترجمة عربية
   - text_product_critical_changes = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_copy = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - audit_duplicate_models = ""  # TODO: English translation
   - audit_negative_stock = ""  # TODO: English translation
   - audit_products_without_categories = ""  # TODO: English translation
   - audit_products_without_images = ""  # TODO: English translation
   - audit_products_without_prices = ""  # TODO: English translation
   - error_add_failed = ""  # TODO: English translation
   - error_edit_failed = ""  # TODO: English translation
   - error_empty_file = ""  # TODO: English translation
   - error_import_row = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_field = ""  # TODO: English translation
   - error_invalid_file_type = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_meta_title = ""  # TODO: English translation
   - error_missing_data = ""  # TODO: English translation
   - error_missing_required_data = ""  # TODO: English translation
   - error_model = ""  # TODO: English translation
   - error_model_exists = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_permission_advanced = ""  # TODO: English translation
   - error_product_has_dependencies = ""  # TODO: English translation
   - error_product_id_required = ""  # TODO: English translation
   - error_product_not_found = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_abc_recommendation_a = ""  # TODO: English translation
   - text_abc_recommendation_b = ""  # TODO: English translation
   - text_abc_recommendation_c = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_auto_saved = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_field_updated = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_import_success = ""  # TODO: English translation
   - text_initial_inventory = ""  # TODO: English translation
   - text_inventory_adjustment = ""  # TODO: English translation
   - text_low_stock_alert = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_price_adjustment = ""  # TODO: English translation
   - text_product_added_notification = ""  # TODO: English translation
   - text_product_critical_changes = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_copy = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
