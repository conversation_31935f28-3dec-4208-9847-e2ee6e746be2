CREATE TABLE `cod_2fa_message_templates` (
  `template_id` int(11) NOT NULL,
  `template_type` enum('sms','email') NOT NULL COMMENT 'نوع القالب',
  `language_code` varchar(5) NOT NULL DEFAULT 'ar' COMMENT 'رمز اللغة',
  `purpose` enum('2fa_code','phone_verify','device_alert','backup_codes') NOT NULL COMMENT 'الغرض',
  `subject` varchar(255) DEFAULT NULL COMMENT 'موضوع الرسالة (للبريد الإلكتروني)',
  `message_body` text NOT NULL COMMENT 'نص الرسالة',
  `variables` text DEFAULT NULL COMMENT 'المتغيرات المتاحة',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط أم لا',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `cod_2fa_settings` (
  `setting_id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL COMMENT 'مفتاح الإعداد',
  `setting_value` text NOT NULL COMMENT 'قيمة الإعداد',
  `description` varchar(255) DEFAULT NULL COMMENT 'وصف الإعداد',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط أم لا',
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='إعدادات المصادقة الثنائية';

CREATE TABLE `cod_abandoned_cart` (
  `cart_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` varchar(32) NOT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp(),
  `last_activity` datetime NOT NULL,
  `items_count` int(11) NOT NULL DEFAULT 0,
  `total_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `recovery_email_sent` tinyint(1) NOT NULL DEFAULT 0,
  `email_sent_date` datetime DEFAULT NULL,
  `recovery_date` datetime DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_abandoned_cart_recovery` (
  `recovery_id` int(11) NOT NULL,
  `cart_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` varchar(20) NOT NULL,
  `data` text NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_abandoned_cart_template` (
  `template_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `content` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_accounts` (
  `account_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` bigint(20) NOT NULL DEFAULT 0,
  `account_code` bigint(20) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp(),
  `account_type` enum('debit','credit') NOT NULL DEFAULT 'debit'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_account_description` (
  `account_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_activity_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action_type` varchar(50) NOT NULL,
  `module` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_address` (
  `address_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `company` varchar(40) DEFAULT NULL,
  `address_1` varchar(128) NOT NULL,
  `address_2` varchar(128) NOT NULL,
  `city` varchar(128) NOT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `country_id` int(11) NOT NULL DEFAULT 63,
  `zone_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_announcement` (
  `announcement_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` enum('general','catalog','inventory','system','urgent','info') NOT NULL DEFAULT 'general',
  `priority` enum('low','normal','high','urgent','critical') NOT NULL DEFAULT 'normal',
  `status` enum('draft','active','scheduled','expired','archived') NOT NULL DEFAULT 'draft',
  `start_date` datetime DEFAULT NULL COMMENT 'تاريخ بدء عرض الإعلان',
  `end_date` datetime DEFAULT NULL COMMENT 'تاريخ انتهاء عرض الإعلان',
  `target_groups` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'مجموعات المستخدمين المستهدفة' CHECK (json_valid(`target_groups`)),
  `target_users` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'المستخدمين المستهدفين' CHECK (json_valid(`target_users`)),
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'بيانات إضافية للإعلان' CHECK (json_valid(`metadata`)),
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول الإعلانات الرئيسي';

CREATE TABLE `cod_announcement_attachment` (
  `attachment_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `uploaded_by` int(11) NOT NULL,
  `uploaded_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول مرفقات الإعلانات';

CREATE TABLE `cod_announcement_comment` (
  `comment_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `parent_comment_id` int(11) DEFAULT NULL COMMENT 'للردود على التعليقات',
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'approved',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول تعليقات الإعلانات';

CREATE TABLE `cod_announcement_view` (
  `view_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `viewed_at` datetime NOT NULL DEFAULT current_timestamp(),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول مشاهدات الإعلانات';

CREATE TABLE `cod_api` (
  `api_id` int(11) NOT NULL,
  `username` varchar(64) NOT NULL,
  `key` mediumtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_api_ip` (
  `api_ip_id` int(11) NOT NULL,
  `api_id` int(11) NOT NULL,
  `ip` varchar(40) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_api_session` (
  `api_session_id` int(11) NOT NULL,
  `api_id` int(11) NOT NULL,
  `session_id` varchar(32) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_asset_types` (
  `asset_type_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attendance` (
  `attendance_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `checkin_time` datetime DEFAULT NULL,
  `checkout_time` datetime DEFAULT NULL,
  `status` enum('present','absent','late','on_leave') NOT NULL DEFAULT 'present',
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute` (
  `attribute_id` int(11) NOT NULL,
  `attribute_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute_description` (
  `attribute_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute_group` (
  `attribute_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_attribute_group_description` (
  `attribute_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_audit_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `reference_type` varchar(50) NOT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `before_data` mediumtext DEFAULT NULL,
  `after_data` mediumtext DEFAULT NULL,
  `timestamp` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_audit_plan` (
  `plan_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `year` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('draft','approved','in_progress','completed') NOT NULL DEFAULT 'draft',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_audit_task` (
  `task_id` int(11) NOT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `process` varchar(100) DEFAULT NULL,
  `risk_level` enum('low','medium','high') NOT NULL DEFAULT 'medium',
  `status` enum('pending','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
  `start_date` date DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `assigned_to` int(11) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `findings` text DEFAULT NULL,
  `recommendations` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank` (
  `bank_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `account_number` varchar(64) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `branch` varchar(64) NOT NULL,
  `swift_code` varchar(64) NOT NULL,
  `address` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank_account` (
  `account_id` int(11) NOT NULL,
  `account_name` varchar(100) NOT NULL,
  `bank_name` varchar(100) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `currency` varchar(3) NOT NULL,
  `current_balance` decimal(15,4) NOT NULL,
  `account_type` enum('bank','credit_card','e_wallet') NOT NULL DEFAULT 'bank'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank_reconciliation` (
  `reconciliation_id` int(11) NOT NULL,
  `bank_account_id` int(11) NOT NULL,
  `statement_date` date NOT NULL,
  `statement_opening_balance` decimal(15,4) NOT NULL,
  `statement_closing_balance` decimal(15,4) NOT NULL,
  `system_closing_balance` decimal(15,4) NOT NULL COMMENT 'الرصيد المتوقع في النظام قبل التسوية',
  `difference` decimal(15,4) NOT NULL COMMENT 'الفارق إن وجد',
  `status` enum('open','closed') NOT NULL DEFAULT 'open',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bank_transaction` (
  `bank_transaction_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `bank_account_id` int(11) NOT NULL,
  `transaction_date` datetime NOT NULL,
  `transaction_type` enum('deposit','withdraw','transfer_in','transfer_out','check_in','check_out') NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `reference` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_banner` (
  `banner_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `status` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_banner_image` (
  `banner_image_id` int(11) NOT NULL,
  `banner_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(64) NOT NULL,
  `link` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_category` (
  `category_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_comment` (
  `comment_id` int(11) NOT NULL,
  `post_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL DEFAULT 0,
  `author` varchar(64) NOT NULL,
  `email` varchar(96) NOT NULL,
  `website` varchar(255) DEFAULT NULL,
  `content` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `notify` tinyint(1) NOT NULL DEFAULT 0,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_post` (
  `post_id` int(11) NOT NULL,
  `author_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `short_description` text DEFAULT NULL,
  `content` longtext NOT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` varchar(255) DEFAULT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `comment_status` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `hits` int(11) NOT NULL DEFAULT 0,
  `date_published` datetime DEFAULT NULL,
  `date_created` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_post_to_category` (
  `post_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_post_to_tag` (
  `post_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_blog_tag` (
  `tag_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `slug` varchar(64) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch` (
  `branch_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `address_id` int(11) NOT NULL,
  `type` enum('store','warehouse') NOT NULL,
  `eta_branch_id` varchar(50) DEFAULT NULL,
  `available_online` tinyint(1) NOT NULL DEFAULT 0,
  `telephone` varchar(32) NOT NULL,
  `email` varchar(96) NOT NULL,
  `manager_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch_address` (
  `address_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `company` varchar(40) DEFAULT NULL,
  `address_1` varchar(128) NOT NULL,
  `address_2` varchar(128) DEFAULT NULL,
  `city` varchar(128) NOT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `country_id` int(11) NOT NULL,
  `zone_id` int(11) NOT NULL,
  `custom_field` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch_distance` (
  `distance_id` int(11) NOT NULL,
  `from_branch_id` int(11) NOT NULL COMMENT 'الفرع المرسل',
  `to_zone_id` int(11) NOT NULL COMMENT 'المحافظة المستقبلة',
  `distance_km` decimal(8,2) NOT NULL COMMENT 'المسافة بالكيلومتر',
  `estimated_delivery_hours` decimal(5,2) NOT NULL COMMENT 'ساعات التوصيل المتوقعة',
  `shipping_cost_per_kg` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT 'تكلفة الشحن لكل كيلو',
  `priority` enum('primary','secondary','backup') NOT NULL DEFAULT 'primary',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_branch_inventory_snapshot` (
  `snapshot_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `snapshot_date` datetime NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `average_cost` decimal(15,4) NOT NULL,
  `last_movement_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_budget` (
  `budget_id` int(11) NOT NULL,
  `budget_name` varchar(100) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_budget_line` (
  `line_id` int(11) NOT NULL,
  `budget_id` int(11) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `amount` decimal(15,4) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_bundle_performance_analysis` (
  `analysis_id` int(11) NOT NULL,
  `bundle_id` int(11) NOT NULL,
  `analysis_date` date NOT NULL,
  `total_views` int(11) NOT NULL DEFAULT 0,
  `total_orders` int(11) NOT NULL DEFAULT 0,
  `total_revenue` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_discount_given` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `conversion_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `average_order_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `profit_margin` decimal(5,2) NOT NULL DEFAULT 0.00,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='تحليل أداء الباقات';

CREATE TABLE `cod_bundle_usage_log` (
  `usage_id` int(11) NOT NULL,
  `bundle_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` varchar(32) DEFAULT NULL,
  `quantity_used` int(11) NOT NULL DEFAULT 1,
  `original_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_applied` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `final_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `bundle_items_data` text DEFAULT NULL COMMENT 'تفاصيل عناصر الباقة المستخدمة',
  `status` enum('reserved','confirmed','cancelled','refunded') NOT NULL DEFAULT 'reserved',
  `used_at` datetime NOT NULL DEFAULT current_timestamp(),
  `confirmed_at` datetime DEFAULT NULL,
  `cancelled_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='سجل استخدام الباقات';

CREATE TABLE `cod_cart` (
  `cart_id` int(10) UNSIGNED NOT NULL,
  `api_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `session_id` varchar(32) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL DEFAULT 37,
  `recurring_id` int(11) NOT NULL,
  `option` mediumtext NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(15,4) DEFAULT NULL,
  `is_free` tinyint(1) NOT NULL DEFAULT 0,
  `bundle_id` int(11) DEFAULT NULL,
  `product_quantity_discount_id` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `selected_bundles` text DEFAULT NULL,
  `bundle_options` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_cash` (
  `cash_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `code` varchar(64) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `responsible_user_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_cash_transaction` (
  `cash_transaction_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `cash_id` int(11) NOT NULL COMMENT 'الخزنة/الصندوق المرتبط',
  `transaction_type` enum('cash_in','cash_out') NOT NULL COMMENT 'إيداع أو سحب',
  `amount` decimal(15,4) NOT NULL,
  `reference` varchar(100) DEFAULT NULL COMMENT 'مثلاً رقم فاتورة البيع أو قيد محاسبي',
  `note` text DEFAULT NULL COMMENT 'ملاحظات عامة',
  `created_by` int(11) NOT NULL COMMENT 'user_id الذي قام بالتسجيل',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category` (
  `category_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int(11) NOT NULL DEFAULT 0,
  `top` tinyint(1) NOT NULL,
  `column` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_description` (
  `category_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` varchar(255) NOT NULL,
  `meta_keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_filter` (
  `category_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_path` (
  `category_id` int(11) NOT NULL,
  `path_id` int(11) NOT NULL,
  `level` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_to_layout` (
  `category_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_category_to_store` (
  `category_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_checks` (
  `check_id` int(11) NOT NULL,
  `check_number` varchar(50) NOT NULL,
  `check_type` enum('incoming','outgoing') NOT NULL DEFAULT 'incoming',
  `bank_account_id` int(11) DEFAULT NULL,
  `payee_name` varchar(255) NOT NULL,
  `issue_date` date NOT NULL,
  `due_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency_id` int(11) DEFAULT NULL,
  `status` enum('outstanding','cleared','bounced','cancelled') NOT NULL DEFAULT 'outstanding',
  `reference` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_compliance_record` (
  `compliance_id` int(11) NOT NULL,
  `compliance_type` varchar(100) NOT NULL COMMENT 'مثلاً: إقرار ضريبي، التأمينات الاجتماعية، بيئي، إلخ',
  `reference_code` varchar(100) DEFAULT NULL COMMENT 'رقم المستند الرسمي لدى الجهة المختصة',
  `description` text DEFAULT NULL COMMENT 'تفاصيل الالتزام',
  `due_date` date DEFAULT NULL COMMENT 'تاريخ الاستحقاق أو آخر موعد لتقديم الإقرار',
  `status` enum('pending','submitted','approved','rejected','closed') DEFAULT 'pending',
  `responsible_user_id` int(11) DEFAULT NULL COMMENT 'المسؤول عن المتابعة',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_consignment_inventory` (
  `consignment_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `unit_id` int(11) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','returned','sold') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_cost_calculation_settings` (
  `setting_id` int(11) NOT NULL,
  `cost_method` enum('weighted_average','fifo','specific') NOT NULL DEFAULT 'weighted_average' COMMENT 'طريقة حساب التكلفة',
  `apply_price_variance_to` enum('inventory','expense','proportional') NOT NULL DEFAULT 'proportional' COMMENT 'طريقة معالجة فروق الأسعار',
  `variance_threshold_percentage` decimal(5,2) NOT NULL DEFAULT 2.00 COMMENT 'نسبة العتبة لاعتبار الفرق كبيرًا',
  `update_cost_on_return` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'تحديث التكلفة عند الإرجاع',
  `transfer_cost_method` enum('source_cost','fixed_cost','include_transfer_cost') NOT NULL DEFAULT 'source_cost' COMMENT 'طريقة نقل التكلفة عند التحويل',
  `batch_tracking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل تتبع الدفعات',
  `expiry_tracking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل تتبع تاريخ الصلاحية',
  `allow_negative_inventory` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'السماح بالمخزون السالب',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_country` (
  `country_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL,
  `iso_code_2` varchar(2) NOT NULL,
  `iso_code_3` varchar(3) NOT NULL,
  `address_format` mediumtext NOT NULL,
  `postcode_required` tinyint(1) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon` (
  `coupon_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL,
  `code` varchar(20) NOT NULL,
  `type` char(1) NOT NULL,
  `discount` decimal(15,4) NOT NULL,
  `logged` tinyint(1) NOT NULL,
  `shipping` tinyint(1) NOT NULL,
  `total` decimal(15,4) NOT NULL,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `uses_total` int(11) NOT NULL,
  `uses_customer` varchar(11) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon_category` (
  `coupon_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon_history` (
  `coupon_history_id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_coupon_product` (
  `coupon_product_id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_campaign` (
  `campaign_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('seo','adwords','social_media','email','other') NOT NULL DEFAULT 'other',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `budget` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `code` varchar(64) DEFAULT NULL,
  `status` enum('active','inactive','completed') NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL,
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `actual_spend` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `invoice_reference` varchar(50) DEFAULT NULL,
  `add_expense` tinyint(1) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_contact` (
  `contact_id` int(11) NOT NULL,
  `firstname` varchar(100) NOT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `position` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_deal` (
  `deal_id` int(11) NOT NULL,
  `account_id` int(11) DEFAULT NULL,
  `opportunity_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `stage` enum('qualification','proposal','negotiation','closed_won','closed_lost') NOT NULL DEFAULT 'qualification',
  `amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `probability` decimal(5,2) NOT NULL DEFAULT 0.00,
  `expected_close_date` date DEFAULT NULL,
  `status` enum('open','closed','on_hold') NOT NULL DEFAULT 'open',
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_lead` (
  `lead_id` int(11) NOT NULL,
  `firstname` varchar(100) NOT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `source` varchar(50) DEFAULT NULL,
  `status` enum('new','contacted','qualified','unqualified','converted') NOT NULL DEFAULT 'new',
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_crm_opportunity` (
  `opportunity_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `lead_id` int(11) DEFAULT NULL,
  `stage` enum('qualification','proposal','negotiation','closed_won','closed_lost') NOT NULL DEFAULT 'qualification',
  `probability` decimal(5,2) NOT NULL DEFAULT 0.00,
  `amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `close_date` date DEFAULT NULL,
  `assigned_to_user_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('open','closed','on_hold') NOT NULL DEFAULT 'open',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_currency` (
  `currency_id` int(11) NOT NULL,
  `title` varchar(32) NOT NULL,
  `code` varchar(3) NOT NULL,
  `symbol_left` varchar(12) NOT NULL,
  `symbol_right` varchar(12) NOT NULL,
  `decimal_place` char(1) NOT NULL,
  `value` double(15,8) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_currency_rate_history` (
  `rate_history_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `rate_date` date NOT NULL,
  `exchange_rate` decimal(15,8) NOT NULL,
  `changed_by` int(11) DEFAULT NULL,
  `note` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer` (
  `customer_id` int(11) NOT NULL,
  `eta_customer_type` enum('P','B') DEFAULT 'P',
  `eta_tax_id` varchar(100) DEFAULT NULL,
  `eta_commercial_registration` varchar(100) DEFAULT NULL,
  `eta_activity_code` varchar(20) DEFAULT NULL,
  `account_code` bigint(20) DEFAULT NULL,
  `customer_group_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `language_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `password` varchar(40) NOT NULL,
  `salt` varchar(9) NOT NULL,
  `cart` mediumtext DEFAULT NULL,
  `wishlist` mediumtext DEFAULT NULL,
  `newsletter` tinyint(1) NOT NULL DEFAULT 0,
  `address_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL,
  `ip` varchar(40) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `safe` tinyint(1) NOT NULL,
  `is_vip` tinyint(1) NOT NULL DEFAULT 0,
  `vip_level` varchar(20) DEFAULT NULL,
  `vip_since` date DEFAULT NULL,
  `vip_notes` text DEFAULT NULL,
  `token` mediumtext NOT NULL,
  `code` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_activity` (
  `customer_activity_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `key` varchar(64) NOT NULL,
  `data` mediumtext NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_affiliate` (
  `customer_id` int(11) NOT NULL,
  `company` varchar(40) NOT NULL,
  `website` varchar(255) NOT NULL,
  `tracking` varchar(64) NOT NULL,
  `commission` decimal(4,2) NOT NULL DEFAULT 0.00,
  `tax` varchar(64) NOT NULL,
  `payment` varchar(6) NOT NULL,
  `cheque` varchar(100) NOT NULL,
  `paypal` varchar(64) NOT NULL,
  `bank_name` varchar(64) NOT NULL,
  `bank_branch_number` varchar(64) NOT NULL,
  `bank_swift_code` varchar(64) NOT NULL,
  `bank_account_name` varchar(64) NOT NULL,
  `bank_account_number` varchar(64) NOT NULL,
  `custom_field` mediumtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_approval` (
  `customer_approval_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `type` varchar(9) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_credit_limit` (
  `limit_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `credit_limit` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `current_balance` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `payment_terms` int(11) DEFAULT NULL COMMENT 'فترة السداد بالأيام',
  `status` enum('active','suspended','pending_approval') NOT NULL DEFAULT 'active',
  `approved_by` int(11) DEFAULT NULL,
  `approval_date` datetime DEFAULT NULL,
  `last_review_date` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_feedback` (
  `feedback_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `feedback_type` enum('complaint','suggestion','inquiry','appreciation') NOT NULL,
  `subject` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `status` enum('new','in_progress','resolved','closed','cancelled') NOT NULL DEFAULT 'new',
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `closed_at` datetime DEFAULT NULL,
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, product',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `assigned_to` int(11) DEFAULT NULL COMMENT 'الموظف المسؤول',
  `satisfaction_rating` int(11) DEFAULT NULL COMMENT 'تقييم رضا العميل (1-5)',
  `source` enum('website','email','phone','social_media','in_store','app') NOT NULL DEFAULT 'website'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_group` (
  `customer_group_id` int(11) NOT NULL,
  `approval` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_group_description` (
  `customer_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_history` (
  `customer_history_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `comment` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_ip` (
  `customer_ip_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_login` (
  `customer_login_id` int(11) NOT NULL,
  `email` varchar(96) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `total` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_note` (
  `note_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `note` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_online` (
  `ip` varchar(40) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `url` mediumtext NOT NULL,
  `referer` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_return_inventory` (
  `return_inventory_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `return_to_stock` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'هل ترجع للمخزون أم تالفة',
  `updated_inventory` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم تحديث المخزون',
  `movement_id` int(11) DEFAULT NULL COMMENT 'معرف حركة المخزون',
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_reward` (
  `customer_reward_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL DEFAULT 0,
  `order_id` int(11) NOT NULL DEFAULT 0,
  `description` mediumtext NOT NULL,
  `points` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_search` (
  `customer_search_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `keyword` varchar(255) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `sub_category` tinyint(1) NOT NULL,
  `description` tinyint(1) NOT NULL,
  `products` int(11) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_transaction` (
  `customer_transaction_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `description` mediumtext NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_customer_wishlist` (
  `customer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field` (
  `custom_field_id` int(11) NOT NULL,
  `type` varchar(32) NOT NULL,
  `value` mediumtext NOT NULL,
  `validation` varchar(255) NOT NULL,
  `location` varchar(10) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_customer_group` (
  `custom_field_id` int(11) NOT NULL,
  `customer_group_id` int(11) NOT NULL,
  `required` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_description` (
  `custom_field_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_value` (
  `custom_field_value_id` int(11) NOT NULL,
  `custom_field_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_field_value_description` (
  `custom_field_value_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `custom_field_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_custom_report` (
  `report_id` int(11) NOT NULL,
  `report_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `report_query` text NOT NULL,
  `parameters` text DEFAULT NULL,
  `output_format` varchar(20) NOT NULL DEFAULT 'table',
  `is_public` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_data_access_control` (
  `access_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_group_id` int(11) DEFAULT NULL,
  `resource_type` varchar(50) NOT NULL COMMENT 'branch, product_category, etc',
  `resource_id` int(11) NOT NULL,
  `permission_level` varchar(20) NOT NULL DEFAULT 'view',
  `granted_by` int(11) NOT NULL,
  `granted_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_document_permission` (
  `permission_id` int(11) NOT NULL,
  `document_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT 'user_id أو NULL إذا كان للمجموعة',
  `user_group_id` int(11) DEFAULT NULL COMMENT 'user_group_id أو NULL إذا كان للمستخدم',
  `permission_type` enum('view','edit','delete','approve','share') NOT NULL DEFAULT 'view',
  `granted_by` int(11) NOT NULL,
  `granted_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_dynamic_pricing_rule` (
  `rule_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('percentage','fixed','formula') NOT NULL,
  `value` decimal(15,4) NOT NULL,
  `formula` text DEFAULT NULL,
  `condition_type` enum('customer_group','total_spent','purchase_history','time_period','stock_level','competitor_price') NOT NULL,
  `condition_value` text NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 0,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_advance` (
  `advance_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `request_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `status` enum('pending','approved','rejected','settled') NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_advance_installment` (
  `installment_id` int(11) NOT NULL,
  `advance_id` int(11) NOT NULL,
  `installment_number` int(11) NOT NULL,
  `due_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `paid_date` date DEFAULT NULL,
  `paid_amount` decimal(15,4) DEFAULT NULL,
  `status` enum('unpaid','paid','partial') NOT NULL DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_documents` (
  `document_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `document_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_employee_profile` (
  `employee_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `job_title` varchar(100) NOT NULL,
  `hiring_date` date NOT NULL,
  `salary` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` enum('active','inactive','terminated') NOT NULL DEFAULT 'active',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_eta_activity_codes` (
  `activity_id` int(11) NOT NULL,
  `activity_code` varchar(20) NOT NULL,
  `description_ar` varchar(255) NOT NULL,
  `description_en` varchar(255) NOT NULL,
  `parent_code` varchar(20) DEFAULT NULL,
  `level` int(11) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع الأنشطة الاقتصادية';

CREATE TABLE `cod_eta_activity_log` (
  `log_id` int(11) NOT NULL,
  `activity_type` varchar(50) NOT NULL,
  `entity_type` varchar(50) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `document_id` int(11) DEFAULT NULL,
  `queue_id` int(11) DEFAULT NULL,
  `description` text NOT NULL,
  `details` longtext DEFAULT NULL COMMENT 'تفاصيل إضافية بصيغة JSON',
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `status` enum('success','warning','error','info') DEFAULT 'info',
  `execution_time` decimal(8,3) DEFAULT NULL COMMENT 'وقت التنفيذ بالثواني',
  `memory_usage` int(11) DEFAULT NULL COMMENT 'استخدام الذاكرة بالبايت',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل أنشطة ETA';

CREATE TABLE `cod_eta_documents` (
  `document_id` int(11) NOT NULL,
  `entity_type` enum('order','invoice','credit_note','debit_note','receipt') NOT NULL,
  `entity_id` int(11) NOT NULL,
  `internal_id` varchar(100) NOT NULL COMMENT 'الرقم الداخلي للمستند',
  `eta_uuid` varchar(50) DEFAULT NULL COMMENT 'معرف ETA الفريد',
  `eta_long_id` varchar(100) DEFAULT NULL COMMENT 'المعرف الطويل من ETA',
  `submission_uuid` varchar(50) DEFAULT NULL COMMENT 'معرف الإرسال',
  `document_type` varchar(10) NOT NULL COMMENT 'I=Invoice, C=Credit, D=Debit',
  `document_version` varchar(10) DEFAULT '1.0',
  `status` enum('draft','submitted','processing','approved','rejected','cancelled','failed') NOT NULL DEFAULT 'draft',
  `eta_status` varchar(50) DEFAULT NULL COMMENT 'الحالة من ETA',
  `submission_data` longtext DEFAULT NULL COMMENT 'البيانات المرسلة',
  `eta_response` longtext DEFAULT NULL COMMENT 'الرد من ETA',
  `validation_errors` text DEFAULT NULL,
  `signature_value` text DEFAULT NULL COMMENT 'التوقيع الرقمي',
  `qr_code` text DEFAULT NULL COMMENT 'رمز QR',
  `pdf_url` varchar(500) DEFAULT NULL COMMENT 'رابط ملف PDF',
  `total_amount` decimal(15,4) DEFAULT 0.0000,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `currency_code` varchar(3) DEFAULT 'EGP',
  `exchange_rate` decimal(10,6) DEFAULT 1.000000,
  `date_issued` datetime DEFAULT NULL,
  `submitted_at` datetime DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `rejected_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='المستندات المرسلة إلى ETA';

CREATE TABLE `cod_eta_document_lines` (
  `line_id` int(11) NOT NULL,
  `document_id` int(11) NOT NULL,
  `line_number` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `item_code` varchar(100) DEFAULT NULL COMMENT 'كود الصنف',
  `internal_code` varchar(100) DEFAULT NULL COMMENT 'الكود الداخلي',
  `gpc_code` varchar(20) DEFAULT NULL COMMENT 'رمز GPC',
  `egs_code` varchar(20) DEFAULT NULL COMMENT 'رمز EGS',
  `description` text NOT NULL,
  `item_type` varchar(10) DEFAULT 'GS1' COMMENT 'GS1 أو EGS',
  `unit_type` varchar(10) DEFAULT 'EA' COMMENT 'وحدة القياس',
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `unit_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `sales_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,4) DEFAULT 0.0000,
  `discount_rate` decimal(5,2) DEFAULT 0.00,
  `net_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_type` varchar(10) DEFAULT 'T1' COMMENT 'نوع الضريبة',
  `tax_subtype` varchar(10) DEFAULT NULL COMMENT 'النوع الفرعي للضريبة',
  `tax_rate` decimal(5,2) DEFAULT 0.00,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `currency_sold` varchar(3) DEFAULT 'EGP',
  `currency_exchange_rate` decimal(10,6) DEFAULT 1.000000,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='بنود المستندات المرسلة إلى ETA';

CREATE TABLE `cod_eta_queue` (
  `queue_id` int(11) NOT NULL,
  `entity_type` enum('order','invoice','credit_note','debit_note','receipt') NOT NULL,
  `entity_id` int(11) NOT NULL,
  `action` enum('submit','cancel','update','retry') NOT NULL DEFAULT 'submit',
  `priority` tinyint(4) NOT NULL DEFAULT 5 COMMENT '1=عالي، 5=عادي، 10=منخفض',
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `attempts` int(11) NOT NULL DEFAULT 0,
  `max_attempts` int(11) NOT NULL DEFAULT 3,
  `payload` longtext DEFAULT NULL COMMENT 'البيانات المرسلة بصيغة JSON',
  `response` longtext DEFAULT NULL COMMENT 'الرد من ETA',
  `error_message` text DEFAULT NULL,
  `scheduled_at` datetime DEFAULT NULL COMMENT 'موعد التنفيذ المجدول',
  `processed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `created_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='طوابير معالجة ETA';

CREATE TABLE `cod_eta_settings` (
  `setting_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','integer','decimal','boolean','json','encrypted') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `is_encrypted` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات ETA';

CREATE TABLE `cod_eta_statistics` (
  `stat_id` int(11) NOT NULL,
  `stat_date` date NOT NULL,
  `stat_hour` tinyint(4) DEFAULT NULL COMMENT 'الساعة للإحصائيات بالساعة',
  `documents_submitted` int(11) DEFAULT 0,
  `documents_approved` int(11) DEFAULT 0,
  `documents_rejected` int(11) DEFAULT 0,
  `documents_failed` int(11) DEFAULT 0,
  `total_amount` decimal(15,4) DEFAULT 0.0000,
  `total_tax` decimal(15,4) DEFAULT 0.0000,
  `avg_response_time` decimal(8,3) DEFAULT NULL COMMENT 'متوسط وقت الاستجابة',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT 'معدل النجاح',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إحصائيات ETA';

CREATE TABLE `cod_eta_tax_codes` (
  `tax_code_id` int(11) NOT NULL,
  `tax_type` varchar(10) NOT NULL COMMENT 'T1=VAT, T2=Table, T3=Stamp, T4=Entertainment',
  `tax_subtype` varchar(10) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL,
  `description_ar` varchar(255) NOT NULL,
  `description_en` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `effective_from` date DEFAULT NULL,
  `effective_to` date DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='رموز الضرائب المصرية';

CREATE TABLE `cod_eta_tokens` (
  `token_id` int(11) NOT NULL,
  `access_token` text NOT NULL,
  `token_type` varchar(50) DEFAULT 'Bearer',
  `expires_at` datetime NOT NULL,
  `scope` varchar(255) DEFAULT 'InvoicingAPI',
  `environment` enum('sandbox','production') DEFAULT 'sandbox',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='رموز الوصول لـ ETA';

CREATE TABLE `cod_eta_unit_types` (
  `unit_id` int(11) NOT NULL,
  `unit_code` varchar(10) NOT NULL,
  `description_ar` varchar(100) NOT NULL,
  `description_en` varchar(100) NOT NULL,
  `category` varchar(50) DEFAULT NULL COMMENT 'فئة الوحدة',
  `conversion_factor` decimal(10,6) DEFAULT 1.000000 COMMENT 'معامل التحويل للوحدة الأساسية',
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع الوحدات المصرية';

CREATE TABLE `cod_event` (
  `event_id` int(11) NOT NULL,
  `code` varchar(64) NOT NULL,
  `trigger` mediumtext NOT NULL,
  `action` mediumtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_extension` (
  `extension_id` int(11) NOT NULL,
  `type` varchar(32) NOT NULL,
  `code` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_extension_install` (
  `extension_install_id` int(11) NOT NULL,
  `extension_download_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_extension_path` (
  `extension_path_id` int(11) NOT NULL,
  `extension_install_id` int(11) NOT NULL,
  `path` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_feedback_history` (
  `history_id` int(11) NOT NULL,
  `feedback_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` enum('created','updated','assigned','responded','resolved','reopened','closed') NOT NULL,
  `comment` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `visibility` enum('internal','customer','both') NOT NULL DEFAULT 'internal'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_feedback_message` (
  `message_id` int(11) NOT NULL,
  `feedback_id` int(11) NOT NULL,
  `sender_type` enum('customer','employee','system') NOT NULL,
  `sender_id` int(11) NOT NULL COMMENT 'customer_id أو user_id حسب sender_type',
  `message` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_internal` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل الرسالة داخلية فقط',
  `attachment_path` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_feedback_template` (
  `template_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `category` enum('complaint','suggestion','inquiry','appreciation') NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `tags` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter` (
  `filter_id` int(11) NOT NULL,
  `filter_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter_description` (
  `filter_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `filter_group_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter_group` (
  `filter_group_id` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_filter_group_description` (
  `filter_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_financial_forecast` (
  `forecast_id` int(11) NOT NULL,
  `forecast_name` varchar(100) NOT NULL,
  `forecast_type` varchar(20) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `forecast_value` decimal(15,4) NOT NULL,
  `actual_value` decimal(15,4) DEFAULT NULL,
  `variance` decimal(15,4) DEFAULT NULL,
  `variance_percentage` decimal(5,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_fixed_assets` (
  `asset_id` int(11) NOT NULL,
  `asset_code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `asset_type_id` int(11) NOT NULL,
  `purchase_date` date NOT NULL,
  `purchase_value` decimal(15,2) NOT NULL,
  `current_value` decimal(15,2) NOT NULL,
  `depreciation_method` varchar(50) NOT NULL,
  `useful_life` int(11) NOT NULL,
  `salvage_value` decimal(15,2) NOT NULL,
  `status` enum('active','disposed','under_maintenance') NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_fixed_asset_history` (
  `history_id` int(11) NOT NULL,
  `asset_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_geo_zone` (
  `geo_zone_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_gift_card` (
  `gift_card_id` int(11) NOT NULL,
  `code` varchar(255) NOT NULL,
  `from_name` varchar(255) NOT NULL,
  `from_email` varchar(255) NOT NULL,
  `to_name` varchar(255) NOT NULL,
  `to_email` varchar(255) NOT NULL,
  `message` mediumtext DEFAULT NULL,
  `amount` decimal(15,4) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_goods_receipt` (
  `goods_receipt_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `receipt_number` varchar(50) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `receipt_date` date NOT NULL,
  `status` enum('pending','received','partially_received','cancelled') NOT NULL DEFAULT 'pending',
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `invoice_number` varchar(50) DEFAULT NULL COMMENT 'رقم فاتورة المورد',
  `invoice_date` date DEFAULT NULL COMMENT 'تاريخ فاتورة المورد',
  `invoice_amount` decimal(15,4) DEFAULT NULL COMMENT 'قيمة فاتورة المورد',
  `currency_id` int(11) DEFAULT NULL COMMENT 'عملة الفاتورة',
  `exchange_rate` decimal(15,6) DEFAULT NULL COMMENT 'سعر الصرف عند الاستلام',
  `matching_status` enum('pending','matched','partial','mismatch') NOT NULL DEFAULT 'pending' COMMENT 'حالة المطابقة مع الفاتورة وأمر الشراء',
  `quality_check_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل يتطلب فحص جودة',
  `quality_checked_by` int(11) DEFAULT NULL COMMENT 'من قام بفحص الجودة',
  `quality_check_date` datetime DEFAULT NULL COMMENT 'تاريخ فحص الجودة',
  `quality_check_result` text DEFAULT NULL COMMENT 'نتيجة فحص الجودة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_goods_receipt_item` (
  `receipt_item_id` int(11) NOT NULL,
  `goods_receipt_id` int(11) NOT NULL,
  `po_item_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity_received` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quality_result` enum('pending','passed','failed','partial') NOT NULL DEFAULT 'pending',
  `remarks` text DEFAULT NULL,
  `invoice_unit_price` decimal(15,4) DEFAULT NULL COMMENT 'سعر الوحدة في الفاتورة',
  `cost_difference` decimal(15,4) DEFAULT NULL COMMENT 'فرق التكلفة عن أمر الشراء',
  `is_cost_updated` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم تحديث التكلفة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_governance_issue` (
  `issue_id` int(11) NOT NULL,
  `issue_type` enum('compliance','risk','legal','audit','other') NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `status` enum('open','in_progress','resolved','closed') NOT NULL DEFAULT 'open',
  `responsible_user_id` int(11) DEFAULT NULL,
  `responsible_group_id` int(11) DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `resolved_at` datetime DEFAULT NULL,
  `resolution_notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_governance_meeting` (
  `meeting_id` int(11) NOT NULL,
  `meeting_type` varchar(50) DEFAULT NULL COMMENT 'مثلاً: مجلس إدارة، لجنة مراجعة، جمعية عمومية...',
  `title` varchar(255) NOT NULL COMMENT 'عنوان الاجتماع أو موضوعه',
  `meeting_date` datetime NOT NULL COMMENT 'موعد الاجتماع',
  `location` varchar(255) DEFAULT NULL,
  `agenda` text DEFAULT NULL COMMENT 'جدول الأعمال',
  `decisions` text DEFAULT NULL COMMENT 'القرارات المتخذة في الاجتماع',
  `added_by` int(11) DEFAULT NULL COMMENT 'user_id الذي سجّل المحضر',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_gpc_codes` (
  `gpc_id` int(11) NOT NULL,
  `gpc_code` int(11) NOT NULL,
  `title` varchar(512) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_aramex_details_new` (
  `id` int(11) NOT NULL,
  `order_id` text NOT NULL,
  `tracking_num` text NOT NULL,
  `shipping_label` mediumtext NOT NULL,
  `invoice` mediumtext NOT NULL,
  `return_label` mediumtext DEFAULT NULL,
  `return_invoice` mediumtext DEFAULT NULL,
  `one` mediumtext DEFAULT NULL,
  `two` mediumtext DEFAULT NULL,
  `three` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_aramex_pickup_details` (
  `id` int(11) NOT NULL,
  `order_id` text NOT NULL,
  `status` text NOT NULL,
  `confirm_no` mediumtext NOT NULL,
  `ready_time` mediumtext NOT NULL,
  `pickup_date` mediumtext DEFAULT NULL,
  `one` mediumtext DEFAULT NULL,
  `two` mediumtext DEFAULT NULL,
  `three` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_fedex_details_new` (
  `id` int(11) NOT NULL,
  `order_id` text NOT NULL,
  `tracking_num` text NOT NULL,
  `shipping_label` mediumtext NOT NULL,
  `invoice` mediumtext NOT NULL,
  `return_label` mediumtext DEFAULT NULL,
  `return_invoice` mediumtext DEFAULT NULL,
  `one` mediumtext DEFAULT NULL,
  `two` mediumtext DEFAULT NULL,
  `three` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_hitshippo_fedex_token` (
  `id` int(11) NOT NULL,
  `token` text NOT NULL,
  `timestamp_created` text NOT NULL,
  `mode` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_import_allocation` (
  `allocation_id` int(11) NOT NULL,
  `shipment_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `allocated_amount` decimal(15,4) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_import_charge` (
  `charge_id` int(11) NOT NULL,
  `shipment_id` int(11) NOT NULL,
  `charge_type` varchar(50) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_import_shipment` (
  `shipment_id` int(11) NOT NULL,
  `reference_number` varchar(50) NOT NULL,
  `origin_country` varchar(3) DEFAULT NULL,
  `arrival_port` varchar(100) DEFAULT NULL,
  `shipment_date` date NOT NULL,
  `document_reference` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('in_progress','completed','cancelled') NOT NULL DEFAULT 'in_progress',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information` (
  `information_id` int(11) NOT NULL,
  `bottom` int(11) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information_description` (
  `information_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(64) NOT NULL,
  `description` longtext NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` varchar(255) NOT NULL,
  `meta_keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information_to_layout` (
  `information_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_information_to_store` (
  `information_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_payment` (
  `payment_id` int(11) NOT NULL,
  `schedule_id` int(11) NOT NULL,
  `payment_date` datetime NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `payment_method` enum('cash','bank_transfer','credit_card','cheque','online') NOT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `received_by` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `bank_account_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_plan` (
  `plan_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `total_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ الإجمالي للخطة',
  `down_payment` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الدفعة المقدمة',
  `remaining_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ المتبقي بعد الدفعة المقدمة',
  `installment_amount` decimal(15,4) NOT NULL COMMENT 'قيمة القسط الواحد',
  `number_of_installments` int(11) NOT NULL,
  `interest_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total_interest` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `admin_fees` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_with_interest` decimal(15,4) NOT NULL COMMENT 'المبلغ الإجمالي شاملاً الفوائد والرسوم',
  `installment_frequency` enum('monthly','biweekly','weekly') NOT NULL DEFAULT 'monthly',
  `start_date` date NOT NULL,
  `status` enum('pending','active','completed','cancelled','defaulted') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `grace_period_days` int(11) NOT NULL DEFAULT 0 COMMENT 'فترة سماح بالأيام',
  `late_payment_fee` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم التأخير',
  `late_payment_fee_percentage` decimal(5,2) NOT NULL DEFAULT 0.00,
  `guarantor_id` int(11) DEFAULT NULL COMMENT 'كفيل العميل إن وجد'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_plan_template` (
  `template_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `number_of_installments` int(11) NOT NULL,
  `interest_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'نسبة الفائدة السنوية',
  `admin_fees` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم إدارية ثابتة',
  `admin_fees_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'نسبة الرسوم الإدارية',
  `down_payment_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'نسبة الدفعة المقدمة',
  `minimum_order_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `maximum_order_amount` decimal(15,4) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_reminder` (
  `reminder_id` int(11) NOT NULL,
  `schedule_id` int(11) NOT NULL,
  `reminder_type` enum('before_due','on_due','overdue','final_notice') NOT NULL,
  `channel` enum('email','sms','whatsapp','system','call') NOT NULL,
  `scheduled_date` datetime NOT NULL,
  `sent_date` datetime DEFAULT NULL,
  `status` enum('pending','sent','failed','cancelled') NOT NULL DEFAULT 'pending',
  `message` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_installment_schedule` (
  `schedule_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `installment_number` int(11) NOT NULL,
  `due_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `principal_amount` decimal(15,4) NOT NULL COMMENT 'مبلغ أصل الدين',
  `interest_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ الفائدة',
  `status` enum('upcoming','due','paid','partial','late','defaulted') NOT NULL DEFAULT 'upcoming',
  `payment_date` datetime DEFAULT NULL,
  `payment_amount` decimal(15,4) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `late_fee_applied` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_attachment` (
  `attachment_id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `uploaded_at` datetime NOT NULL DEFAULT current_timestamp(),
  `thumbnail_path` varchar(255) DEFAULT NULL COMMENT 'مسار الصورة المصغرة إن وجدت'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_audit` (
  `audit_id` int(11) NOT NULL,
  `audit_subject` varchar(255) NOT NULL COMMENT 'موضوع التدقيق',
  `audit_type` varchar(50) DEFAULT NULL COMMENT 'نوع التدقيق: مالي, تشغيلي.. الخ',
  `description` text DEFAULT NULL COMMENT 'نطاق أو تفاصيل التدقيق',
  `auditor_user_id` int(11) NOT NULL COMMENT 'يتعيّن تلقائياً حسب المستخدم الحالي',
  `scheduled_date` date NOT NULL COMMENT 'تاريخ بدء التدقيق',
  `completion_date` date DEFAULT NULL COMMENT 'تاريخ إكمال التدقيق',
  `findings` text DEFAULT NULL COMMENT 'النتائج',
  `recommendations` text DEFAULT NULL COMMENT 'التوصيات',
  `status` enum('scheduled','in_progress','completed','cancelled') NOT NULL DEFAULT 'scheduled',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_control` (
  `control_id` int(11) NOT NULL,
  `control_name` varchar(255) NOT NULL COMMENT 'اسم الإجراء/السياسة',
  `description` text DEFAULT NULL COMMENT 'تفاصيل أو شرح للإجراء',
  `responsible_group_id` int(11) NOT NULL DEFAULT 0 COMMENT 'رقم مجموعة المستخدم المسؤولة (إدارة معينة)',
  `effective_date` date NOT NULL COMMENT 'تاريخ سريان السياسة',
  `review_date` date DEFAULT NULL COMMENT 'تاريخ آخر مراجعة',
  `status` enum('active','obsolete') NOT NULL DEFAULT 'active' COMMENT 'حالة الإجراء',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_conversation` (
  `conversation_id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `type` enum('private','group','department') NOT NULL DEFAULT 'private',
  `creator_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','archived') NOT NULL DEFAULT 'active',
  `associated_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, purchase, etc',
  `reference_id` int(11) DEFAULT NULL COMMENT 'المعرف المرتبط مثل order_id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_message` (
  `message_id` int(11) NOT NULL,
  `conversation_id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message_text` text DEFAULT NULL,
  `sent_at` datetime NOT NULL DEFAULT current_timestamp(),
  `edited_at` datetime DEFAULT NULL,
  `is_system_message` tinyint(1) NOT NULL DEFAULT 0,
  `message_type` enum('text','file','link','task','workflow','approval') NOT NULL DEFAULT 'text',
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, document, meeting',
  `reference_id` int(11) DEFAULT NULL COMMENT 'المعرف المرتبط بالمودل',
  `parent_message_id` int(11) DEFAULT NULL,
  `mentions` text DEFAULT NULL COMMENT 'قائمة المستخدمين المشار إليهم'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_internal_participant` (
  `participant_id` int(11) NOT NULL,
  `conversation_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `joined_at` datetime NOT NULL DEFAULT current_timestamp(),
  `left_at` datetime DEFAULT NULL,
  `role` enum('member','admin') NOT NULL DEFAULT 'member',
  `last_read_message_id` int(11) DEFAULT NULL,
  `notification_settings` enum('all','mentions','none') NOT NULL DEFAULT 'all'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_abc_analysis` (
  `abc_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `value_contribution` decimal(15,4) NOT NULL,
  `percentage_of_total` decimal(5,2) NOT NULL,
  `cumulative_percentage` decimal(5,2) NOT NULL,
  `abc_class` char(1) NOT NULL,
  `analysis_date` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_accounting_reconciliation` (
  `reconciliation_id` int(11) NOT NULL,
  `period_start_date` date NOT NULL,
  `period_end_date` date NOT NULL,
  `branch_id` int(11) NOT NULL,
  `status` enum('draft','in_progress','completed','approved') NOT NULL DEFAULT 'draft',
  `total_system_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_accounting_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `difference_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `completed_at` datetime DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_accounting_reconciliation_item` (
  `item_id` int(11) NOT NULL,
  `reconciliation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `system_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `system_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `accounting_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `difference_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `adjustment_journal_id` int(11) DEFAULT NULL,
  `is_adjusted` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_account_mapping` (
  `mapping_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL COMMENT 'الفرع أو المستودع',
  `product_category_id` int(11) DEFAULT NULL COMMENT 'يمكن ربط فئة محددة، أو NULL للكل',
  `inventory_account_code` bigint(20) NOT NULL COMMENT 'حساب المخزون',
  `cogs_account_code` bigint(20) NOT NULL COMMENT 'حساب تكلفة المبيعات',
  `purchase_account_code` bigint(20) NOT NULL COMMENT 'حساب المشتريات (مؤقت)',
  `inventory_adjustment_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب تسويات المخزون',
  `price_variance_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب فروق أسعار المشتريات',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_alert` (
  `alert_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `alert_type` enum('minimum','maximum','expired','slow_moving','damaged') NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `threshold` decimal(15,4) NOT NULL,
  `status` enum('active','acknowledged','resolved','ignored') NOT NULL DEFAULT 'active',
  `days_no_movement` int(11) DEFAULT NULL,
  `last_movement_date` date DEFAULT NULL,
  `recommended_action` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `acknowledged_by` int(11) DEFAULT NULL,
  `acknowledged_at` datetime DEFAULT NULL,
  `resolved_at` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_cost_history` (
  `history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `old_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `new_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `change_reason` varchar(20) NOT NULL,
  `notes` mediumtext NOT NULL,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_cost_update` (
  `update_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `old_cost` decimal(15,4) NOT NULL,
  `new_cost` decimal(15,4) NOT NULL,
  `update_date` datetime NOT NULL DEFAULT current_timestamp(),
  `source_type` enum('purchase','adjustment','import') NOT NULL,
  `source_id` int(11) NOT NULL COMMENT 'معرف المصدر',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_count` (
  `count_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `system_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `counted_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_difference` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `counted_by` int(11) NOT NULL DEFAULT 0,
  `count_date` datetime NOT NULL,
  `notes` mediumtext NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `applied_by` int(11) DEFAULT NULL,
  `applied_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_reservation` (
  `reservation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `source_type` varchar(50) NOT NULL COMMENT 'order, quotation, etc',
  `source_id` int(11) NOT NULL,
  `reservation_date` datetime NOT NULL DEFAULT current_timestamp(),
  `expiry_date` datetime DEFAULT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_role_permissions` (
  `permission_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role_type` enum('warehouse_manager','branch_manager','ecommerce_manager','store_staff','cashier') NOT NULL,
  `branch_id` int(11) DEFAULT NULL COMMENT 'الفرع المخصص للمستخدم (null = جميع الفروع)',
  `can_view_physical_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_edit_physical_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_view_virtual_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_edit_virtual_inventory` tinyint(1) NOT NULL DEFAULT 0,
  `can_view_cost` tinyint(1) NOT NULL DEFAULT 0,
  `can_edit_cost` tinyint(1) NOT NULL DEFAULT 0,
  `can_manage_bundles` tinyint(1) NOT NULL DEFAULT 0,
  `can_manage_units` tinyint(1) NOT NULL DEFAULT 0,
  `can_access_reports` tinyint(1) NOT NULL DEFAULT 0,
  `can_export_data` tinyint(1) NOT NULL DEFAULT 0,
  `restrictions` text DEFAULT NULL COMMENT 'قيود إضافية بصيغة JSON',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='صلاحيات الأدوار في نظام المخزون';

CREATE TABLE `cod_inventory_sheet` (
  `sheet_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `sheet_date` datetime NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `created_by` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  `completed_by` int(11) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_sheet_item` (
  `sheet_item_id` int(11) NOT NULL,
  `sheet_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `system_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `counted_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` mediumtext NOT NULL,
  `count_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_status_log` (
  `log_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL COMMENT 'معرف المنتج',
  `warehouse_id` int(11) NOT NULL COMMENT 'معرف المستودع',
  `unit_id` int(11) NOT NULL COMMENT 'معرف الوحدة',
  `batch_id` int(11) DEFAULT NULL COMMENT 'معرف الدفعة',
  `status_from` enum('available','reserved','maintenance','quality_check','damaged','expired','quarantine') NOT NULL,
  `status_to` enum('available','reserved','maintenance','quality_check','damaged','expired','quarantine') NOT NULL,
  `quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المنقولة',
  `reason_id` int(11) DEFAULT NULL COMMENT 'معرف السبب',
  `reason_notes` text DEFAULT NULL COMMENT 'ملاحظات السبب',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `reference_number` varchar(100) DEFAULT NULL COMMENT 'رقم المرجع',
  `created_by` int(11) NOT NULL COMMENT 'المنشئ',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_sync_rules` (
  `rule_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `sync_type` enum('auto','manual','threshold','percentage') NOT NULL DEFAULT 'manual',
  `sync_threshold` decimal(15,4) DEFAULT NULL COMMENT 'عتبة التحويل التلقائي',
  `max_virtual_ratio` decimal(5,2) DEFAULT 1.50 COMMENT 'أقصى نسبة للمخزون الوهمي من الفعلي',
  `min_physical_quantity` decimal(15,4) DEFAULT 0.0000 COMMENT 'أقل كمية فعلية مطلوبة',
  `auto_sync_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `notification_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='قواعد مزامنة المخزون الوهمي والفعلي';

CREATE TABLE `cod_inventory_transfer` (
  `transfer_id` int(11) NOT NULL,
  `transfer_number` varchar(30) NOT NULL,
  `source_branch_id` int(11) NOT NULL,
  `destination_branch_id` int(11) NOT NULL,
  `transfer_date` date NOT NULL,
  `notes` mediumtext NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `created_by` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  `completed_by` int(11) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_transfer_item` (
  `item_id` int(11) NOT NULL,
  `transfer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_turnover` (
  `analysis_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `beginning_inventory` decimal(15,4) NOT NULL,
  `ending_inventory` decimal(15,4) NOT NULL,
  `average_inventory` decimal(15,4) NOT NULL,
  `cost_of_goods_sold` decimal(15,4) NOT NULL,
  `turnover_ratio` decimal(10,2) NOT NULL,
  `days_on_hand` int(11) NOT NULL,
  `analysis_date` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_inventory_turnover_analysis` (
  `analysis_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `analysis_period` enum('daily','weekly','monthly','quarterly','yearly') NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `opening_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `closing_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `average_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_sold` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_received` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `turnover_ratio` decimal(8,4) NOT NULL DEFAULT 0.0000,
  `days_of_supply` decimal(8,2) NOT NULL DEFAULT 0.00,
  `stockout_days` int(11) NOT NULL DEFAULT 0,
  `overstock_days` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='تحليل دوران المخزون';

CREATE TABLE `cod_inventory_valuation` (
  `valuation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL DEFAULT 0,
  `valuation_date` date NOT NULL,
  `average_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `date_added` datetime NOT NULL,
  `transaction_reference_id` int(11) NOT NULL DEFAULT 0,
  `transaction_type` varchar(20) NOT NULL,
  `previous_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `previous_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `movement_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `movement_cost` decimal(15,4) NOT NULL DEFAULT 0.0000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_invoices` (
  `invoice_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `issuer_type` varchar(2) DEFAULT NULL,
  `issuer_id` varchar(50) DEFAULT NULL,
  `issuer_name` varchar(200) DEFAULT NULL,
  `issuer_country` varchar(3) DEFAULT NULL,
  `issuer_governate` varchar(100) DEFAULT NULL,
  `issuer_region_city` varchar(100) DEFAULT NULL,
  `issuer_street` varchar(200) DEFAULT NULL,
  `issuer_building_number` varchar(100) DEFAULT NULL,
  `issuer_postal_code` varchar(20) DEFAULT NULL,
  `issuer_floor` varchar(50) DEFAULT NULL,
  `issuer_room` varchar(50) DEFAULT NULL,
  `issuer_landmark` varchar(200) DEFAULT NULL,
  `issuer_additional_info` varchar(500) DEFAULT NULL,
  `receiver_type` varchar(2) DEFAULT NULL,
  `receiver_id` varchar(50) DEFAULT NULL,
  `receiver_name` varchar(200) DEFAULT NULL,
  `receiver_country` varchar(3) DEFAULT NULL,
  `receiver_governate` varchar(100) DEFAULT NULL,
  `receiver_region_city` varchar(100) DEFAULT NULL,
  `receiver_street` varchar(200) DEFAULT NULL,
  `receiver_building_number` varchar(100) DEFAULT NULL,
  `receiver_postal_code` varchar(20) DEFAULT NULL,
  `receiver_floor` varchar(50) DEFAULT NULL,
  `receiver_room` varchar(50) DEFAULT NULL,
  `receiver_landmark` varchar(200) DEFAULT NULL,
  `receiver_additional_info` varchar(500) DEFAULT NULL,
  `document_type` varchar(5) DEFAULT NULL,
  `document_version` varchar(10) DEFAULT NULL,
  `date_time_issued` datetime DEFAULT NULL,
  `taxpayer_activity_code` varchar(10) DEFAULT NULL,
  `internal_id` varchar(50) DEFAULT NULL,
  `purchase_order_reference` varchar(50) DEFAULT NULL,
  `purchase_order_description` varchar(255) DEFAULT NULL,
  `sales_order_reference` varchar(50) DEFAULT NULL,
  `sales_order_description` varchar(255) DEFAULT NULL,
  `proforma_invoice_number` varchar(50) DEFAULT NULL,
  `total_sales_amount` decimal(13,5) DEFAULT NULL,
  `total_discount_amount` decimal(13,5) DEFAULT NULL,
  `net_amount` decimal(13,5) DEFAULT NULL,
  `total_amount` decimal(13,5) DEFAULT NULL,
  `extra_discount_amount` decimal(13,5) DEFAULT NULL,
  `total_items_discount_amount` decimal(13,5) DEFAULT NULL,
  `submission_uuid` varchar(36) DEFAULT NULL,
  `status` enum('pending','submitted','accepted','rejected') DEFAULT 'pending',
  `rejection_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_journals` (
  `journal_id` int(11) NOT NULL,
  `refnum` varchar(50) DEFAULT NULL,
  `thedate` date NOT NULL,
  `description` mediumtext NOT NULL,
  `added_by` varchar(100) DEFAULT NULL,
  `last_edit_by` varchar(100) DEFAULT NULL,
  `audit_by` varchar(100) DEFAULT NULL,
  `is_cancelled` tinyint(1) NOT NULL DEFAULT 0,
  `cancelled_date` datetime DEFAULT NULL,
  `cancelled_by` varchar(100) DEFAULT NULL,
  `audited` tinyint(1) NOT NULL DEFAULT 0,
  `audit_date` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `entrytype` tinyint(1) NOT NULL DEFAULT 2 COMMENT '1 يدوي\r\n2 آلي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_journal_attachments` (
  `attachment_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_journal_entries` (
  `entry_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL,
  `account_code` bigint(20) NOT NULL,
  `is_debit` tinyint(1) NOT NULL,
  `amount` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_language` (
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `code` varchar(5) NOT NULL,
  `locale` varchar(255) NOT NULL,
  `image` varchar(64) NOT NULL,
  `directory` varchar(32) NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_layout` (
  `layout_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_layout_module` (
  `layout_module_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL,
  `code` varchar(64) NOT NULL,
  `position` varchar(14) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_layout_route` (
  `layout_route_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `route` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_leave_request` (
  `leave_request_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `leave_type_id` int(11) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('pending','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
  `reason` text DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_leave_type` (
  `leave_type_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_legal_contract` (
  `contract_id` int(11) NOT NULL,
  `contract_type` varchar(50) NOT NULL COMMENT 'نوع العقد: تأجير فرع، توريد، ...',
  `title` varchar(255) NOT NULL COMMENT 'عنوان العقد',
  `party_id` int(11) DEFAULT NULL COMMENT 'ربط مع طرف خارجي (مورد، عميل...) أو فرع',
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL COMMENT 'تاريخ الانتهاء أو null إن كان مفتوح المدة',
  `status` enum('active','expired','terminated','draft') DEFAULT 'draft',
  `value` decimal(15,2) DEFAULT 0.00 COMMENT 'قيمة العقد المالية إن وجدت',
  `description` text DEFAULT NULL COMMENT 'وصف أو شروط العقد',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_length_class` (
  `length_class_id` int(11) NOT NULL,
  `value` decimal(15,8) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_length_class_description` (
  `length_class_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(32) NOT NULL,
  `unit` varchar(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_location` (
  `location_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `address` mediumtext NOT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `geocode` varchar(32) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `open` mediumtext NOT NULL,
  `comment` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_manufacturer` (
  `manufacturer_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_manufacturer_to_store` (
  `manufacturer_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_marketing` (
  `marketing_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` mediumtext NOT NULL,
  `code` varchar(64) NOT NULL,
  `clicks` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_meeting_attendees` (
  `attendee_id` int(11) NOT NULL,
  `meeting_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT 'إذا كان الحاضر موظف بالشركة',
  `external_name` varchar(255) DEFAULT NULL COMMENT 'إذا كان الحاضر من خارج الشركة',
  `role_in_meeting` varchar(50) DEFAULT NULL COMMENT 'عضو مجلس إدارة، مستشار، ...',
  `presence_status` enum('attended','excused','absent') DEFAULT 'attended'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_message_recipient` (
  `message_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_modification` (
  `modification_id` int(11) NOT NULL,
  `extension_install_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `code` varchar(64) NOT NULL,
  `author` varchar(64) NOT NULL,
  `version` varchar(32) NOT NULL,
  `link` varchar(255) NOT NULL,
  `xml` longtext NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_module` (
  `module_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `code` varchar(32) NOT NULL,
  `setting` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_notices` (
  `notice_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `type` enum('credit','debit') NOT NULL,
  `issuer_type` varchar(2) DEFAULT NULL,
  `issuer_id` varchar(50) DEFAULT NULL,
  `issuer_name` varchar(200) DEFAULT NULL,
  `receiver_type` varchar(2) DEFAULT NULL,
  `receiver_id` varchar(50) DEFAULT NULL,
  `receiver_name` varchar(200) DEFAULT NULL,
  `document_type` varchar(5) DEFAULT NULL,
  `document_version` varchar(10) DEFAULT NULL,
  `date_time_issued` datetime DEFAULT NULL,
  `taxpayer_activity_code` varchar(10) DEFAULT NULL,
  `internal_id` varchar(50) DEFAULT NULL,
  `references` text DEFAULT NULL,
  `total_amount` decimal(13,5) DEFAULT NULL,
  `total_sales_amount` decimal(13,5) DEFAULT NULL,
  `total_discount_amount` decimal(13,5) DEFAULT NULL,
  `net_amount` decimal(13,5) DEFAULT NULL,
  `submission_uuid` varchar(36) DEFAULT NULL,
  `status` enum('pending','submitted','accepted','rejected') DEFAULT 'pending',
  `rejection_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_notification_automation` (
  `rule_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `trigger_event` varchar(100) NOT NULL COMMENT 'الحدث المحفز',
  `trigger_conditions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'شروط التشغيل' CHECK (json_valid(`trigger_conditions`)),
  `notification_template_id` int(11) NOT NULL,
  `target_type` enum('specific_users','user_groups','dynamic','all_users') NOT NULL DEFAULT 'specific_users',
  `target_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'إعدادات المستهدفين' CHECK (json_valid(`target_config`)),
  `delay_minutes` int(11) NOT NULL DEFAULT 0 COMMENT 'تأخير الإرسال بالدقائق',
  `status` enum('active','inactive','testing') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول قواعد أتمتة الإشعارات';

CREATE TABLE `cod_notification_automation_log` (
  `log_id` int(11) NOT NULL,
  `rule_id` int(11) NOT NULL,
  `event_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'بيانات الحدث المحفز' CHECK (json_valid(`event_data`)),
  `notifications_sent` int(11) NOT NULL DEFAULT 0,
  `execution_status` enum('success','failed','partial') NOT NULL DEFAULT 'success',
  `error_message` text DEFAULT NULL,
  `executed_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول سجل تنفيذ قواعد الأتمتة';

CREATE TABLE `cod_notification_template` (
  `template_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` enum('info','success','warning','error','approval','reminder','system') NOT NULL DEFAULT 'info',
  `priority` enum('low','normal','high','urgent','critical') NOT NULL DEFAULT 'normal',
  `category` varchar(100) NOT NULL DEFAULT 'general',
  `channels` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'قنوات الإرسال المدعومة' CHECK (json_valid(`channels`)),
  `variables` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'المتغيرات المتاحة في القالب' CHECK (json_valid(`variables`)),
  `email_subject` varchar(255) DEFAULT NULL,
  `email_content` text DEFAULT NULL,
  `sms_content` varchar(500) DEFAULT NULL,
  `status` enum('draft','active','inactive') NOT NULL DEFAULT 'draft',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'قالب نظامي لا يمكن حذفه',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول قوالب الإشعارات';

CREATE TABLE `cod_notification_user` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option` (
  `option_id` int(11) NOT NULL,
  `type` varchar(32) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option_description` (
  `option_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option_value` (
  `option_value_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_option_value_description` (
  `option_value_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order` (
  `order_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `invoice_no` int(11) NOT NULL DEFAULT 0,
  `invoice_prefix` varchar(26) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `store_name` varchar(64) NOT NULL,
  `store_url` varchar(255) NOT NULL,
  `customer_id` int(11) NOT NULL DEFAULT 0,
  `customer_group_id` int(11) NOT NULL DEFAULT 0,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `custom_field` mediumtext NOT NULL,
  `payment_firstname` varchar(255) NOT NULL,
  `payment_lastname` varchar(32) DEFAULT NULL,
  `payment_company` varchar(60) NOT NULL,
  `payment_address_1` varchar(128) NOT NULL,
  `payment_address_2` varchar(128) NOT NULL,
  `payment_city` varchar(128) NOT NULL,
  `payment_postcode` varchar(10) NOT NULL,
  `payment_country` varchar(128) NOT NULL,
  `payment_country_id` int(11) NOT NULL,
  `payment_zone` varchar(128) NOT NULL,
  `payment_zone_id` int(11) NOT NULL,
  `payment_address_format` mediumtext NOT NULL,
  `payment_custom_field` mediumtext NOT NULL,
  `payment_method` varchar(128) NOT NULL,
  `payment_code` varchar(128) NOT NULL,
  `shipping_firstname` varchar(255) NOT NULL,
  `shipping_lastname` varchar(32) DEFAULT NULL,
  `shipping_company` varchar(40) NOT NULL,
  `shipping_address_1` varchar(128) NOT NULL,
  `shipping_address_2` varchar(128) NOT NULL,
  `shipping_city` varchar(128) NOT NULL,
  `shipping_postcode` varchar(10) NOT NULL,
  `shipping_country` varchar(128) NOT NULL,
  `shipping_country_id` int(11) NOT NULL,
  `shipping_zone` varchar(128) NOT NULL,
  `shipping_zone_id` int(11) NOT NULL,
  `shipping_address_format` mediumtext NOT NULL,
  `shipping_custom_field` mediumtext NOT NULL,
  `shipping_method` varchar(128) NOT NULL,
  `shipping_code` varchar(128) NOT NULL,
  `comment` mediumtext NOT NULL,
  `total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `order_status_id` int(11) NOT NULL DEFAULT 0,
  `eta_status` enum('pending','queued','submitted','processing','approved','rejected','cancelled','failed') DEFAULT NULL,
  `eta_document_id` int(11) DEFAULT NULL,
  `eta_uuid` varchar(50) DEFAULT NULL,
  `eta_long_id` varchar(100) DEFAULT NULL,
  `eta_submission_uuid` varchar(50) DEFAULT NULL,
  `eta_submitted_at` datetime DEFAULT NULL,
  `eta_approved_at` datetime DEFAULT NULL,
  `eta_updated_at` datetime DEFAULT NULL,
  `affiliate_id` int(11) NOT NULL,
  `commission` decimal(15,4) NOT NULL,
  `marketing_id` int(11) NOT NULL,
  `tracking` varchar(64) NOT NULL,
  `language_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `currency_code` varchar(3) NOT NULL,
  `currency_value` decimal(15,8) NOT NULL DEFAULT 1.00000000,
  `ip` varchar(40) NOT NULL,
  `forwarded_ip` varchar(40) NOT NULL,
  `user_agent` varchar(255) NOT NULL,
  `accept_language` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  `order_posuser_id` int(11) NOT NULL,
  `order_posuser_name` varchar(255) NOT NULL,
  `shift_id` int(11) NOT NULL DEFAULT 0,
  `rin_customer` varchar(255) DEFAULT NULL,
  `qr_code` varchar(255) DEFAULT NULL,
  `uuid` varchar(255) DEFAULT NULL,
  `longId` varchar(255) DEFAULT NULL,
  `hashKey` varchar(255) DEFAULT NULL,
  `submissionId` varchar(255) DEFAULT NULL,
  `fbcapidyad_ordflag` tinyint(1) DEFAULT 0,
  `confirmation_status` enum('Canceled','Pending','Confirmed') NOT NULL DEFAULT 'Pending',
  `confirmation_token` varchar(64) DEFAULT NULL,
  `installment_plan_id` int(11) DEFAULT NULL,
  `eta_auto_submit` tinyint(1) DEFAULT 1 COMMENT 'إرسال تلقائي لـ ETA'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_cogs` (
  `order_cogs_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `total_cogs` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي',
  `calculation_method` varchar(20) NOT NULL DEFAULT 'weighted_average' COMMENT 'طريقة حساب التكلفة',
  `inventory_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب المخزون المستخدم',
  `cogs_account_code` bigint(20) DEFAULT NULL COMMENT 'حساب تكلفة المبيعات المستخدم',
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_history` (
  `order_history_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `order_status_id` int(11) NOT NULL,
  `notify` tinyint(1) NOT NULL DEFAULT 0,
  `comment` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_option` (
  `order_option_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `order_product_id` int(11) NOT NULL,
  `product_option_id` int(11) NOT NULL,
  `product_option_value_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `type` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_product` (
  `order_product_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL DEFAULT 37,
  `name` varchar(255) NOT NULL,
  `model` varchar(64) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `reward` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_shipment` (
  `order_shipment_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  `shipping_courier_id` varchar(255) NOT NULL DEFAULT '',
  `tracking_number` varchar(255) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_shipment_history` (
  `history_id` int(11) NOT NULL,
  `order_shipment_id` int(11) NOT NULL,
  `status` varchar(50) NOT NULL COMMENT 'مثال: تم الاستلام من الفرع، في الطريق، إلخ',
  `location` varchar(255) DEFAULT NULL COMMENT 'موقع الشحنة أو ملاحظات',
  `updated_by` int(11) NOT NULL COMMENT 'user_id الموظف الذي حدّث الحالة',
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_status` (
  `order_status_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_total` (
  `order_total_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `code` varchar(32) NOT NULL,
  `title` varchar(255) NOT NULL,
  `value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_order_voucher` (
  `order_voucher_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `voucher_id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `code` varchar(10) NOT NULL,
  `from_name` varchar(64) NOT NULL,
  `from_email` varchar(96) NOT NULL,
  `to_name` varchar(64) NOT NULL,
  `to_email` varchar(96) NOT NULL,
  `voucher_theme_id` int(11) NOT NULL,
  `message` mediumtext NOT NULL,
  `amount` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_paymentlinks` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `paymentlink` mediumtext NOT NULL,
  `order_desc` mediumtext DEFAULT NULL,
  `order_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `order_currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_gateway` (
  `gateway_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive','testing') NOT NULL DEFAULT 'inactive',
  `supported_methods` varchar(255) NOT NULL COMMENT 'طرق الدفع المدعومة مفصولة بفواصل',
  `currencies` varchar(100) NOT NULL COMMENT 'العملات المدعومة مفصولة بفواصل',
  `logo` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `supports_refunds` tinyint(1) NOT NULL DEFAULT 0,
  `supports_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `fixed_fee` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `percentage_fee` decimal(5,2) NOT NULL DEFAULT 0.00,
  `minimum_fee` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `support_contact` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_gateway_config` (
  `config_id` int(11) NOT NULL,
  `gateway_id` int(11) NOT NULL,
  `key` varchar(100) NOT NULL,
  `value` text NOT NULL,
  `is_sensitive` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل البيانات حساسة (مثل API keys)',
  `environment` enum('live','test','both') NOT NULL DEFAULT 'both',
  `last_updated` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_invoice` (
  `payment_invoice_id` int(11) NOT NULL,
  `payment_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `amount_paid` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_settlement` (
  `settlement_id` int(11) NOT NULL,
  `gateway_id` int(11) NOT NULL,
  `settlement_date` date NOT NULL,
  `settlement_reference` varchar(100) DEFAULT NULL,
  `amount` decimal(15,4) NOT NULL,
  `fee_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_amount` decimal(15,4) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `status` enum('pending','reconciled','disputed') NOT NULL DEFAULT 'pending',
  `bank_account_id` int(11) DEFAULT NULL COMMENT 'الحساب البنكي المحول إليه',
  `bank_transaction_id` int(11) DEFAULT NULL COMMENT 'معرف المعاملة البنكية',
  `transaction_count` int(11) NOT NULL DEFAULT 0 COMMENT 'عدد المعاملات في التسوية',
  `settlement_file` varchar(255) DEFAULT NULL COMMENT 'ملف التسوية إن وجد',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `reconciled_by` int(11) DEFAULT NULL,
  `reconciled_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_settlement_transaction` (
  `settlement_transaction_id` int(11) NOT NULL,
  `settlement_id` int(11) NOT NULL,
  `transaction_id` int(11) NOT NULL,
  `status` enum('matched','unmatched','disputed') NOT NULL DEFAULT 'matched',
  `discrepancy_amount` decimal(15,4) DEFAULT NULL COMMENT 'قيمة التباين إن وجد',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payment_transaction` (
  `transaction_id` int(11) NOT NULL,
  `gateway_id` int(11) NOT NULL,
  `gateway_transaction_id` varchar(100) DEFAULT NULL COMMENT 'معرف المعاملة لدى البوابة',
  `order_id` int(11) DEFAULT NULL,
  `installment_payment_id` int(11) DEFAULT NULL,
  `customer_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `fee_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم البوابة',
  `net_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ الصافي = المبلغ - الرسوم',
  `transaction_type` enum('payment','refund','authorization','capture','void') NOT NULL,
  `payment_method` varchar(50) NOT NULL COMMENT 'طريقة الدفع المستخدمة',
  `status` enum('pending','completed','failed','refunded','partial_refund','voided') NOT NULL DEFAULT 'pending',
  `error_code` varchar(50) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `request_data` mediumtext DEFAULT NULL COMMENT 'بيانات الطلب المرسلة للبوابة',
  `response_data` mediumtext DEFAULT NULL COMMENT 'رد البوابة',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `reference_transaction_id` int(11) DEFAULT NULL COMMENT 'في حالة الاسترداد أو التقاط المعاملة المرجعية',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `settlement_status` enum('pending','settled','failed') NOT NULL DEFAULT 'pending',
  `settlement_date` date DEFAULT NULL,
  `bank_transfer_id` int(11) DEFAULT NULL COMMENT 'المعرف المرجعي للتحويل البنكي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payroll_entry` (
  `payroll_entry_id` int(11) NOT NULL,
  `payroll_period_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `base_salary` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `allowances` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `deductions` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_salary` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `payment_status` enum('pending','paid') NOT NULL DEFAULT 'pending',
  `payment_date` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_payroll_period` (
  `payroll_period_id` int(11) NOT NULL,
  `period_name` varchar(50) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('open','closed') NOT NULL DEFAULT 'open',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_closed` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_performance_criteria` (
  `criteria_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_performance_review` (
  `review_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `review_date` date NOT NULL,
  `reviewer_id` int(11) NOT NULL,
  `overall_score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `comments` text DEFAULT NULL,
  `status` enum('pending','completed') NOT NULL DEFAULT 'pending',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_performance_review_criteria` (
  `review_criteria_id` int(11) NOT NULL,
  `review_id` int(11) NOT NULL,
  `criteria_id` int(11) NOT NULL,
  `score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `comments` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_permission` (
  `permission_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `key` varchar(255) NOT NULL,
  `type` enum('access','modify','other') NOT NULL DEFAULT 'other',
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_cash_handover` (
  `handover_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `from_user_id` int(11) NOT NULL,
  `to_user_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `handover_time` datetime NOT NULL,
  `notes` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_session` (
  `session_id` int(11) NOT NULL,
  `session_number` varchar(50) NOT NULL,
  `terminal_id` int(11) NOT NULL,
  `cashier_id` int(11) NOT NULL,
  `shift_id` int(11) DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','closed','suspended','cancelled') NOT NULL DEFAULT 'active',
  `opening_cash` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `closing_cash` decimal(15,4) DEFAULT NULL,
  `expected_cash` decimal(15,4) DEFAULT NULL,
  `cash_difference` decimal(15,4) DEFAULT NULL,
  `total_sales` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_transactions` int(11) NOT NULL DEFAULT 0,
  `total_items` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_shift` (
  `shift_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `terminal_id` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `starting_cash` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `ending_cash` decimal(15,4) DEFAULT NULL,
  `expected_cash` decimal(15,4) DEFAULT NULL,
  `cash_difference` decimal(15,4) DEFAULT NULL,
  `status` enum('active','closed','balanced') NOT NULL DEFAULT 'active',
  `notes` mediumtext DEFAULT NULL,
  `total_sales` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_cogs` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `last_order_id` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_terminal` (
  `terminal_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `printer_type` varchar(32) DEFAULT NULL,
  `printer_name` varchar(64) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pos_transaction` (
  `transaction_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `type` enum('sale','cash_in','cash_out','refund','void','drop') NOT NULL,
  `payment_method` varchar(32) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `reference` varchar(64) DEFAULT NULL,
  `notes` mediumtext DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product` (
  `product_id` int(11) NOT NULL,
  `eta_item_code` varchar(100) DEFAULT NULL,
  `eta_item_type` varchar(10) DEFAULT 'GS1',
  `eta_unit_type` varchar(10) DEFAULT 'EA',
  `gpc_code` varchar(20) DEFAULT NULL,
  `egs_code` varchar(20) DEFAULT NULL,
  `eta_tax_type` varchar(10) DEFAULT 'T1',
  `eta_tax_subtype` varchar(10) DEFAULT 'V009',
  `model` varchar(64) NOT NULL,
  `type` enum('product','consu','service') NOT NULL DEFAULT 'product',
  `sku` varchar(64) NOT NULL,
  `upc` varchar(12) NOT NULL,
  `ean` varchar(14) NOT NULL,
  `jan` varchar(13) NOT NULL,
  `isbn` varchar(17) NOT NULL,
  `mpn` varchar(64) NOT NULL,
  `location` varchar(128) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `average_cost` decimal(15,4) DEFAULT 0.0000,
  `stock_status_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `manufacturer_id` int(11) NOT NULL,
  `shipping` tinyint(1) NOT NULL DEFAULT 1,
  `price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `points` int(11) NOT NULL DEFAULT 0,
  `tax_class_id` int(11) NOT NULL,
  `date_available` date NOT NULL DEFAULT '1970-01-01',
  `weight` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `weight_class_id` int(11) NOT NULL DEFAULT 0,
  `length` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `width` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `height` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `length_class_id` int(11) NOT NULL DEFAULT 0,
  `subtract` tinyint(1) NOT NULL DEFAULT 1,
  `minimum` int(11) NOT NULL DEFAULT 1,
  `track_expiry` tinyint(1) NOT NULL DEFAULT 0,
  `expiry_alert_days` int(11) DEFAULT NULL,
  `track_batch` tinyint(1) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `viewed` int(11) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_attribute` (
  `product_id` int(11) NOT NULL,
  `attribute_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `text` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_barcode` (
  `product_barcode_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `product_option_id` int(11) DEFAULT NULL,
  `product_option_value_id` int(11) DEFAULT NULL,
  `barcode` varchar(255) NOT NULL,
  `type` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_batch` (
  `batch_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `batch_number` varchar(100) NOT NULL,
  `manufacturing_date` date DEFAULT NULL,
  `expiry_date` date NOT NULL,
  `initial_quantity` decimal(15,4) NOT NULL,
  `remaining_quantity` decimal(15,4) NOT NULL,
  `cost` decimal(15,4) NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_bundle` (
  `bundle_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `discount_type` enum('percentage','fixed','product') NOT NULL,
  `discount_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `min_quantity` int(11) NOT NULL DEFAULT 1 COMMENT 'أقل كمية للباقة',
  `max_quantity` int(11) DEFAULT NULL COMMENT 'أقصى كمية للباقة',
  `valid_from` datetime DEFAULT NULL COMMENT 'تاريخ بداية صلاحية الباقة',
  `valid_to` datetime DEFAULT NULL COMMENT 'تاريخ انتهاء صلاحية الباقة',
  `usage_limit` int(11) DEFAULT NULL COMMENT 'حد الاستخدام الإجمالي',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT 'عدد مرات الاستخدام',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT 'أولوية العرض'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_bundle_item` (
  `bundle_item_id` int(11) NOT NULL,
  `bundle_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_id` int(11) NOT NULL,
  `is_free` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_description` (
  `product_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `tag` mediumtext NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` varchar(255) NOT NULL,
  `meta_keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_dynamic_pricing` (
  `product_id` int(11) NOT NULL,
  `rule_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_egs` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `egs_code` varchar(60) NOT NULL,
  `gpc_code` varchar(30) DEFAULT NULL,
  `eta_status` varchar(100) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_filter` (
  `product_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_image` (
  `product_image_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_inventory` (
  `product_inventory_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_available` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `is_consignment` tinyint(1) NOT NULL DEFAULT 0,
  `consignment_supplier_id` int(11) DEFAULT NULL,
  `average_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `reserved_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية المحجوزة للطلبات',
  `last_sync_at` datetime DEFAULT NULL COMMENT 'آخر مزامنة بين الوهمي والفعلي',
  `sync_status` enum('synced','pending','error') NOT NULL DEFAULT 'synced',
  `auto_sync_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `quantity_maintenance` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مخزون الصيانة',
  `quantity_quality_check` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'قيد الفحص',
  `quantity_damaged` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'تالف',
  `quantity_expired` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'منتهي الصلاحية',
  `quantity_quarantine` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'حجر صحي',
  `last_status_update` datetime DEFAULT NULL COMMENT 'آخر تحديث للحالة',
  `status_updated_by` int(11) DEFAULT NULL COMMENT 'محدث الحالة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_inventory_history` (
  `history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `transaction_date` datetime NOT NULL,
  `transaction_type` varchar(20) NOT NULL,
  `reference_id` int(11) NOT NULL DEFAULT 0,
  `reference_type` varchar(30) NOT NULL DEFAULT '',
  `quantity_before` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_after` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `quantity_change` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cost_before` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cost_after` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `created_by` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_movement` (
  `product_movement_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `product_id` int(11) NOT NULL,
  `type` enum('purchase','sale','adjustment','transfer','consignment','import','receipt') NOT NULL,
  `movement_reference_type` varchar(50) DEFAULT NULL,
  `movement_reference_id` int(11) DEFAULT NULL,
  `source_document_type` varchar(50) DEFAULT NULL COMMENT 'نوع المستند المصدر (فاتورة، أمر شراء، إلخ)',
  `source_document_id` int(11) DEFAULT NULL COMMENT 'معرف المستند المصدر',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `quantity` decimal(15,4) NOT NULL,
  `unit_cost` decimal(15,4) DEFAULT NULL,
  `unit_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL DEFAULT 0,
  `reference` varchar(50) DEFAULT NULL,
  `old_average_cost` decimal(15,4) DEFAULT NULL,
  `new_average_cost` decimal(15,4) DEFAULT NULL,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `cost_before_movement` decimal(15,4) DEFAULT NULL COMMENT 'التكلفة قبل الحركة',
  `cost_after_movement` decimal(15,4) DEFAULT NULL COMMENT 'التكلفة بعد الحركة',
  `movement_value` decimal(15,4) DEFAULT NULL COMMENT 'قيمة الحركة بالتكلفة',
  `effect_on_cost` enum('increase','decrease','no_change') DEFAULT NULL,
  `detailed_calculation` text DEFAULT NULL,
  `related_costs` decimal(15,4) DEFAULT NULL,
  `is_cost_finalized` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_option` (
  `product_option_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `value` mediumtext NOT NULL,
  `required` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_option_value` (
  `product_option_value_id` int(11) NOT NULL,
  `product_option_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `option_value_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `subtract` tinyint(1) NOT NULL,
  `price` decimal(15,4) NOT NULL,
  `price_prefix` varchar(1) NOT NULL,
  `points` int(11) NOT NULL,
  `points_prefix` varchar(1) NOT NULL,
  `weight` decimal(15,8) NOT NULL,
  `weight_prefix` varchar(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_price_history` (
  `history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `old_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `new_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `change_date` datetime NOT NULL DEFAULT current_timestamp(),
  `change_type` enum('manual','cost_update','import','system') NOT NULL DEFAULT 'manual',
  `changed_by` int(11) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_pricing` (
  `product_pricing_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `base_price` decimal(15,2) NOT NULL DEFAULT 0.00,
  `special_price` decimal(15,2) DEFAULT NULL,
  `wholesale_price` decimal(15,2) DEFAULT NULL,
  `half_wholesale_price` decimal(15,2) DEFAULT NULL,
  `last_updated` datetime DEFAULT NULL,
  `custom_price` decimal(15,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_quantity_discounts` (
  `discount_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('buy_x_get_y','buy_x_get_discount') NOT NULL,
  `buy_quantity` int(11) NOT NULL,
  `get_quantity` int(11) NOT NULL,
  `discount_type` enum('percentage','fixed') NOT NULL,
  `discount_value` decimal(15,4) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `unit_id` int(11) DEFAULT 37,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `notes` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_recommendation` (
  `recommendation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `related_product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `customer_group_id` int(11) DEFAULT NULL,
  `discount_type` enum('percentage','fixed') DEFAULT NULL,
  `discount_value` decimal(15,4) DEFAULT NULL,
  `type` enum('upsell','cross_sell') NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_recurring` (
  `product_id` int(11) NOT NULL,
  `recurring_id` int(11) NOT NULL,
  `customer_group_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_related` (
  `product_id` int(11) NOT NULL,
  `related_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_reward` (
  `product_reward_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL DEFAULT 0,
  `customer_group_id` int(11) NOT NULL DEFAULT 0,
  `points` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_to_category` (
  `product_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_to_layout` (
  `product_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `layout_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_to_store` (
  `product_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_product_unit` (
  `product_unit_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `unit_type` enum('base','additional') NOT NULL DEFAULT 'base',
  `conversion_factor` decimal(15,4) NOT NULL DEFAULT 1.0000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_pt_transactions` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `payment_method` varchar(32) NOT NULL,
  `transaction_ref` varchar(64) NOT NULL,
  `parent_ref` varchar(64) DEFAULT NULL,
  `transaction_type` varchar(32) NOT NULL,
  `transaction_status` tinyint(1) NOT NULL,
  `transaction_amount` decimal(15,4) NOT NULL,
  `transaction_currency` varchar(8) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_document` (
  `document_id` int(11) NOT NULL,
  `reference_type` enum('requisition','rfq','purchase_order','goods_receipt','supplier_invoice','vendor_payment','purchase_return') NOT NULL,
  `reference_id` int(11) NOT NULL,
  `document_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'حجم الملف بالبايت',
  `document_type` varchar(100) DEFAULT NULL COMMENT 'نوع محتوى الملف',
  `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل الملف متاح للعامة',
  `uploaded_by` int(11) NOT NULL,
  `upload_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_log` (
  `log_id` int(11) NOT NULL,
  `reference_id` int(11) NOT NULL,
  `reference_type` enum('requisition','quote','purchase_order','goods_receipt','invoice','payment') NOT NULL,
  `action` varchar(255) NOT NULL,
  `user_id` int(11) NOT NULL,
  `details` text DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_matching` (
  `matching_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL COMMENT 'Purchase Order ID',
  `receipt_id` int(11) DEFAULT NULL COMMENT 'Goods Receipt ID',
  `invoice_id` int(11) DEFAULT NULL COMMENT 'Supplier Invoice ID',
  `status` enum('pending','matched','partial','mismatch') NOT NULL DEFAULT 'pending',
  `matched_by` int(11) DEFAULT NULL COMMENT 'User who performed matching',
  `matched_at` datetime DEFAULT NULL COMMENT 'Matching date/time',
  `notes` text DEFAULT NULL COMMENT 'Matching notes',
  `total_variance_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل يتطلب موافقة على المطابقة',
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_matching_item` (
  `matching_item_id` int(11) NOT NULL,
  `matching_id` int(11) NOT NULL,
  `po_item_id` int(11) NOT NULL COMMENT 'PO Item ID',
  `receipt_item_id` int(11) DEFAULT NULL COMMENT 'Receipt Item ID',
  `invoice_item_id` int(11) DEFAULT NULL COMMENT 'Invoice Item ID',
  `quantity_ordered` decimal(15,4) NOT NULL COMMENT 'Ordered quantity',
  `quantity_received` decimal(15,4) DEFAULT NULL COMMENT 'Received quantity',
  `quantity_invoiced` decimal(15,4) DEFAULT NULL COMMENT 'Invoiced quantity',
  `unit_price_ordered` decimal(15,4) NOT NULL COMMENT 'Price in PO',
  `unit_price_invoiced` decimal(15,4) DEFAULT NULL COMMENT 'Price in Invoice',
  `status` enum('pending','matched','mismatch') NOT NULL DEFAULT 'pending',
  `variance_amount` decimal(15,4) DEFAULT NULL COMMENT 'Price variance amount',
  `variance_notes` text DEFAULT NULL COMMENT 'Variance explanation',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order` (
  `po_id` int(11) NOT NULL,
  `po_number` varchar(50) NOT NULL,
  `quotation_id` int(11) DEFAULT NULL,
  `supplier_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` enum('draft','pending_review','approved','rejected','cancelled','completed') NOT NULL DEFAULT 'draft',
  `order_date` date NOT NULL,
  `expected_delivery_date` date DEFAULT NULL,
  `subtotal` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` int(11) NOT NULL,
  `source_type` enum('direct','requisition','quotation') NOT NULL DEFAULT 'direct' COMMENT 'مصدر أمر الشراء',
  `source_id` int(11) DEFAULT NULL COMMENT 'معرف المصدر (رقم الطلب أو العرض)',
  `payment_terms` text DEFAULT NULL COMMENT 'شروط الدفع',
  `delivery_terms` text DEFAULT NULL COMMENT 'شروط التسليم',
  `currency_id` int(11) NOT NULL DEFAULT 1 COMMENT 'العملة',
  `exchange_rate` decimal(15,6) NOT NULL DEFAULT 1.000000 COMMENT 'سعر الصرف',
  `is_cost_updated` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم تحديث التكلفة',
  `financial_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تمت الموافقة المالية',
  `financial_approved_by` int(11) DEFAULT NULL COMMENT 'من اعتمد مالياً',
  `financial_approval_date` datetime DEFAULT NULL COMMENT 'تاريخ الاعتماد المالي',
  `budget_allocated` decimal(15,4) DEFAULT NULL COMMENT 'المبلغ المخصص من الموازنة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order_history` (
  `history_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order_item` (
  `po_item_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(15,4) NOT NULL,
  `description` text DEFAULT NULL,
  `original_unit_price` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'السعر الأصلي بعملة المورد',
  `exchange_rate` decimal(15,6) NOT NULL DEFAULT 1.000000 COMMENT 'سعر الصرف عند الإنشاء',
  `is_received` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم الاستلام',
  `received_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية المستلمة',
  `source_item_id` int(11) DEFAULT NULL COMMENT 'معرف البند المصدر (من الطلب أو العرض)',
  `average_cost` decimal(15,4) DEFAULT NULL COMMENT 'متوسط التكلفة المحسوب',
  `original_cost` decimal(15,4) DEFAULT NULL COMMENT 'التكلفة الأصلية قبل التعديل',
  `cost_updated_at` datetime DEFAULT NULL COMMENT 'تاريخ تحديث التكلفة',
  `cost_updated_by` int(11) DEFAULT NULL COMMENT 'من قام بتحديث التكلفة',
  `budget_line_id` int(11) DEFAULT NULL COMMENT 'بند الموازنة المرتبط'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_order_tracking` (
  `tracking_id` int(11) NOT NULL,
  `po_id` int(11) NOT NULL,
  `status_change` enum('created','sent_to_vendor','confirmed_by_vendor','partially_received','fully_received','cancelled','closed') NOT NULL,
  `status_date` datetime NOT NULL DEFAULT current_timestamp(),
  `expected_delivery_date` date DEFAULT NULL,
  `actual_delivery_date` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_price_variance` (
  `variance_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `receipt_id` int(11) DEFAULT NULL COMMENT 'معرف استلام البضاعة',
  `invoice_id` int(11) DEFAULT NULL COMMENT 'معرف فاتورة المورد',
  `po_id` int(11) DEFAULT NULL COMMENT 'معرف أمر الشراء',
  `receipt_price` decimal(15,4) NOT NULL COMMENT 'سعر الاستلام الأولي',
  `invoice_price` decimal(15,4) NOT NULL COMMENT 'سعر الفاتورة النهائي',
  `quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المستلمة',
  `remaining_quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المتبقية عند وصول الفاتورة',
  `variance_amount` decimal(15,4) NOT NULL COMMENT 'مبلغ الفرق الإجمالي',
  `adjustment_to_inventory` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ تعديل المخزون',
  `adjustment_to_cogs` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ تعديل تكلفة المبيعات',
  `adjustment_date` datetime NOT NULL,
  `adjustment_type` enum('cost_update','variance_account') NOT NULL COMMENT 'نوع التعديل',
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_quotation` (
  `quotation_id` int(11) NOT NULL,
  `quotation_number` varchar(50) NOT NULL,
  `requisition_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `total_amount` decimal(15,4) NOT NULL,
  `is_expired` tinyint(1) NOT NULL DEFAULT 0,
  `exchange_rate` decimal(15,6) NOT NULL DEFAULT 1.000000,
  `status` enum('draft','pending','approved','rejected','converted') NOT NULL DEFAULT 'draft',
  `rejection_reason` text DEFAULT NULL,
  `validity_date` date DEFAULT NULL,
  `delivery_terms` text DEFAULT NULL,
  `payment_terms` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `comparison_notes` text DEFAULT NULL,
  `technical_approval` tinyint(1) NOT NULL DEFAULT 0,
  `technical_approved_by` int(11) DEFAULT NULL,
  `technical_approval_date` datetime DEFAULT NULL,
  `financial_approval` tinyint(1) NOT NULL DEFAULT 0,
  `financial_approved_by` int(11) DEFAULT NULL,
  `financial_approval_date` datetime DEFAULT NULL,
  `attachment_count` int(11) NOT NULL DEFAULT 0,
  `is_best_offer` tinyint(1) NOT NULL DEFAULT 0,
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `tax_included` tinyint(1) NOT NULL DEFAULT 0,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_type` varchar(10) NOT NULL DEFAULT 'fixed',
  `has_discount` tinyint(1) NOT NULL DEFAULT 0,
  `discount_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_quotation_history` (
  `history_id` int(11) NOT NULL,
  `quotation_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_quotation_item` (
  `quotation_item_id` int(11) NOT NULL,
  `quotation_id` int(11) NOT NULL,
  `requisition_item_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `alternative_product_id` int(11) DEFAULT NULL,
  `is_alternative` tinyint(1) NOT NULL DEFAULT 0,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `unit_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `line_total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `discount_type` enum('percentage','fixed') NOT NULL DEFAULT 'fixed',
  `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `delivery_time` varchar(50) DEFAULT NULL,
  `warranty_period` varchar(50) DEFAULT NULL,
  `is_best_price` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_requisition` (
  `requisition_id` int(11) NOT NULL,
  `req_number` varchar(50) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `user_group_id` int(11) NOT NULL,
  `status` enum('draft','pending','approved','rejected','cancelled','processing','completed') NOT NULL DEFAULT 'draft',
  `rejection_reason` text DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `required_date` date DEFAULT NULL COMMENT 'التاريخ المتوقع للاستلام',
  `notes` mediumtext DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `date_modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_requisition_history` (
  `history_id` int(11) NOT NULL,
  `requisition_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_requisition_item` (
  `requisition_item_id` int(11) NOT NULL,
  `requisition_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_return` (
  `return_id` int(11) NOT NULL,
  `return_number` varchar(50) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `purchase_order_id` int(11) DEFAULT NULL,
  `goods_receipt_id` int(11) DEFAULT NULL,
  `return_date` date NOT NULL,
  `status` enum('pending','approved','completed','cancelled') NOT NULL DEFAULT 'pending',
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `journal_ref` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_return_history` (
  `history_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_purchase_return_item` (
  `return_item_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `price` decimal(15,4) NOT NULL,
  `total` decimal(15,4) NOT NULL,
  `reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quality_inspection` (
  `inspection_id` int(11) NOT NULL,
  `inspection_number` varchar(50) NOT NULL,
  `receipt_id` int(11) NOT NULL,
  `inspector_id` int(11) NOT NULL,
  `status` enum('pending','passed','failed','partially_passed') NOT NULL DEFAULT 'pending',
  `inspection_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quality_inspection_history` (
  `history_id` int(11) NOT NULL,
  `inspection_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quality_inspection_result` (
  `result_id` int(11) NOT NULL,
  `goods_receipt_id` int(11) NOT NULL,
  `receipt_item_id` int(11) NOT NULL,
  `checked_by` int(11) NOT NULL,
  `check_date` datetime NOT NULL,
  `result` enum('passed','failed') NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_queue_jobs` (
  `id` int(11) NOT NULL,
  `job` mediumtext NOT NULL,
  `status` enum('pending','processing','done','failed') NOT NULL DEFAULT 'pending',
  `attempts` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_quotation_signatures` (
  `signature_id` int(11) NOT NULL,
  `reference_type` enum('quotation','comparison','purchase_order') NOT NULL,
  `reference_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `position` varchar(100) NOT NULL,
  `signature_date` datetime NOT NULL DEFAULT current_timestamp(),
  `signature_image` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `order_num` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_receipts` (
  `receipt_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `seller_rin` varchar(30) DEFAULT NULL,
  `seller_name` varchar(200) DEFAULT NULL,
  `buyer_type` varchar(1) DEFAULT NULL,
  `buyer_id` varchar(30) DEFAULT NULL,
  `buyer_name` varchar(100) DEFAULT NULL,
  `total_sales` decimal(13,5) DEFAULT NULL,
  `total_commercial_discount` decimal(13,5) DEFAULT NULL,
  `total_items_discount` decimal(13,5) DEFAULT NULL,
  `extra_discount` decimal(13,5) DEFAULT NULL,
  `net_amount` decimal(13,5) DEFAULT NULL,
  `fees_amount` decimal(13,5) DEFAULT NULL,
  `total_amount` decimal(13,5) DEFAULT NULL,
  `tax_totals` text DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `date_time_issued` datetime DEFAULT NULL,
  `submission_uuid` varchar(36) DEFAULT NULL,
  `status` enum('pending','submitted','accepted','rejected') DEFAULT 'pending',
  `rejection_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_recommendation_rule` (
  `rule_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `condition_type` enum('category','price_range','customer_group','purchase_history') NOT NULL,
  `condition_value` text NOT NULL,
  `recommendation_type` enum('upsell','cross_sell') NOT NULL,
  `product_ids` text NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_report_execution_log` (
  `execution_id` int(11) NOT NULL,
  `report_id` int(11) NOT NULL,
  `execution_start` datetime NOT NULL,
  `execution_end` datetime DEFAULT NULL,
  `status` enum('started','completed','failed') NOT NULL DEFAULT 'started',
  `file_path` varchar(255) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `sent_to` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return` (
  `return_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) NOT NULL,
  `email` varchar(96) NOT NULL,
  `telephone` varchar(32) NOT NULL,
  `product` varchar(255) NOT NULL,
  `model` varchar(64) NOT NULL,
  `quantity` int(11) NOT NULL,
  `opened` tinyint(1) NOT NULL,
  `return_reason_id` int(11) NOT NULL,
  `return_action_id` int(11) NOT NULL,
  `return_status_id` int(11) NOT NULL,
  `comment` mediumtext DEFAULT NULL,
  `date_ordered` date DEFAULT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_action` (
  `return_action_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_history` (
  `return_history_id` int(11) NOT NULL,
  `return_id` int(11) NOT NULL,
  `return_status_id` int(11) NOT NULL,
  `notify` tinyint(1) NOT NULL,
  `comment` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_reason` (
  `return_reason_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_return_status` (
  `return_status_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_review` (
  `review_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `author` varchar(64) NOT NULL,
  `text` mediumtext NOT NULL,
  `rating` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_risk_register` (
  `risk_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL COMMENT 'عنوان الخطر',
  `description` text DEFAULT NULL COMMENT 'تفاصيل الخطر',
  `risk_category` varchar(50) DEFAULT NULL COMMENT 'تصنيف الخطر: مالي، قانوني، تشغيل...',
  `likelihood` enum('low','medium','high') DEFAULT 'medium' COMMENT 'احتمالية الحدوث',
  `impact` enum('low','medium','high') DEFAULT 'medium' COMMENT 'تأثير الخطر',
  `owner_user_id` int(11) DEFAULT NULL COMMENT 'من المسؤول عن مراقبة هذا الخطر',
  `mitigation_plan` text DEFAULT NULL COMMENT 'خطة الحد من الخطر',
  `status` enum('open','mitigated','closed') DEFAULT 'open',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `owner_group_id` int(11) NOT NULL DEFAULT 0,
  `nature_of_risk` enum('ongoing','one_time') NOT NULL DEFAULT 'ongoing',
  `risk_start_date` date DEFAULT NULL,
  `risk_end_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_sales_forecast` (
  `forecast_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `forecast_date` date NOT NULL,
  `forecast_quantity` decimal(15,4) NOT NULL,
  `forecast_amount` decimal(15,4) NOT NULL,
  `confidence_level` decimal(5,2) NOT NULL DEFAULT 90.00,
  `method` varchar(50) NOT NULL DEFAULT 'moving_average',
  `actual_quantity` decimal(15,4) DEFAULT NULL,
  `variance_percentage` decimal(5,2) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_sales_quotation` (
  `quotation_id` int(11) NOT NULL,
  `quotation_number` varchar(50) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `quotation_date` date NOT NULL,
  `valid_until` date NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL,
  `converted_to_order` tinyint(1) NOT NULL DEFAULT 0,
  `order_id` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_sales_quotation_item` (
  `item_id` int(11) NOT NULL,
  `quotation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `price` decimal(15,4) NOT NULL,
  `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total` decimal(15,4) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_scheduled_report` (
  `report_id` int(11) NOT NULL,
  `report_name` varchar(255) NOT NULL,
  `report_type` varchar(100) NOT NULL,
  `parameters` text NOT NULL,
  `format` enum('pdf','excel','csv','html') NOT NULL DEFAULT 'pdf',
  `frequency` enum('daily','weekly','monthly','quarterly','yearly') NOT NULL DEFAULT 'monthly',
  `next_run` datetime NOT NULL,
  `last_run` datetime DEFAULT NULL,
  `recipients` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_dashboard_widget` tinyint(1) NOT NULL DEFAULT 0,
  `widget_title` varchar(100) DEFAULT NULL,
  `widget_type` varchar(20) DEFAULT 'table',
  `access_groups` text DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_internal_link` (
  `link_id` int(11) NOT NULL,
  `source_page` varchar(255) NOT NULL,
  `target_page` varchar(255) NOT NULL,
  `anchor_text` varchar(255) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_keyword_tracking` (
  `tracking_id` int(11) NOT NULL,
  `keyword` varchar(255) NOT NULL,
  `search_engine` varchar(50) NOT NULL DEFAULT 'google',
  `position` int(11) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `last_checked` datetime NOT NULL,
  `previous_position` int(11) DEFAULT NULL,
  `status` enum('improved','declined','unchanged','new') NOT NULL DEFAULT 'new'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_page_analysis` (
  `analysis_id` int(11) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `target_keyword` varchar(255) NOT NULL,
  `title_score` int(11) NOT NULL DEFAULT 0,
  `meta_score` int(11) NOT NULL DEFAULT 0,
  `content_score` int(11) NOT NULL DEFAULT 0,
  `technical_score` int(11) NOT NULL DEFAULT 0,
  `overall_score` int(11) NOT NULL DEFAULT 0,
  `suggestions` text DEFAULT NULL,
  `date_analysis` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_settings` (
  `setting_id` int(11) NOT NULL,
  `code` varchar(128) NOT NULL,
  `key` varchar(128) NOT NULL,
  `value` text NOT NULL,
  `serialized` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_seo_url` (
  `seo_url_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `query` varchar(255) NOT NULL,
  `keyword` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_session` (
  `session_id` varchar(32) NOT NULL,
  `data` mediumtext NOT NULL,
  `expire` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_setting` (
  `setting_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `code` varchar(128) NOT NULL,
  `key` varchar(128) NOT NULL,
  `value` mediumtext NOT NULL,
  `serialized` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_company` (
  `company_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `website` varchar(255) DEFAULT NULL,
  `contact_name` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(50) DEFAULT NULL,
  `contact_email` varchar(100) DEFAULT NULL,
  `api_url` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `logo` varchar(255) DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `tracking_url_template` varchar(255) DEFAULT NULL COMMENT 'قالب URL لتتبع الشحنة: {tracking_number}',
  `supports_cod` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'يدعم الدفع عند الاستلام',
  `supports_api` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'يدعم الربط عبر API',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_company_config` (
  `config_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `key` varchar(100) NOT NULL,
  `value` text NOT NULL,
  `is_sensitive` tinyint(1) NOT NULL DEFAULT 0,
  `environment` enum('live','test','both') NOT NULL DEFAULT 'both',
  `last_updated` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_courier` (
  `shipping_courier_id` int(11) NOT NULL,
  `shipping_courier_code` varchar(255) NOT NULL DEFAULT '',
  `shipping_courier_name` varchar(255) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_coverage` (
  `coverage_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `zone_id` int(11) DEFAULT NULL COMMENT 'المنطقة من جدول zone',
  `country_id` int(11) DEFAULT NULL COMMENT 'الدولة من جدول country',
  `geo_zone_id` int(11) DEFAULT NULL COMMENT 'المنطقة الجغرافية',
  `city` varchar(100) DEFAULT NULL COMMENT 'مدينة محددة',
  `area` varchar(100) DEFAULT NULL COMMENT 'منطقة محددة',
  `zip_code` varchar(20) DEFAULT NULL COMMENT 'الرمز البريدي',
  `priority` enum('primary','secondary','backup') NOT NULL DEFAULT 'primary',
  `delivery_days` int(11) NOT NULL DEFAULT 1 COMMENT 'أيام التوصيل المتوقعة',
  `delivery_days_max` int(11) DEFAULT NULL COMMENT 'الحد الأقصى لأيام التوصيل',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_order` (
  `shipping_order_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `tracking_number` varchar(100) DEFAULT NULL,
  `shipment_date` datetime DEFAULT NULL,
  `scheduled_delivery_date` date DEFAULT NULL,
  `actual_delivery_date` datetime DEFAULT NULL,
  `shipping_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cod_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مبلغ الدفع عند الاستلام',
  `status` enum('pending','processed','shipped','in_transit','delivered','returned','failed','cancelled') NOT NULL DEFAULT 'pending',
  `package_weight` decimal(10,2) DEFAULT NULL,
  `package_dimensions` varchar(50) DEFAULT NULL COMMENT 'الأبعاد بالسنتيمتر: طول×عرض×ارتفاع',
  `shipping_label_url` varchar(255) DEFAULT NULL,
  `invoice_url` varchar(255) DEFAULT NULL,
  `awb_number` varchar(50) DEFAULT NULL COMMENT 'رقم بوليصة الشحن',
  `api_response` mediumtext DEFAULT NULL COMMENT 'استجابة API للشحن',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_rate` (
  `rate_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `coverage_id` int(11) DEFAULT NULL,
  `weight_from` decimal(10,2) NOT NULL DEFAULT 0.00,
  `weight_to` decimal(10,2) DEFAULT NULL,
  `price` decimal(15,4) NOT NULL,
  `price_type` enum('fixed','per_kg','percentage') NOT NULL DEFAULT 'fixed',
  `min_price` decimal(15,4) DEFAULT NULL,
  `max_price` decimal(15,4) DEFAULT NULL,
  `additional_kg_price` decimal(15,4) DEFAULT NULL COMMENT 'سعر الكيلو الإضافي',
  `cod_fee` decimal(15,4) DEFAULT NULL COMMENT 'رسوم الدفع عند الاستلام',
  `cod_fee_type` enum('fixed','percentage') DEFAULT NULL,
  `effective_from` date NOT NULL,
  `effective_to` date DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_settlement` (
  `settlement_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `settlement_date` date NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `total_orders` int(11) NOT NULL DEFAULT 0,
  `total_cod_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `shipping_fees` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cod_fees` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `net_amount` decimal(15,4) NOT NULL COMMENT 'المبلغ الصافي للتسوية',
  `payment_direction` enum('to_company','from_company') NOT NULL COMMENT 'اتجاه الدفع: لنا أو علينا',
  `status` enum('pending','reconciled','disputed') NOT NULL DEFAULT 'pending',
  `bank_transaction_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `reconciled_by` int(11) DEFAULT NULL,
  `reconciled_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_settlement_order` (
  `settlement_order_id` int(11) NOT NULL,
  `settlement_id` int(11) NOT NULL,
  `shipping_order_id` int(11) NOT NULL,
  `cod_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `shipping_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cod_fee` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` enum('matched','unmatched','disputed') NOT NULL DEFAULT 'matched',
  `discrepancy_amount` decimal(15,4) DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_shipping_tracking` (
  `tracking_id` int(11) NOT NULL,
  `shipping_order_id` int(11) NOT NULL,
  `status` varchar(100) NOT NULL,
  `status_details` text DEFAULT NULL,
  `tracking_date` datetime NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `agent_name` varchar(100) DEFAULT NULL,
  `source` enum('api','manual','customer') NOT NULL DEFAULT 'api',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL COMMENT 'NULL إذا كان من API'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_statistics` (
  `statistics_id` int(11) NOT NULL,
  `code` varchar(64) NOT NULL,
  `value` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_adjustment` (
  `adjustment_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `adjustment_number` varchar(50) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `type` enum('increase','decrease') NOT NULL,
  `status` enum('draft','approved','cancelled') NOT NULL DEFAULT 'draft',
  `adjustment_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_adjustment_history` (
  `history_id` int(11) NOT NULL,
  `adjustment_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_adjustment_item` (
  `adjustment_item_id` int(11) NOT NULL,
  `adjustment_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_count` (
  `stock_count_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL COMMENT 'الفرع/المخزن الذي يتم الجرد فيه',
  `reference_code` varchar(50) DEFAULT NULL COMMENT 'يمكن تخزين رقم الجرد أو رمز معين لاستخدامه في الطباعة',
  `count_date` date NOT NULL COMMENT 'تاريخ الجرد الفعلي',
  `status` enum('draft','in_progress','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT 'الحالة: تحت الإنشاء، قيد الجرد، منتهية، ملغاة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات عامة حول الجرد',
  `created_by` int(11) NOT NULL COMMENT 'منشئ جلسة الجرد',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_count_item` (
  `count_item_id` int(11) NOT NULL,
  `stock_count_id` int(11) NOT NULL COMMENT 'FK إلى رأس الجرد',
  `product_id` int(11) NOT NULL COMMENT 'رقم المنتج',
  `unit_id` int(11) NOT NULL COMMENT 'الوحدة التي يتم الجرد بها',
  `system_qty` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية المسجلة في النظام قبل الجرد',
  `counted_qty` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الكمية التي وجدها الموظف فعليًا',
  `difference` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT '(counted_qty - system_qty)',
  `barcode` varchar(255) DEFAULT NULL COMMENT 'في حال تم إدخال الباركود أثناء عملية الجرد، للبحث السريع',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_status` (
  `stock_status_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_transfer` (
  `transfer_id` int(11) NOT NULL,
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `transfer_number` varchar(50) NOT NULL,
  `from_branch_id` int(11) NOT NULL,
  `to_branch_id` int(11) NOT NULL,
  `status` enum('pending','in_transit','completed','cancelled') NOT NULL DEFAULT 'pending',
  `transfer_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_transfer_history` (
  `history_id` int(11) NOT NULL,
  `transfer_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_stock_transfer_item` (
  `transfer_item_id` int(11) NOT NULL,
  `transfer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_store` (
  `store_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `url` varchar(255) NOT NULL,
  `ssl` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier` (
  `supplier_id` int(11) NOT NULL,
  `account_code` bigint(20) DEFAULT NULL,
  `supplier_group_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `language_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `telephone` varchar(32) NOT NULL,
  `fax` varchar(32) NOT NULL,
  `password` varchar(40) NOT NULL,
  `salt` varchar(9) NOT NULL,
  `cart` mediumtext DEFAULT NULL,
  `wishlist` mediumtext DEFAULT NULL,
  `newsletter` tinyint(1) NOT NULL DEFAULT 0,
  `address_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL,
  `ip` varchar(40) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `safe` tinyint(1) NOT NULL,
  `token` mediumtext NOT NULL,
  `code` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_address` (
  `address_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(32) DEFAULT NULL,
  `company` varchar(40) DEFAULT NULL,
  `address_1` varchar(128) NOT NULL,
  `address_2` varchar(128) NOT NULL,
  `city` varchar(128) NOT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `country_id` int(11) NOT NULL DEFAULT 63,
  `zone_id` int(11) NOT NULL DEFAULT 0,
  `custom_field` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_evaluation` (
  `evaluation_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `evaluator_id` int(11) NOT NULL,
  `evaluation_date` date NOT NULL,
  `quality_score` decimal(3,2) NOT NULL,
  `delivery_score` decimal(3,2) NOT NULL,
  `price_score` decimal(3,2) NOT NULL,
  `service_score` decimal(3,2) NOT NULL,
  `overall_score` decimal(3,2) NOT NULL,
  `comments` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_group` (
  `supplier_group_id` int(11) NOT NULL,
  `approval` int(11) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_group_description` (
  `supplier_group_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `description` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_invoice` (
  `invoice_id` int(11) NOT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `po_id` int(11) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  `invoice_date` date NOT NULL,
  `due_date` date NOT NULL,
  `subtotal` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) NOT NULL,
  `discount_amount` decimal(15,4) NOT NULL,
  `total_amount` decimal(15,4) NOT NULL,
  `status` enum('pending','approved','paid','cancelled') NOT NULL DEFAULT 'pending',
  `journal_id` int(11) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` int(11) NOT NULL,
  `journal_ref` int(11) DEFAULT NULL,
  `matching_status` enum('pending','matched','partial','mismatch') NOT NULL DEFAULT 'pending' COMMENT 'حالة المطابقة مع الاستلام وأمر الشراء',
  `payment_status` enum('unpaid','partially_paid','paid') NOT NULL DEFAULT 'unpaid' COMMENT 'حالة السداد',
  `paid_amount` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'المبلغ المسدد',
  `last_payment_date` datetime DEFAULT NULL COMMENT 'تاريخ آخر سداد'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_invoice_history` (
  `history_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_invoice_item` (
  `invoice_item_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `po_item_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL,
  `discount_rate` decimal(5,2) NOT NULL,
  `total_price` decimal(15,4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_supplier_product_price` (
  `price_id` int(11) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL DEFAULT 1,
  `price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `min_quantity` decimal(15,4) DEFAULT NULL COMMENT 'الحد الأدنى للكمية بهذا السعر',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل هذا هو السعر الافتراضي',
  `start_date` date DEFAULT NULL COMMENT 'تاريخ بداية السعر',
  `end_date` date DEFAULT NULL COMMENT 'تاريخ نهاية السعر',
  `last_purchase_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_system_events` (
  `event_id` int(11) NOT NULL,
  `event_type` varchar(50) NOT NULL COMMENT 'نوع الحدث',
  `event_action` varchar(50) NOT NULL COMMENT 'الإجراء المتخذ',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `event_data` text DEFAULT NULL COMMENT 'بيانات الحدث بتنسيق JSON',
  `user_id` int(11) DEFAULT NULL COMMENT 'المستخدم المسبب للحدث',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'عنوان IP',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_system_notifications` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_group_id` int(11) DEFAULT NULL,
  `type` enum('info','warning','error','success','alert') NOT NULL DEFAULT 'info',
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expiry_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_task` (
  `task_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `assigned_to` int(11) NOT NULL COMMENT 'User ID the task is assigned to',
  `assigned_by` int(11) NOT NULL COMMENT 'User ID who assigned the task',
  `due_date` date DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `completed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = pending, 1 = completed',
  `completed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'Optional: Module related to the task (e.g., order, customer)',
  `reference_id` int(11) DEFAULT NULL COMMENT 'Optional: ID related to the module'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores user tasks';

CREATE TABLE `cod_tax_class` (
  `tax_class_id` int(11) NOT NULL,
  `eta_tax_type` varchar(10) DEFAULT 'T1',
  `eta_tax_subtype` varchar(10) DEFAULT 'V009',
  `title` varchar(32) NOT NULL,
  `description` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_tax_rate` (
  `tax_rate_id` int(11) NOT NULL,
  `geo_zone_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(32) NOT NULL,
  `rate` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `type` char(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_tax_rate_to_customer_group` (
  `tax_rate_id` int(11) NOT NULL,
  `customer_group_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_tax_rule` (
  `tax_rule_id` int(11) NOT NULL,
  `tax_class_id` int(11) NOT NULL,
  `tax_rate_id` int(11) NOT NULL,
  `based` varchar(10) NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_theme` (
  `theme_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `theme` varchar(64) NOT NULL,
  `route` varchar(64) NOT NULL,
  `code` longtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_translation` (
  `translation_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `route` varchar(64) NOT NULL,
  `key` varchar(64) NOT NULL,
  `value` mediumtext NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unavailability_reasons` (
  `reason_id` int(11) NOT NULL,
  `reason_code` varchar(20) NOT NULL COMMENT 'كود السبب',
  `reason_name_ar` varchar(100) NOT NULL COMMENT 'اسم السبب بالعربية',
  `reason_name_en` varchar(100) NOT NULL COMMENT 'اسم السبب بالإنجليزية',
  `status_type` enum('maintenance','quality_check','damaged','expired','quarantine') NOT NULL,
  `description_ar` text DEFAULT NULL COMMENT 'وصف بالعربية',
  `description_en` text DEFAULT NULL COMMENT 'وصف بالإنجليزية',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط',
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'يحتاج موافقة',
  `auto_expire_days` int(11) DEFAULT NULL COMMENT 'انتهاء تلقائي بالأيام',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'ترتيب العرض',
  `created_by` int(11) NOT NULL COMMENT 'المنشئ',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unified_document` (
  `document_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `document_type` enum('contract','invoice','report','proposal','policy','other') NOT NULL DEFAULT 'other',
  `file_path` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_size` int(10) UNSIGNED NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `version` varchar(20) NOT NULL DEFAULT '1.0',
  `status` enum('draft','pending_approval','approved','rejected','archived') NOT NULL DEFAULT 'draft',
  `tags` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `creator_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `parent_document_id` int(11) DEFAULT NULL COMMENT 'المستند الأب في حالة الإصدارات',
  `expiry_date` date DEFAULT NULL COMMENT 'تاريخ انتهاء الصلاحية إن وجد',
  `reference_module` varchar(50) DEFAULT NULL COMMENT 'المودل المرتبط مثل order, customer',
  `reference_id` int(11) DEFAULT NULL COMMENT 'المعرف المرتبط بالمودل',
  `is_template` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل المستند قالب',
  `approval_workflow_id` int(11) DEFAULT NULL COMMENT 'معرف سير عمل الموافقة إن وجد'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unified_notification` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` enum('info','success','warning','error','approval','reminder','system') NOT NULL DEFAULT 'info',
  `module` varchar(50) NOT NULL COMMENT 'المودل المُنشئ للإشعار',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع مثل message, order, approval',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `read_at` datetime DEFAULT NULL,
  `expiry_at` datetime DEFAULT NULL,
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `channels` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'قنوات الإرسال' CHECK (json_valid(`channels`)),
  `scheduled_at` datetime DEFAULT NULL COMMENT 'موعد الإرسال المجدول',
  `sent_at` datetime DEFAULT NULL COMMENT 'وقت الإرسال الفعلي',
  `delivery_status` enum('pending','sent','delivered','failed') NOT NULL DEFAULT 'pending',
  `action_url` varchar(255) DEFAULT NULL COMMENT 'الرابط للعمل المطلوب',
  `created_by` int(11) DEFAULT NULL COMMENT 'المستخدم المنشئ أو NULL للنظام'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول الإشعارات الموحدة المحدث';

CREATE TABLE `cod_unified_workflow` (
  `workflow_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `workflow_type` enum('document_approval','purchase_approval','leave_request','expense_claim','payment_approval','other') NOT NULL,
  `status` enum('active','inactive','archived') NOT NULL DEFAULT 'active',
  `creator_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_template` tinyint(1) NOT NULL DEFAULT 0,
  `department_id` int(11) DEFAULT NULL,
  `escalation_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل تصعيد الطلبات',
  `escalation_after_days` int(11) DEFAULT NULL COMMENT 'التصعيد بعد عدد أيام',
  `notify_creator` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'إشعار المنشئ بالتغييرات'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unit` (
  `unit_id` int(11) NOT NULL,
  `code` varchar(10) NOT NULL,
  `desc_en` varchar(255) NOT NULL,
  `desc_ar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unit_conversion_history` (
  `conversion_history_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `from_unit_id` int(11) NOT NULL,
  `to_unit_id` int(11) NOT NULL,
  `from_quantity` decimal(15,4) NOT NULL,
  `to_quantity` decimal(15,4) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_unit_conversion_log` (
  `conversion_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `from_unit_id` int(11) NOT NULL,
  `to_unit_id` int(11) NOT NULL,
  `from_quantity` decimal(15,4) NOT NULL,
  `to_quantity` decimal(15,4) NOT NULL,
  `conversion_factor` decimal(15,4) NOT NULL,
  `conversion_type` enum('manual','auto_sale','auto_bundle','auto_sync') NOT NULL,
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف العملية المرجعية',
  `converted_by` int(11) DEFAULT NULL,
  `converted_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='سجل تحويلات الوحدات';

CREATE TABLE `cod_upload` (
  `upload_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user` (
  `user_id` int(11) NOT NULL,
  `user_group_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL DEFAULT 2,
  `username` varchar(20) NOT NULL,
  `password` varchar(40) NOT NULL,
  `salt` varchar(9) NOT NULL,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) NOT NULL,
  `email` varchar(96) NOT NULL,
  `image` varchar(255) NOT NULL,
  `code` varchar(40) NOT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `ip` varchar(40) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT 0,
  `two_factor_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تفعيل المصادقة الثنائية',
  `two_factor_secret` varchar(32) DEFAULT NULL COMMENT 'مفتاح المصادقة الثنائية',
  `two_factor_backup_codes` text DEFAULT NULL COMMENT 'رموز النسخ الاحتياطي',
  `two_factor_last_used` datetime DEFAULT NULL COMMENT 'آخر استخدام للمصادقة الثنائية',
  `phone_number` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف للرسائل النصية',
  `phone_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تأكيد رقم الهاتف'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_2fa_attempts` (
  `attempt_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `attempt_type` enum('totp','sms','email','backup') NOT NULL COMMENT 'نوع المحاولة',
  `code_entered` varchar(10) NOT NULL COMMENT 'الرمز المدخل',
  `is_successful` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'نجح أم فشل',
  `ip_address` varchar(45) NOT NULL COMMENT 'عنوان IP',
  `user_agent` varchar(255) DEFAULT NULL COMMENT 'معلومات المتصفح',
  `attempt_time` datetime NOT NULL DEFAULT current_timestamp(),
  `failure_reason` varchar(100) DEFAULT NULL COMMENT 'سبب الفشل'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `cod_user_activity_log` (
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `ip` varchar(40) NOT NULL,
  `date_activity` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_group` (
  `user_group_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `permission` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_group_permission` (
  `user_group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_kpi_assignment` (
  `assignment_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT 'FK referencing cod_user.user_id',
  `kpi_code` varchar(50) NOT NULL COMMENT 'Matches kpi_code in cod_dashboard_kpi',
  `assigned_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Links users to specific KPIs they can view or manage';

CREATE TABLE `cod_user_login_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `login_time` datetime NOT NULL DEFAULT current_timestamp(),
  `logout_time` datetime DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `failure_reason` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_notification_preferences` (
  `preference_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'تفضيلات الإشعارات للمستخدم' CHECK (json_valid(`preferences`)),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول إعدادات الإشعارات للمستخدمين';

CREATE TABLE `cod_user_permission` (
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_session` (
  `session_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `user_agent` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_user_trusted_devices` (
  `device_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `device_fingerprint` varchar(64) NOT NULL COMMENT 'بصمة الجهاز',
  `device_name` varchar(100) NOT NULL COMMENT 'اسم الجهاز',
  `ip_address` varchar(45) NOT NULL COMMENT 'عنوان IP',
  `user_agent` varchar(255) NOT NULL COMMENT 'معلومات المتصفح',
  `location` varchar(100) DEFAULT NULL COMMENT 'الموقع الجغرافي',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط أم لا',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `last_used_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `expires_at` datetime NOT NULL COMMENT 'تاريخ انتهاء الثقة'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='الأجهزة الموثوقة للمستخدمين';

CREATE TABLE `cod_user_verification_codes` (
  `code_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `code` varchar(10) NOT NULL COMMENT 'رمز التحقق',
  `code_type` enum('sms','email','phone_verification') NOT NULL COMMENT 'نوع الرمز',
  `purpose` enum('2fa','phone_verify','password_reset','login') NOT NULL COMMENT 'الغرض من الرمز',
  `is_used` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تم استخدامه أم لا',
  `attempts_count` int(11) NOT NULL DEFAULT 0 COMMENT 'عدد المحاولات',
  `max_attempts` int(11) NOT NULL DEFAULT 3 COMMENT 'الحد الأقصى للمحاولات',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL COMMENT 'تاريخ انتهاء الصلاحية',
  `used_at` datetime DEFAULT NULL COMMENT 'تاريخ الاستخدام',
  `ip_address` varchar(45) NOT NULL COMMENT 'عنوان IP'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `cod_vendor_payment` (
  `payment_id` int(11) NOT NULL,
  `payment_number` varchar(50) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  `payment_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `payment_method` enum('cash','bank_transfer','cheque','credit_card') NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` int(11) NOT NULL,
  `invoice_id` int(11) DEFAULT NULL COMMENT 'معرف فاتورة المورد',
  `po_id` int(11) DEFAULT NULL COMMENT 'معرف أمر الشراء',
  `currency_id` int(11) DEFAULT NULL COMMENT 'العملة',
  `exchange_rate` decimal(15,6) DEFAULT NULL COMMENT 'سعر الصرف',
  `journal_id` int(11) DEFAULT NULL COMMENT 'معرف القيد المحاسبي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_virtual_inventory_log` (
  `log_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `old_quantity_available` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `new_quantity_available` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `change_reason` enum('manual','auto_sync','bundle_update','promotion','order_reserve','order_cancel') NOT NULL,
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف مرجعي (طلب، عرض، إلخ)',
  `notes` text DEFAULT NULL,
  `changed_by` int(11) NOT NULL,
  `changed_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='سجل تغييرات المخزون الوهمي';

CREATE TABLE `cod_visitors_stats` (
  `visit_date` date NOT NULL,
  `visits` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher` (
  `voucher_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `code` varchar(10) NOT NULL,
  `from_name` varchar(64) NOT NULL,
  `from_email` varchar(96) NOT NULL,
  `to_name` varchar(64) NOT NULL,
  `to_email` varchar(96) NOT NULL,
  `voucher_theme_id` int(11) NOT NULL,
  `message` mediumtext NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher_history` (
  `voucher_history_id` int(11) NOT NULL,
  `voucher_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `date_added` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher_theme` (
  `voucher_theme_id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_voucher_theme_description` (
  `voucher_theme_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_warranty` (
  `warranty_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `warranty_status` enum('active','expired','claimed','void') NOT NULL DEFAULT 'active',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_weight_class` (
  `weight_class_id` int(11) NOT NULL,
  `value` decimal(15,8) NOT NULL DEFAULT 0.00000000
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_weight_class_description` (
  `weight_class_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `title` varchar(32) NOT NULL,
  `unit` varchar(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_workflow_approval` (
  `approval_id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `step_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` enum('approved','rejected','delegated','commented') NOT NULL,
  `comment` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `delegated_to` int(11) DEFAULT NULL COMMENT 'تفويض الموافقة لمستخدم آخر'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_workflow_request` (
  `request_id` int(11) NOT NULL,
  `workflow_id` int(11) NOT NULL,
  `current_step_id` int(11) DEFAULT NULL,
  `requester_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('draft','pending','in_progress','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `reference_module` varchar(50) NOT NULL COMMENT 'المودل المرتبط مثل document, purchase',
  `reference_id` int(11) NOT NULL COMMENT 'معرف المرجع',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `completed_at` datetime DEFAULT NULL,
  `due_date` datetime DEFAULT NULL COMMENT 'الموعد النهائي للطلب'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_workflow_step` (
  `step_id` int(11) NOT NULL,
  `workflow_id` int(11) NOT NULL,
  `step_name` varchar(255) NOT NULL,
  `step_order` int(11) NOT NULL,
  `approver_user_id` int(11) DEFAULT NULL COMMENT 'المستخدم المعتمد أو NULL لمجموعة',
  `approver_group_id` int(11) DEFAULT NULL COMMENT 'المجموعة المعتمدة أو NULL لمستخدم',
  `approval_type` enum('any_one','all','percentage','sequential') NOT NULL DEFAULT 'any_one',
  `approval_percentage` int(11) DEFAULT NULL,
  `is_final_step` tinyint(1) NOT NULL DEFAULT 0,
  `on_reject_goto_step` int(11) DEFAULT NULL COMMENT 'الخطوة التالية عند الرفض',
  `instructions` text DEFAULT NULL,
  `deadline_days` int(11) DEFAULT NULL COMMENT 'مهلة الموافقة بالأيام',
  `reminder_days` int(11) DEFAULT NULL COMMENT 'تذكير قبل الموعد النهائي بأيام'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_zone` (
  `zone_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `name` varchar(128) NOT NULL,
  `code` varchar(32) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_zone_distance` (
  `zone_distance_id` int(11) NOT NULL,
  `from_zone_id` int(11) NOT NULL COMMENT 'المحافظة المرسلة',
  `to_zone_id` int(11) NOT NULL COMMENT 'المحافظة المستقبلة',
  `distance_km` decimal(8,2) NOT NULL COMMENT 'المسافة بالكيلومتر',
  `travel_time_hours` decimal(5,2) NOT NULL COMMENT 'وقت السفر بالساعات',
  `road_quality` enum('excellent','good','fair','poor') NOT NULL DEFAULT 'good',
  `toll_cost` decimal(8,2) NOT NULL DEFAULT 0.00,
  `fuel_cost_estimate` decimal(8,2) NOT NULL DEFAULT 0.00,
  `is_direct_route` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cod_zone_to_geo_zone` (
  `zone_to_geo_zone_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `zone_id` int(11) NOT NULL DEFAULT 0,
  `geo_zone_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


ALTER TABLE `cod_2fa_message_templates`
  ADD PRIMARY KEY (`template_id`),
  ADD UNIQUE KEY `unique_template` (`template_type`,`language_code`,`purpose`);

ALTER TABLE `cod_2fa_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `unique_setting_key` (`setting_key`);

ALTER TABLE `cod_abandoned_cart`
  ADD PRIMARY KEY (`cart_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_abandoned_cart_recovery`
  ADD PRIMARY KEY (`recovery_id`),
  ADD KEY `cart_id` (`cart_id`);

ALTER TABLE `cod_abandoned_cart_template`
  ADD PRIMARY KEY (`template_id`);

ALTER TABLE `cod_accounts`
  ADD PRIMARY KEY (`account_id`),
  ADD UNIQUE KEY `account_code` (`account_code`),
  ADD KEY `parent_id` (`parent_id`),
  ADD KEY `idx_account_parent` (`parent_id`,`account_code`);

ALTER TABLE `cod_account_description`
  ADD PRIMARY KEY (`account_id`,`language_id`),
  ADD KEY `language_id` (`language_id`);

ALTER TABLE `cod_activity_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `action_type` (`action_type`),
  ADD KEY `module` (`module`),
  ADD KEY `reference_type_id` (`reference_type`,`reference_id`),
  ADD KEY `created_at` (`created_at`),
  ADD KEY `idx_user_action_created` (`user_id`,`action_type`,`created_at`);

ALTER TABLE `cod_address`
  ADD PRIMARY KEY (`address_id`),
  ADD KEY `customer_id` (`customer_id`);

ALTER TABLE `cod_announcement`
  ADD PRIMARY KEY (`announcement_id`),
  ADD KEY `idx_announcement_type` (`type`),
  ADD KEY `idx_announcement_status` (`status`),
  ADD KEY `idx_announcement_priority` (`priority`),
  ADD KEY `idx_announcement_dates` (`start_date`,`end_date`),
  ADD KEY `fk_announcement_creator` (`created_by`),
  ADD KEY `idx_announcement_search` (`title`,`content`(100));

ALTER TABLE `cod_announcement_attachment`
  ADD PRIMARY KEY (`attachment_id`),
  ADD KEY `idx_attachment_announcement` (`announcement_id`),
  ADD KEY `idx_attachment_sort` (`sort_order`),
  ADD KEY `fk_attachment_uploader` (`uploaded_by`);

ALTER TABLE `cod_announcement_comment`
  ADD PRIMARY KEY (`comment_id`),
  ADD KEY `idx_comment_announcement` (`announcement_id`),
  ADD KEY `idx_comment_user` (`user_id`),
  ADD KEY `idx_comment_parent` (`parent_comment_id`),
  ADD KEY `idx_comment_status` (`status`);

ALTER TABLE `cod_announcement_view`
  ADD PRIMARY KEY (`view_id`),
  ADD UNIQUE KEY `unique_announcement_user_view` (`announcement_id`,`user_id`),
  ADD KEY `idx_view_date` (`viewed_at`),
  ADD KEY `fk_view_user` (`user_id`);

ALTER TABLE `cod_api`
  ADD PRIMARY KEY (`api_id`);

ALTER TABLE `cod_api_ip`
  ADD PRIMARY KEY (`api_ip_id`);

ALTER TABLE `cod_api_session`
  ADD PRIMARY KEY (`api_session_id`);

ALTER TABLE `cod_asset_types`
  ADD PRIMARY KEY (`asset_type_id`);

ALTER TABLE `cod_attendance`
  ADD PRIMARY KEY (`attendance_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_attribute`
  ADD PRIMARY KEY (`attribute_id`);

ALTER TABLE `cod_attribute_description`
  ADD PRIMARY KEY (`attribute_id`,`language_id`),
  ADD KEY `language_id` (`language_id`);

ALTER TABLE `cod_attribute_group`
  ADD PRIMARY KEY (`attribute_group_id`);

ALTER TABLE `cod_attribute_group_description`
  ADD PRIMARY KEY (`attribute_group_id`,`language_id`),
  ADD KEY `language_id` (`language_id`);

ALTER TABLE `cod_audit_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `reference_type` (`reference_type`);

ALTER TABLE `cod_audit_plan`
  ADD PRIMARY KEY (`plan_id`),
  ADD KEY `year` (`year`),
  ADD KEY `status` (`status`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `approved_by` (`approved_by`);

ALTER TABLE `cod_audit_task`
  ADD PRIMARY KEY (`task_id`),
  ADD KEY `plan_id` (`plan_id`),
  ADD KEY `status` (`status`),
  ADD KEY `assigned_to` (`assigned_to`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_bank`
  ADD PRIMARY KEY (`bank_id`),
  ADD KEY `account_code` (`account_code`);

ALTER TABLE `cod_bank_account`
  ADD PRIMARY KEY (`account_id`);

ALTER TABLE `cod_bank_reconciliation`
  ADD PRIMARY KEY (`reconciliation_id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`);

ALTER TABLE `cod_bank_transaction`
  ADD PRIMARY KEY (`bank_transaction_id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`);

ALTER TABLE `cod_banner`
  ADD PRIMARY KEY (`banner_id`);

ALTER TABLE `cod_banner_image`
  ADD PRIMARY KEY (`banner_image_id`);

ALTER TABLE `cod_blog_category`
  ADD PRIMARY KEY (`category_id`);

ALTER TABLE `cod_blog_comment`
  ADD PRIMARY KEY (`comment_id`);

ALTER TABLE `cod_blog_post`
  ADD PRIMARY KEY (`post_id`);

ALTER TABLE `cod_blog_post_to_category`
  ADD PRIMARY KEY (`post_id`,`category_id`);

ALTER TABLE `cod_blog_post_to_tag`
  ADD PRIMARY KEY (`post_id`,`tag_id`);

ALTER TABLE `cod_blog_tag`
  ADD PRIMARY KEY (`tag_id`);

ALTER TABLE `cod_branch`
  ADD PRIMARY KEY (`branch_id`),
  ADD KEY `eta_branch_id` (`eta_branch_id`,`manager_id`);

ALTER TABLE `cod_branch_address`
  ADD PRIMARY KEY (`address_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `cod_branch_distance`
  ADD PRIMARY KEY (`distance_id`),
  ADD UNIQUE KEY `unique_branch_zone` (`from_branch_id`,`to_zone_id`),
  ADD KEY `idx_branch_distance_from` (`from_branch_id`),
  ADD KEY `idx_branch_distance_to` (`to_zone_id`),
  ADD KEY `idx_branch_distance_priority` (`priority`),
  ADD KEY `idx_branch_distance_active` (`is_active`);

ALTER TABLE `cod_branch_inventory_snapshot`
  ADD PRIMARY KEY (`snapshot_id`),
  ADD KEY `idx_branch_product` (`branch_id`,`product_id`),
  ADD KEY `idx_date` (`snapshot_date`);

ALTER TABLE `cod_budget`
  ADD PRIMARY KEY (`budget_id`),
  ADD KEY `idx_period_status` (`period_start`,`period_end`,`status`),
  ADD KEY `fk_budget_creator` (`created_by`),
  ADD KEY `fk_budget_approver` (`approved_by`);

ALTER TABLE `cod_budget_line`
  ADD PRIMARY KEY (`line_id`),
  ADD KEY `idx_budget_account` (`budget_id`,`account_code`),
  ADD KEY `fk_budget_line_account` (`account_code`);

ALTER TABLE `cod_bundle_performance_analysis`
  ADD PRIMARY KEY (`analysis_id`),
  ADD UNIQUE KEY `idx_bundle_date` (`bundle_id`,`analysis_date`),
  ADD KEY `idx_analysis_date` (`analysis_date`),
  ADD KEY `idx_performance` (`conversion_rate`,`profit_margin`);

ALTER TABLE `cod_bundle_usage_log`
  ADD PRIMARY KEY (`usage_id`),
  ADD KEY `idx_bundle_date` (`bundle_id`,`used_at`),
  ADD KEY `idx_order_bundle` (`order_id`,`bundle_id`),
  ADD KEY `idx_customer_bundle` (`customer_id`,`bundle_id`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_cart`
  ADD PRIMARY KEY (`cart_id`),
  ADD KEY `cart_id` (`api_id`,`customer_id`,`session_id`,`product_id`,`recurring_id`),
  ADD KEY `is_free` (`is_free`,`bundle_id`,`product_quantity_discount_id`,`group_id`);

ALTER TABLE `cod_cash`
  ADD PRIMARY KEY (`cash_id`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `responsible_user_id` (`responsible_user_id`);

ALTER TABLE `cod_cash_transaction`
  ADD PRIMARY KEY (`cash_transaction_id`),
  ADD KEY `idx_cash_id` (`cash_id`);

ALTER TABLE `cod_category`
  ADD PRIMARY KEY (`category_id`),
  ADD KEY `parent_id` (`parent_id`);

ALTER TABLE `cod_category_description`
  ADD PRIMARY KEY (`category_id`,`language_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_category_filter`
  ADD PRIMARY KEY (`category_id`,`filter_id`);

ALTER TABLE `cod_category_path`
  ADD PRIMARY KEY (`category_id`,`path_id`);

ALTER TABLE `cod_category_to_layout`
  ADD PRIMARY KEY (`category_id`,`store_id`);

ALTER TABLE `cod_category_to_store`
  ADD PRIMARY KEY (`category_id`,`store_id`);

ALTER TABLE `cod_checks`
  ADD PRIMARY KEY (`check_id`),
  ADD KEY `idx_bank_account_id` (`bank_account_id`),
  ADD KEY `idx_currency_id` (`currency_id`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_updated_by` (`updated_by`);

ALTER TABLE `cod_compliance_record`
  ADD PRIMARY KEY (`compliance_id`),
  ADD KEY `idx_compliance_type` (`compliance_type`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `cod_compliance_record_fk_user` (`responsible_user_id`);

ALTER TABLE `cod_consignment_inventory`
  ADD PRIMARY KEY (`consignment_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `supplier_id` (`supplier_id`);

ALTER TABLE `cod_cost_calculation_settings`
  ADD PRIMARY KEY (`setting_id`);

ALTER TABLE `cod_country`
  ADD PRIMARY KEY (`country_id`);

ALTER TABLE `cod_coupon`
  ADD PRIMARY KEY (`coupon_id`);

ALTER TABLE `cod_coupon_category`
  ADD PRIMARY KEY (`coupon_id`,`category_id`);

ALTER TABLE `cod_coupon_history`
  ADD PRIMARY KEY (`coupon_history_id`);

ALTER TABLE `cod_coupon_product`
  ADD PRIMARY KEY (`coupon_product_id`);

ALTER TABLE `cod_crm_campaign`
  ADD PRIMARY KEY (`campaign_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_contact`
  ADD PRIMARY KEY (`contact_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_deal`
  ADD PRIMARY KEY (`deal_id`),
  ADD KEY `idx_opportunity_id` (`opportunity_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_lead`
  ADD PRIMARY KEY (`lead_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_crm_opportunity`
  ADD PRIMARY KEY (`opportunity_id`),
  ADD KEY `idx_lead_id` (`lead_id`),
  ADD KEY `idx_assigned_to_user_id` (`assigned_to_user_id`);

ALTER TABLE `cod_currency`
  ADD PRIMARY KEY (`currency_id`);

ALTER TABLE `cod_currency_rate_history`
  ADD PRIMARY KEY (`rate_history_id`),
  ADD KEY `idx_currency_id` (`currency_id`),
  ADD KEY `idx_rate_date` (`rate_date`),
  ADD KEY `idx_changed_by` (`changed_by`);

ALTER TABLE `cod_customer`
  ADD PRIMARY KEY (`customer_id`),
  ADD UNIQUE KEY `email` (`email`,`telephone`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `idx_customer_name_email` (`firstname`,`email`),
  ADD KEY `idx_eta_tax_id` (`eta_tax_id`),
  ADD KEY `idx_eta_commercial_registration` (`eta_commercial_registration`);

ALTER TABLE `cod_customer_activity`
  ADD PRIMARY KEY (`customer_activity_id`);

ALTER TABLE `cod_customer_affiliate`
  ADD PRIMARY KEY (`customer_id`);

ALTER TABLE `cod_customer_approval`
  ADD PRIMARY KEY (`customer_approval_id`);

ALTER TABLE `cod_customer_credit_limit`
  ADD PRIMARY KEY (`limit_id`),
  ADD UNIQUE KEY `customer_id` (`customer_id`),
  ADD KEY `approved_by` (`approved_by`);

ALTER TABLE `cod_customer_feedback`
  ADD PRIMARY KEY (`feedback_id`),
  ADD KEY `fk_feedback_customer` (`customer_id`),
  ADD KEY `fk_feedback_assigned_to` (`assigned_to`),
  ADD KEY `idx_feedback_reference` (`reference_module`,`reference_id`),
  ADD KEY `idx_feedback_status` (`status`,`created_at`);

ALTER TABLE `cod_customer_group`
  ADD PRIMARY KEY (`customer_group_id`);

ALTER TABLE `cod_customer_group_description`
  ADD PRIMARY KEY (`customer_group_id`,`language_id`);

ALTER TABLE `cod_customer_history`
  ADD PRIMARY KEY (`customer_history_id`);

ALTER TABLE `cod_customer_ip`
  ADD PRIMARY KEY (`customer_ip_id`),
  ADD KEY `ip` (`ip`);

ALTER TABLE `cod_customer_login`
  ADD PRIMARY KEY (`customer_login_id`),
  ADD KEY `email` (`email`),
  ADD KEY `ip` (`ip`);

ALTER TABLE `cod_customer_note`
  ADD PRIMARY KEY (`note_id`);

ALTER TABLE `cod_customer_online`
  ADD PRIMARY KEY (`ip`);

ALTER TABLE `cod_customer_return_inventory`
  ADD PRIMARY KEY (`return_inventory_id`),
  ADD KEY `return_id` (`return_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `movement_id` (`movement_id`),
  ADD KEY `journal_id` (`journal_id`);

ALTER TABLE `cod_customer_reward`
  ADD PRIMARY KEY (`customer_reward_id`);

ALTER TABLE `cod_customer_search`
  ADD PRIMARY KEY (`customer_search_id`);

ALTER TABLE `cod_customer_transaction`
  ADD PRIMARY KEY (`customer_transaction_id`);

ALTER TABLE `cod_customer_wishlist`
  ADD PRIMARY KEY (`customer_id`,`product_id`);

ALTER TABLE `cod_custom_field`
  ADD PRIMARY KEY (`custom_field_id`);

ALTER TABLE `cod_custom_field_customer_group`
  ADD PRIMARY KEY (`custom_field_id`,`customer_group_id`);

ALTER TABLE `cod_custom_field_description`
  ADD PRIMARY KEY (`custom_field_id`,`language_id`);

ALTER TABLE `cod_custom_field_value`
  ADD PRIMARY KEY (`custom_field_value_id`);

ALTER TABLE `cod_custom_field_value_description`
  ADD PRIMARY KEY (`custom_field_value_id`,`language_id`);

ALTER TABLE `cod_custom_report`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `idx_creator_public` (`created_by`,`is_public`);

ALTER TABLE `cod_data_access_control`
  ADD PRIMARY KEY (`access_id`),
  ADD UNIQUE KEY `idx_access_unique` (`user_id`,`user_group_id`,`resource_type`,`resource_id`),
  ADD KEY `idx_resource` (`resource_type`,`resource_id`),
  ADD KEY `fk_access_group` (`user_group_id`),
  ADD KEY `fk_access_grantor` (`granted_by`);

ALTER TABLE `cod_document_permission`
  ADD PRIMARY KEY (`permission_id`),
  ADD KEY `fk_doc_permission_document` (`document_id`),
  ADD KEY `fk_doc_permission_user` (`user_id`),
  ADD KEY `fk_doc_permission_group` (`user_group_id`),
  ADD KEY `fk_doc_permission_grantor` (`granted_by`);

ALTER TABLE `cod_dynamic_pricing_rule`
  ADD PRIMARY KEY (`rule_id`);

ALTER TABLE `cod_employee_advance`
  ADD PRIMARY KEY (`advance_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_updated_by` (`updated_by`);

ALTER TABLE `cod_employee_advance_installment`
  ADD PRIMARY KEY (`installment_id`),
  ADD KEY `idx_advance_id` (`advance_id`),
  ADD KEY `idx_due_date` (`due_date`);

ALTER TABLE `cod_employee_documents`
  ADD PRIMARY KEY (`document_id`),
  ADD KEY `idx_employee_id` (`employee_id`);

ALTER TABLE `cod_employee_profile`
  ADD PRIMARY KEY (`employee_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_eta_activity_codes`
  ADD PRIMARY KEY (`activity_id`),
  ADD UNIQUE KEY `uk_activity_code` (`activity_code`),
  ADD KEY `idx_parent_code` (`parent_code`),
  ADD KEY `idx_level` (`level`),
  ADD KEY `idx_is_active` (`is_active`);

ALTER TABLE `cod_eta_activity_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_activity_type` (`activity_type`),
  ADD KEY `idx_entity` (`entity_type`,`entity_id`),
  ADD KEY `idx_document_id` (`document_id`),
  ADD KEY `idx_queue_id` (`queue_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_eta_activity_log_type_date` (`activity_type`,`created_at`);

ALTER TABLE `cod_eta_documents`
  ADD PRIMARY KEY (`document_id`),
  ADD UNIQUE KEY `uk_entity` (`entity_type`,`entity_id`),
  ADD UNIQUE KEY `uk_eta_uuid` (`eta_uuid`),
  ADD KEY `idx_internal_id` (`internal_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_eta_status` (`eta_status`),
  ADD KEY `idx_document_type` (`document_type`),
  ADD KEY `idx_submitted_at` (`submitted_at`),
  ADD KEY `idx_eta_documents_status_date` (`status`,`submitted_at`);

ALTER TABLE `cod_eta_document_lines`
  ADD PRIMARY KEY (`line_id`),
  ADD KEY `idx_document_id` (`document_id`),
  ADD KEY `idx_product_id` (`product_id`),
  ADD KEY `idx_item_code` (`item_code`),
  ADD KEY `idx_gpc_code` (`gpc_code`);

ALTER TABLE `cod_eta_queue`
  ADD PRIMARY KEY (`queue_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_entity` (`entity_type`,`entity_id`),
  ADD KEY `idx_priority_status` (`priority`,`status`),
  ADD KEY `idx_scheduled_at` (`scheduled_at`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_eta_queue_priority_status` (`priority`,`status`,`scheduled_at`);

ALTER TABLE `cod_eta_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `uk_setting_key` (`setting_key`),
  ADD KEY `idx_is_active` (`is_active`);

ALTER TABLE `cod_eta_statistics`
  ADD PRIMARY KEY (`stat_id`),
  ADD UNIQUE KEY `uk_stat_date_hour` (`stat_date`,`stat_hour`),
  ADD KEY `idx_stat_date` (`stat_date`),
  ADD KEY `idx_eta_statistics_date_success` (`stat_date`,`success_rate`);

ALTER TABLE `cod_eta_tax_codes`
  ADD PRIMARY KEY (`tax_code_id`),
  ADD UNIQUE KEY `uk_tax_code` (`tax_type`,`tax_subtype`),
  ADD KEY `idx_tax_rate` (`tax_rate`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_effective_dates` (`effective_from`,`effective_to`);

ALTER TABLE `cod_eta_tokens`
  ADD PRIMARY KEY (`token_id`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_environment` (`environment`);

ALTER TABLE `cod_eta_unit_types`
  ADD PRIMARY KEY (`unit_id`),
  ADD UNIQUE KEY `uk_unit_code` (`unit_code`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_is_active` (`is_active`);

ALTER TABLE `cod_event`
  ADD PRIMARY KEY (`event_id`);

ALTER TABLE `cod_extension`
  ADD PRIMARY KEY (`extension_id`);

ALTER TABLE `cod_extension_install`
  ADD PRIMARY KEY (`extension_install_id`);

ALTER TABLE `cod_extension_path`
  ADD PRIMARY KEY (`extension_path_id`);

ALTER TABLE `cod_feedback_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `fk_feedback_history_feedback` (`feedback_id`),
  ADD KEY `fk_feedback_history_user` (`user_id`);

ALTER TABLE `cod_feedback_message`
  ADD PRIMARY KEY (`message_id`),
  ADD KEY `fk_feedback_message_feedback` (`feedback_id`);

ALTER TABLE `cod_feedback_template`
  ADD PRIMARY KEY (`template_id`),
  ADD KEY `fk_feedback_template_creator` (`created_by`);

ALTER TABLE `cod_filter`
  ADD PRIMARY KEY (`filter_id`);

ALTER TABLE `cod_filter_description`
  ADD PRIMARY KEY (`filter_id`,`language_id`);

ALTER TABLE `cod_filter_group`
  ADD PRIMARY KEY (`filter_group_id`);

ALTER TABLE `cod_filter_group_description`
  ADD PRIMARY KEY (`filter_group_id`,`language_id`);

ALTER TABLE `cod_financial_forecast`
  ADD PRIMARY KEY (`forecast_id`),
  ADD KEY `idx_type_period` (`forecast_type`,`period_start`,`period_end`),
  ADD KEY `fk_financial_forecast_user` (`created_by`);

ALTER TABLE `cod_fixed_assets`
  ADD PRIMARY KEY (`asset_id`),
  ADD KEY `asset_type_id` (`asset_type_id`);

ALTER TABLE `cod_fixed_asset_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `asset_id` (`asset_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_geo_zone`
  ADD PRIMARY KEY (`geo_zone_id`);

ALTER TABLE `cod_gift_card`
  ADD PRIMARY KEY (`gift_card_id`);

ALTER TABLE `cod_goods_receipt`
  ADD PRIMARY KEY (`goods_receipt_id`),
  ADD KEY `currency_id` (`currency_id`),
  ADD KEY `idx_receipt_dates` (`receipt_date`,`created_at`),
  ADD KEY `quality_checked_by` (`quality_checked_by`);

ALTER TABLE `cod_goods_receipt_item`
  ADD PRIMARY KEY (`receipt_item_id`),
  ADD KEY `fk_goods_receipt_item_po_item` (`po_item_id`),
  ADD KEY `idx_receipt_item_po_item` (`goods_receipt_id`,`po_item_id`);

ALTER TABLE `cod_governance_issue`
  ADD PRIMARY KEY (`issue_id`),
  ADD KEY `issue_type` (`issue_type`),
  ADD KEY `priority` (`priority`),
  ADD KEY `status` (`status`),
  ADD KEY `responsible_user_id` (`responsible_user_id`),
  ADD KEY `responsible_group_id` (`responsible_group_id`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_governance_meeting`
  ADD PRIMARY KEY (`meeting_id`),
  ADD KEY `idx_meeting_type` (`meeting_type`),
  ADD KEY `idx_meeting_date` (`meeting_date`),
  ADD KEY `cod_governance_meeting_fk_user` (`added_by`),
  ADD KEY `idx_meeting_date_range` (`meeting_date`);

ALTER TABLE `cod_gpc_codes`
  ADD PRIMARY KEY (`gpc_id`),
  ADD KEY `gpc_code` (`gpc_code`);

ALTER TABLE `cod_hitshippo_aramex_details_new`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_hitshippo_aramex_pickup_details`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_hitshippo_fedex_details_new`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_hitshippo_fedex_token`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_import_allocation`
  ADD PRIMARY KEY (`allocation_id`);

ALTER TABLE `cod_import_charge`
  ADD PRIMARY KEY (`charge_id`);

ALTER TABLE `cod_import_shipment`
  ADD PRIMARY KEY (`shipment_id`);

ALTER TABLE `cod_information`
  ADD PRIMARY KEY (`information_id`);

ALTER TABLE `cod_information_description`
  ADD PRIMARY KEY (`information_id`,`language_id`);

ALTER TABLE `cod_information_to_layout`
  ADD PRIMARY KEY (`information_id`,`store_id`);

ALTER TABLE `cod_information_to_store`
  ADD PRIMARY KEY (`information_id`,`store_id`);

ALTER TABLE `cod_installment_payment`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `fk_installment_payment_schedule` (`schedule_id`),
  ADD KEY `fk_installment_payment_receiver` (`received_by`),
  ADD KEY `fk_installment_payment_bank` (`bank_account_id`);

ALTER TABLE `cod_installment_plan`
  ADD PRIMARY KEY (`plan_id`),
  ADD KEY `fk_installment_plan_order` (`order_id`),
  ADD KEY `fk_installment_plan_customer` (`customer_id`),
  ADD KEY `fk_installment_plan_template` (`template_id`),
  ADD KEY `fk_installment_plan_approver` (`approved_by`),
  ADD KEY `fk_installment_plan_guarantor` (`guarantor_id`);

ALTER TABLE `cod_installment_plan_template`
  ADD PRIMARY KEY (`template_id`),
  ADD KEY `fk_installment_template_creator` (`created_by`);

ALTER TABLE `cod_installment_reminder`
  ADD PRIMARY KEY (`reminder_id`),
  ADD KEY `fk_installment_reminder_schedule` (`schedule_id`),
  ADD KEY `fk_installment_reminder_creator` (`created_by`);

ALTER TABLE `cod_installment_schedule`
  ADD PRIMARY KEY (`schedule_id`),
  ADD UNIQUE KEY `unique_installment_number` (`plan_id`,`installment_number`),
  ADD KEY `idx_installment_due_date` (`due_date`,`status`);

ALTER TABLE `cod_internal_attachment`
  ADD PRIMARY KEY (`attachment_id`),
  ADD KEY `fk_attachment_message` (`message_id`);

ALTER TABLE `cod_internal_audit`
  ADD PRIMARY KEY (`audit_id`),
  ADD KEY `idx_auditor_user_id` (`auditor_user_id`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_internal_control`
  ADD PRIMARY KEY (`control_id`),
  ADD KEY `idx_responsible_group_id` (`responsible_group_id`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_internal_conversation`
  ADD PRIMARY KEY (`conversation_id`),
  ADD KEY `fk_conversation_creator` (`creator_id`),
  ADD KEY `idx_conversation_reference` (`associated_module`,`reference_id`);

ALTER TABLE `cod_internal_message`
  ADD PRIMARY KEY (`message_id`),
  ADD KEY `fk_message_conversation` (`conversation_id`),
  ADD KEY `fk_message_sender` (`sender_id`),
  ADD KEY `fk_message_parent` (`parent_message_id`),
  ADD KEY `idx_message_reference` (`reference_module`,`reference_id`);

ALTER TABLE `cod_internal_participant`
  ADD PRIMARY KEY (`participant_id`),
  ADD UNIQUE KEY `unique_conversation_user` (`conversation_id`,`user_id`),
  ADD KEY `fk_participant_user` (`user_id`);

ALTER TABLE `cod_inventory_abc_analysis`
  ADD PRIMARY KEY (`abc_id`),
  ADD KEY `idx_product_branch_period` (`product_id`,`branch_id`,`period_end`),
  ADD KEY `idx_abc_class` (`abc_class`),
  ADD KEY `fk_abc_branch` (`branch_id`),
  ADD KEY `fk_abc_user` (`created_by`);

ALTER TABLE `cod_inventory_accounting_reconciliation`
  ADD PRIMARY KEY (`reconciliation_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `approved_by` (`approved_by`);

ALTER TABLE `cod_inventory_accounting_reconciliation_item`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `reconciliation_id` (`reconciliation_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `adjustment_journal_id` (`adjustment_journal_id`);

ALTER TABLE `cod_inventory_account_mapping`
  ADD PRIMARY KEY (`mapping_id`),
  ADD KEY `branch_category_idx` (`branch_id`,`product_category_id`);

ALTER TABLE `cod_inventory_alert`
  ADD PRIMARY KEY (`alert_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `alert_type` (`alert_type`),
  ADD KEY `status` (`status`),
  ADD KEY `acknowledged_by` (`acknowledged_by`);

ALTER TABLE `cod_inventory_cost_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `date_added` (`date_added`),
  ADD KEY `idx_inventory_cost_history_date` (`date_added`,`product_id`);

ALTER TABLE `cod_inventory_cost_update`
  ADD PRIMARY KEY (`update_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `journal_id` (`journal_id`),
  ADD KEY `idx_inventory_cost` (`product_id`,`update_date`);

ALTER TABLE `cod_inventory_count`
  ADD PRIMARY KEY (`count_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `count_date` (`count_date`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_inventory_reservation`
  ADD PRIMARY KEY (`reservation_id`),
  ADD KEY `idx_product_branch` (`product_id`,`branch_id`),
  ADD KEY `idx_source` (`source_type`,`source_id`),
  ADD KEY `fk_reservation_branch` (`branch_id`),
  ADD KEY `fk_reservation_unit` (`unit_id`),
  ADD KEY `fk_reservation_user` (`created_by`);

ALTER TABLE `cod_inventory_role_permissions`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `idx_user_role` (`user_id`,`role_type`),
  ADD KEY `idx_role_type` (`role_type`),
  ADD KEY `idx_branch_role` (`branch_id`,`role_type`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_inventory_sheet`
  ADD PRIMARY KEY (`sheet_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `sheet_date` (`sheet_date`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_inventory_sheet_item`
  ADD PRIMARY KEY (`sheet_item_id`),
  ADD KEY `sheet_id` (`sheet_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`);

ALTER TABLE `cod_inventory_status_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_product_warehouse` (`product_id`,`warehouse_id`),
  ADD KEY `idx_status_change` (`status_from`,`status_to`),
  ADD KEY `idx_created_date` (`created_at`),
  ADD KEY `idx_batch` (`batch_id`),
  ADD KEY `idx_reason` (`reason_id`),
  ADD KEY `idx_reference` (`reference_type`,`reference_id`);

ALTER TABLE `cod_inventory_sync_rules`
  ADD PRIMARY KEY (`rule_id`),
  ADD UNIQUE KEY `idx_product_branch` (`product_id`,`branch_id`),
  ADD KEY `idx_sync_type` (`sync_type`),
  ADD KEY `idx_active_rules` (`is_active`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_inventory_transfer`
  ADD PRIMARY KEY (`transfer_id`),
  ADD KEY `source_branch_id` (`source_branch_id`),
  ADD KEY `destination_branch_id` (`destination_branch_id`),
  ADD KEY `transfer_date` (`transfer_date`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_inventory_transfer_item`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`);

ALTER TABLE `cod_inventory_turnover`
  ADD PRIMARY KEY (`analysis_id`),
  ADD KEY `idx_product_branch_period` (`product_id`,`branch_id`,`period_end`),
  ADD KEY `fk_turnover_branch` (`branch_id`),
  ADD KEY `fk_turnover_user` (`created_by`);

ALTER TABLE `cod_inventory_turnover_analysis`
  ADD PRIMARY KEY (`analysis_id`),
  ADD UNIQUE KEY `idx_product_branch_period` (`product_id`,`branch_id`,`unit_id`,`analysis_period`,`period_start`),
  ADD KEY `idx_turnover_ratio` (`turnover_ratio`),
  ADD KEY `idx_period` (`analysis_period`,`period_start`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `unit_id` (`unit_id`);

ALTER TABLE `cod_inventory_valuation`
  ADD PRIMARY KEY (`valuation_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `valuation_date` (`valuation_date`),
  ADD KEY `idx_inventory_valuation_date` (`valuation_date`,`product_id`,`branch_id`);

ALTER TABLE `cod_invoices`
  ADD PRIMARY KEY (`invoice_id`);

ALTER TABLE `cod_journals`
  ADD PRIMARY KEY (`journal_id`),
  ADD KEY `thedate` (`thedate`),
  ADD KEY `is_cancelled` (`is_cancelled`),
  ADD KEY `idx_journal_date` (`thedate`,`is_cancelled`),
  ADD KEY `idx_date_cancelled` (`thedate`,`is_cancelled`);

ALTER TABLE `cod_journal_attachments`
  ADD PRIMARY KEY (`attachment_id`),
  ADD KEY `journal_id` (`journal_id`);

ALTER TABLE `cod_journal_entries`
  ADD PRIMARY KEY (`entry_id`),
  ADD KEY `journal_id` (`journal_id`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `idx_journal_entries_account` (`account_code`,`is_debit`),
  ADD KEY `idx_journal_entries_account_date` (`account_code`,`journal_id`);

ALTER TABLE `cod_language`
  ADD PRIMARY KEY (`language_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_layout`
  ADD PRIMARY KEY (`layout_id`);

ALTER TABLE `cod_layout_module`
  ADD PRIMARY KEY (`layout_module_id`);

ALTER TABLE `cod_layout_route`
  ADD PRIMARY KEY (`layout_route_id`);

ALTER TABLE `cod_leave_request`
  ADD PRIMARY KEY (`leave_request_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_leave_type_id` (`leave_type_id`),
  ADD KEY `idx_approved_by` (`approved_by`),
  ADD KEY `idx_user_status_date` (`user_id`,`status`,`start_date`,`end_date`);

ALTER TABLE `cod_leave_type`
  ADD PRIMARY KEY (`leave_type_id`);

ALTER TABLE `cod_legal_contract`
  ADD PRIMARY KEY (`contract_id`),
  ADD KEY `idx_contract_type` (`contract_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_start_end_date` (`start_date`,`end_date`);

ALTER TABLE `cod_length_class`
  ADD PRIMARY KEY (`length_class_id`);

ALTER TABLE `cod_length_class_description`
  ADD PRIMARY KEY (`length_class_id`,`language_id`);

ALTER TABLE `cod_location`
  ADD PRIMARY KEY (`location_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_manufacturer`
  ADD PRIMARY KEY (`manufacturer_id`);

ALTER TABLE `cod_manufacturer_to_store`
  ADD PRIMARY KEY (`manufacturer_id`,`store_id`);

ALTER TABLE `cod_marketing`
  ADD PRIMARY KEY (`marketing_id`);

ALTER TABLE `cod_meeting_attendees`
  ADD PRIMARY KEY (`attendee_id`),
  ADD KEY `idx_meeting_id` (`meeting_id`),
  ADD KEY `idx_user_meeting` (`user_id`,`meeting_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_message_recipient`
  ADD PRIMARY KEY (`message_id`,`user_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_modification`
  ADD PRIMARY KEY (`modification_id`);

ALTER TABLE `cod_module`
  ADD PRIMARY KEY (`module_id`);

ALTER TABLE `cod_notices`
  ADD PRIMARY KEY (`notice_id`);

ALTER TABLE `cod_notification_automation`
  ADD PRIMARY KEY (`rule_id`),
  ADD KEY `idx_automation_event` (`trigger_event`),
  ADD KEY `idx_automation_status` (`status`),
  ADD KEY `fk_automation_template` (`notification_template_id`),
  ADD KEY `fk_automation_creator` (`created_by`),
  ADD KEY `idx_automation_trigger` (`trigger_event`,`status`);

ALTER TABLE `cod_notification_automation_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_log_rule` (`rule_id`),
  ADD KEY `idx_log_date` (`executed_at`),
  ADD KEY `idx_log_status` (`execution_status`);

ALTER TABLE `cod_notification_template`
  ADD PRIMARY KEY (`template_id`),
  ADD UNIQUE KEY `unique_template_name` (`name`),
  ADD KEY `idx_template_category` (`category`),
  ADD KEY `idx_template_type` (`type`),
  ADD KEY `idx_template_status` (`status`),
  ADD KEY `fk_template_creator` (`created_by`),
  ADD KEY `idx_template_search` (`name`,`title`);

ALTER TABLE `cod_notification_user`
  ADD PRIMARY KEY (`notification_id`,`user_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_option`
  ADD PRIMARY KEY (`option_id`);

ALTER TABLE `cod_option_description`
  ADD PRIMARY KEY (`option_id`,`language_id`);

ALTER TABLE `cod_option_value`
  ADD PRIMARY KEY (`option_value_id`);

ALTER TABLE `cod_option_value_description`
  ADD PRIMARY KEY (`option_value_id`,`language_id`);

ALTER TABLE `cod_order`
  ADD PRIMARY KEY (`order_id`),
  ADD KEY `idx_order_status_date` (`order_status_id`,`date_added`),
  ADD KEY `idx_order_customer` (`customer_id`,`date_added`),
  ADD KEY `idx_date_status` (`date_added`,`order_status_id`),
  ADD KEY `idx_eta_status` (`eta_status`),
  ADD KEY `idx_eta_document_id` (`eta_document_id`),
  ADD KEY `idx_eta_uuid` (`eta_uuid`),
  ADD KEY `idx_eta_submission_uuid` (`eta_submission_uuid`);

ALTER TABLE `cod_order_cogs`
  ADD PRIMARY KEY (`order_cogs_id`),
  ADD UNIQUE KEY `order_id` (`order_id`),
  ADD KEY `idx_order_cogs_journal` (`journal_id`);

ALTER TABLE `cod_order_history`
  ADD PRIMARY KEY (`order_history_id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `fk_orderhistory_status` (`order_status_id`);

ALTER TABLE `cod_order_option`
  ADD PRIMARY KEY (`order_option_id`);

ALTER TABLE `cod_order_product`
  ADD PRIMARY KEY (`order_product_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `idx_product_id` (`product_id`);

ALTER TABLE `cod_order_shipment`
  ADD PRIMARY KEY (`order_shipment_id`);

ALTER TABLE `cod_order_shipment_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `idx_order_shipment_id` (`order_shipment_id`),
  ADD KEY `idx_updated_by` (`updated_by`);

ALTER TABLE `cod_order_status`
  ADD PRIMARY KEY (`order_status_id`,`language_id`);

ALTER TABLE `cod_order_total`
  ADD PRIMARY KEY (`order_total_id`),
  ADD KEY `order_id` (`order_id`);

ALTER TABLE `cod_order_voucher`
  ADD PRIMARY KEY (`order_voucher_id`);

ALTER TABLE `cod_paymentlinks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`);

ALTER TABLE `cod_payment_gateway`
  ADD PRIMARY KEY (`gateway_id`),
  ADD UNIQUE KEY `unique_gateway_code` (`code`);

ALTER TABLE `cod_payment_gateway_config`
  ADD PRIMARY KEY (`config_id`),
  ADD UNIQUE KEY `unique_gateway_config` (`gateway_id`,`key`,`environment`),
  ADD KEY `fk_gateway_config_updater` (`updated_by`);

ALTER TABLE `cod_payment_invoice`
  ADD PRIMARY KEY (`payment_invoice_id`),
  ADD KEY `fk_payment_id` (`payment_id`),
  ADD KEY `fk_invoice_id_payment` (`invoice_id`);

ALTER TABLE `cod_payment_settlement`
  ADD PRIMARY KEY (`settlement_id`),
  ADD KEY `fk_payment_settlement_gateway` (`gateway_id`),
  ADD KEY `fk_payment_settlement_bank` (`bank_account_id`),
  ADD KEY `fk_payment_settlement_bank_tx` (`bank_transaction_id`),
  ADD KEY `fk_payment_settlement_user` (`reconciled_by`);

ALTER TABLE `cod_payment_settlement_transaction`
  ADD PRIMARY KEY (`settlement_transaction_id`),
  ADD UNIQUE KEY `unique_settlement_transaction` (`settlement_id`,`transaction_id`),
  ADD KEY `fk_settlement_transaction_tx` (`transaction_id`);

ALTER TABLE `cod_payment_transaction`
  ADD PRIMARY KEY (`transaction_id`),
  ADD KEY `fk_payment_transaction_gateway` (`gateway_id`),
  ADD KEY `fk_payment_transaction_order` (`order_id`),
  ADD KEY `fk_payment_transaction_customer` (`customer_id`),
  ADD KEY `fk_payment_transaction_installment` (`installment_payment_id`),
  ADD KEY `fk_payment_transaction_reference` (`reference_transaction_id`),
  ADD KEY `idx_payment_transaction_gateway_id` (`gateway_transaction_id`),
  ADD KEY `idx_payment_transaction_status` (`status`,`created_at`);

ALTER TABLE `cod_payroll_entry`
  ADD PRIMARY KEY (`payroll_entry_id`),
  ADD KEY `idx_payroll_period_id` (`payroll_period_id`),
  ADD KEY `idx_user_id` (`user_id`);

ALTER TABLE `cod_payroll_period`
  ADD PRIMARY KEY (`payroll_period_id`);

ALTER TABLE `cod_performance_criteria`
  ADD PRIMARY KEY (`criteria_id`);

ALTER TABLE `cod_performance_review`
  ADD PRIMARY KEY (`review_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_reviewer_id` (`reviewer_id`);

ALTER TABLE `cod_performance_review_criteria`
  ADD PRIMARY KEY (`review_criteria_id`),
  ADD KEY `idx_review_id` (`review_id`),
  ADD KEY `idx_criteria_id` (`criteria_id`);

ALTER TABLE `cod_permission`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `key` (`key`);

ALTER TABLE `cod_pos_cash_handover`
  ADD PRIMARY KEY (`handover_id`),
  ADD KEY `shift_id` (`shift_id`),
  ADD KEY `from_user_id` (`from_user_id`),
  ADD KEY `to_user_id` (`to_user_id`);

ALTER TABLE `cod_pos_session`
  ADD PRIMARY KEY (`session_id`),
  ADD UNIQUE KEY `unique_session_number` (`session_number`),
  ADD KEY `idx_session_terminal` (`terminal_id`),
  ADD KEY `idx_session_cashier` (`cashier_id`),
  ADD KEY `idx_session_status` (`status`);

ALTER TABLE `cod_pos_shift`
  ADD PRIMARY KEY (`shift_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `terminal_id` (`terminal_id`);

ALTER TABLE `cod_pos_terminal`
  ADD PRIMARY KEY (`terminal_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `cod_pos_transaction`
  ADD PRIMARY KEY (`transaction_id`),
  ADD KEY `shift_id` (`shift_id`),
  ADD KEY `order_id` (`order_id`);

ALTER TABLE `cod_product`
  ADD PRIMARY KEY (`product_id`),
  ADD KEY `idx_model` (`model`),
  ADD KEY `idx_sku` (`sku`),
  ADD KEY `idx_product_search` (`model`,`sku`,`upc`,`ean`,`mpn`),
  ADD KEY `idx_product_price` (`price`,`status`),
  ADD KEY `idx_eta_item_code` (`eta_item_code`),
  ADD KEY `idx_gpc_code` (`gpc_code`),
  ADD KEY `idx_egs_code` (`egs_code`);

ALTER TABLE `cod_product_attribute`
  ADD PRIMARY KEY (`product_id`,`attribute_id`,`language_id`);

ALTER TABLE `cod_product_barcode`
  ADD PRIMARY KEY (`product_barcode_id`),
  ADD UNIQUE KEY `unique_barcode` (`barcode`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `product_option_id` (`product_option_id`),
  ADD KEY `product_option_value_id` (`product_option_value_id`),
  ADD KEY `barcode` (`barcode`);

ALTER TABLE `cod_product_batch`
  ADD PRIMARY KEY (`batch_id`),
  ADD KEY `idx_product_branch_expiry` (`product_id`,`branch_id`,`expiry_date`),
  ADD KEY `fk_batch_branch` (`branch_id`),
  ADD KEY `fk_batch_user` (`created_by`);

ALTER TABLE `cod_product_bundle`
  ADD PRIMARY KEY (`bundle_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_bundle_status` (`status`),
  ADD KEY `idx_discount_type` (`discount_type`),
  ADD KEY `idx_validity` (`valid_from`,`valid_to`),
  ADD KEY `idx_usage` (`usage_count`,`usage_limit`),
  ADD KEY `idx_priority` (`priority`);

ALTER TABLE `cod_product_bundle_item`
  ADD PRIMARY KEY (`bundle_item_id`),
  ADD KEY `bundle_id` (`bundle_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `idx_bundle_product` (`bundle_id`,`product_id`),
  ADD KEY `idx_free_items` (`is_free`);

ALTER TABLE `cod_product_description`
  ADD PRIMARY KEY (`product_id`,`language_id`),
  ADD KEY `name` (`name`);

ALTER TABLE `cod_product_dynamic_pricing`
  ADD PRIMARY KEY (`product_id`,`rule_id`);

ALTER TABLE `cod_product_egs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product` (`product_id`),
  ADD KEY `status` (`eta_status`),
  ADD KEY `gpc_code` (`gpc_code`),
  ADD KEY `egs_code` (`egs_code`);

ALTER TABLE `cod_product_filter`
  ADD PRIMARY KEY (`product_id`,`filter_id`);

ALTER TABLE `cod_product_image`
  ADD PRIMARY KEY (`product_image_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_product_inventory`
  ADD PRIMARY KEY (`product_inventory_id`),
  ADD UNIQUE KEY `idx_product_branch_unit` (`product_id`,`branch_id`,`unit_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_branch` (`branch_id`),
  ADD KEY `idx_inventory_product_branch` (`product_id`,`branch_id`),
  ADD KEY `idx_inventory_branch_quantity` (`branch_id`,`quantity`),
  ADD KEY `idx_branch_product_unit` (`branch_id`,`product_id`,`unit_id`),
  ADD KEY `idx_quantity_available` (`quantity_available`),
  ADD KEY `idx_average_cost` (`average_cost`),
  ADD KEY `idx_consignment` (`is_consignment`,`consignment_supplier_id`),
  ADD KEY `idx_reserved_quantity` (`reserved_quantity`),
  ADD KEY `idx_sync_status` (`sync_status`),
  ADD KEY `idx_last_sync` (`last_sync_at`),
  ADD KEY `idx_maintenance_qty` (`quantity_maintenance`),
  ADD KEY `idx_quality_check_qty` (`quantity_quality_check`),
  ADD KEY `idx_damaged_qty` (`quantity_damaged`),
  ADD KEY `idx_expired_qty` (`quantity_expired`),
  ADD KEY `idx_quarantine_qty` (`quantity_quarantine`),
  ADD KEY `idx_status_update` (`last_status_update`);

ALTER TABLE `cod_product_inventory_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `transaction_date` (`transaction_date`),
  ADD KEY `transaction_type` (`transaction_type`);

ALTER TABLE `cod_product_movement`
  ADD PRIMARY KEY (`product_movement_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_date` (`date_added`),
  ADD KEY `idx_product_movement_reference` (`movement_reference_type`,`movement_reference_id`),
  ADD KEY `idx_source_document` (`source_document_type`,`source_document_id`);

ALTER TABLE `cod_product_option`
  ADD PRIMARY KEY (`product_option_id`);

ALTER TABLE `cod_product_option_value`
  ADD PRIMARY KEY (`product_option_value_id`);

ALTER TABLE `cod_product_price_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `changed_by` (`changed_by`);

ALTER TABLE `cod_product_pricing`
  ADD PRIMARY KEY (`product_pricing_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_product_quantity_discounts`
  ADD PRIMARY KEY (`discount_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_product_recommendation`
  ADD PRIMARY KEY (`recommendation_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `related_product_id` (`related_product_id`);

ALTER TABLE `cod_product_recurring`
  ADD PRIMARY KEY (`product_id`,`recurring_id`,`customer_group_id`);

ALTER TABLE `cod_product_related`
  ADD PRIMARY KEY (`product_id`,`related_id`);

ALTER TABLE `cod_product_reward`
  ADD PRIMARY KEY (`product_reward_id`);

ALTER TABLE `cod_product_to_category`
  ADD PRIMARY KEY (`product_id`,`category_id`),
  ADD KEY `category_id` (`category_id`);

ALTER TABLE `cod_product_to_layout`
  ADD PRIMARY KEY (`product_id`,`store_id`);

ALTER TABLE `cod_product_to_store`
  ADD PRIMARY KEY (`product_id`,`store_id`);

ALTER TABLE `cod_product_unit`
  ADD PRIMARY KEY (`product_unit_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_product_unit_type` (`product_id`,`unit_type`),
  ADD KEY `idx_conversion_factor` (`conversion_factor`);

ALTER TABLE `cod_pt_transactions`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_purchase_document`
  ADD PRIMARY KEY (`document_id`),
  ADD KEY `idx_document_search` (`document_name`,`reference_type`,`reference_id`);

ALTER TABLE `cod_purchase_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_reference` (`reference_type`,`reference_id`),
  ADD KEY `idx_user` (`user_id`),
  ADD KEY `idx_created` (`created_at`);

ALTER TABLE `cod_purchase_matching`
  ADD PRIMARY KEY (`matching_id`),
  ADD KEY `idx_status_dates` (`status`,`matched_at`),
  ADD KEY `idx_documents` (`po_id`,`receipt_id`,`invoice_id`),
  ADD KEY `fk_matching_receipt` (`receipt_id`),
  ADD KEY `fk_matching_invoice` (`invoice_id`),
  ADD KEY `fk_matching_user` (`matched_by`);

ALTER TABLE `cod_purchase_matching_item`
  ADD PRIMARY KEY (`matching_item_id`),
  ADD KEY `idx_matching` (`matching_id`),
  ADD KEY `idx_status_quantities` (`status`,`quantity_ordered`,`quantity_received`,`quantity_invoiced`),
  ADD KEY `idx_items` (`po_item_id`,`receipt_item_id`,`invoice_item_id`),
  ADD KEY `fk_matching_item_receipt` (`receipt_item_id`),
  ADD KEY `fk_matching_item_invoice` (`invoice_item_id`),
  ADD KEY `idx_matching_item_status` (`status`,`variance_amount`);

ALTER TABLE `cod_purchase_order`
  ADD PRIMARY KEY (`po_id`),
  ADD KEY `currency_id` (`currency_id`),
  ADD KEY `idx_po_source` (`source_type`,`source_id`),
  ADD KEY `idx_po_dates` (`order_date`,`expected_delivery_date`),
  ADD KEY `idx_po_status_total` (`status`,`total_amount`),
  ADD KEY `financial_approved_by` (`financial_approved_by`),
  ADD KEY `requisition_id` (`quotation_id`),
  ADD KEY `supplier_id` (`supplier_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `quotation_id` (`quotation_id`);

ALTER TABLE `cod_purchase_order_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `fk_po_history_po` (`po_id`);

ALTER TABLE `cod_purchase_order_item`
  ADD PRIMARY KEY (`po_item_id`),
  ADD KEY `cost_updated_by` (`cost_updated_by`),
  ADD KEY `fk_po_item_product` (`product_id`),
  ADD KEY `fk_po_item_unit` (`unit_id`),
  ADD KEY `idx_po_item_product` (`po_id`,`product_id`);

ALTER TABLE `cod_purchase_order_tracking`
  ADD PRIMARY KEY (`tracking_id`),
  ADD KEY `po_id` (`po_id`),
  ADD KEY `status_change` (`status_change`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_purchase_price_variance`
  ADD PRIMARY KEY (`variance_id`),
  ADD KEY `product_branch_idx` (`product_id`,`branch_id`),
  ADD KEY `receipt_invoice_idx` (`receipt_id`,`invoice_id`),
  ADD KEY `fk_variance_branch` (`branch_id`);

ALTER TABLE `cod_purchase_quotation`
  ADD PRIMARY KEY (`quotation_id`),
  ADD KEY `requisition_id` (`requisition_id`),
  ADD KEY `supplier_id` (`supplier_id`),
  ADD KEY `technical_approved_by` (`technical_approved_by`),
  ADD KEY `financial_approved_by` (`financial_approved_by`),
  ADD KEY `idx_quotation_status_dates` (`status`,`created_at`),
  ADD KEY `idx_quotation_requisition_supplier` (`requisition_id`,`supplier_id`);

ALTER TABLE `cod_purchase_quotation_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `quotation_id` (`quotation_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_quotation_history_dates` (`quotation_id`,`created_at`);

ALTER TABLE `cod_purchase_quotation_item`
  ADD PRIMARY KEY (`quotation_item_id`),
  ADD KEY `quotation_id` (`quotation_id`),
  ADD KEY `requisition_item_id` (`requisition_item_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `alternative_product_id` (`alternative_product_id`),
  ADD KEY `idx_quotation_item_product` (`quotation_id`,`product_id`);

ALTER TABLE `cod_purchase_requisition`
  ADD PRIMARY KEY (`requisition_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `department_id` (`user_group_id`),
  ADD KEY `user_group_id` (`user_group_id`),
  ADD KEY `created_by` (`created_by`,`updated_by`),
  ADD KEY `created_at` (`created_at`,`updated_at`),
  ADD KEY `status` (`status`),
  ADD KEY `req_number` (`req_number`),
  ADD KEY `idx_requisition_status` (`status`);

ALTER TABLE `cod_purchase_requisition_history`
  ADD PRIMARY KEY (`history_id`);

ALTER TABLE `cod_purchase_requisition_item`
  ADD PRIMARY KEY (`requisition_item_id`),
  ADD KEY `requisition_id` (`requisition_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `idx_req_item_requisition_product` (`requisition_id`,`product_id`);

ALTER TABLE `cod_purchase_return`
  ADD PRIMARY KEY (`return_id`),
  ADD KEY `fk_purchase_return_supplier` (`supplier_id`),
  ADD KEY `fk_purchase_return_po` (`purchase_order_id`),
  ADD KEY `fk_purchase_return_receipt` (`goods_receipt_id`),
  ADD KEY `fk_purchase_return_user` (`created_by`);

ALTER TABLE `cod_purchase_return_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `return_id` (`return_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_purchase_return_item`
  ADD PRIMARY KEY (`return_item_id`),
  ADD KEY `fk_return_item_return` (`return_id`),
  ADD KEY `fk_return_item_product` (`product_id`),
  ADD KEY `fk_return_item_unit` (`unit_id`);

ALTER TABLE `cod_quality_inspection`
  ADD PRIMARY KEY (`inspection_id`),
  ADD KEY `receipt_id` (`receipt_id`),
  ADD KEY `inspector_id` (`inspector_id`);

ALTER TABLE `cod_quality_inspection_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `inspection_id` (`inspection_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_quality_inspection_result`
  ADD PRIMARY KEY (`result_id`),
  ADD KEY `goods_receipt_id` (`goods_receipt_id`),
  ADD KEY `receipt_item_id` (`receipt_item_id`);

ALTER TABLE `cod_queue_jobs`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `cod_quotation_signatures`
  ADD PRIMARY KEY (`signature_id`),
  ADD KEY `reference_type_id` (`reference_type`,`reference_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_receipts`
  ADD PRIMARY KEY (`receipt_id`);

ALTER TABLE `cod_recommendation_rule`
  ADD PRIMARY KEY (`rule_id`);

ALTER TABLE `cod_report_execution_log`
  ADD PRIMARY KEY (`execution_id`),
  ADD KEY `report_id` (`report_id`),
  ADD KEY `execution_start` (`execution_start`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_return`
  ADD PRIMARY KEY (`return_id`);

ALTER TABLE `cod_return_action`
  ADD PRIMARY KEY (`return_action_id`,`language_id`);

ALTER TABLE `cod_return_history`
  ADD PRIMARY KEY (`return_history_id`);

ALTER TABLE `cod_return_reason`
  ADD PRIMARY KEY (`return_reason_id`,`language_id`);

ALTER TABLE `cod_return_status`
  ADD PRIMARY KEY (`return_status_id`,`language_id`);

ALTER TABLE `cod_review`
  ADD PRIMARY KEY (`review_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_risk_register`
  ADD PRIMARY KEY (`risk_id`),
  ADD KEY `idx_risk_category` (`risk_category`),
  ADD KEY `cod_risk_register_fk_owner` (`owner_user_id`),
  ADD KEY `status` (`status`),
  ADD KEY `owner_group_id` (`owner_group_id`);

ALTER TABLE `cod_sales_forecast`
  ADD PRIMARY KEY (`forecast_id`),
  ADD KEY `idx_product_branch_date` (`product_id`,`branch_id`,`forecast_date`),
  ADD KEY `fk_forecast_branch` (`branch_id`),
  ADD KEY `fk_forecast_user` (`created_by`);

ALTER TABLE `cod_sales_quotation`
  ADD PRIMARY KEY (`quotation_id`),
  ADD KEY `idx_customer_date` (`customer_id`,`quotation_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `fk_sales_quotation_branch` (`branch_id`),
  ADD KEY `fk_sales_quotation_user` (`created_by`);

ALTER TABLE `cod_sales_quotation_item`
  ADD PRIMARY KEY (`item_id`),
  ADD KEY `idx_sales__quotation_product` (`quotation_id`,`product_id`),
  ADD KEY `fk_sales__quotation_item_product` (`product_id`),
  ADD KEY `fk_sales__quotation_item_unit` (`unit_id`);

ALTER TABLE `cod_scheduled_report`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `report_type` (`report_type`),
  ADD KEY `frequency` (`frequency`),
  ADD KEY `next_run` (`next_run`),
  ADD KEY `created_by` (`created_by`);

ALTER TABLE `cod_seo_internal_link`
  ADD PRIMARY KEY (`link_id`);

ALTER TABLE `cod_seo_keyword_tracking`
  ADD PRIMARY KEY (`tracking_id`);

ALTER TABLE `cod_seo_page_analysis`
  ADD PRIMARY KEY (`analysis_id`);

ALTER TABLE `cod_seo_settings`
  ADD PRIMARY KEY (`setting_id`);

ALTER TABLE `cod_seo_url`
  ADD PRIMARY KEY (`seo_url_id`),
  ADD KEY `query` (`query`),
  ADD KEY `keyword` (`keyword`);

ALTER TABLE `cod_session`
  ADD PRIMARY KEY (`session_id`);

ALTER TABLE `cod_setting`
  ADD PRIMARY KEY (`setting_id`);

ALTER TABLE `cod_shipping_company`
  ADD PRIMARY KEY (`company_id`),
  ADD UNIQUE KEY `unique_shipping_code` (`code`);

ALTER TABLE `cod_shipping_company_config`
  ADD PRIMARY KEY (`config_id`),
  ADD UNIQUE KEY `unique_shipping_config` (`company_id`,`key`,`environment`),
  ADD KEY `fk_shipping_config_updater` (`updated_by`);

ALTER TABLE `cod_shipping_courier`
  ADD PRIMARY KEY (`shipping_courier_id`);

ALTER TABLE `cod_shipping_coverage`
  ADD PRIMARY KEY (`coverage_id`),
  ADD KEY `fk_shipping_coverage_company` (`company_id`),
  ADD KEY `fk_shipping_coverage_zone` (`zone_id`),
  ADD KEY `fk_shipping_coverage_country` (`country_id`),
  ADD KEY `fk_shipping_coverage_geo_zone` (`geo_zone_id`);

ALTER TABLE `cod_shipping_order`
  ADD PRIMARY KEY (`shipping_order_id`),
  ADD UNIQUE KEY `unique_order_company` (`order_id`,`company_id`),
  ADD KEY `fk_shipping_order_company` (`company_id`),
  ADD KEY `fk_shipping_order_creator` (`created_by`),
  ADD KEY `idx_shipping_order_tracking` (`tracking_number`),
  ADD KEY `idx_shipping_order_status` (`status`);

ALTER TABLE `cod_shipping_rate`
  ADD PRIMARY KEY (`rate_id`),
  ADD KEY `fk_shipping_rate_company` (`company_id`),
  ADD KEY `fk_shipping_rate_coverage` (`coverage_id`),
  ADD KEY `fk_shipping_rate_creator` (`created_by`);

ALTER TABLE `cod_shipping_settlement`
  ADD PRIMARY KEY (`settlement_id`),
  ADD KEY `fk_shipping_settlement_company` (`company_id`),
  ADD KEY `fk_shipping_settlement_bank` (`bank_transaction_id`),
  ADD KEY `fk_shipping_settlement_creator` (`created_by`),
  ADD KEY `fk_shipping_settlement_reconciler` (`reconciled_by`);

ALTER TABLE `cod_shipping_settlement_order`
  ADD PRIMARY KEY (`settlement_order_id`),
  ADD UNIQUE KEY `unique_settlement_order` (`settlement_id`,`shipping_order_id`),
  ADD KEY `fk_settlement_order_shipping` (`shipping_order_id`);

ALTER TABLE `cod_shipping_tracking`
  ADD PRIMARY KEY (`tracking_id`),
  ADD KEY `fk_shipping_tracking_order` (`shipping_order_id`),
  ADD KEY `fk_shipping_tracking_user` (`created_by`);

ALTER TABLE `cod_statistics`
  ADD PRIMARY KEY (`statistics_id`);

ALTER TABLE `cod_stock_adjustment`
  ADD PRIMARY KEY (`adjustment_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `cod_stock_adjustment_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `adjustment_id` (`adjustment_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_stock_adjustment_item`
  ADD PRIMARY KEY (`adjustment_item_id`),
  ADD KEY `adjustment_id` (`adjustment_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_stock_count`
  ADD PRIMARY KEY (`stock_count_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_stock_count_item`
  ADD PRIMARY KEY (`count_item_id`),
  ADD KEY `stock_count_id` (`stock_count_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `barcode` (`barcode`(190)) COMMENT 'للبحث السريع بالباركود (قد يكفي فهرس 190-200)';

ALTER TABLE `cod_stock_status`
  ADD PRIMARY KEY (`stock_status_id`,`language_id`);

ALTER TABLE `cod_stock_transfer`
  ADD PRIMARY KEY (`transfer_id`),
  ADD KEY `from_branch_id` (`from_branch_id`),
  ADD KEY `to_branch_id` (`to_branch_id`);

ALTER TABLE `cod_stock_transfer_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_stock_transfer_item`
  ADD PRIMARY KEY (`transfer_item_id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `cod_store`
  ADD PRIMARY KEY (`store_id`);

ALTER TABLE `cod_supplier`
  ADD PRIMARY KEY (`supplier_id`),
  ADD UNIQUE KEY `email` (`email`,`telephone`),
  ADD KEY `account_code` (`account_code`),
  ADD KEY `firstname` (`firstname`),
  ADD KEY `idx_supplier_name_email` (`firstname`,`email`);

ALTER TABLE `cod_supplier_address`
  ADD PRIMARY KEY (`address_id`),
  ADD KEY `customer_id` (`supplier_id`);

ALTER TABLE `cod_supplier_evaluation`
  ADD PRIMARY KEY (`evaluation_id`),
  ADD KEY `supplier_id` (`supplier_id`),
  ADD KEY `evaluator_id` (`evaluator_id`);

ALTER TABLE `cod_supplier_group`
  ADD PRIMARY KEY (`supplier_group_id`);

ALTER TABLE `cod_supplier_group_description`
  ADD PRIMARY KEY (`supplier_group_id`,`language_id`);

ALTER TABLE `cod_supplier_invoice`
  ADD PRIMARY KEY (`invoice_id`),
  ADD KEY `fk_po_id_invoice` (`po_id`);

ALTER TABLE `cod_supplier_invoice_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `user_id` (`user_id`);

ALTER TABLE `cod_supplier_invoice_item`
  ADD PRIMARY KEY (`invoice_item_id`),
  ADD KEY `fk_invoice_id` (`invoice_id`);

ALTER TABLE `cod_supplier_product_price`
  ADD PRIMARY KEY (`price_id`),
  ADD UNIQUE KEY `supplier_product_unit` (`supplier_id`,`product_id`,`unit_id`,`currency_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `currency_id` (`currency_id`);

ALTER TABLE `cod_system_events`
  ADD PRIMARY KEY (`event_id`),
  ADD KEY `event_type` (`event_type`,`event_action`),
  ADD KEY `reference_idx` (`reference_type`,`reference_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `created_at` (`created_at`);

ALTER TABLE `cod_system_notifications`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `user_group_id` (`user_group_id`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `reference_type_id` (`reference_type`,`reference_id`),
  ADD KEY `idx_user_expiry_created` (`user_id`,`expiry_date`,`created_at`),
  ADD KEY `idx_group_expiry_created` (`user_group_id`,`expiry_date`,`created_at`);

ALTER TABLE `cod_task`
  ADD PRIMARY KEY (`task_id`),
  ADD KEY `idx_assigned_to_completed` (`assigned_to`,`completed`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `fk_task_assigned_by` (`assigned_by`);

ALTER TABLE `cod_tax_class`
  ADD PRIMARY KEY (`tax_class_id`);

ALTER TABLE `cod_tax_rate`
  ADD PRIMARY KEY (`tax_rate_id`);

ALTER TABLE `cod_tax_rate_to_customer_group`
  ADD PRIMARY KEY (`tax_rate_id`,`customer_group_id`);

ALTER TABLE `cod_tax_rule`
  ADD PRIMARY KEY (`tax_rule_id`);

ALTER TABLE `cod_theme`
  ADD PRIMARY KEY (`theme_id`);

ALTER TABLE `cod_translation`
  ADD PRIMARY KEY (`translation_id`);

ALTER TABLE `cod_unavailability_reasons`
  ADD PRIMARY KEY (`reason_id`),
  ADD UNIQUE KEY `unique_reason_code` (`reason_code`),
  ADD KEY `idx_status_type` (`status_type`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

ALTER TABLE `cod_unified_document`
  ADD PRIMARY KEY (`document_id`),
  ADD KEY `fk_document_creator` (`creator_id`),
  ADD KEY `fk_document_department` (`department_id`),
  ADD KEY `fk_document_parent` (`parent_document_id`),
  ADD KEY `idx_document_reference` (`reference_module`,`reference_id`);

ALTER TABLE `cod_unified_notification`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `fk_notification_user` (`user_id`),
  ADD KEY `idx_notification_reference` (`reference_type`,`reference_id`),
  ADD KEY `idx_notification_status` (`user_id`,`read_at`),
  ADD KEY `fk_notification_creator` (`created_by`),
  ADD KEY `idx_notification_type` (`type`),
  ADD KEY `idx_notification_priority` (`priority`),
  ADD KEY `idx_notification_scheduled` (`scheduled_at`),
  ADD KEY `idx_notification_delivery` (`delivery_status`);

ALTER TABLE `cod_unified_workflow`
  ADD PRIMARY KEY (`workflow_id`),
  ADD KEY `fk_workflow_creator` (`creator_id`),
  ADD KEY `idx_workflow_department` (`department_id`);

ALTER TABLE `cod_unit`
  ADD PRIMARY KEY (`unit_id`);

ALTER TABLE `cod_unit_conversion_history`
  ADD PRIMARY KEY (`conversion_history_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `from_unit_id` (`from_unit_id`),
  ADD KEY `to_unit_id` (`to_unit_id`);

ALTER TABLE `cod_unit_conversion_log`
  ADD PRIMARY KEY (`conversion_id`),
  ADD KEY `idx_product_date` (`product_id`,`converted_at`),
  ADD KEY `idx_conversion_type` (`conversion_type`),
  ADD KEY `idx_units` (`from_unit_id`,`to_unit_id`),
  ADD KEY `to_unit_id` (`to_unit_id`),
  ADD KEY `converted_by` (`converted_by`);

ALTER TABLE `cod_upload`
  ADD PRIMARY KEY (`upload_id`);

ALTER TABLE `cod_user`
  ADD PRIMARY KEY (`user_id`),
  ADD KEY `idx_user_2fa_enabled` (`two_factor_enabled`),
  ADD KEY `idx_user_phone_verified` (`phone_verified`);

ALTER TABLE `cod_user_2fa_attempts`
  ADD PRIMARY KEY (`attempt_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_attempt_time` (`attempt_time`),
  ADD KEY `idx_2fa_attempts_user_time` (`user_id`,`attempt_time`);

ALTER TABLE `cod_user_activity_log`
  ADD PRIMARY KEY (`activity_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `date_activity` (`date_activity`);

ALTER TABLE `cod_user_group`
  ADD PRIMARY KEY (`user_group_id`);

ALTER TABLE `cod_user_group_permission`
  ADD PRIMARY KEY (`user_group_id`,`permission_id`);

ALTER TABLE `cod_user_kpi_assignment`
  ADD PRIMARY KEY (`assignment_id`),
  ADD UNIQUE KEY `user_kpi_unique` (`user_id`,`kpi_code`),
  ADD KEY `kpi_code` (`kpi_code`);

ALTER TABLE `cod_user_login_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_user_login` (`user_id`,`login_time`),
  ADD KEY `idx_status` (`status`);

ALTER TABLE `cod_user_notification_preferences`
  ADD PRIMARY KEY (`preference_id`),
  ADD UNIQUE KEY `unique_user_preferences` (`user_id`);

ALTER TABLE `cod_user_permission`
  ADD PRIMARY KEY (`user_id`,`permission_id`);

ALTER TABLE `cod_user_session`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `token` (`token`),
  ADD KEY `status` (`status`);

ALTER TABLE `cod_user_trusted_devices`
  ADD PRIMARY KEY (`device_id`),
  ADD UNIQUE KEY `unique_user_device` (`user_id`,`device_fingerprint`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_trusted_devices_active` (`user_id`,`is_active`,`expires_at`);

ALTER TABLE `cod_user_verification_codes`
  ADD PRIMARY KEY (`code_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_code_type` (`code_type`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_verification_codes_user_type` (`user_id`,`code_type`,`expires_at`);

ALTER TABLE `cod_vendor_payment`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `po_id` (`po_id`),
  ADD KEY `currency_id` (`currency_id`),
  ADD KEY `journal_id` (`journal_id`),
  ADD KEY `idx_payment_dates` (`payment_date`,`created_at`);

ALTER TABLE `cod_virtual_inventory_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_product_date` (`product_id`,`changed_at`),
  ADD KEY `idx_branch_date` (`branch_id`,`changed_at`),
  ADD KEY `idx_change_reason` (`change_reason`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `changed_by` (`changed_by`);

ALTER TABLE `cod_visitors_stats`
  ADD PRIMARY KEY (`visit_date`);

ALTER TABLE `cod_voucher`
  ADD PRIMARY KEY (`voucher_id`);

ALTER TABLE `cod_voucher_history`
  ADD PRIMARY KEY (`voucher_history_id`);

ALTER TABLE `cod_voucher_theme`
  ADD PRIMARY KEY (`voucher_theme_id`);

ALTER TABLE `cod_voucher_theme_description`
  ADD PRIMARY KEY (`voucher_theme_id`,`language_id`);

ALTER TABLE `cod_warranty`
  ADD PRIMARY KEY (`warranty_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `customer_id` (`customer_id`);

ALTER TABLE `cod_weight_class`
  ADD PRIMARY KEY (`weight_class_id`);

ALTER TABLE `cod_weight_class_description`
  ADD PRIMARY KEY (`weight_class_id`,`language_id`);

ALTER TABLE `cod_workflow_approval`
  ADD PRIMARY KEY (`approval_id`),
  ADD KEY `fk_workflow_approval_request` (`request_id`),
  ADD KEY `fk_workflow_approval_step` (`step_id`),
  ADD KEY `fk_workflow_approval_user` (`user_id`),
  ADD KEY `fk_workflow_approval_delegate` (`delegated_to`),
  ADD KEY `idx_request_step_user` (`request_id`,`step_id`,`user_id`),
  ADD KEY `idx_user_created` (`user_id`,`created_at`),
  ADD KEY `idx_created_action` (`created_at`,`action`);

ALTER TABLE `cod_workflow_request`
  ADD PRIMARY KEY (`request_id`),
  ADD KEY `fk_workflow_request_workflow` (`workflow_id`),
  ADD KEY `fk_workflow_request_step` (`current_step_id`),
  ADD KEY `fk_workflow_request_requester` (`requester_id`),
  ADD KEY `idx_workflow_request_reference` (`reference_module`,`reference_id`),
  ADD KEY `idx_status_current_step` (`status`,`current_step_id`),
  ADD KEY `idx_requester_status` (`requester_id`,`status`,`created_at`),
  ADD KEY `idx_created_status` (`created_at`,`status`),
  ADD KEY `idx_status_priority_created` (`status`,`priority`,`created_at`);

ALTER TABLE `cod_workflow_step`
  ADD PRIMARY KEY (`step_id`),
  ADD KEY `fk_workflow_step_workflow` (`workflow_id`),
  ADD KEY `fk_workflow_step_user` (`approver_user_id`),
  ADD KEY `fk_workflow_step_group` (`approver_group_id`),
  ADD KEY `idx_workflow_step_order` (`workflow_id`,`step_order`),
  ADD KEY `idx_approver_user` (`approver_user_id`),
  ADD KEY `idx_approver_group` (`approver_group_id`);

ALTER TABLE `cod_zone`
  ADD PRIMARY KEY (`zone_id`);

ALTER TABLE `cod_zone_distance`
  ADD PRIMARY KEY (`zone_distance_id`),
  ADD UNIQUE KEY `unique_zone_to_zone` (`from_zone_id`,`to_zone_id`),
  ADD KEY `idx_zone_distance_from` (`from_zone_id`),
  ADD KEY `idx_zone_distance_to` (`to_zone_id`),
  ADD KEY `idx_zone_distance_km` (`distance_km`);

ALTER TABLE `cod_zone_to_geo_zone`
  ADD PRIMARY KEY (`zone_to_geo_zone_id`);


ALTER TABLE `cod_2fa_message_templates`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_2fa_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_abandoned_cart`
  MODIFY `cart_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_abandoned_cart_recovery`
  MODIFY `recovery_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_abandoned_cart_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_accounts`
  MODIFY `account_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_activity_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_address`
  MODIFY `address_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement`
  MODIFY `announcement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement_attachment`
  MODIFY `attachment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement_comment`
  MODIFY `comment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_announcement_view`
  MODIFY `view_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_api`
  MODIFY `api_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_api_ip`
  MODIFY `api_ip_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_api_session`
  MODIFY `api_session_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_asset_types`
  MODIFY `asset_type_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_attendance`
  MODIFY `attendance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_attribute`
  MODIFY `attribute_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_attribute_group`
  MODIFY `attribute_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_audit_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_audit_plan`
  MODIFY `plan_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_audit_task`
  MODIFY `task_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank`
  MODIFY `bank_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank_account`
  MODIFY `account_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank_reconciliation`
  MODIFY `reconciliation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bank_transaction`
  MODIFY `bank_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_banner`
  MODIFY `banner_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_banner_image`
  MODIFY `banner_image_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_category`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_comment`
  MODIFY `comment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_post`
  MODIFY `post_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_blog_tag`
  MODIFY `tag_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch`
  MODIFY `branch_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch_address`
  MODIFY `address_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch_distance`
  MODIFY `distance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_branch_inventory_snapshot`
  MODIFY `snapshot_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_budget`
  MODIFY `budget_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_budget_line`
  MODIFY `line_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bundle_performance_analysis`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_bundle_usage_log`
  MODIFY `usage_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cart`
  MODIFY `cart_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cash`
  MODIFY `cash_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cash_transaction`
  MODIFY `cash_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_category`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_checks`
  MODIFY `check_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_compliance_record`
  MODIFY `compliance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_consignment_inventory`
  MODIFY `consignment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_cost_calculation_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_country`
  MODIFY `country_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_coupon`
  MODIFY `coupon_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_coupon_history`
  MODIFY `coupon_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_coupon_product`
  MODIFY `coupon_product_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_campaign`
  MODIFY `campaign_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_contact`
  MODIFY `contact_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_deal`
  MODIFY `deal_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_lead`
  MODIFY `lead_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_crm_opportunity`
  MODIFY `opportunity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_currency`
  MODIFY `currency_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_currency_rate_history`
  MODIFY `rate_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_activity`
  MODIFY `customer_activity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_approval`
  MODIFY `customer_approval_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_credit_limit`
  MODIFY `limit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_feedback`
  MODIFY `feedback_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_group`
  MODIFY `customer_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_history`
  MODIFY `customer_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_ip`
  MODIFY `customer_ip_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_login`
  MODIFY `customer_login_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_note`
  MODIFY `note_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_return_inventory`
  MODIFY `return_inventory_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_reward`
  MODIFY `customer_reward_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_search`
  MODIFY `customer_search_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_customer_transaction`
  MODIFY `customer_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_custom_field`
  MODIFY `custom_field_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_custom_field_value`
  MODIFY `custom_field_value_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_custom_report`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_data_access_control`
  MODIFY `access_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_document_permission`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_dynamic_pricing_rule`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_advance`
  MODIFY `advance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_advance_installment`
  MODIFY `installment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_documents`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_employee_profile`
  MODIFY `employee_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_activity_codes`
  MODIFY `activity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_activity_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_documents`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_document_lines`
  MODIFY `line_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_queue`
  MODIFY `queue_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_statistics`
  MODIFY `stat_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_tax_codes`
  MODIFY `tax_code_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_tokens`
  MODIFY `token_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_eta_unit_types`
  MODIFY `unit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_event`
  MODIFY `event_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_extension`
  MODIFY `extension_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_extension_install`
  MODIFY `extension_install_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_extension_path`
  MODIFY `extension_path_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_feedback_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_feedback_message`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_feedback_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_filter`
  MODIFY `filter_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_filter_group`
  MODIFY `filter_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_financial_forecast`
  MODIFY `forecast_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_fixed_assets`
  MODIFY `asset_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_fixed_asset_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_geo_zone`
  MODIFY `geo_zone_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_gift_card`
  MODIFY `gift_card_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_governance_issue`
  MODIFY `issue_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_governance_meeting`
  MODIFY `meeting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_gpc_codes`
  MODIFY `gpc_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_aramex_details_new`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_aramex_pickup_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_fedex_details_new`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_hitshippo_fedex_token`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_import_allocation`
  MODIFY `allocation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_import_charge`
  MODIFY `charge_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_import_shipment`
  MODIFY `shipment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_information`
  MODIFY `information_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_payment`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_plan`
  MODIFY `plan_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_plan_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_reminder`
  MODIFY `reminder_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_installment_schedule`
  MODIFY `schedule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_attachment`
  MODIFY `attachment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_audit`
  MODIFY `audit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_control`
  MODIFY `control_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_conversation`
  MODIFY `conversation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_message`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_internal_participant`
  MODIFY `participant_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_abc_analysis`
  MODIFY `abc_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_accounting_reconciliation`
  MODIFY `reconciliation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_accounting_reconciliation_item`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_account_mapping`
  MODIFY `mapping_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_alert`
  MODIFY `alert_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_cost_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_cost_update`
  MODIFY `update_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_count`
  MODIFY `count_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_reservation`
  MODIFY `reservation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_role_permissions`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_sheet`
  MODIFY `sheet_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_sheet_item`
  MODIFY `sheet_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_status_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_sync_rules`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_transfer`
  MODIFY `transfer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_transfer_item`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_turnover`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_turnover_analysis`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_inventory_valuation`
  MODIFY `valuation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_invoices`
  MODIFY `invoice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_journals`
  MODIFY `journal_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_journal_attachments`
  MODIFY `attachment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_journal_entries`
  MODIFY `entry_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_language`
  MODIFY `language_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_layout`
  MODIFY `layout_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_layout_module`
  MODIFY `layout_module_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_layout_route`
  MODIFY `layout_route_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_leave_request`
  MODIFY `leave_request_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_leave_type`
  MODIFY `leave_type_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_legal_contract`
  MODIFY `contract_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_length_class`
  MODIFY `length_class_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_location`
  MODIFY `location_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_manufacturer`
  MODIFY `manufacturer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_marketing`
  MODIFY `marketing_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_meeting_attendees`
  MODIFY `attendee_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_modification`
  MODIFY `modification_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_module`
  MODIFY `module_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notices`
  MODIFY `notice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notification_automation`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notification_automation_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_notification_template`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_option`
  MODIFY `option_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_option_value`
  MODIFY `option_value_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order`
  MODIFY `order_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_cogs`
  MODIFY `order_cogs_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_history`
  MODIFY `order_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_option`
  MODIFY `order_option_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_product`
  MODIFY `order_product_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_shipment`
  MODIFY `order_shipment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_shipment_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_status`
  MODIFY `order_status_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_total`
  MODIFY `order_total_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_order_voucher`
  MODIFY `order_voucher_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_paymentlinks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_gateway`
  MODIFY `gateway_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_gateway_config`
  MODIFY `config_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_invoice`
  MODIFY `payment_invoice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_settlement`
  MODIFY `settlement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_settlement_transaction`
  MODIFY `settlement_transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payment_transaction`
  MODIFY `transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payroll_entry`
  MODIFY `payroll_entry_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_payroll_period`
  MODIFY `payroll_period_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_performance_criteria`
  MODIFY `criteria_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_performance_review`
  MODIFY `review_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_performance_review_criteria`
  MODIFY `review_criteria_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_permission`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_cash_handover`
  MODIFY `handover_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_session`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_shift`
  MODIFY `shift_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_terminal`
  MODIFY `terminal_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pos_transaction`
  MODIFY `transaction_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product`
  MODIFY `product_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_barcode`
  MODIFY `product_barcode_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_batch`
  MODIFY `batch_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_bundle`
  MODIFY `bundle_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_bundle_item`
  MODIFY `bundle_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_egs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_image`
  MODIFY `product_image_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_inventory`
  MODIFY `product_inventory_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_inventory_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_movement`
  MODIFY `product_movement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_option`
  MODIFY `product_option_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_option_value`
  MODIFY `product_option_value_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_price_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_pricing`
  MODIFY `product_pricing_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_quantity_discounts`
  MODIFY `discount_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_recommendation`
  MODIFY `recommendation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_reward`
  MODIFY `product_reward_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_product_unit`
  MODIFY `product_unit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_pt_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_document`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_matching`
  MODIFY `matching_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_matching_item`
  MODIFY `matching_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order`
  MODIFY `po_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order_item`
  MODIFY `po_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_order_tracking`
  MODIFY `tracking_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_price_variance`
  MODIFY `variance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_quotation`
  MODIFY `quotation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_quotation_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_quotation_item`
  MODIFY `quotation_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_requisition`
  MODIFY `requisition_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_requisition_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_requisition_item`
  MODIFY `requisition_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_return`
  MODIFY `return_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_return_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_purchase_return_item`
  MODIFY `return_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quality_inspection`
  MODIFY `inspection_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quality_inspection_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quality_inspection_result`
  MODIFY `result_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_queue_jobs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_quotation_signatures`
  MODIFY `signature_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_receipts`
  MODIFY `receipt_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_recommendation_rule`
  MODIFY `rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_report_execution_log`
  MODIFY `execution_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return`
  MODIFY `return_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_action`
  MODIFY `return_action_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_history`
  MODIFY `return_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_reason`
  MODIFY `return_reason_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_return_status`
  MODIFY `return_status_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_review`
  MODIFY `review_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_risk_register`
  MODIFY `risk_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_sales_forecast`
  MODIFY `forecast_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_sales_quotation`
  MODIFY `quotation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_sales_quotation_item`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_scheduled_report`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_internal_link`
  MODIFY `link_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_keyword_tracking`
  MODIFY `tracking_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_page_analysis`
  MODIFY `analysis_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_seo_url`
  MODIFY `seo_url_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_setting`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_company`
  MODIFY `company_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_company_config`
  MODIFY `config_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_coverage`
  MODIFY `coverage_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_order`
  MODIFY `shipping_order_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_rate`
  MODIFY `rate_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_settlement`
  MODIFY `settlement_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_settlement_order`
  MODIFY `settlement_order_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_shipping_tracking`
  MODIFY `tracking_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_statistics`
  MODIFY `statistics_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_adjustment`
  MODIFY `adjustment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_adjustment_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_adjustment_item`
  MODIFY `adjustment_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_count`
  MODIFY `stock_count_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_count_item`
  MODIFY `count_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_status`
  MODIFY `stock_status_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_transfer`
  MODIFY `transfer_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_transfer_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_stock_transfer_item`
  MODIFY `transfer_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_store`
  MODIFY `store_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier`
  MODIFY `supplier_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_address`
  MODIFY `address_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_evaluation`
  MODIFY `evaluation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_group`
  MODIFY `supplier_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_invoice`
  MODIFY `invoice_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_invoice_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_invoice_item`
  MODIFY `invoice_item_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_supplier_product_price`
  MODIFY `price_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_system_events`
  MODIFY `event_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_system_notifications`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_task`
  MODIFY `task_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_tax_class`
  MODIFY `tax_class_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_tax_rate`
  MODIFY `tax_rate_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_tax_rule`
  MODIFY `tax_rule_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_theme`
  MODIFY `theme_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_translation`
  MODIFY `translation_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unavailability_reasons`
  MODIFY `reason_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unified_document`
  MODIFY `document_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unified_notification`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unified_workflow`
  MODIFY `workflow_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unit`
  MODIFY `unit_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unit_conversion_history`
  MODIFY `conversion_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_unit_conversion_log`
  MODIFY `conversion_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_upload`
  MODIFY `upload_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_2fa_attempts`
  MODIFY `attempt_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_activity_log`
  MODIFY `activity_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_group`
  MODIFY `user_group_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_kpi_assignment`
  MODIFY `assignment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_login_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_notification_preferences`
  MODIFY `preference_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_session`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_trusted_devices`
  MODIFY `device_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_user_verification_codes`
  MODIFY `code_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_vendor_payment`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_virtual_inventory_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_voucher`
  MODIFY `voucher_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_voucher_history`
  MODIFY `voucher_history_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_voucher_theme`
  MODIFY `voucher_theme_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_warranty`
  MODIFY `warranty_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_weight_class`
  MODIFY `weight_class_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_workflow_approval`
  MODIFY `approval_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_workflow_request`
  MODIFY `request_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_workflow_step`
  MODIFY `step_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_zone`
  MODIFY `zone_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_zone_distance`
  MODIFY `zone_distance_id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `cod_zone_to_geo_zone`
  MODIFY `zone_to_geo_zone_id` int(11) NOT NULL AUTO_INCREMENT;


ALTER TABLE `cod_activity_log`
  ADD CONSTRAINT `fk_activity_log_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_announcement`
  ADD CONSTRAINT `fk_announcement_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_announcement_attachment`
  ADD CONSTRAINT `fk_attachment_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `cod_announcement` (`announcement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_attachment_uploader` FOREIGN KEY (`uploaded_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_announcement_comment`
  ADD CONSTRAINT `fk_comment_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `cod_announcement` (`announcement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_comment_parent` FOREIGN KEY (`parent_comment_id`) REFERENCES `cod_announcement_comment` (`comment_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_comment_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_announcement_view`
  ADD CONSTRAINT `fk_view_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `cod_announcement` (`announcement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_view_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_attendance`
  ADD CONSTRAINT `cod_attendance_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_audit_plan`
  ADD CONSTRAINT `fk_audit_plan_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_audit_plan_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_audit_task`
  ADD CONSTRAINT `fk_audit_task_assigned` FOREIGN KEY (`assigned_to`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_audit_task_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_audit_task_plan` FOREIGN KEY (`plan_id`) REFERENCES `cod_audit_plan` (`plan_id`) ON DELETE SET NULL;

ALTER TABLE `cod_bank_reconciliation`
  ADD CONSTRAINT `fk_cod_bank_reconciliation_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE CASCADE;

ALTER TABLE `cod_bank_transaction`
  ADD CONSTRAINT `fk_cod_bank_transaction_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE CASCADE;

ALTER TABLE `cod_budget`
  ADD CONSTRAINT `fk_budget_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_budget_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_budget_line`
  ADD CONSTRAINT `fk_budget_line_account` FOREIGN KEY (`account_code`) REFERENCES `cod_accounts` (`account_code`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_budget_line_budget` FOREIGN KEY (`budget_id`) REFERENCES `cod_budget` (`budget_id`) ON DELETE CASCADE;

ALTER TABLE `cod_bundle_performance_analysis`
  ADD CONSTRAINT `cod_bundle_performance_analysis_ibfk_1` FOREIGN KEY (`bundle_id`) REFERENCES `cod_product_bundle` (`bundle_id`) ON DELETE CASCADE;

ALTER TABLE `cod_bundle_usage_log`
  ADD CONSTRAINT `cod_bundle_usage_log_ibfk_1` FOREIGN KEY (`bundle_id`) REFERENCES `cod_product_bundle` (`bundle_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_bundle_usage_log_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE SET NULL;

ALTER TABLE `cod_cash_transaction`
  ADD CONSTRAINT `fk_cod_cash_transaction_cash` FOREIGN KEY (`cash_id`) REFERENCES `cod_cash` (`cash_id`) ON DELETE CASCADE;

ALTER TABLE `cod_checks`
  ADD CONSTRAINT `fk_cod_checks_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_checks_created_by` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_checks_currency` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_checks_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `cod_compliance_record`
  ADD CONSTRAINT `cod_compliance_record_fk_user` FOREIGN KEY (`responsible_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_campaign`
  ADD CONSTRAINT `cod_crm_campaign_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_contact`
  ADD CONSTRAINT `cod_crm_contact_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_deal`
  ADD CONSTRAINT `cod_crm_deal_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_lead`
  ADD CONSTRAINT `cod_crm_lead_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_crm_opportunity`
  ADD CONSTRAINT `cod_crm_opportunity_fk_lead` FOREIGN KEY (`lead_id`) REFERENCES `cod_crm_lead` (`lead_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `cod_crm_opportunity_fk_user` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_currency_rate_history`
  ADD CONSTRAINT `fk_cod_rate_history_currency` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_rate_history_user` FOREIGN KEY (`changed_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `cod_customer_credit_limit`
  ADD CONSTRAINT `fk_credit_limit_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_credit_limit_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE CASCADE;

ALTER TABLE `cod_customer_feedback`
  ADD CONSTRAINT `fk_feedback_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_feedback_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE CASCADE;

ALTER TABLE `cod_customer_return_inventory`
  ADD CONSTRAINT `fk_return_inventory_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`),
  ADD CONSTRAINT `fk_return_inventory_journal` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_return_inventory_movement` FOREIGN KEY (`movement_id`) REFERENCES `cod_product_movement` (`product_movement_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_return_inventory_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `fk_return_inventory_return` FOREIGN KEY (`return_id`) REFERENCES `cod_return` (`return_id`) ON DELETE CASCADE;

ALTER TABLE `cod_custom_report`
  ADD CONSTRAINT `fk_custom_report_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_data_access_control`
  ADD CONSTRAINT `fk_access_grantor` FOREIGN KEY (`granted_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_access_group` FOREIGN KEY (`user_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_access_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_document_permission`
  ADD CONSTRAINT `fk_doc_permission_document` FOREIGN KEY (`document_id`) REFERENCES `cod_unified_document` (`document_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_doc_permission_grantor` FOREIGN KEY (`granted_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_doc_permission_group` FOREIGN KEY (`user_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_doc_permission_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_employee_advance`
  ADD CONSTRAINT `fk_cod_employee_advance_created_by` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_employee_advance_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cod_employee_advance_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_employee_advance_installment`
  ADD CONSTRAINT `fk_cod_employee_advance_installment_advance` FOREIGN KEY (`advance_id`) REFERENCES `cod_employee_advance` (`advance_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_employee_documents`
  ADD CONSTRAINT `cod_employee_documents_fk_employee` FOREIGN KEY (`employee_id`) REFERENCES `cod_employee_profile` (`employee_id`) ON DELETE CASCADE;

ALTER TABLE `cod_employee_profile`
  ADD CONSTRAINT `cod_employee_profile_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_eta_document_lines`
  ADD CONSTRAINT `fk_eta_line_document` FOREIGN KEY (`document_id`) REFERENCES `cod_eta_documents` (`document_id`) ON DELETE CASCADE;

ALTER TABLE `cod_feedback_history`
  ADD CONSTRAINT `fk_feedback_history_feedback` FOREIGN KEY (`feedback_id`) REFERENCES `cod_customer_feedback` (`feedback_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_feedback_history_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_feedback_message`
  ADD CONSTRAINT `fk_feedback_message_feedback` FOREIGN KEY (`feedback_id`) REFERENCES `cod_customer_feedback` (`feedback_id`) ON DELETE CASCADE;

ALTER TABLE `cod_feedback_template`
  ADD CONSTRAINT `fk_feedback_template_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_financial_forecast`
  ADD CONSTRAINT `fk_financial_forecast_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_fixed_assets`
  ADD CONSTRAINT `cod_fixed_assets_ibfk_1` FOREIGN KEY (`asset_type_id`) REFERENCES `cod_asset_types` (`asset_type_id`);

ALTER TABLE `cod_goods_receipt`
  ADD CONSTRAINT `cod_goods_receipt_ibfk_1` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `cod_goods_receipt_ibfk_2` FOREIGN KEY (`quality_checked_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_goods_receipt_item`
  ADD CONSTRAINT `fk_goods_receipt_item_po_item` FOREIGN KEY (`po_item_id`) REFERENCES `cod_purchase_order_item` (`po_item_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_governance_issue`
  ADD CONSTRAINT `fk_governance_issue_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_governance_issue_group` FOREIGN KEY (`responsible_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_governance_issue_user` FOREIGN KEY (`responsible_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_governance_meeting`
  ADD CONSTRAINT `cod_governance_meeting_fk_user` FOREIGN KEY (`added_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_installment_payment`
  ADD CONSTRAINT `fk_installment_payment_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_payment_receiver` FOREIGN KEY (`received_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_payment_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `cod_installment_schedule` (`schedule_id`) ON DELETE CASCADE;

ALTER TABLE `cod_installment_plan`
  ADD CONSTRAINT `fk_installment_plan_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_plan_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`),
  ADD CONSTRAINT `fk_installment_plan_guarantor` FOREIGN KEY (`guarantor_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_plan_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_installment_plan_template` FOREIGN KEY (`template_id`) REFERENCES `cod_installment_plan_template` (`template_id`) ON DELETE SET NULL;

ALTER TABLE `cod_installment_plan_template`
  ADD CONSTRAINT `fk_installment_template_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_installment_reminder`
  ADD CONSTRAINT `fk_installment_reminder_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_installment_reminder_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `cod_installment_schedule` (`schedule_id`) ON DELETE CASCADE;

ALTER TABLE `cod_installment_schedule`
  ADD CONSTRAINT `fk_installment_schedule_plan` FOREIGN KEY (`plan_id`) REFERENCES `cod_installment_plan` (`plan_id`) ON DELETE CASCADE;

ALTER TABLE `cod_internal_attachment`
  ADD CONSTRAINT `fk_attachment_message` FOREIGN KEY (`message_id`) REFERENCES `cod_internal_message` (`message_id`) ON DELETE CASCADE;

ALTER TABLE `cod_internal_audit`
  ADD CONSTRAINT `fk_internal_audit_auditor` FOREIGN KEY (`auditor_user_id`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_internal_conversation`
  ADD CONSTRAINT `fk_conversation_creator` FOREIGN KEY (`creator_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_internal_message`
  ADD CONSTRAINT `fk_message_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `cod_internal_conversation` (`conversation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_message_parent` FOREIGN KEY (`parent_message_id`) REFERENCES `cod_internal_message` (`message_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_message_sender` FOREIGN KEY (`sender_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_internal_participant`
  ADD CONSTRAINT `fk_participant_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `cod_internal_conversation` (`conversation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_participant_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_abc_analysis`
  ADD CONSTRAINT `fk_abc_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_abc_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_abc_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_accounting_reconciliation`
  ADD CONSTRAINT `fk_inventory_recon_approver` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_inventory_recon_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`),
  ADD CONSTRAINT `fk_inventory_recon_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_accounting_reconciliation_item`
  ADD CONSTRAINT `fk_recon_item_journal` FOREIGN KEY (`adjustment_journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_recon_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `fk_recon_item_reconciliation` FOREIGN KEY (`reconciliation_id`) REFERENCES `cod_inventory_accounting_reconciliation` (`reconciliation_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_account_mapping`
  ADD CONSTRAINT `fk_inventory_mapping_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_alert`
  ADD CONSTRAINT `fk_inventory_alert_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inventory_alert_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inventory_alert_user` FOREIGN KEY (`acknowledged_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_inventory_cost_update`
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`),
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `cod_inventory_cost_update_ibfk_4` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`);

ALTER TABLE `cod_inventory_reservation`
  ADD CONSTRAINT `fk_reservation_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_reservation_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_reservation_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_reservation_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_role_permissions`
  ADD CONSTRAINT `cod_inventory_role_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_role_permissions_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_role_permissions_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_sync_rules`
  ADD CONSTRAINT `cod_inventory_sync_rules_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_sync_rules_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_sync_rules_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_inventory_turnover`
  ADD CONSTRAINT `fk_turnover_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_turnover_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_turnover_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_inventory_turnover_analysis`
  ADD CONSTRAINT `cod_inventory_turnover_analysis_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_turnover_analysis_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_inventory_turnover_analysis_ibfk_3` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE;

ALTER TABLE `cod_journal_attachments`
  ADD CONSTRAINT `cod_journal_attachments_ibfk_1` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE CASCADE;

ALTER TABLE `cod_journal_entries`
  ADD CONSTRAINT `cod_journal_entries_ibfk_1` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`) ON DELETE CASCADE;

ALTER TABLE `cod_leave_request`
  ADD CONSTRAINT `cod_leave_request_fk_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `cod_leave_request_fk_leave_type` FOREIGN KEY (`leave_type_id`) REFERENCES `cod_leave_type` (`leave_type_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_leave_request_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_meeting_attendees`
  ADD CONSTRAINT `cod_meeting_attendees_fk_meeting` FOREIGN KEY (`meeting_id`) REFERENCES `cod_governance_meeting` (`meeting_id`) ON DELETE CASCADE;

ALTER TABLE `cod_notification_automation`
  ADD CONSTRAINT `fk_automation_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_automation_template` FOREIGN KEY (`notification_template_id`) REFERENCES `cod_notification_template` (`template_id`);

ALTER TABLE `cod_notification_automation_log`
  ADD CONSTRAINT `fk_log_automation_rule` FOREIGN KEY (`rule_id`) REFERENCES `cod_notification_automation` (`rule_id`) ON DELETE CASCADE;

ALTER TABLE `cod_notification_template`
  ADD CONSTRAINT `fk_template_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_order_history`
  ADD CONSTRAINT `fk_orderhistory_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_orderhistory_status` FOREIGN KEY (`order_status_id`) REFERENCES `cod_order_status` (`order_status_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_order_product`
  ADD CONSTRAINT `fk_orderproduct_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_orderproduct_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_order_shipment_history`
  ADD CONSTRAINT `fk_shipment_history_shipment` FOREIGN KEY (`order_shipment_id`) REFERENCES `cod_order_shipment` (`order_shipment_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_shipment_history_user` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_payment_gateway_config`
  ADD CONSTRAINT `fk_gateway_config_gateway` FOREIGN KEY (`gateway_id`) REFERENCES `cod_payment_gateway` (`gateway_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_gateway_config_updater` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_payment_invoice`
  ADD CONSTRAINT `fk_invoice_id_payment` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`),
  ADD CONSTRAINT `fk_payment_id` FOREIGN KEY (`payment_id`) REFERENCES `cod_vendor_payment` (`payment_id`) ON DELETE CASCADE;

ALTER TABLE `cod_payment_settlement`
  ADD CONSTRAINT `fk_payment_settlement_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `cod_bank_account` (`account_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_settlement_bank_tx` FOREIGN KEY (`bank_transaction_id`) REFERENCES `cod_bank_transaction` (`bank_transaction_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_settlement_gateway` FOREIGN KEY (`gateway_id`) REFERENCES `cod_payment_gateway` (`gateway_id`),
  ADD CONSTRAINT `fk_payment_settlement_user` FOREIGN KEY (`reconciled_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_payment_settlement_transaction`
  ADD CONSTRAINT `fk_settlement_transaction_settlement` FOREIGN KEY (`settlement_id`) REFERENCES `cod_payment_settlement` (`settlement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_settlement_transaction_tx` FOREIGN KEY (`transaction_id`) REFERENCES `cod_payment_transaction` (`transaction_id`) ON DELETE CASCADE;

ALTER TABLE `cod_payment_transaction`
  ADD CONSTRAINT `fk_payment_transaction_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`),
  ADD CONSTRAINT `fk_payment_transaction_gateway` FOREIGN KEY (`gateway_id`) REFERENCES `cod_payment_gateway` (`gateway_id`),
  ADD CONSTRAINT `fk_payment_transaction_installment` FOREIGN KEY (`installment_payment_id`) REFERENCES `cod_installment_payment` (`payment_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_transaction_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_payment_transaction_reference` FOREIGN KEY (`reference_transaction_id`) REFERENCES `cod_payment_transaction` (`transaction_id`) ON DELETE SET NULL;

ALTER TABLE `cod_payroll_entry`
  ADD CONSTRAINT `cod_payroll_entry_fk_period` FOREIGN KEY (`payroll_period_id`) REFERENCES `cod_payroll_period` (`payroll_period_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_payroll_entry_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_performance_review`
  ADD CONSTRAINT `cod_performance_review_fk_reviewer` FOREIGN KEY (`reviewer_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_performance_review_fk_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_performance_review_criteria`
  ADD CONSTRAINT `cod_performance_review_criteria_fk_criteria` FOREIGN KEY (`criteria_id`) REFERENCES `cod_performance_criteria` (`criteria_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_performance_review_criteria_fk_review` FOREIGN KEY (`review_id`) REFERENCES `cod_performance_review` (`review_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_batch`
  ADD CONSTRAINT `fk_batch_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_batch_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_batch_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_product_inventory`
  ADD CONSTRAINT `fk_cod_product_inventory_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_movement`
  ADD CONSTRAINT `fk_cod_product_movement_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_price_history`
  ADD CONSTRAINT `fk_price_history_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_price_history_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`),
  ADD CONSTRAINT `fk_price_history_user` FOREIGN KEY (`changed_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_product_pricing`
  ADD CONSTRAINT `fk_cod_product_pricing_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_quantity_discounts`
  ADD CONSTRAINT `cod_product_quantity_discounts_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_product_unit`
  ADD CONSTRAINT `fk_cod_product_unit_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_purchase_matching`
  ADD CONSTRAINT `fk_matching_invoice` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_matching_po` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`),
  ADD CONSTRAINT `fk_matching_receipt` FOREIGN KEY (`receipt_id`) REFERENCES `cod_goods_receipt` (`goods_receipt_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_matching_user` FOREIGN KEY (`matched_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_purchase_matching_item`
  ADD CONSTRAINT `fk_matching_item_invoice` FOREIGN KEY (`invoice_item_id`) REFERENCES `cod_supplier_invoice_item` (`invoice_item_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_matching_item_matching` FOREIGN KEY (`matching_id`) REFERENCES `cod_purchase_matching` (`matching_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_matching_item_po` FOREIGN KEY (`po_item_id`) REFERENCES `cod_purchase_order_item` (`po_item_id`),
  ADD CONSTRAINT `fk_matching_item_receipt` FOREIGN KEY (`receipt_item_id`) REFERENCES `cod_goods_receipt_item` (`receipt_item_id`) ON DELETE SET NULL;

ALTER TABLE `cod_purchase_order`
  ADD CONSTRAINT `cod_purchase_order_ibfk_2` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `cod_purchase_order_ibfk_3` FOREIGN KEY (`financial_approved_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_po_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `cod_supplier` (`supplier_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_order_history`
  ADD CONSTRAINT `fk_po_history_po` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_order_item`
  ADD CONSTRAINT `cod_purchase_order_item_ibfk_1` FOREIGN KEY (`cost_updated_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_po_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_po_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_order_tracking`
  ADD CONSTRAINT `fk_po_tracking_po` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_po_tracking_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_purchase_price_variance`
  ADD CONSTRAINT `fk_variance_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_variance_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE;

ALTER TABLE `cod_purchase_quotation`
  ADD CONSTRAINT `fk_quotation_financial_approval` FOREIGN KEY (`financial_approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_quotation_technical_approval` FOREIGN KEY (`technical_approved_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_purchase_quotation_item`
  ADD CONSTRAINT `cod_purchase_quotation_item_ibfk_1` FOREIGN KEY (`quotation_id`) REFERENCES `cod_purchase_quotation` (`quotation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_purchase_quotation_item_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `cod_purchase_quotation_item_ibfk_3` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`),
  ADD CONSTRAINT `fk_quotation_item_alt_product` FOREIGN KEY (`alternative_product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_quotation_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_quotation_item_quotation` FOREIGN KEY (`quotation_id`) REFERENCES `cod_purchase_quotation` (`quotation_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_quotation_item_requisition_item` FOREIGN KEY (`requisition_item_id`) REFERENCES `cod_purchase_requisition_item` (`requisition_item_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_quotation_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_purchase_return`
  ADD CONSTRAINT `fk_purchase_return_po` FOREIGN KEY (`purchase_order_id`) REFERENCES `cod_purchase_order` (`po_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_purchase_return_receipt` FOREIGN KEY (`goods_receipt_id`) REFERENCES `cod_goods_receipt` (`goods_receipt_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_purchase_return_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `cod_supplier` (`supplier_id`),
  ADD CONSTRAINT `fk_purchase_return_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_purchase_return_item`
  ADD CONSTRAINT `fk_return_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`),
  ADD CONSTRAINT `fk_return_item_return` FOREIGN KEY (`return_id`) REFERENCES `cod_purchase_return` (`return_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_return_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`);

ALTER TABLE `cod_quotation_signatures`
  ADD CONSTRAINT `fk_quotation_signatures_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_report_execution_log`
  ADD CONSTRAINT `fk_report_execution_report` FOREIGN KEY (`report_id`) REFERENCES `cod_scheduled_report` (`report_id`) ON DELETE CASCADE;

ALTER TABLE `cod_risk_register`
  ADD CONSTRAINT `cod_risk_register_fk_owner` FOREIGN KEY (`owner_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_sales_forecast`
  ADD CONSTRAINT `fk_forecast_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_forecast_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_forecast_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_sales_quotation`
  ADD CONSTRAINT `fk_sales_quotation_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales_quotation_customer` FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales_quotation_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_sales_quotation_item`
  ADD CONSTRAINT `fk_sales__quotation_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales__quotation_item_quotation` FOREIGN KEY (`quotation_id`) REFERENCES `cod_sales_quotation` (`quotation_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sales__quotation_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE;

ALTER TABLE `cod_scheduled_report`
  ADD CONSTRAINT `fk_scheduled_report_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_shipping_company_config`
  ADD CONSTRAINT `fk_shipping_config_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_config_updater` FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_shipping_coverage`
  ADD CONSTRAINT `fk_shipping_coverage_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_coverage_country` FOREIGN KEY (`country_id`) REFERENCES `cod_country` (`country_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_coverage_geo_zone` FOREIGN KEY (`geo_zone_id`) REFERENCES `cod_geo_zone` (`geo_zone_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_coverage_zone` FOREIGN KEY (`zone_id`) REFERENCES `cod_zone` (`zone_id`) ON DELETE SET NULL;

ALTER TABLE `cod_shipping_order`
  ADD CONSTRAINT `fk_shipping_order_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`),
  ADD CONSTRAINT `fk_shipping_order_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_shipping_order_order` FOREIGN KEY (`order_id`) REFERENCES `cod_order` (`order_id`) ON DELETE CASCADE;

ALTER TABLE `cod_shipping_rate`
  ADD CONSTRAINT `fk_shipping_rate_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_rate_coverage` FOREIGN KEY (`coverage_id`) REFERENCES `cod_shipping_coverage` (`coverage_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_rate_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_shipping_settlement`
  ADD CONSTRAINT `fk_shipping_settlement_bank` FOREIGN KEY (`bank_transaction_id`) REFERENCES `cod_bank_transaction` (`bank_transaction_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_shipping_settlement_company` FOREIGN KEY (`company_id`) REFERENCES `cod_shipping_company` (`company_id`),
  ADD CONSTRAINT `fk_shipping_settlement_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_shipping_settlement_reconciler` FOREIGN KEY (`reconciled_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_shipping_settlement_order`
  ADD CONSTRAINT `fk_settlement_order_settlement` FOREIGN KEY (`settlement_id`) REFERENCES `cod_shipping_settlement` (`settlement_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_settlement_order_shipping` FOREIGN KEY (`shipping_order_id`) REFERENCES `cod_shipping_order` (`shipping_order_id`);

ALTER TABLE `cod_shipping_tracking`
  ADD CONSTRAINT `fk_shipping_tracking_order` FOREIGN KEY (`shipping_order_id`) REFERENCES `cod_shipping_order` (`shipping_order_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_shipping_tracking_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_stock_count`
  ADD CONSTRAINT `fk_stock_count_branch` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_stock_count_user` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_stock_count_item`
  ADD CONSTRAINT `fk_stock_count_item_count` FOREIGN KEY (`stock_count_id`) REFERENCES `cod_stock_count` (`stock_count_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_stock_count_item_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_stock_count_item_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON UPDATE CASCADE;

ALTER TABLE `cod_supplier_invoice`
  ADD CONSTRAINT `fk_po_id_invoice` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`);

ALTER TABLE `cod_supplier_invoice_item`
  ADD CONSTRAINT `fk_invoice_id` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`) ON DELETE CASCADE;

ALTER TABLE `cod_supplier_product_price`
  ADD CONSTRAINT `fk_supplier_price_currency` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `fk_supplier_price_product` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_supplier_price_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `cod_supplier` (`supplier_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_supplier_price_unit` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`);

ALTER TABLE `cod_system_notifications`
  ADD CONSTRAINT `fk_notification_group` FOREIGN KEY (`user_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_notification_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_task`
  ADD CONSTRAINT `fk_task_assigned_by` FOREIGN KEY (`assigned_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_task_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_unified_document`
  ADD CONSTRAINT `fk_document_creator` FOREIGN KEY (`creator_id`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_document_parent` FOREIGN KEY (`parent_document_id`) REFERENCES `cod_unified_document` (`document_id`) ON DELETE SET NULL;

ALTER TABLE `cod_unified_notification`
  ADD CONSTRAINT `fk_notification_creator` FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_unified_workflow`
  ADD CONSTRAINT `fk_workflow_creator` FOREIGN KEY (`creator_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_unit_conversion_log`
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_2` FOREIGN KEY (`from_unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_3` FOREIGN KEY (`to_unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_unit_conversion_log_ibfk_4` FOREIGN KEY (`converted_by`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL;

ALTER TABLE `cod_user_2fa_attempts`
  ADD CONSTRAINT `cod_user_2fa_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_activity_log`
  ADD CONSTRAINT `fk_user_activity_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_user_kpi_assignment`
  ADD CONSTRAINT `fk_user_kpi_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_user_login_log`
  ADD CONSTRAINT `fk_login_log_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_notification_preferences`
  ADD CONSTRAINT `fk_preferences_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_session`
  ADD CONSTRAINT `fk_user_session_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cod_user_trusted_devices`
  ADD CONSTRAINT `cod_user_trusted_devices_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_user_verification_codes`
  ADD CONSTRAINT `cod_user_verification_codes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_vendor_payment`
  ADD CONSTRAINT `cod_vendor_payment_ibfk_1` FOREIGN KEY (`invoice_id`) REFERENCES `cod_supplier_invoice` (`invoice_id`),
  ADD CONSTRAINT `cod_vendor_payment_ibfk_2` FOREIGN KEY (`po_id`) REFERENCES `cod_purchase_order` (`po_id`),
  ADD CONSTRAINT `cod_vendor_payment_ibfk_3` FOREIGN KEY (`currency_id`) REFERENCES `cod_currency` (`currency_id`),
  ADD CONSTRAINT `cod_vendor_payment_ibfk_4` FOREIGN KEY (`journal_id`) REFERENCES `cod_journals` (`journal_id`);

ALTER TABLE `cod_virtual_inventory_log`
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_3` FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cod_virtual_inventory_log_ibfk_4` FOREIGN KEY (`changed_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE;

ALTER TABLE `cod_workflow_approval`
  ADD CONSTRAINT `fk_approval_request` FOREIGN KEY (`request_id`) REFERENCES `cod_workflow_request` (`request_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_approval_step` FOREIGN KEY (`step_id`) REFERENCES `cod_workflow_step` (`step_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_approval_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_workflow_approval_delegate` FOREIGN KEY (`delegated_to`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_approval_request` FOREIGN KEY (`request_id`) REFERENCES `cod_workflow_request` (`request_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_workflow_approval_step` FOREIGN KEY (`step_id`) REFERENCES `cod_workflow_step` (`step_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_workflow_approval_user` FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`);

ALTER TABLE `cod_workflow_request`
  ADD CONSTRAINT `fk_workflow_request_requester` FOREIGN KEY (`requester_id`) REFERENCES `cod_user` (`user_id`),
  ADD CONSTRAINT `fk_workflow_request_step` FOREIGN KEY (`current_step_id`) REFERENCES `cod_workflow_step` (`step_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_request_workflow` FOREIGN KEY (`workflow_id`) REFERENCES `cod_unified_workflow` (`workflow_id`);

ALTER TABLE `cod_workflow_step`
  ADD CONSTRAINT `fk_workflow_step_group` FOREIGN KEY (`approver_group_id`) REFERENCES `cod_user_group` (`user_group_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_step_user` FOREIGN KEY (`approver_user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_workflow_step_workflow` FOREIGN KEY (`workflow_id`) REFERENCES `cod_unified_workflow` (`workflow_id`) ON DELETE CASCADE;