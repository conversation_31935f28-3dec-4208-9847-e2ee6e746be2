📄 Route: pos/shift
📂 Controller: controller\pos\shift.php
🧱 Models used (4):
   ✅ pos/shift (9 functions)
   ✅ branch/branch (7 functions)
   ✅ pos/terminal (7 functions)
   ✅ pos/transaction (8 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\pos\shift.php (72 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\pos\shift.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (17):
   - datetime_format
   - error_active_shift_exists
   - error_branch
   - error_ending_cash
   - error_invalid_shift
   - error_no_active_shift
   - error_permission
   - error_starting_cash
   - error_terminal
   - heading_title
   - heading_title_end
   - heading_title_start
   - heading_title_view
   - text_home
   - text_pagination
   - text_success_end
   - text_success_start

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (17):
   - datetime_format
   - error_active_shift_exists
   - error_ending_cash
   - error_no_active_shift
   - error_permission
   - error_starting_cash
   - error_terminal
   - heading_title
   - heading_title_end
   - heading_title_start
   - heading_title_view
   - text_home
   - text_pagination
   - text_success_end
   - text_success_start
   ... و 2 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 17 items
      - heading_title_end
      - heading_title_start
      - heading_title_view
      - text_home
      - error_no_active_shift

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 17 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:13
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.