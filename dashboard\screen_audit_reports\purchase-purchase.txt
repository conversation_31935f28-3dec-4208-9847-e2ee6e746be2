📄 Route: purchase/purchase
📂 Controller: controller\purchase\purchase.php
🧱 Models used (2):
   ✅ catalog/product (128 functions)
   ✅ purchase/purchase (90 functions)
🎨 Twig templates (1):
   ✅ view\template\purchase\purchase.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\purchase\purchase.php (233 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\purchase\purchase.php (260 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (267):
   - button_approve
   - column_consignment_supplier
   - column_goods_receipt_id
   - column_left
   - column_status
   - entry_date_start
   - entry_payment_date
   - tab_goods_receipt
   - tab_quotation
   - text_approved
   - text_edit_invoice
   - text_edit_stock_transfer
   - text_failed
   - text_from_branch
   - text_manage_quotations
   - text_open_ledger
   - text_select_at_least_two_to_compare
   - text_today
   - text_top_suppliers
   - text_view_details
   ... و 247 متغير آخر

❌ Missing in Arabic (124):
   - column_left
   - column_to_branch
   - entry_order_date
   - entry_payment_date
   - error_transfer_not_found
   - text_convert_to_po
   - text_decrease
   - text_enter_reject_reason
   - text_failed
   - text_from_branch
   - text_increase
   - text_inspection_date
   - text_manage_quotations
   - text_priority_high
   - text_select_at_least_two_to_compare
   ... و 109 متغير آخر

❌ Missing in English (121):
   - column_left
   - entry_order_date
   - error_transfer_not_found
   - text_confirm_delete
   - text_convert_to_po
   - text_decrease
   - text_edit_stock_transfer
   - text_enter_reject_reason
   - text_failed
   - text_from_branch
   - text_increase
   - text_inspection_date
   - text_manage_quotations
   - text_priority_high
   - text_select_at_least_two_to_compare
   ... و 106 متغير آخر

🗄️ Database Tables Used (5):
   ❌ Branch
   ❌ statements
   ❌ stock
   ❌ to
   ❌ vendor

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 124 items
      - text_manage_quotations
      - text_convert_to_po
      - text_select_at_least_two_to_compare
      - text_decrease
      - text_from_branch
   🟡 MISSING_ENGLISH_VARIABLES: 121 items
      - text_manage_quotations
      - text_convert_to_po
      - text_select_at_least_two_to_compare
      - text_decrease
      - text_from_branch
   🔴 INVALID_DATABASE_TABLES: 5 items
      - to
      - stock
      - vendor
      - Branch
      - statements

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 124 متغير عربي و 121 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 5 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:15
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.