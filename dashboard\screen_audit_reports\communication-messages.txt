📄 Route: communication/messages
📂 Controller: controller\communication\messages.php
🧱 Models used (4):
   ✅ communication/messages (14 functions)
   ✅ user/user (47 functions)
   ✅ communication/teams (17 functions)
   ✅ core/central_service_manager (60 functions)
🎨 Twig templates (1):
   ✅ view\template\communication\messages.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\communication\messages.php (195 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\communication\messages.php (195 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (93):
   - action
   - deleted_messages
   - error_compose
   - error_deleted_total
   - error_inbox_messages
   - error_inbox_unread
   - error_message
   - error_permission
   - error_users
   - inbox_total
   - refresh
   - text_back
   - text_compose
   - text_deleted_total
   - text_priorities
   - text_replies
   - text_sent_total
   - text_type_catalog
   - text_type_general
   - text_type_purchase
   ... و 73 متغير آخر

❌ Missing in Arabic (81):
   - action
   - column_left
   - deleted_messages
   - error_back
   - error_inbox_messages
   - error_inbox_unread
   - error_users
   - text_inbox_unread
   - text_replies
   - text_type_catalog
   - text_type_finance
   - text_type_general
   - text_type_inventory
   - text_type_purchase
   - text_user_token
   ... و 66 متغير آخر

❌ Missing in English (81):
   - action
   - column_left
   - deleted_messages
   - error_back
   - error_inbox_messages
   - error_inbox_unread
   - error_users
   - text_inbox_unread
   - text_replies
   - text_type_catalog
   - text_type_finance
   - text_type_general
   - text_type_inventory
   - text_type_purchase
   - text_user_token
   ... و 66 متغير آخر

🗄️ Database Tables Used (15):
   ❌ cod_document
   ✅ cod_document_permission
   ❌ cod_project
   ✅ cod_task
   ❌ cod_team
   ❌ cod_team_member
   ✅ cod_unified_workflow
   ✅ cod_user
   ✅ cod_user_group
   ❌ cod_user_to_group
   ✅ cod_workflow_approval
   ✅ cod_workflow_request
   ✅ cod_workflow_step
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 81 items
      - text_type_catalog
      - text_replies
      - action
      - error_users
      - error_inbox_unread
   🟡 MISSING_ENGLISH_VARIABLES: 81 items
      - text_type_catalog
      - text_replies
      - action
      - error_users
      - error_inbox_unread
   🔴 INVALID_DATABASE_TABLES: 7 items
      - cod_project
      - workflow
      - template
      - cod_document
      - cod_team

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 81 متغير عربي و 81 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 7 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:55
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.