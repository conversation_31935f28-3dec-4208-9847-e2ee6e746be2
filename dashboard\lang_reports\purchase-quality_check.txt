📄 Route: purchase/quality_check
📂 Controller: controller\purchase\quality_check.php
🧱 Models used (2):
   - purchase/goods_receipt
   - purchase/quality_check
🎨 Twig templates (1):
   - view\template\purchase\quality_check.twig
🈯 Arabic Language Files (1):
   - language\ar\purchase\quality_check.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_back
   - button_check
   - button_filter
   - button_reset
   - button_save
   - column_accepted
   - column_action
   - column_notes
   - column_po_number
   - column_product
   - column_quality_status
   - column_quantity
   - column_receipt_date
   - column_receipt_number
   - column_supplier
   - column_unit
   - date_format_short
   - entry_date
   - entry_notes
   - entry_po_number
   - entry_quality_notes
   - entry_quality_status
   - entry_receipt_number
   - entry_supplier
   - error_already_checked
   - error_already_received
   - error_item
   - error_item_not_found
   - error_percentage
   - error_permission
   - error_quality_check_not_required
   - error_quality_check_required
   - error_receipt
   - error_receipt_not_found
   - error_status
   - heading_title
   - heading_title_form
   - text_all_status
   - text_date_from
   - text_date_to
   - text_filter
   - text_form
   - text_history_completed
   - text_history_qc
   - text_home
   - text_item_list
   - text_item_updated
   - text_list
   - text_loading
   - text_no_results
   - text_pagination
   - text_qc_failed
   - text_qc_passed
   - text_qc_pending
   - text_quality_check
   - text_quality_check_completed
   - text_quality_status_fail
   - text_quality_status_partial
   - text_quality_status_pass
   - text_receipt_details
   - text_select
   - text_status_cancelled
   - text_status_partially_received
   - text_status_pending
   - text_status_received
   - text_success

❌ Missing in Arabic:
   - button_back
   - button_check
   - button_filter
   - button_reset
   - button_save
   - column_accepted
   - column_action
   - column_notes
   - column_po_number
   - column_product
   - column_quality_status
   - column_quantity
   - column_receipt_date
   - column_receipt_number
   - column_supplier
   - column_unit
   - date_format_short
   - entry_date
   - entry_notes
   - entry_po_number
   - entry_quality_notes
   - entry_quality_status
   - entry_receipt_number
   - entry_supplier
   - error_already_checked
   - error_already_received
   - error_item
   - error_item_not_found
   - error_percentage
   - error_permission
   - error_quality_check_not_required
   - error_quality_check_required
   - error_receipt
   - error_receipt_not_found
   - error_status
   - heading_title
   - heading_title_form
   - text_all_status
   - text_date_from
   - text_date_to
   - text_filter
   - text_form
   - text_history_completed
   - text_history_qc
   - text_home
   - text_item_list
   - text_item_updated
   - text_list
   - text_loading
   - text_no_results
   - text_pagination
   - text_qc_failed
   - text_qc_passed
   - text_qc_pending
   - text_quality_check
   - text_quality_check_completed
   - text_quality_status_fail
   - text_quality_status_partial
   - text_quality_status_pass
   - text_receipt_details
   - text_select
   - text_status_cancelled
   - text_status_partially_received
   - text_status_pending
   - text_status_received
   - text_success

❌ Missing in English:
   - button_back
   - button_check
   - button_filter
   - button_reset
   - button_save
   - column_accepted
   - column_action
   - column_notes
   - column_po_number
   - column_product
   - column_quality_status
   - column_quantity
   - column_receipt_date
   - column_receipt_number
   - column_supplier
   - column_unit
   - date_format_short
   - entry_date
   - entry_notes
   - entry_po_number
   - entry_quality_notes
   - entry_quality_status
   - entry_receipt_number
   - entry_supplier
   - error_already_checked
   - error_already_received
   - error_item
   - error_item_not_found
   - error_percentage
   - error_permission
   - error_quality_check_not_required
   - error_quality_check_required
   - error_receipt
   - error_receipt_not_found
   - error_status
   - heading_title
   - heading_title_form
   - text_all_status
   - text_date_from
   - text_date_to
   - text_filter
   - text_form
   - text_history_completed
   - text_history_qc
   - text_home
   - text_item_list
   - text_item_updated
   - text_list
   - text_loading
   - text_no_results
   - text_pagination
   - text_qc_failed
   - text_qc_passed
   - text_qc_pending
   - text_quality_check
   - text_quality_check_completed
   - text_quality_status_fail
   - text_quality_status_partial
   - text_quality_status_pass
   - text_receipt_details
   - text_select
   - text_status_cancelled
   - text_status_partially_received
   - text_status_pending
   - text_status_received
   - text_success

💡 Suggested Arabic Additions:
   - button_back = ""  # TODO: ترجمة عربية
   - button_check = ""  # TODO: ترجمة عربية
   - button_filter = ""  # TODO: ترجمة عربية
   - button_reset = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - column_accepted = ""  # TODO: ترجمة عربية
   - column_action = ""  # TODO: ترجمة عربية
   - column_notes = ""  # TODO: ترجمة عربية
   - column_po_number = ""  # TODO: ترجمة عربية
   - column_product = ""  # TODO: ترجمة عربية
   - column_quality_status = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_receipt_date = ""  # TODO: ترجمة عربية
   - column_receipt_number = ""  # TODO: ترجمة عربية
   - column_supplier = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - entry_date = ""  # TODO: ترجمة عربية
   - entry_notes = ""  # TODO: ترجمة عربية
   - entry_po_number = ""  # TODO: ترجمة عربية
   - entry_quality_notes = ""  # TODO: ترجمة عربية
   - entry_quality_status = ""  # TODO: ترجمة عربية
   - entry_receipt_number = ""  # TODO: ترجمة عربية
   - entry_supplier = ""  # TODO: ترجمة عربية
   - error_already_checked = ""  # TODO: ترجمة عربية
   - error_already_received = ""  # TODO: ترجمة عربية
   - error_item = ""  # TODO: ترجمة عربية
   - error_item_not_found = ""  # TODO: ترجمة عربية
   - error_percentage = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_quality_check_not_required = ""  # TODO: ترجمة عربية
   - error_quality_check_required = ""  # TODO: ترجمة عربية
   - error_receipt = ""  # TODO: ترجمة عربية
   - error_receipt_not_found = ""  # TODO: ترجمة عربية
   - error_status = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_title_form = ""  # TODO: ترجمة عربية
   - text_all_status = ""  # TODO: ترجمة عربية
   - text_date_from = ""  # TODO: ترجمة عربية
   - text_date_to = ""  # TODO: ترجمة عربية
   - text_filter = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_history_completed = ""  # TODO: ترجمة عربية
   - text_history_qc = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_item_list = ""  # TODO: ترجمة عربية
   - text_item_updated = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية
   - text_loading = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_qc_failed = ""  # TODO: ترجمة عربية
   - text_qc_passed = ""  # TODO: ترجمة عربية
   - text_qc_pending = ""  # TODO: ترجمة عربية
   - text_quality_check = ""  # TODO: ترجمة عربية
   - text_quality_check_completed = ""  # TODO: ترجمة عربية
   - text_quality_status_fail = ""  # TODO: ترجمة عربية
   - text_quality_status_partial = ""  # TODO: ترجمة عربية
   - text_quality_status_pass = ""  # TODO: ترجمة عربية
   - text_receipt_details = ""  # TODO: ترجمة عربية
   - text_select = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_partially_received = ""  # TODO: ترجمة عربية
   - text_status_pending = ""  # TODO: ترجمة عربية
   - text_status_received = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_back = ""  # TODO: English translation
   - button_check = ""  # TODO: English translation
   - button_filter = ""  # TODO: English translation
   - button_reset = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - column_accepted = ""  # TODO: English translation
   - column_action = ""  # TODO: English translation
   - column_notes = ""  # TODO: English translation
   - column_po_number = ""  # TODO: English translation
   - column_product = ""  # TODO: English translation
   - column_quality_status = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_receipt_date = ""  # TODO: English translation
   - column_receipt_number = ""  # TODO: English translation
   - column_supplier = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - entry_date = ""  # TODO: English translation
   - entry_notes = ""  # TODO: English translation
   - entry_po_number = ""  # TODO: English translation
   - entry_quality_notes = ""  # TODO: English translation
   - entry_quality_status = ""  # TODO: English translation
   - entry_receipt_number = ""  # TODO: English translation
   - entry_supplier = ""  # TODO: English translation
   - error_already_checked = ""  # TODO: English translation
   - error_already_received = ""  # TODO: English translation
   - error_item = ""  # TODO: English translation
   - error_item_not_found = ""  # TODO: English translation
   - error_percentage = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_quality_check_not_required = ""  # TODO: English translation
   - error_quality_check_required = ""  # TODO: English translation
   - error_receipt = ""  # TODO: English translation
   - error_receipt_not_found = ""  # TODO: English translation
   - error_status = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_title_form = ""  # TODO: English translation
   - text_all_status = ""  # TODO: English translation
   - text_date_from = ""  # TODO: English translation
   - text_date_to = ""  # TODO: English translation
   - text_filter = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_history_completed = ""  # TODO: English translation
   - text_history_qc = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_item_list = ""  # TODO: English translation
   - text_item_updated = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
   - text_loading = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_qc_failed = ""  # TODO: English translation
   - text_qc_passed = ""  # TODO: English translation
   - text_qc_pending = ""  # TODO: English translation
   - text_quality_check = ""  # TODO: English translation
   - text_quality_check_completed = ""  # TODO: English translation
   - text_quality_status_fail = ""  # TODO: English translation
   - text_quality_status_partial = ""  # TODO: English translation
   - text_quality_status_pass = ""  # TODO: English translation
   - text_receipt_details = ""  # TODO: English translation
   - text_select = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_partially_received = ""  # TODO: English translation
   - text_status_pending = ""  # TODO: English translation
   - text_status_received = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
