📄 Route: marketplace/event
📂 Controller: controller\marketplace\event.php
🧱 Models used (1):
   ✅ setting/event (10 functions)
🎨 Twig templates (1):
   ✅ view\template\marketplace\event.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\marketplace\event.php (12 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\marketplace\event.php (12 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (32):
   - button_delete
   - button_disable
   - button_enable
   - column_code
   - column_left
   - column_sort_order
   - column_status
   - delete
   - error_permission
   - header
   - pagination
   - sort_code
   - sort_sort_order
   - text_confirm
   - text_disabled
   - text_home
   - text_info
   - text_list
   - text_success
   - text_trigger
   ... و 12 متغير آخر

❌ Missing in Arabic (20):
   - button_delete
   - button_disable
   - button_enable
   - column_left
   - delete
   - header
   - pagination
   - results
   - sort_code
   - success
   - text_confirm
   - text_disabled
   - text_enabled
   - text_home
   - text_no_results
   ... و 5 متغير آخر

❌ Missing in English (20):
   - button_delete
   - button_disable
   - button_enable
   - column_left
   - delete
   - header
   - pagination
   - results
   - sort_code
   - success
   - text_confirm
   - text_disabled
   - text_enabled
   - text_home
   - text_no_results
   ... و 5 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 20 items
      - button_delete
      - pagination
      - sort_code
      - button_disable
      - header
   🟡 MISSING_ENGLISH_VARIABLES: 20 items
      - button_delete
      - pagination
      - sort_code
      - button_disable
      - header

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 20 متغير عربي و 20 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.