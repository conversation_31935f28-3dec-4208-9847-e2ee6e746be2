#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AYM ERP Comprehensive Screen Auditor - Enhanced Edition (V6.0)
Author: AI-Enhanced Expert Review
Purpose: Generate comprehensive screen analysis reports similar to lang_comparison_script.py
Features: MVC analysis, database integration, language validation, function mapping
"""

import re
from pathlib import Path
from datetime import datetime
from collections import defaultdict

class AYMScreenAuditorV6_0:
    
    def __init__(self, dashboard_path):
        self.dashboard_path = Path(dashboard_path)
        self.controller_path = self.dashboard_path / 'controller'
        self.model_path = self.dashboard_path / 'model'
        self.view_path = self.dashboard_path / 'view' / 'template'
        self.lang_path = self.dashboard_path / 'language'
        self.output_path = self.dashboard_path / 'screen_audit_reports'
        self.db_file = self.dashboard_path.parent / 'db.txt'  # قاعدة البيانات
        self.output_path.mkdir(exist_ok=True)
        
        # تحميل جداول قاعدة البيانات
        self.db_tables = self._load_database_tables()
        
        print(f"🔍 AYM Screen Auditor V6.0 Initialized")
        print(f"📁 Reports will be saved to: {self.output_path.resolve()}")
        print(f"📊 Database tables loaded: {len(self.db_tables)} tables")

    def run(self):
        """تشغيل المدقق على جميع الشاشات"""
        controllers = list(self.controller_path.rglob('*.php'))
        total_screens = len(controllers)
        
        print(f"🎯 Starting comprehensive audit of {total_screens} screens...")
        
        for i, ctrl_file in enumerate(controllers, 1):
            route = self._route_from_path(ctrl_file)
            print(f"📋 [{i}/{total_screens}] Analyzing: {route}")
            
            # تحليل شامل للشاشة
            analysis = self._comprehensive_screen_analysis(ctrl_file, route)
            
            # إنشاء التقرير
            report = self._generate_comprehensive_report(route, analysis)
            
            # حفظ التقرير
            report_filename = route.replace('/', '-') + '.txt'
            report_path = self.output_path / report_filename
            report_path.write_text(report, encoding='utf-8')
        
        print(f"\n✅ تم إنشاء {total_screens} تقرير شامل في: {self.output_path}")
        print("🎉 جميع التقارير جاهزة للمراجعة والإصلاح!")

    def _load_database_tables(self):
        """تحميل جداول قاعدة البيانات من ملف db.txt"""
        tables = set()
        if self.db_file.exists():
            try:
                content = self.db_file.read_text(encoding='utf-8')
                # البحث عن أسماء الجداول
                table_pattern = re.compile(r'CREATE TABLE `?(\w+)`?', re.IGNORECASE)
                tables.update(table_pattern.findall(content))
            except Exception as e:
                print(f"⚠️ Warning: Could not load database tables: {e}")
        return tables

    def _route_from_path(self, ctrl_file):
        """استخراج المسار من ملف الكونترولر"""
        return str(ctrl_file.relative_to(self.controller_path).with_suffix('')).replace('\\', '/')

    def _comprehensive_screen_analysis(self, ctrl_file, route):
        """تحليل شامل للشاشة"""
        analysis = {
            'route': route,
            'controller_file': str(ctrl_file),
            'models': [],
            'views': [],
            'language_files': {'ar': [], 'en': []},
            'functions': [],
            'database_tables': [],
            'language_variables': {'used': set(), 'ar_defined': set(), 'en_defined': set()},
            'issues': [],
            'recommendations': []
        }

        # تحليل الكونترولر
        if ctrl_file.exists():
            analysis.update(self._analyze_controller(ctrl_file))

        # البحث عن الموديلات المرتبطة
        analysis['models'] = self._find_related_models(route, analysis.get('model_calls', []))

        # البحث عن القوالب المرتبطة
        analysis['views'] = self._find_related_views(route)

        # البحث عن ملفات اللغة
        analysis['language_files'] = self._find_language_files(route)

        # تحليل متغيرات اللغة
        analysis['language_variables'] = self._analyze_language_variables(
            ctrl_file, analysis['models'], analysis['views'], analysis['language_files']
        )

        # تحليل استخدام قاعدة البيانات
        analysis['database_tables'] = self._analyze_database_usage(
            ctrl_file, analysis['models']
        )

        # تحديد المشاكل والتوصيات
        analysis['issues'] = self._identify_issues(analysis)
        analysis['recommendations'] = self._generate_recommendations(analysis)

        return analysis

    def _analyze_controller(self, ctrl_file):
        """تحليل ملف الكونترولر"""
        try:
            content = ctrl_file.read_text(encoding='utf-8')
            
            return {
                'model_calls': re.findall(r'\$this->load->model\(["\']([^"\']+)["\']\)', content),
                'language_calls': re.findall(r'\$this->load->language\(["\']([^"\']+)["\']\)', content),
                'functions': re.findall(r'function\s+(\w+)\s*\(', content),
                'permissions': re.findall(r'hasPermission\(["\']([^"\']+)["\']\)', content),
                'config_usage': re.findall(r'\$this->config->get\(["\']([^"\']+)["\']\)', content),
                'central_services': 'central_service_manager' in content,
                'hardcoded_text': len(re.findall(r'[\u0600-\u06FF]', content)),
                'ajax_endpoints': re.findall(r'route["\']?\s*:\s*["\']([^"\']+)["\']', content)
            }
        except Exception as e:
            return {'error': str(e)}

    def _find_related_models(self, route, model_calls):
        """البحث عن الموديلات المرتبطة"""
        models = []
        seen_models = set()  # لتجنب التكرارات

        # الموديلات المستدعاة مباشرة
        for model_call in model_calls:
            if model_call not in seen_models:
                seen_models.add(model_call)
                model_path = self.model_path / f"{model_call}.php"
                if model_path.exists():
                    models.append({
                        'path': str(model_path),
                        'route': model_call,
                        'exists': True,
                        'functions': self._extract_functions(model_path)
                    })
                else:
                    models.append({
                        'path': str(model_path),
                        'route': model_call,
                        'exists': False,
                        'functions': []
                    })

        # البحث عن موديل بنفس اسم الكونترولر
        if route not in seen_models:
            default_model_path = self.model_path / f"{route}.php"
            if default_model_path.exists():
                models.append({
                    'path': str(default_model_path),
                    'route': route,
                    'exists': True,
                    'functions': self._extract_functions(default_model_path),
                    'is_default': True
                })

        return models

    def _find_related_views(self, route):
        """البحث عن القوالب المرتبطة"""
        views = []
        
        # البحث عن ملف القالب الرئيسي
        main_view = self.view_path / f"{route}.twig"
        if main_view.exists():
            views.append({
                'path': str(main_view),
                'type': 'main',
                'exists': True,
                'language_vars': self._extract_twig_variables(main_view)
            })
        
        # البحث عن مجلد القوالب
        view_folder = self.view_path / route
        if view_folder.is_dir():
            for twig_file in view_folder.glob('*.twig'):
                views.append({
                    'path': str(twig_file),
                    'type': 'folder',
                    'exists': True,
                    'language_vars': self._extract_twig_variables(twig_file)
                })
        
        return views

    def _find_language_files(self, route):
        """البحث عن ملفات اللغة"""
        lang_files = {'ar': [], 'en': []}
        
        for lang_code in ['ar', 'en-gb']:
            lang_key = 'ar' if lang_code == 'ar' else 'en'
            lang_file = self.lang_path / lang_code / f"{route}.php"
            
            if lang_file.exists():
                lang_files[lang_key].append({
                    'path': str(lang_file),
                    'exists': True,
                    'variables': self._extract_language_variables(lang_file)
                })
            else:
                lang_files[lang_key].append({
                    'path': str(lang_file),
                    'exists': False,
                    'variables': set()
                })
        
        return lang_files

    def _extract_functions(self, file_path):
        """استخراج الدوال من ملف PHP"""
        try:
            content = file_path.read_text(encoding='utf-8')
            return re.findall(r'function\s+(\w+)\s*\(', content)
        except:
            return []

    def _extract_twig_variables(self, twig_file):
        """استخراج متغيرات اللغة من ملف Twig"""
        try:
            content = twig_file.read_text(encoding='utf-8')
            return set(re.findall(r'\{\{\s*(\w+)\s*\}\}', content))
        except:
            return set()

    def _extract_language_variables(self, lang_file):
        """استخراج متغيرات اللغة من ملف PHP"""
        try:
            content = lang_file.read_text(encoding='utf-8')
            return set(re.findall(r"\$_\['([^']+)'\]", content))
        except:
            return set()

    def _analyze_language_variables(self, ctrl_file, models, views, lang_files):
        """تحليل متغيرات اللغة"""
        used_vars = set()

        # استخراج المتغيرات المستخدمة من الكونترولر
        try:
            ctrl_content = ctrl_file.read_text(encoding='utf-8')
            used_vars.update(re.findall(r'\$this->language->get\(["\']([^"\']+)["\']\)', ctrl_content))
        except:
            pass

        # استخراج المتغيرات من الموديلات
        for model in models:
            if model['exists']:
                try:
                    model_content = Path(model['path']).read_text(encoding='utf-8')
                    used_vars.update(re.findall(r'\$this->language->get\(["\']([^"\']+)["\']\)', model_content))
                except:
                    pass

        # استخراج المتغيرات من القوالب
        for view in views:
            used_vars.update(view.get('language_vars', set()))

        # استخراج المتغيرات المعرفة في ملفات اللغة
        ar_defined = set()
        en_defined = set()

        for lang_file in lang_files['ar']:
            ar_defined.update(lang_file.get('variables', set()))

        for lang_file in lang_files['en']:
            en_defined.update(lang_file.get('variables', set()))

        return {
            'used': used_vars,
            'ar_defined': ar_defined,
            'en_defined': en_defined,
            'missing_ar': used_vars - ar_defined,
            'missing_en': used_vars - en_defined,
            'unused_ar': ar_defined - used_vars,
            'unused_en': en_defined - used_vars
        }

    def _analyze_database_usage(self, ctrl_file, models):
        """تحليل استخدام قاعدة البيانات"""
        used_tables = set()

        # أنماط البحث عن الجداول
        table_patterns = [
            r'FROM\s+`?(\w+)`?',
            r'INSERT\s+INTO\s+`?(\w+)`?',
            r'UPDATE\s+`?(\w+)`?',
            r'DELETE\s+FROM\s+`?(\w+)`?',
            r'JOIN\s+`?(\w+)`?',
            r'TABLE_\w+\s*=\s*["\'](\w+)["\']'
        ]

        # تحليل الكونترولر
        try:
            ctrl_content = ctrl_file.read_text(encoding='utf-8')
            for pattern in table_patterns:
                used_tables.update(re.findall(pattern, ctrl_content, re.IGNORECASE))
        except:
            pass

        # تحليل الموديلات
        for model in models:
            if model['exists']:
                try:
                    model_content = Path(model['path']).read_text(encoding='utf-8')
                    for pattern in table_patterns:
                        used_tables.update(re.findall(pattern, model_content, re.IGNORECASE))
                except:
                    pass

        return {
            'used_tables': used_tables,
            'valid_tables': used_tables & self.db_tables,
            'invalid_tables': used_tables - self.db_tables
        }

    def _identify_issues(self, analysis):
        """تحديد المشاكل في الشاشة"""
        issues = []

        # مشاكل ملفات اللغة
        lang_vars = analysis['language_variables']
        if lang_vars['missing_ar']:
            issues.append({
                'type': 'MISSING_ARABIC_VARIABLES',
                'severity': 'HIGH',
                'count': len(lang_vars['missing_ar']),
                'details': list(lang_vars['missing_ar'])[:10]  # أول 10 متغيرات
            })

        if lang_vars['missing_en']:
            issues.append({
                'type': 'MISSING_ENGLISH_VARIABLES',
                'severity': 'HIGH',
                'count': len(lang_vars['missing_en']),
                'details': list(lang_vars['missing_en'])[:10]
            })

        # مشاكل قاعدة البيانات
        db_usage = analysis['database_tables']
        if db_usage['invalid_tables']:
            issues.append({
                'type': 'INVALID_DATABASE_TABLES',
                'severity': 'CRITICAL',
                'count': len(db_usage['invalid_tables']),
                'details': list(db_usage['invalid_tables'])
            })

        # مشاكل الملفات المفقودة
        missing_models = [m for m in analysis['models'] if not m['exists']]
        if missing_models:
            issues.append({
                'type': 'MISSING_MODEL_FILES',
                'severity': 'MEDIUM',
                'count': len(missing_models),
                'details': [m['route'] for m in missing_models]
            })

        missing_views = [v for v in analysis['views'] if not v['exists']]
        if missing_views:
            issues.append({
                'type': 'MISSING_VIEW_FILES',
                'severity': 'MEDIUM',
                'count': len(missing_views),
                'details': [v['path'] for v in missing_views]
            })

        return issues

    def _generate_recommendations(self, analysis):
        """إنشاء التوصيات للإصلاح"""
        recommendations = []

        # توصيات ملفات اللغة
        lang_vars = analysis['language_variables']
        if lang_vars['missing_ar'] or lang_vars['missing_en']:
            recommendations.append({
                'type': 'LANGUAGE_SYNC',
                'priority': 'HIGH',
                'action': 'إضافة المتغيرات المفقودة إلى ملفات اللغة',
                'details': f"إضافة {len(lang_vars['missing_ar'])} متغير عربي و {len(lang_vars['missing_en'])} متغير إنجليزي"
            })

        # توصيات قاعدة البيانات
        db_usage = analysis['database_tables']
        if db_usage['invalid_tables']:
            recommendations.append({
                'type': 'DATABASE_VALIDATION',
                'priority': 'CRITICAL',
                'action': 'مراجعة أسماء الجداول المستخدمة',
                'details': f"تصحيح {len(db_usage['invalid_tables'])} جدول غير موجود"
            })

        # توصيات الملفات المفقودة
        missing_models = [m for m in analysis['models'] if not m['exists']]
        if missing_models:
            recommendations.append({
                'type': 'CREATE_MISSING_FILES',
                'priority': 'MEDIUM',
                'action': 'إنشاء ملفات الموديل المفقودة',
                'details': f"إنشاء {len(missing_models)} ملف موديل"
            })

        return recommendations

    def _generate_comprehensive_report(self, route, analysis):
        """إنشاء التقرير الشامل"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # حساب النقاط
        total_issues = len(analysis['issues'])
        severity_score = sum(3 if issue['severity'] == 'CRITICAL' else 2 if issue['severity'] == 'HIGH' else 1
                           for issue in analysis['issues'])
        health_score = max(0, 100 - (severity_score * 5))

        # رمز الحالة
        status_emoji = "✅" if health_score >= 90 else "⚠️" if health_score >= 70 else "❌"

        report = f"""📄 Route: {route}
📂 Controller: {analysis['controller_file']}
🧱 Models used ({len(analysis['models'])}):"""

        for model in analysis['models']:
            status = "✅" if model['exists'] else "❌"
            report += f"\n   {status} {model['route']} ({len(model.get('functions', []))} functions)"

        report += f"\n🎨 Twig templates ({len(analysis['views'])}):"
        for view in analysis['views']:
            status = "✅" if view['exists'] else "❌"
            report += f"\n   {status} {view['path']}"

        report += f"\n🈯 Arabic Language Files ({len(analysis['language_files']['ar'])}):"
        for lang_file in analysis['language_files']['ar']:
            status = "✅" if lang_file['exists'] else "❌"
            var_count = len(lang_file.get('variables', set()))
            report += f"\n   {status} {lang_file['path']} ({var_count} variables)"

        report += f"\n🇬🇧 English Language Files ({len(analysis['language_files']['en'])}):"
        for lang_file in analysis['language_files']['en']:
            status = "✅" if lang_file['exists'] else "❌"
            var_count = len(lang_file.get('variables', set()))
            report += f"\n   {status} {lang_file['path']} ({var_count} variables)"

        report += f"""
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables ({len(analysis['language_variables']['used'])}):"""

        for var in sorted(list(analysis['language_variables']['used'])[:20]):  # أول 20 متغير
            report += f"\n   - {var}"

        if len(analysis['language_variables']['used']) > 20:
            report += f"\n   ... و {len(analysis['language_variables']['used']) - 20} متغير آخر"

        # المتغيرات المفقودة
        if analysis['language_variables']['missing_ar']:
            report += f"\n\n❌ Missing in Arabic ({len(analysis['language_variables']['missing_ar'])}):"
            for var in sorted(list(analysis['language_variables']['missing_ar'])[:15]):
                report += f"\n   - {var}"
            if len(analysis['language_variables']['missing_ar']) > 15:
                report += f"\n   ... و {len(analysis['language_variables']['missing_ar']) - 15} متغير آخر"

        if analysis['language_variables']['missing_en']:
            report += f"\n\n❌ Missing in English ({len(analysis['language_variables']['missing_en'])}):"
            for var in sorted(list(analysis['language_variables']['missing_en'])[:15]):
                report += f"\n   - {var}"
            if len(analysis['language_variables']['missing_en']) > 15:
                report += f"\n   ... و {len(analysis['language_variables']['missing_en']) - 15} متغير آخر"

        # قاعدة البيانات
        db_usage = analysis['database_tables']
        if db_usage['used_tables']:
            report += f"\n\n🗄️ Database Tables Used ({len(db_usage['used_tables'])}):"
            for table in sorted(db_usage['used_tables']):
                status = "✅" if table in self.db_tables else "❌"
                report += f"\n   {status} {table}"

        # المشاكل
        if analysis['issues']:
            report += f"\n\n🚨 Issues Found ({len(analysis['issues'])}):"
            for issue in analysis['issues']:
                severity_emoji = "🔴" if issue['severity'] == 'CRITICAL' else "🟡" if issue['severity'] == 'HIGH' else "🟢"
                report += f"\n   {severity_emoji} {issue['type']}: {issue['count']} items"
                if issue.get('details'):
                    for detail in issue['details'][:5]:  # أول 5 تفاصيل
                        report += f"\n      - {detail}"

        # التوصيات
        if analysis['recommendations']:
            report += f"\n\n💡 Recommendations ({len(analysis['recommendations'])}):"
            for rec in analysis['recommendations']:
                priority_emoji = "🔴" if rec['priority'] == 'CRITICAL' else "🟡" if rec['priority'] == 'HIGH' else "🟢"
                report += f"\n   {priority_emoji} {rec['action']}"
                report += f"\n      {rec['details']}"

        # النقاط النهائية
        report += f"\n\n📈 Screen Health Score: {status_emoji} {health_score}%"
        report += f"\n📅 Analysis Date: {timestamp}"
        report += f"\n🔧 Total Issues: {total_issues}"

        if health_score >= 90:
            report += "\n\n🎉 ممتاز! هذه الشاشة في حالة جيدة جداً."
        elif health_score >= 70:
            report += "\n\n⚠️ جيد، لكن يحتاج بعض التحسينات."
        else:
            report += "\n\n❌ يحتاج إصلاح عاجل لعدة مشاكل."

        return report

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="AYM ERP Screen Auditor V6.0 - Comprehensive Analysis")
    parser.add_argument("path", nargs="?", default=".", help="Root path to the dashboard directory")
    args = parser.parse_args()

    dashboard_path = Path(args.path)
    if not (dashboard_path / 'controller').exists():
        print("❌ Error: 'controller' directory not found. Please specify the correct dashboard path.")
        print("💡 Example: python aym_auditor_v_6.py F:/2025/ay2/dashboard")
    else:
        AYMScreenAuditorV6_0(args.path).run()
