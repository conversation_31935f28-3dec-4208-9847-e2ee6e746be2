📄 Route: reports/equity_changes
📂 Controller: controller\reports\equity_changes.php
🧱 Models used (1):
   - reports/equity_changes
🎨 Twig templates (1):
   - view\template\reports\equity_changes.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_date_end
   - error_date_range
   - error_date_start
   - error_permission
   - heading_title
   - text_home

❌ Missing in Arabic:
   - date_format_short
   - error_date_end
   - error_date_range
   - error_date_start
   - error_permission
   - heading_title
   - text_home

❌ Missing in English:
   - date_format_short
   - error_date_end
   - error_date_range
   - error_date_start
   - error_permission
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_date_start = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_date_end = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_date_start = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
