📄 Route: extension/report/customer_reward
📂 Controller: controller\extension\report\customer_reward.php
🧱 Models used (2):
   ✅ setting/setting (5 functions)
   ✅ extension/report/customer (12 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\report\customer_reward.php (19 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\report\customer_reward.php (19 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (8):
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (4):
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

❌ Missing in English (4):
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - text_enabled
      - text_home
      - text_disabled
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 4 items
      - text_enabled
      - text_home
      - text_disabled
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 4 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:28
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.