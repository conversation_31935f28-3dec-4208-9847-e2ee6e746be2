📄 Route: extension/extension/payment
📂 Controller: controller\extension\extension\payment.php
🧱 Models used (2):
   ✅ setting/extension (11 functions)
   ✅ user/user_group (9 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\extension\payment.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\extension\payment.php (8 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\extension\payment.php (8 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (17):
   - button_edit
   - button_install
   - button_uninstall
   - column_action
   - column_name
   - column_sort_order
   - column_status
   - error_permission
   - error_warning
   - extension
   - heading_title
   - promotion
   - success
   - text_disabled
   - text_enabled
   - text_no_results
   - text_success

❌ Missing in Arabic (10):
   - button_edit
   - button_install
   - button_uninstall
   - error_warning
   - extension
   - promotion
   - success
   - text_disabled
   - text_enabled
   - text_no_results

❌ Missing in English (10):
   - button_edit
   - button_install
   - button_uninstall
   - error_warning
   - extension
   - promotion
   - success
   - text_disabled
   - text_enabled
   - text_no_results

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 10 items
      - error_warning
      - promotion
      - button_install
      - extension
      - text_disabled
   🟡 MISSING_ENGLISH_VARIABLES: 10 items
      - error_warning
      - promotion
      - button_install
      - extension
      - text_disabled

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 10 متغير عربي و 10 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:22
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.