📄 Route: extension/module/bestseller
📂 Controller: controller\extension\module\bestseller.php
🧱 Models used (1):
   - setting/module
🎨 Twig templates (1):
   - view\template\extension\module\bestseller.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\module\bestseller.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\module\bestseller.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_height
   - error_name
   - error_permission
   - error_width
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_height
   - error_name
   - error_permission
   - error_width
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_height
   - error_name
   - error_permission
   - error_width
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_height = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_width = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_height = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_width = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
