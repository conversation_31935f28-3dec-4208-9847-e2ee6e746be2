📄 Route: extension/module/html
📂 Controller: controller\extension\module\html.php
🧱 Models used (2):
   - localisation/language
   - setting/module
🎨 Twig templates (1):
   - view\template\extension\module\html.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\module\html.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\module\html.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_name
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_name
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_name
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
