📄 Route: report/report
📂 Controller: controller\report\report.php
🧱 Models used (1):
   ✅ setting/extension (11 functions)
🎨 Twig templates (1):
   ✅ view\template\report\report.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\report\report.php (5 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\report\report.php (5 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (10):
   - button_filter
   - column_left
   - extension
   - footer
   - header
   - heading_title
   - report
   - text_filter
   - text_home
   - text_type

❌ Missing in Arabic (7):
   - button_filter
   - column_left
   - extension
   - footer
   - header
   - report
   - text_home

❌ Missing in English (7):
   - button_filter
   - column_left
   - extension
   - footer
   - header
   - report
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 7 items
      - column_left
      - extension
      - text_home
      - button_filter
      - footer
   🟡 MISSING_ENGLISH_VARIABLES: 7 items
      - column_left
      - extension
      - text_home
      - button_filter
      - footer

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 7 متغير عربي و 7 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.