📄 Route: documents/versioning
📂 Controller: controller\documents\versioning.php
🧱 Models used (7):
   ❌ documents/versioning (0 functions)
   ❌ documents/archive (0 functions)
   ✅ workflow/workflow (30 functions)
   ✅ core/central_service_manager (60 functions)
   ✅ communication/unified_notification (16 functions)
   ✅ logging/user_activity (13 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (1):
   ✅ view\template\documents\versioning.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\documents\versioning.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\documents\versioning.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (218):
   - action
   - analytics
   - document_types
   - error_approve
   - error_comparison_result
   - error_create_version
   - error_title_required
   - text_approval_processed
   - text_category_definitions
   - text_compare
   - text_compare_versions
   - text_price_lists
   - text_product_id
   - text_publish
   - text_status_obsolete_desc
   - text_status_published_desc
   - text_strategy_major_minor
   - text_test_results
   - text_version_options
   - text_version_published
   ... و 198 متغير آخر

❌ Missing in Arabic (218):
   - action
   - analytics
   - document_types
   - error_approve
   - error_create_version
   - error_title_required
   - text_approval_processed
   - text_compare
   - text_compare_versions
   - text_product_id
   - text_publish
   - text_status_obsolete_desc
   - text_test_results
   - text_version_options
   - text_version_published
   ... و 203 متغير آخر

❌ Missing in English (218):
   - action
   - analytics
   - document_types
   - error_approve
   - error_create_version
   - error_title_required
   - text_approval_processed
   - text_compare
   - text_compare_versions
   - text_product_id
   - text_publish
   - text_status_obsolete_desc
   - text_test_results
   - text_version_options
   - text_version_published
   ... و 203 متغير آخر

🗄️ Database Tables Used (7):
   ❌ current
   ❌ instance
   ❌ node
   ❌ schedule
   ❌ template
   ❌ the
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 218 items
      - error_create_version
      - text_version_published
      - text_approval_processed
      - action
      - analytics
   🟡 MISSING_ENGLISH_VARIABLES: 218 items
      - error_create_version
      - text_version_published
      - text_approval_processed
      - action
      - analytics
   🔴 INVALID_DATABASE_TABLES: 7 items
      - workflow
      - instance
      - node
      - template
      - current
   🟢 MISSING_MODEL_FILES: 2 items
      - documents/versioning
      - documents/archive

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 218 متغير عربي و 218 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 7 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:01
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.