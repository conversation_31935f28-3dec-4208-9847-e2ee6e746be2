📄 Route: inventory/product_management
📂 Controller: controller\inventory\product_management.php
🧱 Models used (8):
   - catalog/category
   - catalog/manufacturer
   - inventory/product_management
   - inventory/unit
   - localisation/length_class
   - localisation/stock_status
   - localisation/tax_class
   - localisation/weight_class
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\inventory\product_management.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_date_added
   - column_manufacturer
   - column_model
   - column_name
   - column_price
   - column_quantity
   - column_sku
   - column_status
   - date_format_short
   - error_model
   - error_name
   - error_permission
   - error_product_not_found
   - heading_title
   - text_add
   - text_all
   - text_bulk_updated
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_in_stock
   - text_low_stock
   - text_never
   - text_out_of_stock
   - text_overstock
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - column_date_added
   - column_manufacturer
   - column_model
   - column_name
   - column_price
   - column_quantity
   - column_sku
   - column_status
   - date_format_short
   - error_model
   - error_name
   - error_permission
   - error_product_not_found
   - heading_title
   - text_add
   - text_all
   - text_bulk_updated
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_in_stock
   - text_low_stock
   - text_never
   - text_out_of_stock
   - text_overstock
   - text_pagination
   - text_success

❌ Missing in English:
   - column_date_added
   - column_manufacturer
   - column_model
   - column_name
   - column_price
   - column_quantity
   - column_sku
   - column_status
   - date_format_short
   - error_model
   - error_name
   - error_permission
   - error_product_not_found
   - heading_title
   - text_add
   - text_all
   - text_bulk_updated
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_in_stock
   - text_low_stock
   - text_never
   - text_out_of_stock
   - text_overstock
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - column_date_added = ""  # TODO: ترجمة عربية
   - column_manufacturer = ""  # TODO: ترجمة عربية
   - column_model = ""  # TODO: ترجمة عربية
   - column_name = ""  # TODO: ترجمة عربية
   - column_price = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_sku = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_model = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_product_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_bulk_updated = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_in_stock = ""  # TODO: ترجمة عربية
   - text_low_stock = ""  # TODO: ترجمة عربية
   - text_never = ""  # TODO: ترجمة عربية
   - text_out_of_stock = ""  # TODO: ترجمة عربية
   - text_overstock = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_date_added = ""  # TODO: English translation
   - column_manufacturer = ""  # TODO: English translation
   - column_model = ""  # TODO: English translation
   - column_name = ""  # TODO: English translation
   - column_price = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_sku = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_model = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_product_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_bulk_updated = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_in_stock = ""  # TODO: English translation
   - text_low_stock = ""  # TODO: English translation
   - text_never = ""  # TODO: English translation
   - text_out_of_stock = ""  # TODO: English translation
   - text_overstock = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
