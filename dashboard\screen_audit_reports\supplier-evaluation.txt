📄 Route: supplier/evaluation
📂 Controller: controller\supplier\evaluation.php
🧱 Models used (3):
   ✅ supplier/evaluation (14 functions)
   ✅ supplier/supplier (21 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\supplier\evaluation.php (120 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\supplier\evaluation.php (120 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - date_format_short
   - error_delivery_score
   - error_evaluation_date
   - error_permission
   - error_price_score
   - error_quality_score
   - error_service_score
   - error_supplier
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_report
   - text_success

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (3):
   - date_format_short
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.