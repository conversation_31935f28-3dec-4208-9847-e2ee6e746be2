📄 Route: marketplace/installer
📂 Controller: controller\marketplace\installer.php
🧱 Models used (1):
   ✅ setting/extension (11 functions)
🎨 Twig templates (1):
   ✅ view\template\marketplace\installer.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\marketplace\installer.php (17 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\marketplace\installer.php (17 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (22):
   - button_upload
   - column_left
   - date_format_short
   - entry_progress
   - entry_upload
   - error_file
   - error_install
   - error_permission
   - footer
   - header
   - heading_title
   - help_upload
   - text_history
   - text_home
   - text_install
   - text_loading
   - text_pagination
   - text_progress
   - text_upload
   - user_token
   ... و 2 متغير آخر

❌ Missing in Arabic (9):
   - button_upload
   - column_left
   - date_format_short
   - footer
   - header
   - text_home
   - text_loading
   - text_pagination
   - user_token

❌ Missing in English (9):
   - button_upload
   - column_left
   - date_format_short
   - footer
   - header
   - text_home
   - text_loading
   - text_pagination
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 9 items
      - button_upload
      - user_token
      - column_left
      - text_loading
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 9 items
      - button_upload
      - user_token
      - column_left
      - text_loading
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 9 متغير عربي و 9 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.