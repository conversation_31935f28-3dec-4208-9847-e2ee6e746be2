📄 Route: extension/report/customer_order
📂 Controller: controller\extension\report\customer_order.php
🧱 Models used (3):
   - extension/report/customer
   - localisation/order_status
   - setting/setting
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\extension\report\customer_order.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\report\customer_order.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
