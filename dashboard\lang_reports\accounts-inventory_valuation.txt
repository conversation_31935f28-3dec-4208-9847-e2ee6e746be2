📄 Route: accounts/inventory_valuation
📂 Controller: controller\accounts\inventory_valuation.php
🧱 Models used (2):
   - accounts/inventory_valuation
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\inventory_valuation.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\inventory_valuation.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_no_data
   - heading_title
   - print_title
   - text_average_cost
   - text_closing_qty
   - text_form
   - text_from
   - text_in_qty
   - text_inventory_valuation
   - text_inventory_value
   - text_opening_qty
   - text_out_qty
   - text_period
   - text_product_name
   - text_to
   - text_total_value

❌ Missing in Arabic:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_no_data
   - heading_title
   - print_title
   - text_average_cost
   - text_closing_qty
   - text_form
   - text_from
   - text_in_qty
   - text_inventory_valuation
   - text_inventory_value
   - text_opening_qty
   - text_out_qty
   - text_period
   - text_product_name
   - text_to
   - text_total_value

❌ Missing in English:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_no_data
   - heading_title
   - print_title
   - text_average_cost
   - text_closing_qty
   - text_form
   - text_from
   - text_in_qty
   - text_inventory_valuation
   - text_inventory_value
   - text_opening_qty
   - text_out_qty
   - text_period
   - text_product_name
   - text_to
   - text_total_value

💡 Suggested Arabic Additions:
   - button_filter = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - entry_date_start = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_average_cost = ""  # TODO: ترجمة عربية
   - text_closing_qty = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_from = ""  # TODO: ترجمة عربية
   - text_in_qty = ""  # TODO: ترجمة عربية
   - text_inventory_valuation = ""  # TODO: ترجمة عربية
   - text_inventory_value = ""  # TODO: ترجمة عربية
   - text_opening_qty = ""  # TODO: ترجمة عربية
   - text_out_qty = ""  # TODO: ترجمة عربية
   - text_period = ""  # TODO: ترجمة عربية
   - text_product_name = ""  # TODO: ترجمة عربية
   - text_to = ""  # TODO: ترجمة عربية
   - text_total_value = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_filter = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - entry_date_start = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_average_cost = ""  # TODO: English translation
   - text_closing_qty = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_from = ""  # TODO: English translation
   - text_in_qty = ""  # TODO: English translation
   - text_inventory_valuation = ""  # TODO: English translation
   - text_inventory_value = ""  # TODO: English translation
   - text_opening_qty = ""  # TODO: English translation
   - text_out_qty = ""  # TODO: English translation
   - text_period = ""  # TODO: English translation
   - text_product_name = ""  # TODO: English translation
   - text_to = ""  # TODO: English translation
   - text_total_value = ""  # TODO: English translation
