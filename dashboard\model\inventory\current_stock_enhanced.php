<?php
/**
 * موديل المخزون الحالي المحسن - Enterprise Grade Plus
 *
 * التحسينات المطبقة وفقاً للدستور الشامل:
 * - تطبيق الخدمات المركزية الخمس بالكامل
 * - استعلامات SQL محسنة ومعقدة مع WAC
 * - معالجة الأخطاء الشاملة مع try-catch
 * - تكامل مع جداول قاعدة البيانات الصحيحة
 * - حسابات متقدمة للمخزون والتكلفة
 * - دعم الفلاتر المتقدمة (15+ فلتر)
 * - تحليلات متقدمة وإحصائيات شاملة
 * - دعم الوحدات المتعددة والفروع
 * - تتبع الدفعات وانتهاء الصلاحية
 * - نظام ABC Analysis المتقدم
 * - تقارير الربحية والأداء
 *
 * <AUTHOR> ERP Team - Enhanced by AI Agent
 * @version 4.0 - Enterprise Grade Plus
 * @since 2025-07-20
 * @reference الدستور الشامل النهائي v6.0
 */

class ModelInventoryCurrentStockEnhanced extends Model {
    
    private $central_service;
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * الحصول على المخزون الحالي مع فلاتر متقدمة
     * 
     * @param array $data فلاتر البحث والترتيب
     * @return array قائمة المنتجات مع تفاصيل المخزون
     */
    public function getCurrentStock($data = array()) {
        try {
            $sql = "
                SELECT 
                    p.product_id,
                    pd.name as product_name,
                    p.model,
                    p.sku,
                    p.upc,
                    p.ean,
                    p.price,
                    p.average_cost,
                    p.minimum as reorder_level,
                    p.status as product_status,
                    p.track_batch,
                    p.track_expiry,
                    p.expiry_alert_days,
                    p.date_added,
                    p.date_modified,
                    
                    -- معلومات الفئة والشركة المصنعة
                    cd.name as category_name,
                    m.name as manufacturer_name,
                    
                    -- معلومات المخزون من جدول cod_product_inventory
                    pi.quantity as current_stock,
                    pi.quantity_available as available_stock,
                    pi.reserved_quantity,
                    pi.average_cost as inventory_avg_cost,
                    pi.branch_id,
                    b.name as branch_name,
                    pi.unit_id,
                    u.name as unit_name,
                    u.symbol as unit_symbol,
                    
                    -- حسابات متقدمة
                    (pi.quantity * pi.average_cost) as total_value,
                    (pi.quantity_available * pi.average_cost) as available_value,
                    (pi.reserved_quantity * pi.average_cost) as reserved_value,
                    
                    -- حالة المخزون المحسوبة
                    CASE 
                        WHEN pi.quantity <= 0 THEN 'out_of_stock'
                        WHEN pi.quantity <= p.minimum THEN 'low_stock'
                        WHEN pi.quantity >= (p.minimum * 3) THEN 'overstock'
                        ELSE 'in_stock'
                    END as stock_status,
                    
                    -- آخر حركة مخزون
                    (SELECT MAX(pih.transaction_date) 
                     FROM " . DB_PREFIX . "product_inventory_history pih 
                     WHERE pih.product_id = p.product_id 
                     AND pih.branch_id = pi.branch_id) as last_movement_date,
                    
                    -- عدد الحركات في آخر 30 يوم
                    (SELECT COUNT(*) 
                     FROM " . DB_PREFIX . "product_inventory_history pih 
                     WHERE pih.product_id = p.product_id 
                     AND pih.branch_id = pi.branch_id
                     AND pih.transaction_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as movements_30_days,
                    
                    -- مبيعات آخر 30 يوم (من جدول الطلبات)
                    (SELECT COALESCE(SUM(op.quantity), 0) 
                     FROM " . DB_PREFIX . "order_product op
                     LEFT JOIN " . DB_PREFIX . "order o ON (op.order_id = o.order_id)
                     WHERE op.product_id = p.product_id 
                     AND o.order_status_id > 0
                     AND o.date_added >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as sales_30_days,
                    
                    -- معدل دوران المخزون
                    CASE 
                        WHEN pi.quantity > 0 AND pi.average_cost > 0 THEN
                            (SELECT COALESCE(SUM(op.quantity * op.price), 0) 
                             FROM " . DB_PREFIX . "order_product op
                             LEFT JOIN " . DB_PREFIX . "order o ON (op.order_id = o.order_id)
                             WHERE op.product_id = p.product_id 
                             AND o.order_status_id > 0
                             AND o.date_added >= DATE_SUB(NOW(), INTERVAL 365 DAY)) / (pi.quantity * pi.average_cost)
                        ELSE 0
                    END as turnover_ratio,
                    
                    -- تحليل ABC (سيتم حسابه لاحقاً)
                    COALESCE(abc.abc_class, 'C') as abc_class,
                    COALESCE(abc.value_contribution, 0) as abc_value_contribution,
                    
                    -- معلومات الدفعات (إذا كان المنتج يتتبع الدفعات)
                    CASE WHEN p.track_batch = 1 THEN
                        (SELECT COUNT(DISTINCT batch_number) 
                         FROM " . DB_PREFIX . "product_inventory_history pih 
                         WHERE pih.product_id = p.product_id 
                         AND pih.branch_id = pi.branch_id
                         AND pih.batch_number IS NOT NULL
                         AND pih.batch_number != '') 
                    ELSE 0 END as batch_count,
                    
                    -- تنبيهات انتهاء الصلاحية
                    CASE WHEN p.track_expiry = 1 THEN
                        (SELECT COUNT(*) 
                         FROM " . DB_PREFIX . "product_inventory_history pih 
                         WHERE pih.product_id = p.product_id 
                         AND pih.branch_id = pi.branch_id
                         AND pih.expiry_date IS NOT NULL
                         AND pih.expiry_date <= DATE_ADD(NOW(), INTERVAL COALESCE(p.expiry_alert_days, 30) DAY))
                    ELSE 0 END as expiring_batches
                    
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN " . DB_PREFIX . "product_inventory pi ON (p.product_id = pi.product_id)
                LEFT JOIN " . DB_PREFIX . "branch b ON (pi.branch_id = b.branch_id)
                LEFT JOIN " . DB_PREFIX . "unit u ON (pi.unit_id = u.unit_id)
                LEFT JOIN " . DB_PREFIX . "product_to_category p2c ON (p.product_id = p2c.product_id)
                LEFT JOIN " . DB_PREFIX . "category_description cd ON (p2c.category_id = cd.category_id AND cd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN " . DB_PREFIX . "manufacturer m ON (p.manufacturer_id = m.manufacturer_id)
                LEFT JOIN " . DB_PREFIX . "inventory_abc_analysis abc ON (p.product_id = abc.product_id AND abc.branch_id = pi.branch_id)
                WHERE 1=1
            ";

            // تطبيق الفلاتر المتقدمة
            $sql .= $this->applyFilters($data);
            
            // ترتيب النتائج
            $sql .= $this->applySorting($data);
            
            // تحديد عدد النتائج
            $sql .= $this->applyPagination($data);

            $query = $this->db->query($sql);
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'current_stock',
                'عرض المخزون الحالي مع الفلاتر المطبقة',
                array(
                    'filters_applied' => $data,
                    'results_count' => count($query->rows),
                    'user_id' => $this->user->getId()
                )
            );

            return $query->rows;
            
        } catch (Exception $e) {
            // تسجيل الخطأ
            $this->central_service->logActivity(
                'error',
                'current_stock',
                'خطأ في جلب المخزون الحالي: ' . $e->getMessage(),
                array(
                    'error_details' => $e->getTrace(),
                    'user_id' => $this->user->getId()
                )
            );
            
            throw new Exception('خطأ في جلب بيانات المخزون: ' . $e->getMessage());
        }
    }

    /**
     * تطبيق الفلاتر المتقدمة على الاستعلام
     */
    private function applyFilters($data) {
        $where_conditions = array();
        
        // فلتر اسم المنتج
        if (!empty($data['filter_name'])) {
            $where_conditions[] = "pd.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }
        
        // فلتر رمز المنتج
        if (!empty($data['filter_sku'])) {
            $where_conditions[] = "p.sku LIKE '%" . $this->db->escape($data['filter_sku']) . "%'";
        }
        
        // فلتر الموديل
        if (!empty($data['filter_model'])) {
            $where_conditions[] = "p.model LIKE '%" . $this->db->escape($data['filter_model']) . "%'";
        }
        
        // فلتر الفئة
        if (!empty($data['filter_category_id'])) {
            $where_conditions[] = "p2c.category_id = '" . (int)$data['filter_category_id'] . "'";
        }
        
        // فلتر الشركة المصنعة
        if (!empty($data['filter_manufacturer_id'])) {
            $where_conditions[] = "p.manufacturer_id = '" . (int)$data['filter_manufacturer_id'] . "'";
        }
        
        // فلتر الفرع
        if (!empty($data['filter_branch_id'])) {
            $where_conditions[] = "pi.branch_id = '" . (int)$data['filter_branch_id'] . "'";
        }
        
        // فلتر حالة المخزون
        if (!empty($data['filter_stock_status'])) {
            switch ($data['filter_stock_status']) {
                case 'out_of_stock':
                    $where_conditions[] = "pi.quantity <= 0";
                    break;
                case 'low_stock':
                    $where_conditions[] = "pi.quantity > 0 AND pi.quantity <= p.minimum";
                    break;
                case 'in_stock':
                    $where_conditions[] = "pi.quantity > p.minimum AND pi.quantity < (p.minimum * 3)";
                    break;
                case 'overstock':
                    $where_conditions[] = "pi.quantity >= (p.minimum * 3)";
                    break;
            }
        }
        
        // فلتر نطاق الكمية
        if (!empty($data['filter_quantity_from'])) {
            $where_conditions[] = "pi.quantity >= '" . (float)$data['filter_quantity_from'] . "'";
        }
        
        if (!empty($data['filter_quantity_to'])) {
            $where_conditions[] = "pi.quantity <= '" . (float)$data['filter_quantity_to'] . "'";
        }
        
        // فلتر نطاق القيمة
        if (!empty($data['filter_value_from'])) {
            $where_conditions[] = "(pi.quantity * pi.average_cost) >= '" . (float)$data['filter_value_from'] . "'";
        }
        
        if (!empty($data['filter_value_to'])) {
            $where_conditions[] = "(pi.quantity * pi.average_cost) <= '" . (float)$data['filter_value_to'] . "'";
        }
        
        // فلتر تاريخ آخر حركة
        if (!empty($data['filter_last_movement_from'])) {
            $where_conditions[] = "EXISTS (SELECT 1 FROM " . DB_PREFIX . "product_inventory_history pih WHERE pih.product_id = p.product_id AND pih.transaction_date >= '" . $this->db->escape($data['filter_last_movement_from']) . "')";
        }
        
        if (!empty($data['filter_last_movement_to'])) {
            $where_conditions[] = "EXISTS (SELECT 1 FROM " . DB_PREFIX . "product_inventory_history pih WHERE pih.product_id = p.product_id AND pih.transaction_date <= '" . $this->db->escape($data['filter_last_movement_to']) . "')";
        }
        
        // فلتر تصنيف ABC
        if (!empty($data['filter_abc_class'])) {
            $where_conditions[] = "abc.abc_class = '" . $this->db->escape($data['filter_abc_class']) . "'";
        }
        
        // فلتر المنتجات التي تتتبع الدفعات
        if (isset($data['filter_track_batch']) && $data['filter_track_batch'] !== '') {
            $where_conditions[] = "p.track_batch = '" . (int)$data['filter_track_batch'] . "'";
        }
        
        // فلتر المنتجات التي تتتبع انتهاء الصلاحية
        if (isset($data['filter_track_expiry']) && $data['filter_track_expiry'] !== '') {
            $where_conditions[] = "p.track_expiry = '" . (int)$data['filter_track_expiry'] . "'";
        }
        
        // فلتر المنتجات منتهية الصلاحية قريباً
        if (!empty($data['filter_expiring_soon'])) {
            $where_conditions[] = "p.track_expiry = 1 AND EXISTS (SELECT 1 FROM " . DB_PREFIX . "product_inventory_history pih WHERE pih.product_id = p.product_id AND pih.expiry_date <= DATE_ADD(NOW(), INTERVAL COALESCE(p.expiry_alert_days, 30) DAY))";
        }
        
        if (!empty($where_conditions)) {
            return " AND " . implode(" AND ", $where_conditions);
        }
        
        return "";
    }

    /**
     * تطبيق الترتيب على الاستعلام
     */
    private function applySorting($data) {
        $sort_data = array(
            'pd.name',
            'p.model',
            'p.sku',
            'p.price',
            'pi.quantity',
            'pi.quantity_available',
            'total_value',
            'stock_status',
            'last_movement_date',
            'sales_30_days',
            'turnover_ratio',
            'abc_class'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql = " ORDER BY " . $data['sort'];
        } else {
            $sql = " ORDER BY pd.name";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        return $sql;
    }

    /**
     * تطبيق التصفح على الاستعلام
     */
    private function applyPagination($data) {
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            return " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        return "";
    }

    /**
     * الحصول على إجمالي عدد المنتجات
     */
    public function getTotalCurrentStock($data = array()) {
        try {
            $sql = "
                SELECT COUNT(DISTINCT p.product_id) AS total
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN " . DB_PREFIX . "product_inventory pi ON (p.product_id = pi.product_id)
                LEFT JOIN " . DB_PREFIX . "product_to_category p2c ON (p.product_id = p2c.product_id)
                LEFT JOIN " . DB_PREFIX . "inventory_abc_analysis abc ON (p.product_id = abc.product_id AND abc.branch_id = pi.branch_id)
                WHERE 1=1
            ";

            $sql .= $this->applyFilters($data);

            $query = $this->db->query($sql);

            return $query->row['total'];
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'current_stock',
                'خطأ في حساب إجمالي المخزون: ' . $e->getMessage(),
                array('user_id' => $this->user->getId())
            );
            
            return 0;
        }
    }

    /**
     * الحصول على إحصائيات المخزون المتقدمة
     */
    public function getStockStatistics($data = array()) {
        try {
            $sql = "
                SELECT 
                    COUNT(DISTINCT p.product_id) as total_products,
                    COUNT(DISTINCT CASE WHEN pi.quantity > 0 THEN p.product_id END) as products_in_stock,
                    COUNT(DISTINCT CASE WHEN pi.quantity <= 0 THEN p.product_id END) as products_out_of_stock,
                    COUNT(DISTINCT CASE WHEN pi.quantity > 0 AND pi.quantity <= p.minimum THEN p.product_id END) as products_low_stock,
                    COUNT(DISTINCT CASE WHEN pi.quantity >= (p.minimum * 3) THEN p.product_id END) as products_overstock,
                    
                    SUM(pi.quantity) as total_quantity,
                    SUM(pi.quantity_available) as total_available_quantity,
                    SUM(pi.reserved_quantity) as total_reserved_quantity,
                    
                    SUM(pi.quantity * pi.average_cost) as total_inventory_value,
                    SUM(pi.quantity_available * pi.average_cost) as total_available_value,
                    SUM(pi.reserved_quantity * pi.average_cost) as total_reserved_value,
                    
                    AVG(pi.average_cost) as avg_cost,
                    MIN(pi.average_cost) as min_cost,
                    MAX(pi.average_cost) as max_cost,
                    
                    COUNT(DISTINCT pi.branch_id) as total_branches,
                    COUNT(DISTINCT pi.unit_id) as total_units,
                    
                    COUNT(DISTINCT CASE WHEN p.track_batch = 1 THEN p.product_id END) as batch_tracked_products,
                    COUNT(DISTINCT CASE WHEN p.track_expiry = 1 THEN p.product_id END) as expiry_tracked_products
                    
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN " . DB_PREFIX . "product_inventory pi ON (p.product_id = pi.product_id)
                LEFT JOIN " . DB_PREFIX . "product_to_category p2c ON (p.product_id = p2c.product_id)
                WHERE 1=1
            ";

            $sql .= $this->applyFilters($data);

            $query = $this->db->query($sql);
            
            $statistics = $query->row;
            
            // حسابات إضافية
            $statistics['stock_efficiency'] = $statistics['total_products'] > 0 ? 
                round(($statistics['products_in_stock'] / $statistics['total_products']) * 100, 2) : 0;
                
            $statistics['avg_inventory_value'] = $statistics['total_products'] > 0 ? 
                round($statistics['total_inventory_value'] / $statistics['total_products'], 2) : 0;

            return $statistics;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'current_stock',
                'خطأ في حساب إحصائيات المخزون: ' . $e->getMessage(),
                array('user_id' => $this->user->getId())
            );
            
            return array();
        }
    }

    /**
     * الحصول على تحليل ABC للمخزون
     */
    public function getABCAnalysis($branch_id = null) {
        try {
            $sql = "
                SELECT 
                    abc_class,
                    COUNT(*) as product_count,
                    SUM(value_contribution) as total_value,
                    AVG(percentage_of_total) as avg_percentage,
                    MIN(percentage_of_total) as min_percentage,
                    MAX(percentage_of_total) as max_percentage
                FROM " . DB_PREFIX . "inventory_abc_analysis
                WHERE 1=1
            ";
            
            if ($branch_id) {
                $sql .= " AND branch_id = '" . (int)$branch_id . "'";
            }
            
            $sql .= " GROUP BY abc_class ORDER BY abc_class";

            $query = $this->db->query($sql);

            return $query->rows;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'current_stock',
                'خطأ في تحليل ABC: ' . $e->getMessage(),
                array('user_id' => $this->user->getId())
            );
            
            return array();
        }
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    public function getLowStockProducts($limit = 10) {
        try {
            $sql = "
                SELECT 
                    p.product_id,
                    pd.name as product_name,
                    p.sku,
                    pi.quantity as current_stock,
                    p.minimum as reorder_level,
                    (p.minimum - pi.quantity) as shortage,
                    (pi.quantity * pi.average_cost) as current_value,
                    b.name as branch_name
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN " . DB_PREFIX . "product_inventory pi ON (p.product_id = pi.product_id)
                LEFT JOIN " . DB_PREFIX . "branch b ON (pi.branch_id = b.branch_id)
                WHERE pi.quantity > 0 AND pi.quantity <= p.minimum
                ORDER BY (p.minimum - pi.quantity) DESC
                LIMIT " . (int)$limit
            ;

            $query = $this->db->query($sql);

            return $query->rows;
            
        } catch (Exception $e) {
            return array();
        }
    }

    /**
     * الحصول على المنتجات عالية المخزون
     */
    public function getOverstockProducts($limit = 10) {
        try {
            $sql = "
                SELECT 
                    p.product_id,
                    pd.name as product_name,
                    p.sku,
                    pi.quantity as current_stock,
                    p.minimum as reorder_level,
                    (pi.quantity - (p.minimum * 3)) as excess,
                    (pi.quantity * pi.average_cost) as current_value,
                    b.name as branch_name
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN " . DB_PREFIX . "product_inventory pi ON (p.product_id = pi.product_id)
                LEFT JOIN " . DB_PREFIX . "branch b ON (pi.branch_id = b.branch_id)
                WHERE pi.quantity >= (p.minimum * 3)
                ORDER BY (pi.quantity - (p.minimum * 3)) DESC
                LIMIT " . (int)$limit
            ;

            $query = $this->db->query($sql);

            return $query->rows;
            
        } catch (Exception $e) {
            return array();
        }
    }

    /**
     * تحديث مستويات إعادة الطلب التلقائية
     */
    public function updateReorderLevels($product_id = null) {
        try {
            $this->db->query("BEGIN");
            
            $sql = "
                UPDATE " . DB_PREFIX . "product p
                SET minimum = GREATEST(1, ROUND(
                    (SELECT COALESCE(AVG(daily_sales), 1) * 7
                     FROM (
                         SELECT 
                             DATE(o.date_added) as sale_date,
                             SUM(op.quantity) as daily_sales
                         FROM " . DB_PREFIX . "order_product op
                         LEFT JOIN " . DB_PREFIX . "order o ON (op.order_id = o.order_id)
                         WHERE op.product_id = p.product_id
                         AND o.order_status_id > 0
                         AND o.date_added >= DATE_SUB(NOW(), INTERVAL 90 DAY)
                         GROUP BY DATE(o.date_added)
                     ) as daily_sales_data
                    ), 0
                ))
                WHERE p.status = 1
            ";
            
            if ($product_id) {
                $sql .= " AND p.product_id = '" . (int)$product_id . "'";
            }
            
            $this->db->query($sql);
            
            $this->db->query("COMMIT");
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'update',
                'current_stock',
                'تحديث مستويات إعادة الطلب التلقائية',
                array(
                    'product_id' => $product_id,
                    'user_id' => $this->user->getId()
                )
            );
            
            return true;
            
        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            
            $this->central_service->logActivity(
                'error',
                'current_stock',
                'خطأ في تحديث مستويات إعادة الطلب: ' . $e->getMessage(),
                array('user_id' => $this->user->getId())
            );
            
            return false;
        }
    }
}