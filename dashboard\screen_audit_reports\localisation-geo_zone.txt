📄 Route: localisation/geo_zone
📂 Controller: controller\localisation\geo_zone.php
🧱 Models used (3):
   ✅ localisation/geo_zone (11 functions)
   ✅ localisation/country (6 functions)
   ✅ localisation/tax_rate (8 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\localisation\geo_zone.php (17 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\localisation\geo_zone.php (17 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (10):
   - error_description
   - error_name
   - error_permission
   - error_tax_rate
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (2):
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - text_home
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:08
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.