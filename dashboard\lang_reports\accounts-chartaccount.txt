📄 Route: accounts/chartaccount
📂 Controller: controller\accounts\chartaccount.php
🧱 Models used (3):
   - accounts/chartaccount
   - core/central_service_manager
   - localisation/language
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\chartaccount.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\chartaccount.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_account_code
   - column_account_name
   - column_account_nature
   - column_account_type
   - column_current_balance
   - column_parent_account
   - column_status
   - date_format_long
   - error_excel_not_supported
   - error_name
   - error_parent
   - error_permission
   - error_warning
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_import
   - text_pagination
   - text_print_date
   - text_success
   - text_tax_accounts_added
   - text_tree_view

❌ Missing in Arabic:
   - column_account_code
   - column_account_name
   - column_account_nature
   - column_account_type
   - column_current_balance
   - column_parent_account
   - column_status
   - date_format_long
   - error_excel_not_supported
   - error_name
   - error_parent
   - error_permission
   - error_warning
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_import
   - text_pagination
   - text_print_date
   - text_success
   - text_tax_accounts_added
   - text_tree_view

❌ Missing in English:
   - column_account_code
   - column_account_name
   - column_account_nature
   - column_account_type
   - column_current_balance
   - column_parent_account
   - column_status
   - date_format_long
   - error_excel_not_supported
   - error_name
   - error_parent
   - error_permission
   - error_warning
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_import
   - text_pagination
   - text_print_date
   - text_success
   - text_tax_accounts_added
   - text_tree_view

💡 Suggested Arabic Additions:
   - column_account_code = ""  # TODO: ترجمة عربية
   - column_account_name = ""  # TODO: ترجمة عربية
   - column_account_nature = ""  # TODO: ترجمة عربية
   - column_account_type = ""  # TODO: ترجمة عربية
   - column_current_balance = ""  # TODO: ترجمة عربية
   - column_parent_account = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - date_format_long = ""  # TODO: ترجمة عربية
   - error_excel_not_supported = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_parent = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_warning = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_import = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_print_date = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_tax_accounts_added = ""  # TODO: ترجمة عربية
   - text_tree_view = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_account_code = ""  # TODO: English translation
   - column_account_name = ""  # TODO: English translation
   - column_account_nature = ""  # TODO: English translation
   - column_account_type = ""  # TODO: English translation
   - column_current_balance = ""  # TODO: English translation
   - column_parent_account = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - date_format_long = ""  # TODO: English translation
   - error_excel_not_supported = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_parent = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_warning = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_import = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_print_date = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_tax_accounts_added = ""  # TODO: English translation
   - text_tree_view = ""  # TODO: English translation
