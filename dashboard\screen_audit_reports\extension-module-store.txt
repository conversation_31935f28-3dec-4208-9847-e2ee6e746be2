📄 Route: extension/module/store
📂 Controller: controller\extension\module\store.php
🧱 Models used (1):
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\store.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\module\store.php (8 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\store.php (8 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (21):
   - action
   - button_cancel
   - button_save
   - column_left
   - entry_admin
   - entry_status
   - error_permission
   - error_warning
   - footer
   - header
   - heading_title
   - help_admin
   - text_disabled
   - text_edit
   - text_enabled
   - text_extension
   - text_home
   - text_no
   - text_success
   - text_yes
   ... و 1 متغير آخر

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home
   - text_no
   - text_yes

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home
   - text_no
   - text_yes

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_save
      - text_no
      - error_warning
      - column_left
      - text_yes
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - text_no
      - error_warning
      - column_left
      - text_yes

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:24
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.