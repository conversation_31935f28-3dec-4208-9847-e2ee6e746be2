📄 Route: purchase/supplier_contracts
📂 Controller: controller\purchase\supplier_contracts.php
🧱 Models used (3):
   ✅ purchase/supplier_contracts (16 functions)
   ✅ supplier/supplier (21 functions)
   ✅ localisation/currency (8 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\purchase\supplier_contracts.php (161 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\purchase\supplier_contracts.php (161 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (31):
   - date_format_short
   - error_contract_already_terminated
   - error_contract_id
   - error_contract_not_found
   - error_contract_number
   - error_end_date
   - error_end_date_before_start
   - error_new_end_date_invalid
   - error_permission
   - error_start_date
   - error_supplier
   - text_contract_type_exclusive
   - text_contract_type_framework
   - text_contract_type_maintenance
   - text_contract_type_service
   - text_status_active
   - text_status_draft
   - text_status_expired
   - text_status_pending_approval
   - text_status_terminated
   ... و 11 متغير آخر

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (3):
   - date_format_short
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.