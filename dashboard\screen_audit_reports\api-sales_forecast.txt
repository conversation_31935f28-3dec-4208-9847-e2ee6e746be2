📄 Route: api/sales_forecast
📂 Controller: controller\api\sales_forecast.php
🧱 Models used (3):
   ✅ crm/sales_forecast (35 functions)
   ❌ api/authentication (0 functions)
   ❌ api/rate_limit (0 functions)
🎨 Twig templates (1):
   ✅ view\template\api\sales_forecast.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\api\sales_forecast.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\api\sales_forecast.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in Arabic (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

❌ Missing in English (13):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - user_token

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title
   🟡 MISSING_ENGLISH_VARIABLES: 13 items
      - button_save
      - error_warning
      - text_heading_title
      - column_left
      - error_heading_title
   🟢 MISSING_MODEL_FILES: 2 items
      - api/authentication
      - api/rate_limit

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 13 متغير إنجليزي
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ⚠️ 75%
📅 Analysis Date: 2025-07-21 18:32:47
🔧 Total Issues: 3

⚠️ جيد، لكن يحتاج بعض التحسينات.