📄 Route: mail/return
📂 Controller: controller\mail\return.php
🧱 Models used (1):
   ✅ sale/return (13 functions)
🎨 Twig templates (1):
   ✅ view\template\mail\return.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\mail\return.php (6 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\mail\return.php (6 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - comment
   - date_added
   - date_format_short
   - return_id
   - return_status
   - text_comment
   - text_date_added
   - text_footer
   - text_return_id
   - text_return_status
   - text_subject

❌ Missing in Arabic (5):
   - comment
   - date_added
   - date_format_short
   - return_id
   - return_status

❌ Missing in English (5):
   - comment
   - date_added
   - date_format_short
   - return_id
   - return_status

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - return_status
      - return_id
      - comment
      - date_added
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 5 items
      - return_status
      - return_id
      - comment
      - date_added
      - date_format_short

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 5 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:10
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.