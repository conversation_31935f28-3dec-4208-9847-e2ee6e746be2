📄 Route: extension/module/live_options
📂 Controller: controller\extension\module\live_options.php
🧱 Models used (1):
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\module\live_options.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (1):
   - language\en-gb\extension\module\live_options.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_options_container
   - error_permission
   - error_points_container
   - error_price_container
   - error_reward_container
   - error_special_container
   - error_tax_container
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_options_container
   - error_permission
   - error_points_container
   - error_price_container
   - error_reward_container
   - error_special_container
   - error_tax_container
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_options_container
   - error_permission
   - error_points_container
   - error_price_container
   - error_reward_container
   - error_special_container
   - error_tax_container
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_options_container = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_points_container = ""  # TODO: ترجمة عربية
   - error_price_container = ""  # TODO: ترجمة عربية
   - error_reward_container = ""  # TODO: ترجمة عربية
   - error_special_container = ""  # TODO: ترجمة عربية
   - error_tax_container = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_options_container = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_points_container = ""  # TODO: English translation
   - error_price_container = ""  # TODO: English translation
   - error_reward_container = ""  # TODO: English translation
   - error_special_container = ""  # TODO: English translation
   - error_tax_container = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
