📄 Route: supplier/communication
📂 Controller: controller\supplier\communication.php
🧱 Models used (3):
   - mail/mail
   - supplier/communication
   - supplier/supplier
🎨 Twig templates (1):
   - view\template\supplier\communication.twig
🈯 Arabic Language Files (1):
   - language\ar\supplier\communication.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - heading_title
   - text_home
   - text_pagination
   - text_success
   - text_success_reply

❌ Missing in Arabic:
   - date_format_short
   - heading_title
   - text_home
   - text_pagination
   - text_success
   - text_success_reply

❌ Missing in English:
   - date_format_short
   - heading_title
   - text_home
   - text_pagination
   - text_success
   - text_success_reply

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_success_reply = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_success_reply = ""  # TODO: English translation
