📄 Route: customer/customer_group
📂 Controller: controller\customer\customer_group.php
🧱 Models used (4):
   - customer/customer
   - customer/customer_group
   - localisation/language
   - setting/store
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\customer\customer_group.php
🇬🇧 English Language Files (1):
   - language\en-gb\customer\customer_group.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_customer
   - error_default
   - error_name
   - error_permission
   - error_store
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_customer
   - error_default
   - error_name
   - error_permission
   - error_store
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_customer
   - error_default
   - error_name
   - error_permission
   - error_store
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_customer = ""  # TODO: ترجمة عربية
   - error_default = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_store = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_default = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_customer = ""  # TODO: English translation
   - error_default = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_store = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_default = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
