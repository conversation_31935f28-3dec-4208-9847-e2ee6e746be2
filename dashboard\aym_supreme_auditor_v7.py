#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AYM ERP SUPREME Screen Auditor - Master Edition (V7.0)
Author: AI-Enhanced Expert Review
Purpose: Generate SUPREME quality screen analysis reports with complete fix guidance

Features: 
- Complete Constitutional Compliance Analysis
- Detailed Fix Instructions with Code Examples
- Step-by-Step Implementation Guide
- Critical Issues Detection with Solutions
- Performance & Security Analysis
- Database Integration Validation
- Complete MVC Architecture Review
- Exact Code Snippets for Fixes
- Line-by-Line Implementation Guide
"""

import re
from pathlib import Path
from datetime import datetime

class AYMSupremeScreenAuditorV7_0:
    
    def __init__(self, dashboard_path):
        self.dashboard_path = Path(dashboard_path)
        self.controller_path = self.dashboard_path / 'controller'
        self.model_path = self.dashboard_path / 'model'
        self.view_path = self.dashboard_path / 'view' / 'template'
        self.lang_path = self.dashboard_path / 'language'
        self.output_path = self.dashboard_path / 'supreme_audit_reports'
        self.db_file = self.dashboard_path.parent / 'db.txt'
        self.output_path.mkdir(exist_ok=True)
        
        # تحميل جداول قاعدة البيانات
        self.db_tables = self._load_database_tables()
        
        # تحميل الدستور الشامل
        self.constitution = self._load_aym_constitution()
        
        print(f"🏆 AYM SUPREME Screen Auditor V7.0 Initialized")
        print(f"📁 Supreme Quality Reports: {self.output_path.resolve()}")
        print(f"📊 Database tables: {len(self.db_tables)} tables")
        print(f"📜 Constitutional rules: {len(self.constitution)} rules")
        print(f"🎯 Ready to generate SUPREME quality fix guidance!")

    def run(self):
        """تشغيل المدقق الفائق على جميع الشاشات"""
        controllers = list(self.controller_path.rglob('*.php'))
        total_screens = len(controllers)
        
        print(f"🚀 Starting SUPREME audit of {total_screens} screens...")
        
        for i, ctrl_file in enumerate(controllers, 1):
            route = self._route_from_path(ctrl_file)
            print(f"🔍 [{i}/{total_screens}] SUPREME Analysis: {route}")
            
            # تحليل فائق الجودة
            analysis = self._supreme_screen_analysis(ctrl_file, route)
            
            # إنشاء التقرير الفائق
            report = self._generate_supreme_report(route, analysis)
            
            # حفظ التقرير
            report_filename = route.replace('/', '-') + '.md'
            report_path = self.output_path / report_filename
            report_path.write_text(report, encoding='utf-8')
        
        print(f"\n🏆 تم إنشاء {total_screens} تقرير فائق الجودة!")
        print("🎉 جميع التقارير تحتوي على دليل إصلاح شامل مع أمثلة الكود!")

    def _load_database_tables(self):
        """تحميل جداول قاعدة البيانات"""
        tables = set()
        if self.db_file.exists():
            try:
                content = self.db_file.read_text(encoding='utf-8')
                table_pattern = re.compile(r'CREATE TABLE `?(\w+)`?', re.IGNORECASE)
                tables.update(table_pattern.findall(content))
            except Exception as e:
                print(f"⚠️ Warning: Could not load database tables: {e}")
        return tables

    def _load_aym_constitution(self):
        """تحميل الدستور الشامل لـ AYM ERP"""
        return {
            'central_services': {
                'required': ['core/central_service_manager'],
                'description': 'يجب استدعاء الخدمات المركزية في كل كونترولر',
                'fix': 'أضف: $this->load->model("core/central_service_manager");'
            },
            'permissions': {
                'required': ['hasPermission', 'hasKey'],
                'description': 'يجب استخدام نظام الصلاحيات المتقدم',
                'fix': 'أضف: if (!$this->user->hasPermission("modify", "route/name") || !$this->user->hasKey("advanced_permission")) {'
            },
            'language_files': {
                'required': ['ar', 'en-gb'],
                'description': 'يجب وجود ملفات لغة متطابقة للعربية والإنجليزية',
                'fix': 'أنشئ ملفات اللغة المفقودة مع نفس المتغيرات'
            },
            'database_prefix': {
                'required': 'cod_',
                'description': 'جميع الجداول يجب أن تبدأ بـ cod_',
                'fix': 'استخدم: DB_PREFIX . "table_name" أو تأكد من البادئة cod_'
            },
            'mvc_structure': {
                'required': ['controller', 'model', 'view', 'language'],
                'description': 'يجب اتباع هيكل MVC الكامل',
                'fix': 'أنشئ الملفات المفقودة: model, view, language'
            },
            'config_usage': {
                'required': '$this->config->get()',
                'description': 'استخدم الإعدادات المركزية بدلاً من القيم الثابتة',
                'fix': 'استبدل القيم الثابتة بـ: $this->config->get("setting_name")'
            },
            'ajax_security': {
                'required': ['token', 'csrf'],
                'description': 'يجب تأمين طلبات AJAX',
                'fix': 'أضف: $this->session->data["token"] validation'
            },
            'error_handling': {
                'required': ['try-catch', 'logging'],
                'description': 'يجب معالجة الأخطاء وتسجيلها',
                'fix': 'أضف: try-catch blocks مع $this->log->write()'
            }
        }

    def _route_from_path(self, ctrl_file):
        """استخراج المسار من ملف الكونترولر"""
        return str(ctrl_file.relative_to(self.controller_path).with_suffix('')).replace('\\', '/')

    def _supreme_screen_analysis(self, ctrl_file, route):
        """تحليل فائق الجودة للشاشة"""
        analysis = {
            'route': route,
            'controller_file': str(ctrl_file),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'constitutional_compliance': {},
            'mvc_analysis': {},
            'security_analysis': {},
            'performance_analysis': {},
            'database_analysis': {},
            'language_analysis': {},
            'critical_issues': [],
            'fix_instructions': [],
            'code_examples': {},
            'implementation_steps': []
        }

        # تحليل الامتثال الدستوري
        analysis['constitutional_compliance'] = self._analyze_constitutional_compliance(ctrl_file)
        
        # تحليل MVC
        analysis['mvc_analysis'] = self._analyze_mvc_structure(ctrl_file, route)
        
        # تحليل الأمان
        analysis['security_analysis'] = self._analyze_security(ctrl_file)
        
        # تحليل الأداء
        analysis['performance_analysis'] = self._analyze_performance(ctrl_file)
        
        # تحليل قاعدة البيانات
        analysis['database_analysis'] = self._analyze_database_integration(ctrl_file, analysis['mvc_analysis'].get('models', []))
        
        # تحليل اللغة
        analysis['language_analysis'] = self._analyze_language_integration(ctrl_file, route, analysis['mvc_analysis'])
        
        # تحديد المشاكل الحرجة
        analysis['critical_issues'] = self._identify_critical_issues(analysis)
        
        # إنشاء تعليمات الإصلاح
        analysis['fix_instructions'] = self._generate_fix_instructions(analysis)
        
        # إنشاء أمثلة الكود
        analysis['code_examples'] = self._generate_code_examples(analysis)
        
        # إنشاء خطوات التنفيذ
        analysis['implementation_steps'] = self._generate_implementation_steps(analysis)

        return analysis

    def _analyze_constitutional_compliance(self, ctrl_file):
        """تحليل الامتثال للدستور الشامل"""
        compliance = {}

        try:
            content = ctrl_file.read_text(encoding='utf-8')

            # فحص الخدمات المركزية
            compliance['central_services'] = {
                'status': 'central_service_manager' in content,
                'rule': self.constitution['central_services'],
                'found_services': re.findall(r'\$this->load->model\(["\']([^"\']*service[^"\']*)["\']', content)
            }

            # فحص الصلاحيات
            has_permission = 'hasPermission' in content
            has_key = 'hasKey' in content
            compliance['permissions'] = {
                'status': has_permission and has_key,
                'rule': self.constitution['permissions'],
                'has_permission': has_permission,
                'has_key': has_key,
                'found_permissions': re.findall(r'hasPermission\(["\']([^"\']+)["\']', content)
            }

            # فحص استخدام الإعدادات
            config_usage = re.findall(r'\$this->config->get\(["\']([^"\']+)["\']', content)
            compliance['config_usage'] = {
                'status': len(config_usage) > 0,
                'rule': self.constitution['config_usage'],
                'found_configs': config_usage,
                'hardcoded_values': len(re.findall(r'["\'][^"\']*[\u0600-\u06FF][^"\']*["\']', content))
            }

            # فحص أمان AJAX
            ajax_security = 'token' in content and ('csrf' in content or 'session' in content)
            compliance['ajax_security'] = {
                'status': ajax_security,
                'rule': self.constitution['ajax_security'],
                'has_token': 'token' in content,
                'has_csrf': 'csrf' in content or 'session' in content
            }

            # فحص معالجة الأخطاء
            error_handling = 'try' in content and 'catch' in content
            compliance['error_handling'] = {
                'status': error_handling,
                'rule': self.constitution['error_handling'],
                'has_try_catch': error_handling,
                'has_logging': '$this->log' in content or 'error_log' in content
            }

        except Exception as e:
            compliance['error'] = str(e)

        return compliance

    def _analyze_mvc_structure(self, ctrl_file, route):
        """تحليل هيكل MVC"""
        mvc = {
            'controller': {'exists': ctrl_file.exists(), 'path': str(ctrl_file)},
            'models': [],
            'views': [],
            'languages': {'ar': [], 'en': []}
        }

        # تحليل الموديلات
        if ctrl_file.exists():
            content = ctrl_file.read_text(encoding='utf-8')
            model_calls = re.findall(r'\$this->load->model\(["\']([^"\']+)["\']', content)

            seen_models = set()
            for model_call in model_calls:
                if model_call not in seen_models:
                    seen_models.add(model_call)
                    model_path = self.model_path / f"{model_call}.php"
                    mvc['models'].append({
                        'route': model_call,
                        'path': str(model_path),
                        'exists': model_path.exists(),
                        'functions': self._extract_functions(model_path) if model_path.exists() else []
                    })

        # تحليل القوالب
        main_view = self.view_path / f"{route}.twig"
        if main_view.exists():
            mvc['views'].append({
                'type': 'main',
                'path': str(main_view),
                'exists': True,
                'variables': self._extract_twig_variables(main_view)
            })

        view_folder = self.view_path / route
        if view_folder.is_dir():
            for twig_file in view_folder.glob('*.twig'):
                mvc['views'].append({
                    'type': 'folder',
                    'path': str(twig_file),
                    'exists': True,
                    'variables': self._extract_twig_variables(twig_file)
                })

        # تحليل ملفات اللغة
        for lang_code in ['ar', 'en-gb']:
            lang_key = 'ar' if lang_code == 'ar' else 'en'
            lang_file = self.lang_path / lang_code / f"{route}.php"
            mvc['languages'][lang_key].append({
                'path': str(lang_file),
                'exists': lang_file.exists(),
                'variables': self._extract_language_variables(lang_file) if lang_file.exists() else set()
            })

        return mvc

    def _analyze_security(self, ctrl_file):
        """تحليل الأمان"""
        security = {
            'sql_injection': {'status': 'SAFE', 'issues': []},
            'xss_protection': {'status': 'SAFE', 'issues': []},
            'csrf_protection': {'status': 'SAFE', 'issues': []},
            'input_validation': {'status': 'SAFE', 'issues': []},
            'authentication': {'status': 'SAFE', 'issues': []},
            'authorization': {'status': 'SAFE', 'issues': []}
        }

        try:
            content = ctrl_file.read_text(encoding='utf-8')

            # فحص SQL Injection
            sql_patterns = [
                r'\$[^"\']*\s*\.\s*["\'][^"\']*["\']',  # String concatenation in SQL
                r'query\(["\'][^"\']*\$[^"\']*["\']',   # Direct variable in query
            ]
            for pattern in sql_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    security['sql_injection']['status'] = 'VULNERABLE'
                    security['sql_injection']['issues'].extend(matches)

            # فحص XSS
            if 'echo' in content and 'htmlspecialchars' not in content:
                security['xss_protection']['status'] = 'VULNERABLE'
                security['xss_protection']['issues'].append('Direct echo without sanitization')

            # فحص CSRF
            if 'POST' in content and 'token' not in content:
                security['csrf_protection']['status'] = 'VULNERABLE'
                security['csrf_protection']['issues'].append('POST requests without CSRF token')

            # فحص التحقق من المدخلات
            if '$_POST' in content or '$_GET' in content:
                if 'filter_var' not in content and 'validate' not in content:
                    security['input_validation']['status'] = 'VULNERABLE'
                    security['input_validation']['issues'].append('Direct use of $_POST/$_GET without validation')

            # فحص المصادقة
            if 'login' not in content.lower() and 'hasPermission' not in content:
                security['authentication']['status'] = 'MISSING'
                security['authentication']['issues'].append('No authentication checks found')

        except Exception as e:
            security['error'] = str(e)

        return security

    def _analyze_performance(self, ctrl_file):
        """تحليل الأداء"""
        performance = {
            'database_queries': {'count': 0, 'issues': []},
            'memory_usage': {'status': 'GOOD', 'issues': []},
            'caching': {'status': 'NONE', 'issues': []},
            'optimization': {'status': 'GOOD', 'issues': []}
        }

        try:
            content = ctrl_file.read_text(encoding='utf-8')

            # عد استعلامات قاعدة البيانات
            query_patterns = ['query(', 'SELECT', 'INSERT', 'UPDATE', 'DELETE']
            total_queries = sum(content.upper().count(pattern.upper()) for pattern in query_patterns)
            performance['database_queries']['count'] = total_queries

            if total_queries > 10:
                performance['database_queries']['issues'].append(f'Too many database queries: {total_queries}')

            # فحص استخدام الذاكرة
            if 'memory_get_usage' not in content and total_queries > 5:
                performance['memory_usage']['status'] = 'WARNING'
                performance['memory_usage']['issues'].append('No memory monitoring with multiple queries')

            # فحص التخزين المؤقت
            if 'cache' in content.lower():
                performance['caching']['status'] = 'IMPLEMENTED'
            elif total_queries > 3:
                performance['caching']['status'] = 'NEEDED'
                performance['caching']['issues'].append('Caching recommended for multiple queries')

            # فحص التحسين
            if 'foreach' in content and 'query' in content:
                performance['optimization']['status'] = 'WARNING'
                performance['optimization']['issues'].append('Potential N+1 query problem in loops')

        except Exception as e:
            performance['error'] = str(e)

        return performance

    def _analyze_database_integration(self, ctrl_file, models):
        """تحليل تكامل قاعدة البيانات"""
        db_analysis = {
            'tables_used': set(),
            'valid_tables': set(),
            'invalid_tables': set(),
            'prefix_compliance': {'status': True, 'issues': []},
            'query_analysis': {'safe': [], 'unsafe': []}
        }

        try:
            # تحليل الكونترولر
            content = ctrl_file.read_text(encoding='utf-8')
            tables = self._extract_database_tables(content)
            db_analysis['tables_used'].update(tables)

            # تحليل الموديلات
            for model in models:
                if model['exists']:
                    model_content = Path(model['path']).read_text(encoding='utf-8')
                    model_tables = self._extract_database_tables(model_content)
                    db_analysis['tables_used'].update(model_tables)

            # التحقق من صحة الجداول
            for table in db_analysis['tables_used']:
                if table in self.db_tables:
                    db_analysis['valid_tables'].add(table)
                else:
                    db_analysis['invalid_tables'].add(table)

            # التحقق من البادئة
            for table in db_analysis['tables_used']:
                if not table.startswith('cod_') and table not in ['user', 'session']:
                    db_analysis['prefix_compliance']['status'] = False
                    db_analysis['prefix_compliance']['issues'].append(f'Table {table} missing cod_ prefix')

        except Exception as e:
            db_analysis['error'] = str(e)

        return db_analysis

    def _extract_database_tables(self, content):
        """استخراج أسماء الجداول من المحتوى"""
        tables = set()
        patterns = [
            r'FROM\s+`?(\w+)`?',
            r'INSERT\s+INTO\s+`?(\w+)`?',
            r'UPDATE\s+`?(\w+)`?',
            r'DELETE\s+FROM\s+`?(\w+)`?',
            r'JOIN\s+`?(\w+)`?',
            r'TABLE_\w+\s*=\s*["\'](\w+)["\']'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            tables.update(matches)

        return tables

    def _analyze_language_integration(self, ctrl_file, route, mvc_analysis):
        """تحليل تكامل اللغة المتقدم - مدمج من lang_comparison_script.py"""
        lang_analysis = {
            'used_variables': set(),
            'ar_variables': set(),
            'en_variables': set(),
            'missing_ar': set(),
            'missing_en': set(),
            'unused_ar': set(),
            'unused_en': set(),
            'hardcoded_text': [],
            'compliance_score': 0,
            'detailed_analysis': {},
            'suggestions': {'ar': [], 'en': []},
            'language_files_found': {'ar': [], 'en': []},
            'models_analyzed': [],
            'views_analyzed': []
        }

        try:
            # 1. استخراج المتغيرات المستخدمة من الكونترولر (مثل lang_comparison_script.py)
            if ctrl_file.exists():
                content = ctrl_file.read_text(encoding='utf-8', errors='ignore')

                # أنماط متقدمة لاستخراج متغيرات اللغة
                language_patterns = [
                    r'\$this->language->get\(["\']([^"\']+)["\']\)',
                    r'__\(["\']([^"\']+)["\']\)',
                    r'\$lang\[["\']([^"\']+)["\']\]',
                    r'\$data\[["\']([^"\']+)["\']\]\s*=\s*\$this->language->get',
                    r'language->get\(["\']([^"\']+)["\']\)'
                ]

                for pattern in language_patterns:
                    matches = re.findall(pattern, content)
                    lang_analysis['used_variables'].update(matches)

                # البحث عن النصوص المباشرة العربية والإنجليزية
                hardcoded_patterns = [
                    r'["\'][^"\']*[\u0600-\u06FF][^"\']*["\']',  # نصوص عربية
                    r'["\'][A-Za-z\s]{10,}["\']',  # نصوص إنجليزية طويلة
                    r'echo\s+["\'][^"\']+["\']',  # echo مباشر
                    r'print\s+["\'][^"\']+["\']'   # print مباشر
                ]

                for pattern in hardcoded_patterns:
                    matches = re.findall(pattern, content)
                    lang_analysis['hardcoded_text'].extend(matches)

            # 2. تحليل الموديلات المرتبطة
            for model in mvc_analysis.get('models', []):
                if model['exists']:
                    try:
                        model_content = Path(model['path']).read_text(encoding='utf-8', errors='ignore')
                        for pattern in language_patterns:
                            matches = re.findall(pattern, model_content)
                            lang_analysis['used_variables'].update(matches)
                        lang_analysis['models_analyzed'].append(model['route'])
                    except:
                        pass

            # 3. تحليل القوالب (Twig) المتقدم
            for view in mvc_analysis.get('views', []):
                if view['exists']:
                    try:
                        twig_content = Path(view['path']).read_text(encoding='utf-8', errors='ignore')

                        # أنماط Twig المتقدمة
                        twig_patterns = [
                            r'\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}',  # متغيرات Twig عادية
                            r'\{\{\s*\'([^\']+)\'\s*\|\s*trans\s*\}\}',  # ترجمة مع فلتر
                            r'\{\{\s*"([^"]+)"\s*\|\s*trans\s*\}\}',
                            r'trans\(["\']([^"\']+)["\']\)',
                            r'\{\%\s*trans\s*\%\}([^{]+)\{\%\s*endtrans\s*\%\}',  # كتل الترجمة
                        ]

                        for pattern in twig_patterns:
                            matches = re.findall(pattern, twig_content)
                            lang_analysis['used_variables'].update(matches)

                        lang_analysis['views_analyzed'].append(view['path'])
                    except:
                        pass

            # 4. تحليل ملفات اللغة المتقدم
            # العربية
            for lang_file in mvc_analysis['languages']['ar']:
                if lang_file['exists']:
                    try:
                        ar_content = Path(lang_file['path']).read_text(encoding='utf-8', errors='ignore')

                        # إزالة التعليقات
                        ar_content = re.sub(r'/\*.*?\*/', '', ar_content, flags=re.DOTALL)
                        ar_content = re.sub(r'//.*', '', ar_content)

                        # استخراج المتغيرات بأنماط متعددة
                        var_patterns = [
                            r'\$_\[["\']([^"\']+)["\']\]',
                            r'["\']([^"\']+)["\']\s*=>',
                            r'define\(["\']([^"\']+)["\']\s*,',
                        ]

                        for pattern in var_patterns:
                            matches = re.findall(pattern, ar_content)
                            lang_analysis['ar_variables'].update(matches)

                        lang_analysis['language_files_found']['ar'].append(lang_file['path'])
                    except:
                        pass

            # الإنجليزية
            for lang_file in mvc_analysis['languages']['en']:
                if lang_file['exists']:
                    try:
                        en_content = Path(lang_file['path']).read_text(encoding='utf-8', errors='ignore')

                        # إزالة التعليقات
                        en_content = re.sub(r'/\*.*?\*/', '', en_content, flags=re.DOTALL)
                        en_content = re.sub(r'//.*', '', en_content)

                        # استخراج المتغيرات
                        for pattern in var_patterns:
                            matches = re.findall(pattern, en_content)
                            lang_analysis['en_variables'].update(matches)

                        lang_analysis['language_files_found']['en'].append(lang_file['path'])
                    except:
                        pass

            # 5. حساب المتغيرات المفقودة والزائدة (مثل lang_comparison_script.py)
            lang_analysis['missing_ar'] = lang_analysis['used_variables'] - lang_analysis['ar_variables']
            lang_analysis['missing_en'] = lang_analysis['used_variables'] - lang_analysis['en_variables']
            lang_analysis['unused_ar'] = lang_analysis['ar_variables'] - lang_analysis['used_variables']
            lang_analysis['unused_en'] = lang_analysis['en_variables'] - lang_analysis['used_variables']

            # 6. إنشاء اقتراحات الإضافة (مثل lang_comparison_script.py)
            for var in sorted(lang_analysis['missing_ar']):
                lang_analysis['suggestions']['ar'].append(f"$_['{var}'] = '';  // TODO: ترجمة عربية")

            for var in sorted(lang_analysis['missing_en']):
                lang_analysis['suggestions']['en'].append(f"$_['{var}'] = '';  // TODO: English translation")

            # 7. تحليل تفصيلي متقدم
            lang_analysis['detailed_analysis'] = {
                'total_used': len(lang_analysis['used_variables']),
                'total_ar_defined': len(lang_analysis['ar_variables']),
                'total_en_defined': len(lang_analysis['en_variables']),
                'ar_coverage': (len(lang_analysis['ar_variables'] & lang_analysis['used_variables']) / len(lang_analysis['used_variables']) * 100) if lang_analysis['used_variables'] else 100,
                'en_coverage': (len(lang_analysis['en_variables'] & lang_analysis['used_variables']) / len(lang_analysis['used_variables']) * 100) if lang_analysis['used_variables'] else 100,
                'hardcoded_count': len(lang_analysis['hardcoded_text']),
                'models_with_lang': len(lang_analysis['models_analyzed']),
                'views_with_lang': len(lang_analysis['views_analyzed'])
            }

            # 8. حساب نقاط الامتثال المتقدم
            total_used = len(lang_analysis['used_variables'])
            if total_used > 0:
                missing_penalty = len(lang_analysis['missing_ar']) + len(lang_analysis['missing_en'])
                hardcoded_penalty = len(lang_analysis['hardcoded_text'])
                unused_penalty = (len(lang_analysis['unused_ar']) + len(lang_analysis['unused_en'])) * 0.5

                lang_analysis['compliance_score'] = max(0, 100 - (missing_penalty * 5) - (hardcoded_penalty * 3) - unused_penalty)
            else:
                lang_analysis['compliance_score'] = 100

        except Exception as e:
            lang_analysis['error'] = str(e)

        return lang_analysis

    def _extract_functions(self, file_path):
        """استخراج الدوال من ملف PHP"""
        try:
            content = file_path.read_text(encoding='utf-8')
            return re.findall(r'function\s+(\w+)\s*\(', content)
        except:
            return []

    def _extract_twig_variables(self, twig_file):
        """استخراج متغيرات من ملف Twig"""
        try:
            content = twig_file.read_text(encoding='utf-8')
            return set(re.findall(r'\{\{\s*(\w+)\s*\}\}', content))
        except:
            return set()

    def _extract_language_variables(self, lang_file):
        """استخراج متغيرات اللغة من ملف PHP"""
        try:
            content = lang_file.read_text(encoding='utf-8')
            return set(re.findall(r"\$_\['([^']+)'\]", content))
        except:
            return set()

    def _identify_critical_issues(self, analysis):
        """تحديد المشاكل الحرجة"""
        issues = []

        # مشاكل الامتثال الدستوري
        compliance = analysis['constitutional_compliance']
        if not compliance.get('central_services', {}).get('status', False):
            issues.append({
                'type': 'CONSTITUTIONAL_VIOLATION',
                'severity': 'CRITICAL',
                'category': 'Central Services',
                'description': 'لم يتم استدعاء الخدمات المركزية المطلوبة',
                'impact': 'فقدان التدقيق والإشعارات والتكامل مع النظام',
                'fix_priority': 1
            })

        if not compliance.get('permissions', {}).get('status', False):
            issues.append({
                'type': 'SECURITY_VIOLATION',
                'severity': 'CRITICAL',
                'category': 'Permissions',
                'description': 'نظام الصلاحيات غير مكتمل أو مفقود',
                'impact': 'ثغرات أمنية خطيرة في التحكم بالوصول',
                'fix_priority': 1
            })

        # مشاكل الأمان
        security = analysis['security_analysis']
        for sec_type, sec_data in security.items():
            if isinstance(sec_data, dict) and sec_data.get('status') == 'VULNERABLE':
                issues.append({
                    'type': 'SECURITY_VULNERABILITY',
                    'severity': 'CRITICAL',
                    'category': sec_type.replace('_', ' ').title(),
                    'description': f'ثغرة أمنية في {sec_type}',
                    'impact': 'تعرض النظام للهجمات والاختراق',
                    'fix_priority': 1,
                    'details': sec_data.get('issues', [])
                })

        # مشاكل قاعدة البيانات
        db_analysis = analysis['database_analysis']
        if db_analysis.get('invalid_tables'):
            issues.append({
                'type': 'DATABASE_ERROR',
                'severity': 'CRITICAL',
                'category': 'Database Integration',
                'description': 'استخدام جداول غير موجودة في قاعدة البيانات',
                'impact': 'أخطاء في وقت التشغيل وفشل العمليات',
                'fix_priority': 1,
                'details': list(db_analysis['invalid_tables'])
            })

        # مشاكل اللغة
        lang_analysis = analysis['language_analysis']
        if len(lang_analysis['missing_ar']) > 5 or len(lang_analysis['missing_en']) > 5:
            issues.append({
                'type': 'LANGUAGE_MISMATCH',
                'severity': 'HIGH',
                'category': 'Internationalization',
                'description': 'عدم تطابق كبير في متغيرات اللغة',
                'impact': 'نصوص مفقودة وتجربة مستخدم سيئة',
                'fix_priority': 2,
                'details': {
                    'missing_ar': len(lang_analysis['missing_ar']),
                    'missing_en': len(lang_analysis['missing_en'])
                }
            })

        if len(lang_analysis['hardcoded_text']) > 3:
            issues.append({
                'type': 'HARDCODED_TEXT',
                'severity': 'MEDIUM',
                'category': 'Code Quality',
                'description': 'نصوص مباشرة في الكود تمنع الترجمة',
                'impact': 'صعوبة في الصيانة والترجمة',
                'fix_priority': 3,
                'details': lang_analysis['hardcoded_text'][:5]
            })

        # مشاكل الأداء
        performance = analysis['performance_analysis']
        if performance['database_queries']['count'] > 10:
            issues.append({
                'type': 'PERFORMANCE_ISSUE',
                'severity': 'MEDIUM',
                'category': 'Performance',
                'description': 'عدد كبير من استعلامات قاعدة البيانات',
                'impact': 'بطء في الاستجابة وضغط على الخادم',
                'fix_priority': 3,
                'details': f"{performance['database_queries']['count']} queries found"
            })

        # مشاكل MVC
        mvc = analysis['mvc_analysis']
        missing_models = [m for m in mvc['models'] if not m['exists']]
        if missing_models:
            issues.append({
                'type': 'MISSING_FILES',
                'severity': 'HIGH',
                'category': 'MVC Structure',
                'description': 'ملفات موديل مفقودة',
                'impact': 'أخطاء في وقت التشغيل',
                'fix_priority': 2,
                'details': [m['route'] for m in missing_models]
            })

        return sorted(issues, key=lambda x: x['fix_priority'])

    def _generate_fix_instructions(self, analysis):
        """إنشاء تعليمات الإصلاح التفصيلية"""
        instructions = []

        # تعليمات الامتثال الدستوري
        compliance = analysis['constitutional_compliance']

        if not compliance.get('central_services', {}).get('status', False):
            instructions.append({
                'priority': 1,
                'category': 'Constitutional Compliance',
                'title': 'إضافة الخدمات المركزية',
                'description': 'يجب استدعاء الخدمات المركزية في بداية الكونترولر',
                'steps': [
                    'افتح ملف الكونترولر',
                    'أضف في بداية كل دالة: $this->load->model("core/central_service_manager");',
                    'استخدم الخدمات: $this->model_core_central_service_manager->logActivity();',
                    'تأكد من تسجيل جميع العمليات المهمة'
                ],
                'code_location': 'في بداية كل دالة في الكونترولر',
                'testing': 'تأكد من عمل التدقيق والإشعارات'
            })

        if not compliance.get('permissions', {}).get('status', False):
            instructions.append({
                'priority': 1,
                'category': 'Security',
                'title': 'إضافة نظام الصلاحيات المتقدم',
                'description': 'يجب تطبيق نظام الصلاحيات الشامل',
                'steps': [
                    'أضف فحص الصلاحيات الأساسية: if (!$this->user->hasPermission("modify", "route/name"))',
                    'أضف فحص الصلاحيات المتقدمة: if (!$this->user->hasKey("advanced_permission"))',
                    'أضف رسائل خطأ مناسبة للصلاحيات المرفوضة',
                    'اختبر جميع مستويات الصلاحيات'
                ],
                'code_location': 'في بداية كل دالة تحتاج صلاحيات',
                'testing': 'اختبر مع مستخدمين بصلاحيات مختلفة'
            })

        # تعليمات الأمان
        security = analysis['security_analysis']
        for sec_type, sec_data in security.items():
            if isinstance(sec_data, dict) and sec_data.get('status') == 'VULNERABLE':
                if sec_type == 'sql_injection':
                    instructions.append({
                        'priority': 1,
                        'category': 'Security',
                        'title': 'إصلاح ثغرات SQL Injection',
                        'description': 'استخدام Prepared Statements لمنع SQL Injection',
                        'steps': [
                            'استبدل الاستعلامات المباشرة بـ Prepared Statements',
                            'استخدم: $this->db->query("SELECT * FROM table WHERE id = ?", array($id))',
                            'تجنب ربط المتغيرات مباشرة في الاستعلامات',
                            'استخدم escape functions للبيانات المدخلة'
                        ],
                        'code_location': 'في جميع استعلامات قاعدة البيانات',
                        'testing': 'اختبر مع مدخلات خبيثة'
                    })

        # تعليمات اللغة
        lang_analysis = analysis['language_analysis']
        if lang_analysis['missing_ar'] or lang_analysis['missing_en']:
            instructions.append({
                'priority': 2,
                'category': 'Internationalization',
                'title': 'إصلاح متغيرات اللغة المفقودة',
                'description': 'إضافة المتغيرات المفقودة لملفات اللغة',
                'steps': [
                    f'أضف {len(lang_analysis["missing_ar"])} متغير للملف العربي',
                    f'أضف {len(lang_analysis["missing_en"])} متغير للملف الإنجليزي',
                    'تأكد من تطابق أسماء المتغيرات',
                    'اختبر جميع النصوص في كلا اللغتين'
                ],
                'code_location': 'ملفات اللغة في مجلد language/',
                'testing': 'اختبر التبديل بين اللغات'
            })

        return instructions

    def _generate_code_examples(self, analysis):
        """إنشاء أمثلة الكود للإصلاح"""
        examples = {}

        # مثال الخدمات المركزية
        compliance = analysis['constitutional_compliance']
        if not compliance.get('central_services', {}).get('status', False):
            examples['central_services'] = {
                'title': 'إضافة الخدمات المركزية',
                'before': '''public function index() {
    // كود بدون خدمات مركزية
    $data['products'] = $this->model_catalog_product->getProducts();
}''',
                'after': '''public function index() {
    // إضافة الخدمات المركزية
    $this->load->model('core/central_service_manager');

    // تسجيل النشاط
    $this->model_core_central_service_manager->logActivity([
        'action' => 'view_products',
        'user_id' => $this->user->getId(),
        'route' => $this->request->get['route']
    ]);

    $data['products'] = $this->model_catalog_product->getProducts();

    // إرسال إشعار إذا لزم الأمر
    $this->model_core_central_service_manager->sendNotification([
        'type' => 'info',
        'message' => 'تم عرض قائمة المنتجات',
        'user_id' => $this->user->getId()
    ]);
}'''
            }

        # مثال الصلاحيات
        if not compliance.get('permissions', {}).get('status', False):
            examples['permissions'] = {
                'title': 'إضافة نظام الصلاحيات المتقدم',
                'before': '''public function add() {
    // كود بدون فحص صلاحيات
    if ($this->request->server['REQUEST_METHOD'] == 'POST') {
        $this->model_catalog_product->addProduct($this->request->post);
    }
}''',
                'after': '''public function add() {
    // فحص الصلاحيات الأساسية
    if (!$this->user->hasPermission('modify', 'catalog/product')) {
        $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token']));
    }

    // فحص الصلاحيات المتقدمة
    if (!$this->user->hasKey('product_management_advanced')) {
        $this->session->data['error'] = 'ليس لديك صلاحية للوصول للميزات المتقدمة';
        $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token']));
    }

    if ($this->request->server['REQUEST_METHOD'] == 'POST') {
        $this->model_catalog_product->addProduct($this->request->post);
    }
}'''
            }

        # مثال أمان SQL
        security = analysis['security_analysis']
        if security.get('sql_injection', {}).get('status') == 'VULNERABLE':
            examples['sql_security'] = {
                'title': 'إصلاح ثغرات SQL Injection',
                'before': '''// كود غير آمن
$query = "SELECT * FROM " . DB_PREFIX . "product WHERE product_id = '" . $product_id . "'";
$result = $this->db->query($query);''',
                'after': '''// كود آمن مع Prepared Statements
$query = "SELECT * FROM " . DB_PREFIX . "product WHERE product_id = ?";
$result = $this->db->query($query, array((int)$product_id));

// أو استخدام escape
$query = "SELECT * FROM " . DB_PREFIX . "product WHERE product_id = '" . $this->db->escape($product_id) . "'";
$result = $this->db->query($query);'''
            }

        # مثال متغيرات اللغة
        lang_analysis = analysis['language_analysis']
        if lang_analysis['missing_ar'] or lang_analysis['hardcoded_text']:
            examples['language_variables'] = {
                'title': 'إصلاح متغيرات اللغة',
                'before': '''// نص مباشر في الكود
$data['heading_title'] = 'إدارة المنتجات';
$data['text_confirm'] = 'هل أنت متأكد من الحذف؟';''',
                'after': '''// استخدام متغيرات اللغة
$this->load->language('catalog/product');
$data['heading_title'] = $this->language->get('heading_title');
$data['text_confirm'] = $this->language->get('text_confirm');

// في ملف اللغة العربية (ar/catalog/product.php):
$_['heading_title'] = 'إدارة المنتجات';
$_['text_confirm'] = 'هل أنت متأكد من الحذف؟';

// في ملف اللغة الإنجليزية (en-gb/catalog/product.php):
$_['heading_title'] = 'Product Management';
$_['text_confirm'] = 'Are you sure you want to delete?';'''
            }

        # مثال تحسين الأداء
        performance = analysis['performance_analysis']
        if performance['database_queries']['count'] > 10:
            examples['performance_optimization'] = {
                'title': 'تحسين الأداء وتقليل الاستعلامات',
                'before': '''// كود غير محسن
foreach ($products as $product) {
    $product['category'] = $this->model_catalog_category->getCategory($product['category_id']);
    $product['manufacturer'] = $this->model_catalog_manufacturer->getManufacturer($product['manufacturer_id']);
}''',
                'after': '''// كود محسن مع استعلام واحد
$category_ids = array_unique(array_column($products, 'category_id'));
$manufacturer_ids = array_unique(array_column($products, 'manufacturer_id'));

$categories = $this->model_catalog_category->getCategories(['category_id' => $category_ids]);
$manufacturers = $this->model_catalog_manufacturer->getManufacturers(['manufacturer_id' => $manufacturer_ids]);

// تحويل لمصفوفات مفهرسة للوصول السريع
$category_data = array_column($categories, null, 'category_id');
$manufacturer_data = array_column($manufacturers, null, 'manufacturer_id');

foreach ($products as &$product) {
    $product['category'] = $category_data[$product['category_id']] ?? null;
    $product['manufacturer'] = $manufacturer_data[$product['manufacturer_id']] ?? null;
}'''
            }

        return examples

    def _generate_implementation_steps(self, analysis):
        """إنشاء خطوات التنفيذ التفصيلية"""
        steps = []

        # خطوات الإصلاح حسب الأولوية
        critical_issues = analysis['critical_issues']

        for i, issue in enumerate(critical_issues[:5], 1):  # أول 5 مشاكل حرجة
            step = {
                'step_number': i,
                'priority': issue['fix_priority'],
                'title': f"إصلاح {issue['category']}",
                'description': issue['description'],
                'estimated_time': self._estimate_fix_time(issue),
                'difficulty': self._estimate_difficulty(issue),
                'prerequisites': self._get_prerequisites(issue),
                'detailed_steps': self._get_detailed_steps(issue),
                'validation': self._get_validation_steps(issue),
                'rollback_plan': self._get_rollback_plan(issue)
            }
            steps.append(step)

        return steps

    def _estimate_fix_time(self, issue):
        """تقدير وقت الإصلاح"""
        time_map = {
            'CONSTITUTIONAL_VIOLATION': '15-30 دقيقة',
            'SECURITY_VULNERABILITY': '30-60 دقيقة',
            'DATABASE_ERROR': '10-20 دقيقة',
            'LANGUAGE_MISMATCH': '20-40 دقيقة',
            'PERFORMANCE_ISSUE': '30-90 دقيقة',
            'MISSING_FILES': '15-30 دقيقة'
        }
        return time_map.get(issue['type'], '20-40 دقيقة')

    def _estimate_difficulty(self, issue):
        """تقدير صعوبة الإصلاح"""
        difficulty_map = {
            'CONSTITUTIONAL_VIOLATION': 'متوسط',
            'SECURITY_VULNERABILITY': 'صعب',
            'DATABASE_ERROR': 'سهل',
            'LANGUAGE_MISMATCH': 'سهل',
            'PERFORMANCE_ISSUE': 'صعب',
            'MISSING_FILES': 'سهل'
        }
        return difficulty_map.get(issue['type'], 'متوسط')

    def _get_prerequisites(self, issue):
        """الحصول على المتطلبات المسبقة"""
        prereq_map = {
            'CONSTITUTIONAL_VIOLATION': ['فهم الخدمات المركزية', 'صلاحيات تعديل الكونترولر'],
            'SECURITY_VULNERABILITY': ['فهم أساسيات الأمان', 'معرفة بـ SQL Injection'],
            'DATABASE_ERROR': ['الوصول لقاعدة البيانات', 'فهم هيكل الجداول'],
            'LANGUAGE_MISMATCH': ['فهم نظام اللغات', 'إتقان العربية والإنجليزية'],
            'PERFORMANCE_ISSUE': ['فهم تحسين الاستعلامات', 'معرفة بالتخزين المؤقت'],
            'MISSING_FILES': ['فهم هيكل MVC', 'صلاحيات إنشاء الملفات']
        }
        return prereq_map.get(issue['type'], ['المعرفة الأساسية بـ PHP'])

    def _get_detailed_steps(self, issue):
        """الحصول على الخطوات التفصيلية"""
        if issue['type'] == 'CONSTITUTIONAL_VIOLATION':
            return [
                'افتح ملف الكونترولر في محرر النصوص',
                'ابحث عن بداية كل دالة public',
                'أضف استدعاء الخدمات المركزية',
                'أضف تسجيل النشاط للعمليات المهمة',
                'احفظ الملف واختبر الوظائف'
            ]
        elif issue['type'] == 'LANGUAGE_MISMATCH':
            return [
                'افتح ملفات اللغة العربية والإنجليزية',
                'قارن المتغيرات المستخدمة مع المعرفة',
                'أضف المتغيرات المفقودة لكلا الملفين',
                'تأكد من تطابق أسماء المتغيرات',
                'اختبر عرض النصوص في كلا اللغتين'
            ]
        else:
            return ['راجع التوثيق المناسب', 'طبق الإصلاح المطلوب', 'اختبر النتيجة']

    def _get_validation_steps(self, issue):
        """خطوات التحقق من الإصلاح"""
        return [
            'اختبر الوظيفة الأساسية',
            'تحقق من عدم ظهور أخطاء',
            'اختبر مع بيانات مختلفة',
            'تأكد من عمل جميع الميزات'
        ]

    def _get_rollback_plan(self, issue):
        """خطة التراجع في حالة المشاكل"""
        return [
            'احتفظ بنسخة احتياطية قبل التعديل',
            'في حالة المشاكل، استعد النسخة الأصلية',
            'راجع سجلات الأخطاء',
            'اطلب المساعدة إذا لزم الأمر'
        ]

    def _generate_supreme_report(self, route, analysis):
        """إنشاء التقرير الفائق الجودة"""
        timestamp = analysis['timestamp']

        # حساب النقاط الشامل
        total_issues = len(analysis['critical_issues'])
        critical_count = len([i for i in analysis['critical_issues'] if i['severity'] == 'CRITICAL'])
        high_count = len([i for i in analysis['critical_issues'] if i['severity'] == 'HIGH'])
        medium_count = len([i for i in analysis['critical_issues'] if i['severity'] == 'MEDIUM'])

        # حساب نقاط الصحة المتقدمة
        health_score = 100
        health_score -= critical_count * 20
        health_score -= high_count * 10
        health_score -= medium_count * 5
        health_score = max(0, health_score)

        # تحديد الحالة
        if health_score >= 95:
            status_emoji = "🏆"
            status_text = "SUPREME QUALITY"
            status_color = "🟢"
        elif health_score >= 85:
            status_emoji = "✅"
            status_text = "EXCELLENT"
            status_color = "🟢"
        elif health_score >= 70:
            status_emoji = "⚠️"
            status_text = "NEEDS IMPROVEMENT"
            status_color = "🟡"
        elif health_score >= 50:
            status_emoji = "❌"
            status_text = "CRITICAL ISSUES"
            status_color = "🔴"
        else:
            status_emoji = "💀"
            status_text = "SYSTEM FAILURE"
            status_color = "🔴"

        # بناء التقرير الفائق
        report = f"""# 🏆 AYM ERP SUPREME AUDIT REPORT
## 📄 Route: `{route}`

---

### 📊 EXECUTIVE SUMMARY
| Metric | Value | Status |
|--------|-------|--------|
| **Health Score** | {status_emoji} **{health_score}%** | {status_color} {status_text} |
| **Critical Issues** | 🔴 {critical_count} | {'✅ GOOD' if critical_count == 0 else '❌ NEEDS IMMEDIATE ACTION'} |
| **High Priority** | 🟡 {high_count} | {'✅ GOOD' if high_count <= 2 else '⚠️ NEEDS ATTENTION'} |
| **Medium Priority** | 🟢 {medium_count} | {'✅ GOOD' if medium_count <= 5 else '⚠️ REVIEW NEEDED'} |
| **Analysis Date** | 📅 {timestamp} | ✅ CURRENT |

---

### 🏛️ CONSTITUTIONAL COMPLIANCE ANALYSIS

"""

        # تحليل الامتثال الدستوري
        compliance = analysis['constitutional_compliance']
        for rule_name, rule_data in compliance.items():
            if isinstance(rule_data, dict):
                status = "✅ COMPLIANT" if rule_data.get('status', False) else "❌ VIOLATION"
                report += f"#### {rule_name.replace('_', ' ').title()}\n"
                report += f"**Status:** {status}\n"
                if not rule_data.get('status', False):
                    rule_info = self.constitution.get(rule_name, {})
                    report += f"**Issue:** {rule_info.get('description', 'Unknown issue')}\n"
                    report += f"**Fix:** {rule_info.get('fix', 'See documentation')}\n"
                report += "\n"

        # تحليل MVC
        mvc = analysis['mvc_analysis']
        report += f"""---

### 🏗️ MVC ARCHITECTURE ANALYSIS

#### 📂 Controller
- **File:** `{mvc['controller']['path']}`
- **Status:** {'✅ EXISTS' if mvc['controller']['exists'] else '❌ MISSING'}

#### 🧱 Models ({len(mvc['models'])})
"""

        for model in mvc['models']:
            status = "✅" if model['exists'] else "❌"
            func_count = len(model.get('functions', []))
            report += f"- {status} `{model['route']}` ({func_count} functions)\n"

        report += f"\n#### 🎨 Views ({len(mvc['views'])})\n"
        for view in mvc['views']:
            status = "✅" if view['exists'] else "❌"
            var_count = len(view.get('variables', set()))
            report += f"- {status} `{view['path']}` ({var_count} variables)\n"

        # تحليل اللغة المتقدم (مدمج من lang_comparison_script.py)
        lang = analysis['language_analysis']
        detailed = lang.get('detailed_analysis', {})

        report += f"""
#### 🌐 Advanced Language Analysis (Enhanced from lang_comparison_script.py)

**📊 Coverage Statistics:**
- **Arabic Coverage:** {detailed.get('ar_coverage', 0):.1f}% ({len(lang['ar_variables'] & lang['used_variables'])}/{len(lang['used_variables'])})
- **English Coverage:** {detailed.get('en_coverage', 0):.1f}% ({len(lang['en_variables'] & lang['used_variables'])}/{len(lang['used_variables'])})
- **Total Used Variables:** {detailed.get('total_used', 0)} variables
- **Arabic Defined:** {detailed.get('total_ar_defined', 0)} variables
- **English Defined:** {detailed.get('total_en_defined', 0)} variables

**🔍 Analysis Scope:**
- **Models Analyzed:** {detailed.get('models_with_lang', 0)} models
- **Views Analyzed:** {detailed.get('views_with_lang', 0)} views
- **Arabic Files Found:** {len(lang.get('language_files_found', {}).get('ar', []))} files
- **English Files Found:** {len(lang.get('language_files_found', {}).get('en', []))} files

**⚠️ Issues Detected:**
- **Missing Arabic:** ❌ {len(lang['missing_ar'])} variables
- **Missing English:** ❌ {len(lang['missing_en'])} variables
- **Unused Arabic:** 🧹 {len(lang['unused_ar'])} variables
- **Unused English:** 🧹 {len(lang['unused_en'])} variables
- **Hardcoded Text:** ⚠️ {len(lang['hardcoded_text'])} instances

**📈 Compliance Score:** {lang['compliance_score']:.1f}%

"""

        # إضافة تفاصيل المتغيرات المستخدمة
        if lang['used_variables']:
            report += "**✅ Used Variables:**\n"
            for var in sorted(list(lang['used_variables'])[:20]):  # أول 20 متغير
                ar_status = "✅" if var in lang['ar_variables'] else "❌"
                en_status = "✅" if var in lang['en_variables'] else "❌"
                report += f"   - `{var}` (AR: {ar_status}, EN: {en_status})\n"

            if len(lang['used_variables']) > 20:
                report += f"   ... و {len(lang['used_variables']) - 20} متغير آخر\n"
            report += "\n"

        # إضافة المتغيرات المفقودة مع الاقتراحات
        if lang['missing_ar']:
            report += f"**❌ Missing in Arabic ({len(lang['missing_ar'])}):**\n"
            for var in sorted(list(lang['missing_ar'])[:15]):
                report += f"   - `{var}`\n"
            if len(lang['missing_ar']) > 15:
                report += f"   ... و {len(lang['missing_ar']) - 15} متغير آخر\n"
            report += "\n"

        if lang['missing_en']:
            report += f"**❌ Missing in English ({len(lang['missing_en'])}):**\n"
            for var in sorted(list(lang['missing_en'])[:15]):
                report += f"   - `{var}`\n"
            if len(lang['missing_en']) > 15:
                report += f"   ... و {len(lang['missing_en']) - 15} متغير آخر\n"
            report += "\n"

        # إضافة المتغيرات الزائدة
        if lang['unused_ar']:
            report += f"**🧹 Unused in Arabic ({len(lang['unused_ar'])}):**\n"
            for var in sorted(list(lang['unused_ar'])[:10]):
                report += f"   - `{var}`\n"
            if len(lang['unused_ar']) > 10:
                report += f"   ... و {len(lang['unused_ar']) - 10} متغير آخر\n"
            report += "\n"

        if lang['unused_en']:
            report += f"**🧹 Unused in English ({len(lang['unused_en'])}):**\n"
            for var in sorted(list(lang['unused_en'])[:10]):
                report += f"   - `{var}`\n"
            if len(lang['unused_en']) > 10:
                report += f"   ... و {len(lang['unused_en']) - 10} متغير آخر\n"
            report += "\n"

        # إضافة النصوص المباشرة
        if lang['hardcoded_text']:
            report += f"**⚠️ Hardcoded Text Found ({len(lang['hardcoded_text'])}):**\n"
            for text in lang['hardcoded_text'][:5]:
                report += f"   - `{text[:50]}{'...' if len(text) > 50 else ''}`\n"
            if len(lang['hardcoded_text']) > 5:
                report += f"   ... و {len(lang['hardcoded_text']) - 5} نص آخر\n"
            report += "\n"

"""

        # المشاكل الحرجة
        if analysis['critical_issues']:
            report += "---\n\n### 🚨 CRITICAL ISSUES REQUIRING IMMEDIATE ACTION\n\n"
            for i, issue in enumerate(analysis['critical_issues'], 1):
                severity_emoji = "🔴" if issue['severity'] == 'CRITICAL' else "🟡" if issue['severity'] == 'HIGH' else "🟢"
                report += f"#### {i}. {severity_emoji} {issue['category']}\n"
                report += f"**Type:** {issue['type']}\n"
                report += f"**Severity:** {issue['severity']}\n"
                report += f"**Description:** {issue['description']}\n"
                report += f"**Impact:** {issue['impact']}\n"
                report += f"**Fix Priority:** {issue['fix_priority']}\n"
                if issue.get('details'):
                    report += f"**Details:** {issue['details']}\n"
                report += "\n"

        # تعليمات الإصلاح
        if analysis['fix_instructions']:
            report += "---\n\n### 🔧 DETAILED FIX INSTRUCTIONS\n\n"
            for i, instruction in enumerate(analysis['fix_instructions'], 1):
                report += f"#### {i}. {instruction['title']} (Priority: {instruction['priority']})\n"
                report += f"**Category:** {instruction['category']}\n"
                report += f"**Description:** {instruction['description']}\n"
                report += f"**Location:** {instruction['code_location']}\n"
                report += f"**Testing:** {instruction['testing']}\n"
                report += "\n**Steps:**\n"
                for step_num, step in enumerate(instruction['steps'], 1):
                    report += f"{step_num}. {step}\n"
                report += "\n"

        # أمثلة الكود
        if analysis['code_examples']:
            report += "---\n\n### 💻 CODE EXAMPLES FOR FIXES\n\n"
            for example_key, example in analysis['code_examples'].items():
                report += f"#### {example['title']}\n\n"
                report += "**Before (Problematic Code):**\n```php\n"
                report += example['before']
                report += "\n```\n\n**After (Fixed Code):**\n```php\n"
                report += example['after']
                report += "\n```\n\n"

        # إضافة اقتراحات اللغة المتقدمة (من lang_comparison_script.py)
        lang = analysis['language_analysis']
        if lang.get('suggestions', {}).get('ar') or lang.get('suggestions', {}).get('en'):
            report += "---\n\n### 🌐 ADVANCED LANGUAGE SUGGESTIONS (From lang_comparison_script.py)\n\n"

            if lang.get('suggestions', {}).get('ar'):
                report += "#### 💡 Suggested Arabic Additions:\n```php\n"
                for suggestion in lang['suggestions']['ar'][:20]:  # أول 20 اقتراح
                    report += f"{suggestion}\n"
                if len(lang['suggestions']['ar']) > 20:
                    report += f"// ... و {len(lang['suggestions']['ar']) - 20} متغير آخر\n"
                report += "```\n\n"

            if lang.get('suggestions', {}).get('en'):
                report += "#### 💡 Suggested English Additions:\n```php\n"
                for suggestion in lang['suggestions']['en'][:20]:  # أول 20 اقتراح
                    report += f"{suggestion}\n"
                if len(lang['suggestions']['en']) > 20:
                    report += f"// ... and {len(lang['suggestions']['en']) - 20} more variables\n"
                report += "```\n\n"

            # إضافة ملفات اللغة المكتشفة
            if lang.get('language_files_found'):
                report += "#### 📁 Language Files Discovered:\n"
                if lang['language_files_found'].get('ar'):
                    report += "**Arabic Files:**\n"
                    for file_path in lang['language_files_found']['ar']:
                        report += f"   - `{file_path}`\n"
                    report += "\n"

                if lang['language_files_found'].get('en'):
                    report += "**English Files:**\n"
                    for file_path in lang['language_files_found']['en']:
                        report += f"   - `{file_path}`\n"
                    report += "\n"

            # إضافة الموديلات والقوالب المحللة
            if lang.get('models_analyzed') or lang.get('views_analyzed'):
                report += "#### 🔍 Analysis Scope Details:\n"
                if lang.get('models_analyzed'):
                    report += f"**Models Analyzed ({len(lang['models_analyzed'])}):**\n"
                    for model in lang['models_analyzed']:
                        report += f"   - `{model}`\n"
                    report += "\n"

                if lang.get('views_analyzed'):
                    report += f"**Views Analyzed ({len(lang['views_analyzed'])}):**\n"
                    for view in lang['views_analyzed'][:10]:  # أول 10 قوالب
                        report += f"   - `{view}`\n"
                    if len(lang['views_analyzed']) > 10:
                        report += f"   ... و {len(lang['views_analyzed']) - 10} قالب آخر\n"
                    report += "\n"

        # خطوات التنفيذ
        if analysis['implementation_steps']:
            report += "---\n\n### 📋 STEP-BY-STEP IMPLEMENTATION GUIDE\n\n"
            for step in analysis['implementation_steps']:
                report += f"#### Step {step['step_number']}: {step['title']}\n"
                report += f"**Priority:** {step['priority']} | **Time:** {step['estimated_time']} | **Difficulty:** {step['difficulty']}\n\n"
                report += f"**Description:** {step['description']}\n\n"

                report += "**Prerequisites:**\n"
                for prereq in step['prerequisites']:
                    report += f"- {prereq}\n"

                report += "\n**Detailed Steps:**\n"
                for detail_step in step['detailed_steps']:
                    report += f"1. {detail_step}\n"

                report += "\n**Validation:**\n"
                for validation in step['validation']:
                    report += f"- {validation}\n"

                report += "\n**Rollback Plan:**\n"
                for rollback in step['rollback_plan']:
                    report += f"- {rollback}\n"

                report += "\n---\n\n"

        # الخلاصة والتوصيات
        total_time = self._calculate_total_time(analysis['implementation_steps'][:3])
        compliance_score = self._calculate_compliance_score(compliance)
        security_score = self._calculate_security_score(analysis['security_analysis'])
        mvc_score = self._calculate_mvc_score(mvc)
        lang_score = lang.get('compliance_score', 0)

        # تحديد حالات النجاح/الفشل
        compliance_status = 'PASS' if compliance_score >= 80 else 'FAIL'
        security_status = 'PASS' if security_score >= 80 else 'FAIL'
        lang_status = 'PASS' if lang_score >= 80 else 'FAIL'
        mvc_status = 'PASS' if mvc_score >= 80 else 'FAIL'

        report += "### 🎯 FINAL RECOMMENDATIONS\n\n"
        report += "#### Immediate Actions Required:\n"
        report += f"1. **Fix {critical_count} Critical Issues** - These can break the system\n"
        report += f"2. **Address {high_count} High Priority Items** - Important for stability\n"
        report += f"3. **Review {medium_count} Medium Priority Items** - Good for optimization\n\n"

        report += "#### Success Criteria:\n"
        report += "- Health Score should reach 95%+\n"
        report += "- Zero critical security vulnerabilities\n"
        report += "- Complete constitutional compliance\n"
        report += "- Full language file synchronization\n\n"

        report += "#### Next Steps:\n"
        report += f"1. Start with Priority 1 fixes (estimated time: {total_time})\n"
        report += "2. Test thoroughly after each fix\n"
        report += "3. Re-run this audit to verify improvements\n"
        report += "4. Document any custom solutions\n\n"

        report += "---\n\n"
        report += "### 📈 QUALITY METRICS SUMMARY\n\n"
        report += "| Category | Score | Status |\n"
        report += "|----------|-------|--------|\n"
        report += f"| Constitutional Compliance | {compliance_score}% | {compliance_status} |\n"
        report += f"| Security | {security_score}% | {security_status} |\n"
        report += f"| Language Integration | {lang_score:.1f}% | {lang_status} |\n"
        report += f"| MVC Structure | {mvc_score}% | {mvc_status} |\n"
        report += f"| **OVERALL HEALTH** | **{health_score}%** | **{status_emoji} {status_text}** |\n\n"

        report += "---\n\n"
        report += f"*Generated by AYM ERP Supreme Auditor V7.0 - {timestamp}*\n"
        report += "*This report provides complete guidance for fixing all identified issues.*\n"

        return report

    def _calculate_total_time(self, steps):
        """حساب الوقت الإجمالي المقدر"""
        if not steps:
            return "0 دقيقة"

        total_min = len(steps) * 30  # متوسط 30 دقيقة لكل خطوة
        if total_min < 60:
            return f"{total_min} دقيقة"
        else:
            hours = total_min // 60
            minutes = total_min % 60
            return f"{hours} ساعة و {minutes} دقيقة"

    def _calculate_compliance_score(self, compliance):
        """حساب نقاط الامتثال الدستوري"""
        if not compliance:
            return 0

        total_rules = len([k for k, v in compliance.items() if isinstance(v, dict)])
        compliant_rules = len([k for k, v in compliance.items() if isinstance(v, dict) and v.get('status', False)])

        return int((compliant_rules / total_rules * 100)) if total_rules > 0 else 100

    def _calculate_security_score(self, security):
        """حساب نقاط الأمان"""
        if not security:
            return 0

        total_checks = len([k for k, v in security.items() if isinstance(v, dict)])
        safe_checks = len([k for k, v in security.items() if isinstance(v, dict) and v.get('status') == 'SAFE'])

        return int((safe_checks / total_checks * 100)) if total_checks > 0 else 100

    def _calculate_mvc_score(self, mvc):
        """حساب نقاط هيكل MVC"""
        score = 0

        # نقاط الكونترولر
        if mvc['controller']['exists']:
            score += 25

        # نقاط الموديلات
        if mvc['models']:
            existing_models = len([m for m in mvc['models'] if m['exists']])
            score += int(25 * existing_models / len(mvc['models']))
        else:
            score += 25  # لا توجد موديلات مطلوبة

        # نقاط القوالب
        if mvc['views']:
            existing_views = len([v for v in mvc['views'] if v['exists']])
            score += int(25 * existing_views / len(mvc['views']))
        else:
            score += 25  # لا توجد قوالب مطلوبة

        # نقاط ملفات اللغة
        ar_exists = any(lang['exists'] for lang in mvc['languages']['ar'])
        en_exists = any(lang['exists'] for lang in mvc['languages']['en'])
        if ar_exists and en_exists:
            score += 25
        elif ar_exists or en_exists:
            score += 12

        return score

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="AYM ERP Supreme Screen Auditor V7.0 - Ultimate Quality Analysis")
    parser.add_argument("path", nargs="?", default=".", help="Root path to the dashboard directory")
    args = parser.parse_args()

    dashboard_path = Path(args.path)
    if not (dashboard_path / 'controller').exists():
        print("❌ Error: 'controller' directory not found. Please specify the correct dashboard path.")
        print("💡 Example: python aym_supreme_auditor_v7.py F:/2025/ay2/dashboard")
    else:
        AYMSupremeScreenAuditorV7_0(args.path).run()
