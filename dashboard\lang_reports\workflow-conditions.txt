📄 Route: workflow/conditions
📂 Controller: controller\workflow\conditions.php
🧱 Models used (3):
   - ai/analysis
   - logging/user_activity
   - workflow/conditions
🎨 Twig templates (1):
   - view\template\workflow\conditions.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_condition_evaluation_failed
   - error_condition_required
   - error_permission
   - error_test_data_required
   - heading_title
   - text_ai_anomaly
   - text_ai_classification
   - text_ai_conditions
   - text_ai_conditions_desc
   - text_ai_prediction
   - text_ai_sentiment
   - text_and_condition
   - text_approval_required
   - text_basic_conditions
   - text_basic_conditions_desc
   - text_business_rules
   - text_business_rules_desc
   - text_condition_builder
   - text_condition_evaluated_successfully
   - text_contains
   - text_customer_credit
   - text_data_validation
   - text_data_validation_desc
   - text_date_after
   - text_date_before
   - text_date_between
   - text_date_equals
   - text_date_time_conditions
   - text_date_time_desc
   - text_equals
   - text_greater_than
   - text_home
   - text_if_then_else
   - text_inventory_level
   - text_is_email
   - text_is_empty
   - text_is_numeric
   - text_is_phone
   - text_length_between
   - text_less_than
   - text_logical_conditions
   - text_logical_desc
   - text_not_condition
   - text_not_equals
   - text_or_condition
   - text_regex_match
   - text_test_successful
   - text_time_between
   - text_working_hours

❌ Missing in Arabic:
   - error_condition_evaluation_failed
   - error_condition_required
   - error_permission
   - error_test_data_required
   - heading_title
   - text_ai_anomaly
   - text_ai_classification
   - text_ai_conditions
   - text_ai_conditions_desc
   - text_ai_prediction
   - text_ai_sentiment
   - text_and_condition
   - text_approval_required
   - text_basic_conditions
   - text_basic_conditions_desc
   - text_business_rules
   - text_business_rules_desc
   - text_condition_builder
   - text_condition_evaluated_successfully
   - text_contains
   - text_customer_credit
   - text_data_validation
   - text_data_validation_desc
   - text_date_after
   - text_date_before
   - text_date_between
   - text_date_equals
   - text_date_time_conditions
   - text_date_time_desc
   - text_equals
   - text_greater_than
   - text_home
   - text_if_then_else
   - text_inventory_level
   - text_is_email
   - text_is_empty
   - text_is_numeric
   - text_is_phone
   - text_length_between
   - text_less_than
   - text_logical_conditions
   - text_logical_desc
   - text_not_condition
   - text_not_equals
   - text_or_condition
   - text_regex_match
   - text_test_successful
   - text_time_between
   - text_working_hours

❌ Missing in English:
   - error_condition_evaluation_failed
   - error_condition_required
   - error_permission
   - error_test_data_required
   - heading_title
   - text_ai_anomaly
   - text_ai_classification
   - text_ai_conditions
   - text_ai_conditions_desc
   - text_ai_prediction
   - text_ai_sentiment
   - text_and_condition
   - text_approval_required
   - text_basic_conditions
   - text_basic_conditions_desc
   - text_business_rules
   - text_business_rules_desc
   - text_condition_builder
   - text_condition_evaluated_successfully
   - text_contains
   - text_customer_credit
   - text_data_validation
   - text_data_validation_desc
   - text_date_after
   - text_date_before
   - text_date_between
   - text_date_equals
   - text_date_time_conditions
   - text_date_time_desc
   - text_equals
   - text_greater_than
   - text_home
   - text_if_then_else
   - text_inventory_level
   - text_is_email
   - text_is_empty
   - text_is_numeric
   - text_is_phone
   - text_length_between
   - text_less_than
   - text_logical_conditions
   - text_logical_desc
   - text_not_condition
   - text_not_equals
   - text_or_condition
   - text_regex_match
   - text_test_successful
   - text_time_between
   - text_working_hours

💡 Suggested Arabic Additions:
   - error_condition_evaluation_failed = ""  # TODO: ترجمة عربية
   - error_condition_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_test_data_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_ai_anomaly = ""  # TODO: ترجمة عربية
   - text_ai_classification = ""  # TODO: ترجمة عربية
   - text_ai_conditions = ""  # TODO: ترجمة عربية
   - text_ai_conditions_desc = ""  # TODO: ترجمة عربية
   - text_ai_prediction = ""  # TODO: ترجمة عربية
   - text_ai_sentiment = ""  # TODO: ترجمة عربية
   - text_and_condition = ""  # TODO: ترجمة عربية
   - text_approval_required = ""  # TODO: ترجمة عربية
   - text_basic_conditions = ""  # TODO: ترجمة عربية
   - text_basic_conditions_desc = ""  # TODO: ترجمة عربية
   - text_business_rules = ""  # TODO: ترجمة عربية
   - text_business_rules_desc = ""  # TODO: ترجمة عربية
   - text_condition_builder = ""  # TODO: ترجمة عربية
   - text_condition_evaluated_successfully = ""  # TODO: ترجمة عربية
   - text_contains = ""  # TODO: ترجمة عربية
   - text_customer_credit = ""  # TODO: ترجمة عربية
   - text_data_validation = ""  # TODO: ترجمة عربية
   - text_data_validation_desc = ""  # TODO: ترجمة عربية
   - text_date_after = ""  # TODO: ترجمة عربية
   - text_date_before = ""  # TODO: ترجمة عربية
   - text_date_between = ""  # TODO: ترجمة عربية
   - text_date_equals = ""  # TODO: ترجمة عربية
   - text_date_time_conditions = ""  # TODO: ترجمة عربية
   - text_date_time_desc = ""  # TODO: ترجمة عربية
   - text_equals = ""  # TODO: ترجمة عربية
   - text_greater_than = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_if_then_else = ""  # TODO: ترجمة عربية
   - text_inventory_level = ""  # TODO: ترجمة عربية
   - text_is_email = ""  # TODO: ترجمة عربية
   - text_is_empty = ""  # TODO: ترجمة عربية
   - text_is_numeric = ""  # TODO: ترجمة عربية
   - text_is_phone = ""  # TODO: ترجمة عربية
   - text_length_between = ""  # TODO: ترجمة عربية
   - text_less_than = ""  # TODO: ترجمة عربية
   - text_logical_conditions = ""  # TODO: ترجمة عربية
   - text_logical_desc = ""  # TODO: ترجمة عربية
   - text_not_condition = ""  # TODO: ترجمة عربية
   - text_not_equals = ""  # TODO: ترجمة عربية
   - text_or_condition = ""  # TODO: ترجمة عربية
   - text_regex_match = ""  # TODO: ترجمة عربية
   - text_test_successful = ""  # TODO: ترجمة عربية
   - text_time_between = ""  # TODO: ترجمة عربية
   - text_working_hours = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_condition_evaluation_failed = ""  # TODO: English translation
   - error_condition_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_test_data_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_ai_anomaly = ""  # TODO: English translation
   - text_ai_classification = ""  # TODO: English translation
   - text_ai_conditions = ""  # TODO: English translation
   - text_ai_conditions_desc = ""  # TODO: English translation
   - text_ai_prediction = ""  # TODO: English translation
   - text_ai_sentiment = ""  # TODO: English translation
   - text_and_condition = ""  # TODO: English translation
   - text_approval_required = ""  # TODO: English translation
   - text_basic_conditions = ""  # TODO: English translation
   - text_basic_conditions_desc = ""  # TODO: English translation
   - text_business_rules = ""  # TODO: English translation
   - text_business_rules_desc = ""  # TODO: English translation
   - text_condition_builder = ""  # TODO: English translation
   - text_condition_evaluated_successfully = ""  # TODO: English translation
   - text_contains = ""  # TODO: English translation
   - text_customer_credit = ""  # TODO: English translation
   - text_data_validation = ""  # TODO: English translation
   - text_data_validation_desc = ""  # TODO: English translation
   - text_date_after = ""  # TODO: English translation
   - text_date_before = ""  # TODO: English translation
   - text_date_between = ""  # TODO: English translation
   - text_date_equals = ""  # TODO: English translation
   - text_date_time_conditions = ""  # TODO: English translation
   - text_date_time_desc = ""  # TODO: English translation
   - text_equals = ""  # TODO: English translation
   - text_greater_than = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_if_then_else = ""  # TODO: English translation
   - text_inventory_level = ""  # TODO: English translation
   - text_is_email = ""  # TODO: English translation
   - text_is_empty = ""  # TODO: English translation
   - text_is_numeric = ""  # TODO: English translation
   - text_is_phone = ""  # TODO: English translation
   - text_length_between = ""  # TODO: English translation
   - text_less_than = ""  # TODO: English translation
   - text_logical_conditions = ""  # TODO: English translation
   - text_logical_desc = ""  # TODO: English translation
   - text_not_condition = ""  # TODO: English translation
   - text_not_equals = ""  # TODO: English translation
   - text_or_condition = ""  # TODO: English translation
   - text_regex_match = ""  # TODO: English translation
   - text_test_successful = ""  # TODO: English translation
   - text_time_between = ""  # TODO: English translation
   - text_working_hours = ""  # TODO: English translation
