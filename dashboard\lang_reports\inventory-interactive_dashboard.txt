📄 Route: inventory/interactive_dashboard
📂 Controller: controller\inventory\interactive_dashboard.php
🧱 Models used (1):
   - inventory/interactive_dashboard
🎨 Twig templates (1):
   - view\template\inventory\interactive_dashboard.twig
🈯 Arabic Language Files (1):
   - language\ar\inventory\interactive_dashboard.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_home

❌ Missing in Arabic:
   - heading_title
   - text_home

❌ Missing in English:
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
