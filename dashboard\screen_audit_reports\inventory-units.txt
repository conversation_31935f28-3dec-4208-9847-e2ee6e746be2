📄 Route: inventory/units
📂 Controller: controller\inventory\units.php
🧱 Models used (2):
   ✅ inventory/units (14 functions)
   ✅ localisation/language (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\units.php (160 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\units.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - error_name
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_add
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (11):
   - error_name
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_add
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 11 items
      - text_success
      - text_add
      - text_home
      - text_disabled
      - error_permission

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 11 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:07
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.