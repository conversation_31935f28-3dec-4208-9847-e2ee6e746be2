📄 Route: purchase/accounting_integration_advanced
📂 Controller: controller\purchase\accounting_integration_advanced.php
🧱 Models used (4):
   ❌ purchase/accounting_integration_advanced (0 functions)
   ✅ accounts/audit_trail (13 functions)
   ✅ supplier/supplier (21 functions)
   ✅ catalog/product (128 functions)
🎨 Twig templates (1):
   ✅ view\template\purchase\accounting_integration_advanced.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\purchase\accounting_integration_advanced.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\purchase\accounting_integration_advanced.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (66):
   - action
   - can_sync
   - can_update_settings
   - column_left
   - error_can_update_settings
   - error_settings_url
   - error_sync_inventory_url
   - error_transfer_already_completed
   - error_transfer_not_found
   - error_validate_url
   - footer
   - generate_entries_url
   - products
   - text_can_generate_entries
   - text_heading_title
   - text_report_url
   - text_update_wac_url
   - update_wac_url
   - user_token
   - validate_url
   ... و 46 متغير آخر

❌ Missing in Arabic (66):
   - action
   - can_sync
   - can_update_settings
   - column_left
   - error_can_update_settings
   - error_sync_inventory_url
   - error_transfer_already_completed
   - error_transfer_not_found
   - products
   - text_can_generate_entries
   - text_heading_title
   - text_report_url
   - text_update_wac_url
   - user_token
   - validate_url
   ... و 51 متغير آخر

❌ Missing in English (66):
   - action
   - can_sync
   - can_update_settings
   - column_left
   - error_can_update_settings
   - error_sync_inventory_url
   - error_transfer_already_completed
   - error_transfer_not_found
   - products
   - text_can_generate_entries
   - text_heading_title
   - text_report_url
   - text_update_wac_url
   - user_token
   - validate_url
   ... و 51 متغير آخر

🗄️ Database Tables Used (3):
   ❌ journal_entry
   ❌ statements
   ❌ stock

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 66 items
      - error_sync_inventory_url
      - text_update_wac_url
      - can_sync
      - text_report_url
      - can_update_settings
   🟡 MISSING_ENGLISH_VARIABLES: 66 items
      - error_sync_inventory_url
      - text_update_wac_url
      - can_sync
      - text_report_url
      - can_update_settings
   🔴 INVALID_DATABASE_TABLES: 3 items
      - stock
      - journal_entry
      - statements
   🟢 MISSING_MODEL_FILES: 1 items
      - purchase/accounting_integration_advanced

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 66 متغير عربي و 66 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 3 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:13
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.