📄 Route: marketing/analytics
📂 Controller: controller\marketing\analytics.php
🧱 Models used (1):
   ✅ marketing/analytics (16 functions)
🎨 Twig templates (1):
   ✅ view\template\marketing\analytics.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\marketing\analytics.php (127 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\marketing\analytics.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (54):
   - button_refresh
   - column_conversion_rate
   - column_revenue
   - entry_campaign
   - entry_date_from
   - entry_report_type
   - error_report_generation
   - export_url
   - filter_date_to
   - header
   - stats_url
   - text_all_campaigns
   - text_campaign_details
   - text_conversions
   - text_count
   - text_dashboard
   - text_export_report
   - text_filter
   - text_roi
   - text_total_campaigns
   ... و 34 متغير آخر

❌ Missing in Arabic (13):
   - column_left
   - dashboard
   - error_warning
   - export_url
   - filter_date_from
   - filter_date_to
   - footer
   - header
   - source
   - stats_url
   - success
   - text_home
   - user_token

❌ Missing in English (54):
   - button_refresh
   - column_conversion_rate
   - column_revenue
   - entry_campaign
   - entry_report_type
   - error_report_generation
   - export_url
   - filter_date_to
   - header
   - text_all_campaigns
   - text_campaign_details
   - text_conversions
   - text_export_report
   - text_filter
   - text_total_campaigns
   ... و 39 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - export_url
      - error_warning
      - user_token
      - column_left
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 54 items
      - error_report_generation
      - text_export_report
      - text_total_campaigns
      - column_revenue
      - entry_report_type

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 54 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:10
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.