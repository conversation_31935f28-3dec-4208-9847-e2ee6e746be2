📄 Route: extension/module/fbcapidyad
📂 Controller: controller\extension\module\fbcapidyad.php
🧱 Models used (1):
   ✅ extension/module/fbcapidyad (36 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\fbcapidyad.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\module\fbcapidyad.php (26 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\fbcapidyad.php (26 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_permission
   - error_warning
   - footer
   - header
   - heading_title
   - success
   - text_heading_title
   - text_success
   - user_token

❌ Missing in Arabic (12):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - success
   - text_heading_title
   - user_token

❌ Missing in English (12):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_heading_title
   - error_warning
   - footer
   - header
   - success
   - text_heading_title
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 12 items
      - error_warning
      - button_save
      - user_token
      - text_heading_title
      - column_left
   🟡 MISSING_ENGLISH_VARIABLES: 12 items
      - error_warning
      - button_save
      - user_token
      - text_heading_title
      - column_left

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 12 متغير عربي و 12 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:23
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.