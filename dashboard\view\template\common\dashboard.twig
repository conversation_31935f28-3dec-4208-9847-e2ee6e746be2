{{ header }}{{ column_left }}

<!-- Enterprise Dashboard CSS -->
<style>
.dashboard-container {
  direction: {{ direction }};
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 0;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.dashboard-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.dashboard-actions {
  {{ direction == 'rtl' ? 'float: left;' : 'float: right;' }}
}

.btn-dashboard {
  margin-{{ direction == 'rtl' ? 'right' : 'left' }}: 8px;
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn-dashboard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.25);
}

.breadcrumb-modern {
  background: transparent;
  padding: 0;
  margin: 10px 0 0 0;
}

.breadcrumb-modern > li {
  color: rgba(255,255,255,0.8);
}

.breadcrumb-modern > li + li:before {
  content: "{{ direction == 'rtl' ? '\\f053' : '\\f054' }}";
  font-family: FontAwesome;
  color: rgba(255,255,255,0.6);
  padding: 0 8px;
}

.breadcrumb-modern a {
  color: white;
  text-decoration: none;
}

.breadcrumb-modern a:hover {
  color: #ffd700;
}

/* Modern Card Design */
.card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
  border-bottom: none;
  padding: 15px 20px;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Form Enhancements */
.form-label {
  font-size: 13px;
  color: #495057;
  margin-bottom: 5px;
}

.form-control, .custom-select {
  border-radius: 8px;
  border: 1px solid #e3e6f0;
  padding: 10px 15px;
  transition: all 0.3s ease;
}

.form-control:focus, .custom-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
  border-radius: 0;
  border-color: #e3e6f0;
  background-color: #f8f9fc;
  color: #5a5c69;
}

/* Button Enhancements */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.btn-outline-primary:hover {
  background-color: #667eea;
  border-color: #667eea;
  transform: translateY(-1px);
}

/* RTL Support */
.dashboard-container[dir="rtl"] {
  text-align: right;
}

.dashboard-container[dir="rtl"] .float-right {
  float: left !important;
}

.dashboard-container[dir="rtl"] .float-left {
  float: right !important;
}

.dashboard-container[dir="rtl"] .text-left {
  text-align: right !important;
}

.dashboard-container[dir="rtl"] .text-right {
  text-align: left !important;
}

.dashboard-container[dir="rtl"] .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

.dashboard-container[dir="rtl"] .ml-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-title {
    font-size: 22px;
  }

  .dashboard-actions {
    float: none !important;
    text-align: center;
    margin-bottom: 15px;
  }

  .btn-dashboard {
    margin: 5px;
    display: inline-block;
  }

  .btn-group-justified .btn {
    font-size: 12px;
    padding: 8px 12px;
  }
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

<div id="content" class="dashboard-container">
  <!-- Modern Enterprise Header -->
  <div class="dashboard-header">
    <div class="container-fluid">
      <div class="dashboard-actions">
        <button type="button" id="refresh-dashboard" class="btn btn-primary btn-dashboard"
                title="{{ text_refresh_data }}" data-toggle="tooltip">
          <i class="fa fa-refresh"></i> {{ text_refresh }}
        </button>
        <button type="button" id="export-dashboard" class="btn btn-success btn-dashboard"
                title="{{ text_export_data }}" data-toggle="tooltip">
          <i class="fa fa-download"></i> {{ text_export }}
        </button>
        <button type="button" id="print-dashboard" class="btn btn-info btn-dashboard"
                title="{{ text_print_report }}" data-toggle="tooltip">
          <i class="fa fa-print"></i> {{ text_print }}
        </button>
        <button type="button" id="settings-dashboard" class="btn btn-warning btn-dashboard"
                title="{{ text_dashboard_settings }}" data-toggle="tooltip">
          <i class="fa fa-cog"></i> {{ text_settings }}
        </button>
      </div>
      <h1 class="dashboard-title">
        <i class="fa fa-tachometer"></i> {{ text_smart_dashboard }}
      </h1>
      <ul class="breadcrumb breadcrumb-modern">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <!-- Smart Filters Panel -->
  <div class="container-fluid">
    <div class="card shadow-sm border-0 mb-4">
      <div class="card-header bg-gradient-primary text-white">
        <h4 class="card-title mb-0">
          <i class="fa fa-filter"></i> {{ text_smart_filters }}
          <button type="button" class="btn btn-sm btn-outline-light {{ direction == 'rtl' ? 'float-left' : 'float-right' }}"
                  id="toggle-filters" title="{{ text_toggle_filters }}">
            <i class="fa fa-chevron-up"></i>
          </button>
        </h4>
      </div>
      <div class="card-body" id="filters-body">
        <form id="dashboard-filters" method="get" class="needs-validation" novalidate>
          <input type="hidden" name="route" value="common/dashboard">
          <input type="hidden" name="user_token" value="{{ user_token }}">
          <input type="hidden" name="{{ csrf_token_name }}" value="{{ csrf_token }}">

          <div class="row">
            <!-- Date Range Filter -->
            <div class="col-lg-3 col-md-6 mb-3">
              <div class="form-group">
                <label class="form-label font-weight-bold">
                  <i class="fa fa-calendar text-primary"></i> {{ text_date_range }}
                </label>
                <div class="input-group">
                  <input type="date" name="date_from" class="form-control"
                         value="{{ current_filters.date_from }}" required>
                  <div class="input-group-append input-group-prepend">
                    <span class="input-group-text bg-light">{{ text_to }}</span>
                  </div>
                  <input type="date" name="date_to" class="form-control"
                         value="{{ current_filters.date_to }}" required>
                </div>
              </div>
            </div>

            <!-- Branch Filter -->
            <div class="col-lg-2 col-md-6 mb-3">
              <div class="form-group">
                <label class="form-label font-weight-bold">
                  <i class="fa fa-building text-success"></i> {{ text_branch }}
                </label>
                <select name="branch_id" class="form-control custom-select">
                  <option value="">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"
                          {% if branch.branch_id == current_filters.branch_id %}selected{% endif %}>
                    {{ branch.name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
            </div>

            <!-- Source Filter -->
            <div class="col-lg-2 col-md-6 mb-3">
              <div class="form-group">
                <label class="form-label font-weight-bold">
                  <i class="fa fa-source-fork text-info"></i> {{ text_source }}
                </label>
                <select name="source" class="form-control custom-select">
                  <option value="">{{ text_all_sources }}</option>
                  <option value="online" {% if current_filters.source == 'online' %}selected{% endif %}>
                    {{ text_online_store }}
                  </option>
                  <option value="pos" {% if current_filters.source == 'pos' %}selected{% endif %}>
                    {{ text_pos_systems }}
                  </option>
                  <option value="direct" {% if current_filters.source == 'direct' %}selected{% endif %}>
                    {{ text_direct_sales }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Apply Button -->
            <div class="col-lg-2 col-md-6 mb-3">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary btn-block btn-lg">
                  <i class="fa fa-search"></i> {{ text_apply }}
                </button>
              </div>
            </div>

            <!-- Quick Filters -->
            <div class="col-lg-3 col-md-12 mb-3">
              <div class="form-group">
                <label class="form-label font-weight-bold">
                  <i class="fa fa-clock-o text-warning"></i> {{ text_quick_filters }}
                </label>
                <div class="btn-group btn-group-justified d-flex" role="group">
                  <a href="?route=common/dashboard&user_token={{ user_token }}&date_from={{ 'now'|date('Y-m-d') }}&date_to={{ 'now'|date('Y-m-d') }}"
                     class="btn btn-outline-primary flex-fill">{{ text_today }}</a>
                  <a href="?route=common/dashboard&user_token={{ user_token }}&date_from={{ 'now'|date('Y-m-01') }}&date_to={{ 'now'|date('Y-m-d') }}"
                     class="btn btn-outline-primary flex-fill">{{ text_this_month }}</a>
                  <a href="?route=common/dashboard&user_token={{ user_token }}&date_from={{ 'now'|date('Y-01-01') }}&date_to={{ 'now'|date('Y-m-d') }}"
                     class="btn btn-outline-primary flex-fill">{{ text_this_year }}</a>
                </div>
              </div>
            </div>
          </div>

          <!-- Advanced Filters (Collapsible) -->
          <div class="row">
            <div class="col-12">
              <button type="button" class="btn btn-link text-muted" data-toggle="collapse"
                      data-target="#advanced-filters" aria-expanded="false">
                <i class="fa fa-plus"></i> {{ text_advanced_filters }}
              </button>
            </div>
          </div>

          <div class="collapse" id="advanced-filters">
            <hr>
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label class="form-label">{{ text_category }}</label>
                  <select name="category_id" class="form-control">
                    <option value="">{{ text_all_categories }}</option>
                    {% for category in categories %}
                    <option value="{{ category.category_id }}">{{ category.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label class="form-label">{{ text_customer_group }}</label>
                  <select name="customer_group_id" class="form-control">
                    <option value="">{{ text_all_customer_groups }}</option>
                    {% for group in customer_groups %}
                    <option value="{{ group.customer_group_id }}">{{ group.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label class="form-label">{{ text_currency }}</label>
                  <select name="currency" class="form-control">
                    <option value="">{{ text_all_currencies }}</option>
                    {% for currency in currencies %}
                    <option value="{{ currency.code }}">{{ currency.title }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label class="form-label">{{ text_order_status }}</label>
                  <select name="order_status_id" class="form-control">
                    <option value="">{{ text_all_statuses }}</option>
                    {% for status in order_statuses %}
                    <option value="{{ status.order_status_id }}">{{ status.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Dashboard Content - دقة 100% -->
  <div class="container-fluid">

    <!-- Row 1: Key Metrics -->
    <div class="row">
      <!-- Sales Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-shopping-cart fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ sales_stats.today_sales|number_format(2) }}</div>
                <div>مبيعات اليوم ({{ sales_stats.currency }})</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">{{ sales_stats.today_orders }} طلب</span>
              </div>
              <div class="col-xs-6 text-right">
                {% if sales_stats.sales_change_percent >= 0 %}
                  <span class="text-success">
                    <i class="fa fa-arrow-up"></i> {{ sales_stats.sales_change_percent }}%
                  </span>
                {% else %}
                  <span class="text-danger">
                    <i class="fa fa-arrow-down"></i> {{ sales_stats.sales_change_percent|abs }}%
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Inventory Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ inventory_stats.total_value|number_format(2) }}</div>
                <div>قيمة المخزون ({{ inventory_stats.currency }})</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">{{ inventory_stats.total_products }} منتج</span>
              </div>
              <div class="col-xs-6 text-right">
                {% if inventory_stats.low_stock_count > 0 %}
                  <span class="text-warning">
                    <i class="fa fa-exclamation-triangle"></i> {{ inventory_stats.low_stock_count }} منخفض
                  </span>
                {% else %}
                  <span class="text-success">
                    <i class="fa fa-check"></i> جيد
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Monthly Target Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-bullseye fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ sales_stats.achievement_rate }}%</div>
                <div>تحقيق الهدف الشهري</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">{{ sales_stats.monthly_sales|number_format(0) }} من {{ sales_stats.monthly_target|number_format(0) }}</span>
              </div>
              <div class="col-xs-6 text-right">
                <div class="progress progress-mini">
                  <div class="progress-bar progress-bar-warning" style="width: {{ sales_stats.achievement_rate }}%"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Customers Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-users fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ sales_stats.monthly_customers }}</div>
                <div>عملاء هذا الشهر</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">متوسط الطلب: {{ sales_stats.today_avg_order|number_format(2) }}</span>
              </div>
              <div class="col-xs-6 text-right">
                <span class="text-info">
                  <i class="fa fa-user-plus"></i> نشط
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 2: Charts and Tables -->
    <div class="row">
      <!-- Branch Performance -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-building"></i> أداء الفروع</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>الفرع</th>
                    <th>المدينة</th>
                    <th>المبيعات</th>
                    <th>الطلبات</th>
                    <th>النسبة</th>
                  </tr>
                </thead>
                <tbody>
                  {% for branch in branch_stats.branches %}
                  <tr>
                    <td>
                      <strong>{{ branch.name }}</strong>
                      <small class="text-muted">({{ branch.type }})</small>
                    </td>
                    <td>{{ branch.city }}</td>
                    <td>{{ branch.total_sales|number_format(2) }}</td>
                    <td>{{ branch.total_orders }}</td>
                    <td>
                      <div class="progress progress-mini">
                        <div class="progress-bar" style="width: {{ branch.sales_percentage }}%; background-color: {{ branch.performance_color }}"></div>
                      </div>
                      <small>{{ branch.sales_percentage }}%</small>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Products -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-star"></i> أفضل المنتجات مبيعاً</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>الإيرادات</th>
                    <th>الهامش</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in top_products.products %}
                  <tr>
                    <td>
                      <span class="badge" style="background-color: {{ product.performance_color }}">{{ product.rank }}</span>
                    </td>
                    <td>
                      <strong>{{ product.name }}</strong>
                      <br><small class="text-muted">{{ product.model }}</small>
                    </td>
                    <td>{{ product.quantity_sold }}</td>
                    <td>{{ product.revenue|number_format(2) }}</td>
                    <td>
                      <span class="text-{% if product.profit_margin >= 20 %}success{% elseif product.profit_margin >= 10 %}warning{% else %}danger{% endif %}">
                        {{ product.profit_margin }}%
                      </span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 3: E-commerce & Sales Reps -->
    <div class="row">
      <!-- E-commerce Performance -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-shopping-bag"></i> أداء المتجر الإلكتروني</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ ecommerce_stats.daily_visitors }}</div>
                  <div class="metric-label">زوار اليوم</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ ecommerce_stats.conversion_rate }}%</div>
                  <div class="metric-label">معدل التحويل</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ ecommerce_stats.cart_abandonment_rate }}%</div>
                  <div class="metric-label">معدل التخلي عن السلة</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ ecommerce_stats.shipping_orders }}</div>
                  <div class="metric-label">شحنات جارية</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ ecommerce_stats.new_customers }}</div>
                  <div class="metric-label">عملاء جدد</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-{% if ecommerce_stats.return_rate <= 5 %}success{% elseif ecommerce_stats.return_rate <= 10 %}warning{% else %}danger{% endif %}">{{ ecommerce_stats.return_rate }}%</div>
                  <div class="metric-label">نسبة المرتجعات</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sales Representatives Performance -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-users"></i> أداء المناديب</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>المندوب</th>
                    <th>الصفقات</th>
                    <th>المبيعات</th>
                    <th>الهدف</th>
                  </tr>
                </thead>
                <tbody>
                  {% for rep in sales_reps_stats.sales_reps %}
                  <tr>
                    <td>
                      <strong>{{ rep.name }}</strong>
                      <br><small class="text-muted">{{ rep.unique_customers }} عميل</small>
                    </td>
                    <td>{{ rep.deals_closed }}</td>
                    <td>{{ rep.total_sales|number_format(0) }}</td>
                    <td>
                      <div class="progress progress-mini">
                        <div class="progress-bar" style="width: {{ rep.achievement_rate }}%; background-color: {{ rep.performance_color }}"></div>
                      </div>
                      <small>{{ rep.achievement_rate }}%</small>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            <div class="text-center">
              <small class="text-muted">
                متوسط الأداء العام: {{ sales_reps_stats.summary.avg_performance }}%
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 4: Additional Metrics from info1.md -->
    <div class="row">
      <!-- Quick Metrics Cards -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-primary">{{ ecommerce_stats.average_rating }}/5</h4>
            <p class="text-muted">متوسط التقييم</p>
            <small>{{ ecommerce_stats.total_reviews }} تقييم</small>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-success">{{ inventory_stats.low_stock_percentage }}%</h4>
            <p class="text-muted">نسبة المخزون المنخفض</p>
            <small>{{ inventory_stats.low_stock_count }} من {{ inventory_stats.total_products }}</small>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-info">{{ sales_stats.today_items_sold }}</h4>
            <p class="text-muted">قطع مباعة اليوم</p>
            <small>من {{ sales_stats.today_orders }} طلب</small>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-warning">{{ branch_stats.summary.total_branches }}</h4>
            <p class="text-muted">إجمالي الفروع</p>
            <small>متوسط: {{ branch_stats.summary.avg_sales_per_branch|number_format(0) }}</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 5: Financial & HR Summary -->
    <div class="row">
      <!-- Financial Summary -->
      <div class="col-lg-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-money"></i> الملخص المالي</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ financial_summary.total_revenue|number_format(0) }}</div>
                  <div class="metric-label">إجمالي الإيرادات</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ financial_summary.net_profit|number_format(0) }}</div>
                  <div class="metric-label">صافي الربح</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ financial_summary.total_expenses|number_format(0) }}</div>
                  <div class="metric-label">إجمالي المصروفات</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ financial_summary.bank_balance|number_format(0) }}</div>
                  <div class="metric-label">رصيد البنك</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- HR & CRM Summary -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-users"></i> الموارد البشرية والعملاء</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ hr_stats.active_employees }}</div>
                  <div class="metric-label">موظف نشط</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ hr_stats.pending_leaves }}</div>
                  <div class="metric-label">إجازة معلقة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ crm_stats.active_customers }}</div>
                  <div class="metric-label">عميل نشط</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ crm_stats.new_customers_month }}</div>
                  <div class="metric-label">عميل جديد</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 6: KPI Summary from 170+ existing indicators -->
    <div class="row">
      <div class="col-lg-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-dashboard"></i> مؤشرات الأداء الرئيسية (170+ مؤشر)</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ kpi_data.critical_kpis.accounting.total_accounts|default(0) }}</div>
                  <div class="metric-label">إجمالي الحسابات</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ kpi_data.critical_kpis.inventory.inventory_turnover|default(0) }}</div>
                  <div class="metric-label">دوران المخزون</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ kpi_data.critical_kpis.sales.conversion_rate|default(0) }}%</div>
                  <div class="metric-label">معدل التحويل</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ kpi_data.critical_kpis.procurement.supplier_performance|default(0) }}</div>
                  <div class="metric-label">أداء الموردين</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ kpi_data.critical_kpis.hr.employee_satisfaction|default(0) }}%</div>
                  <div class="metric-label">رضا الموظفين</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ kpi_data.important_kpis.advanced_accounting.roi|default(0) }}%</div>
                  <div class="metric-label">العائد على الاستثمار</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 7: Additional Modules from info1.md -->
    <div class="row">
      <!-- Tasks & Productivity -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-tasks"></i> المهام والإنتاجية</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ tasks_stats.daily_tasks }}</div>
                  <div class="metric-label">مهام يومية</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ tasks_stats.overdue_tasks }}</div>
                  <div class="metric-label">مهام متأخرة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ tasks_stats.completion_rate }}%</div>
                  <div class="metric-label">نسبة الإنجاز</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ tasks_stats.active_projects }}</div>
                  <div class="metric-label">مشاريع جارية</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Logistics & Shipping -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-truck"></i> الشحن واللوجستيك</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ logistics_stats.active_shipments }}</div>
                  <div class="metric-label">شحنات جارية</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ logistics_stats.delayed_shipments }}</div>
                  <div class="metric-label">شحنات متأخرة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ logistics_stats.on_time_delivery_rate }}%</div>
                  <div class="metric-label">التسليم في الوقت</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ logistics_stats.total_shipping_cost|number_format(0) }}</div>
                  <div class="metric-label">تكلفة الشحن</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Business Intelligence -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-brain"></i> ذكاء الأعمال</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ bi_stats.sales_forecast|number_format(0) }}</div>
                  <div class="metric-label">توقعات المبيعات</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ bi_stats.churn_risk_customers }}</div>
                  <div class="metric-label">عملاء معرضون للخسارة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ bi_stats.customer_lifetime_value|number_format(0) }}</div>
                  <div class="metric-label">قيمة العميل الدائمة</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ bi_stats.compound_growth_rate }}%</div>
                  <div class="metric-label">نمو مركب</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- Real-time Auto Refresh Script -->
<script>
$(document).ready(function() {
    var refreshInterval;
    var isRefreshing = false;

    // Auto refresh every 2 minutes
    function startAutoRefresh() {
        refreshInterval = setInterval(function() {
            refreshDashboard();
        }, 120000); // 2 minutes
    }

    // AJAX refresh function
    function refreshDashboard() {
        if (isRefreshing) return;

        isRefreshing = true;
        $('#refresh-dashboard').addClass('loading').prop('disabled', true);

        $.ajax({
            url: 'index.php?route=common/dashboard/refresh&user_token={{ user_token }}',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                updateDashboardData(data);
                showNotification('تم تحديث البيانات بنجاح', 'success');
            },
            error: function() {
                showNotification('خطأ في تحديث البيانات', 'error');
            },
            complete: function() {
                isRefreshing = false;
                $('#refresh-dashboard').removeClass('loading').prop('disabled', false);
            }
        });
    }

    // Update dashboard data
    function updateDashboardData(data) {
        // Add visual feedback
        $('.metric-card, .panel').addClass('updating');

        // Update all sections dynamically
        setTimeout(function() {
            if (data.sales_stats) {
                $('.huge').each(function() {
                    if ($(this).siblings().text().includes('مبيعات')) {
                        $(this).text(formatNumber(data.sales_stats.today_sales));
                    }
                });
            }

            if (data.inventory_stats) {
                $('.huge').each(function() {
                    if ($(this).siblings().text().includes('قيمة المخزون')) {
                        $(this).text(formatNumber(data.inventory_stats.total_value));
                    }
                });
            }

            // Remove updating class and add updated
            $('.metric-card, .panel').removeClass('updating').addClass('updated');

            // Update timestamp
            var timestamp = new Date().toLocaleString('ar-EG');
            if ($('.refresh-indicator').length === 0) {
                $('body').append('<div class="refresh-indicator">آخر تحديث: ' + timestamp + '</div>');
            } else {
                $('.refresh-indicator').text('آخر تحديث: ' + timestamp);
            }

            setTimeout(function() {
                $('.metric-card, .panel').removeClass('updated');
            }, 1000);
        }, 500);
    }

    // Format numbers
    function formatNumber(num) {
        return parseFloat(num).toLocaleString('ar-EG', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        });
    }

    // Show notification
    function showNotification(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var notification = '<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">' +
            '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
            message + '</div>';

        $('body').append(notification);
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // Manual refresh button
    $('#refresh-dashboard').click(function(e) {
        e.preventDefault();
        refreshDashboard();
    });

    // Export functionality
    $('#export-dashboard').click(function() {
        window.open('?route=common/dashboard/export&user_token={{ user_token }}', '_blank');
    });

    // Print functionality
    $('#print-dashboard').click(function() {
        window.print();
    });

    // Start auto refresh
    startAutoRefresh();

    // Stop auto refresh when page is hidden
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            clearInterval(refreshInterval);
        } else {
            startAutoRefresh();
        }
    });
});
</script>
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-info minimize-widget" title="{{ text_minimize_widget|default('تصغير') }}">
              <i class="fa fa-minus"></i>
            </button>
            <button class="btn btn-sm btn-outline-warning fullscreen-widget" title="{{ text_fullscreen_widget|default('لء الشاشة') }}">
              <i class="fa fa-expand"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_revenue" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_revenue|default('إجمالي الإيراات') }}</div>
                <div class="kpi-trend positive">
                  <i class="fa fa-arrow-up"></i>
                  <span data-kpi="revenue_trend" data-format="percentage">0%</span>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="net_profit" data-format="currency">0</div>
                <div class="kpi-label">{{ text_net_profit|default('صافي الربح') }}</div>
                <div class="kpi-trend positive">
                  <i class="fa fa-arrow-up"></i>
                  <span data-kpi="profit_trend" data-format="percentage">0%</span>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="gross_margin" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_gross_margin|default('البح الإجمالي') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="operating_margin" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_operating_margin|default('الربح التشغيلي') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Strategic Financial Metrics Widget -->
      <div class="dashboard-widget" data-widget="strategic_financial" data-widget-type="executive" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-calculator"></i> {{ text_strategic_financial|default('المؤشرات الالية الاستراتيجية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="metric-group">
                <div class="metric" data-metric="roi" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_roi|default('اعائد على الاستثمر') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="roa" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_roa|default('العائد على الأصول') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="roe" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_roe|default('العائد على حقو الملكية') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="metric-group">
                <div class="metric" data-metric="debt_to_equity" data-format="number">0</div>
                <div class="metric-label">{{ text_debt_to_equity|default('نسبة الدين إلى حقوق الملكية') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="current_ratio" data-format="number">0</div>
                <div class="metric-label">{{ text_current_ratio|default('النسبة الحالية') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="working_capital" data-format="currency">0</div>
                <div class="metric-label">{{ text_working_capital|default('رأس المال اعامل') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- === SALES & CRM WIDGETS === -->
      
      <!-- Sales Performance Widget -->
      <div class="dashboard-widget" data-widget="sales_performance" data-widget-type="sales" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-chart-bar"></i> {{ text_sales_performance|default('أداء المبيعات') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_sales" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_sales|default('إجمالي المبيعات') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_orders" data-format="number">0</div>
                <div class="kpi-label">{{ text_total_orders|default('إجمالي الطلبات') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="average_order_value" data-format="currency">0</div>
                <div class="kpi-label">{{ text_average_order_value|default('متوسط قيمة الطلب') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="conversion_rate" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_conversion_rate|default('معدل التحويل') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="line" data-chart-data="sales_trends">
            <canvas id="sales-trends-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Customer Analytics Widget -->
      <div class="dashboard-widget" data-widget="customer_analytics" data-widget-type="sales" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-users"></i> {{ text_customer_analytics|default('تليلات العملاء') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_customers" data-format="number">0</div>
                <div class="kpi-label">{{ text_total_customers|default('إجمالي العملاء') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="new_customers" data-format="number">0</div>
                <div class="kpi-label">{{ text_new_customers|default('الملاء الجدد') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="customer_lifetime_value" data-format="currency">0</div>
                <div class="kpi-label">{{ text_customer_lifetime_value|default('قيمة لعميل مدى الحياة') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="customer_retention_rate" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_customer_retention_rate|default('معدل الاحتفاظ بالعملا') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="pie" data-chart-data="customer_segments">
            <canvas id="customer-segments-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- === INVENTORY & WAREHOUSE WIDGETS === -->
      
      <!-- Inventory Overview Widget -->
      <div class="dashboard-widget" data-widget="inventory_overview" data-widget-type="inventory" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-boxes"></i> {{ text_inventory_overview|default('نظرة عامة على المخزون') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحدي') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزاة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_skus" data-format="number">0</div>
                <div class="kpi-label">{{ text_total_skus|default('إجمالي الصناف') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="inventory_value" data-format="currency">0</div>
                <div class="kpi-label">{{ text_inventory_value|default('قيمة المخون') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="stock_turnover_rate" data-format="number">0</div>
                <div class="kpi-label">{{ text_stock_turnover_rate|default('مدل دوران المخزون') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="inventory_accuracy" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_inventory_accuracy|default('دقة المخزون') }}</div>
              </div>
            </div>
          </div>
          <div class="widget-table" data-table="low_stock_alerts">
            <table>
              <thead>
                <tr>
                  <th>{{ text_product|default('المنتج') }}</th>
                  <th>{{ text_current_stock|default('المخزون الحالي') }}</th>
                  <th>{{ text_min_stock|default('الد الأدنى') }}</th>
                  <th>{{ text_status|default('الحالة') }}</th>
                </tr>
              </thead>
              <tbody>
                <!-- Data will be populated by JavaScript -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- === ETA & TAX COMPLIANCE WIDGETS === -->
      
      <!-- ETA Integration Status Widget -->
      <div class="dashboard-widget" data-widget="eta_status" data-widget-type="eta" data-width="6" data-height="3">
        <div class="widget-header">
          <h5><i class="fa fa-receipt"></i> {{ text_eta_integration_status|default('حالة تكامل الضرائب المصرية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary test-eta-connection" title="{{ text_test_connection|default('اختبار الاتصال') }}">
              <i class="fa fa-plug"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-4">
              <div class="eta-status-card">
                <div class="status-indicator" id="eta-connection-status"></div>
                <div class="status-label">{{ text_connection_status|default('حالة الاتصال') }}</div>
                <div class="status-value" id="eta-connection-text">{{ text_checking|default('جاري الفحص...') }}</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="eta-status-card">
                <div class="status-indicator" id="eta-queue-status"></div>
                <div class="status-label">{{ text_queue_status|default('حالة قائمة الانتظار') }}</div>
                <div class="status-value" id="eta-queue-count">0</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="eta-status-card">
                <div class="status-indicator" id="eta-sync-status"></div>
                <div class="status-label">{{ text_last_sync|default('آخر مزامنة') }}</div>
                <div class="status-value" id="eta-last-sync">{{ text_never|default('أبداً') }}</div>
              </div>
            </div>
          </div>
          <div class="eta-actions">
            <button class="btn btn-sm btn-primary" onclick="processETAQueue()">{{ text_process_queue|default('معالجة قائمة الانتظار') }}</button>
            <button class="btn btn-sm btn-warning" onclick="clearETAQueue()">{{ text_clear_queue|default('مسح قائمة الانتظار') }}</button>
            <button class="btn btn-sm btn-info" onclick="viewETALogs()">{{ text_view_logs|default('عرض السجلات') }}</button>
          </div>
        </div>
      </div>
      
      <!-- E-Invoicing Statistics Widget -->
      <div class="dashboard-widget" data-widget="eta_statistics" data-widget-type="eta" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-chart-pie"></i> {{ text_einvoicing_statistics|default('إحصائيات الفواتير الإلكترونية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_invoices_sent" data-format="number">0</div>
                <div class="kpi-label">{{ text_invoices_sent|default('الفواتير المرسلة') }}</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_invoices_approved" data-format="number">0</div>
                <div class="kpi-label">{{ text_invoices_approved|default('الفواتير المعتمدة') }}</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_invoices_rejected" data-format="number">0</div>
                <div class="kpi-label">{{ text_invoices_rejected|default('الفواتير المرفوضة') }}</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_success_rate" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_success_rate|default('معدل النجاح') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="doughnut" data-chart-data="eta_invoice_status">
            <canvas id="eta-invoice-status-chart"></canvas>
          </div>
          <div class="eta-recent-activity">
            <h6>{{ text_recent_activity|default('النشاط الأخير') }}</h6>
            <div class="activity-list" id="eta-recent-activity">
              <!-- Activity items will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>
      
      <!-- Tax Compliance Widget -->
      <div class="dashboard-widget" data-widget="tax_compliance" data-widget-type="eta" data-width="12" data-height="3">
        <div class="widget-header">
          <h5><i class="fa fa-balance-scale"></i> {{ text_tax_compliance|default('الامتثال الضريبي') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="vat_collected" data-format="currency">0</div>
                <div class="metric-label">{{ text_vat_collected|default('ضريبة القيمة المضافة المحصلة') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="vat_paid" data-format="currency">0</div>
                <div class="metric-label">{{ text_vat_paid|default('ضريبة القيمة المضافة المدفوعة') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="vat_due" data-format="currency">0</div>
                <div class="metric-label">{{ text_vat_due|default('ضريبة القيمة المضافة المستحقة') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="withholding_tax" data-format="currency">0</div>
                <div class="metric-label">{{ text_withholding_tax|default('ضريبة الخصم') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="compliance_score" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_compliance_score|default('نقاط الامتثال') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="pending_submissions" data-format="number">0</div>
                <div class="metric-label">{{ text_pending_submissions|default('المعاملات المعلقة') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- === FINANCE & ACCOUNTING WIDGETS === -->
      
      <!-- Financial Overview Widget -->
      <div class="dashboard-widget" data-widget="financial_overview" data-widget-type="finance" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-dollar-sign"></i> {{ text_financial_overview|default('نظرة عامة على المالية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_revenue" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_revenue|default('إجمالي الإيرادات') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_expenses" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_expenses|default('إجمالي المصروفات') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="accounts_receivable" data-format="currency">0</div>
                <div class="kpi-label">{{ text_accounts_receivable|default('الذمم المدينة') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="accounts_payable" data-format="currency">0</div>
                <div class="kpi-label">{{ text_accounts_payable|default('الذمم الدائنة') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="bar" data-chart-data="financial_ratios">
            <canvas id="financial-ratios-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Cash Flow Analysis Widget -->
      <div class="dashboard-widget" data-widget="cash_flow_analysis" data-widget-type="finance" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-money-bill-wave"></i> {{ text_cash_flow_analysis|default('تحليل التدف النقدي') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="operating_cash_flow" data-format="currency">0</div>
                <div class="kpi-label">{{ text_operating_cash_flow|default('لتدفق النقدي التغيلي') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="free_cash_flow" data-format="currency">0</div>
                <div class="kpi-label">{{ text_free_cash_flow|default('التدفق الندي الحر') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="cash_position" data-format="currency">0</div>
                <div class="kpi-label">{{ text_cash_position|default('المركز النقدي') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="working_capital" data-format="currency">0</div>
                <div class="kpi-label">{{ text_working_capital|default('رأس المال العامل') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="line" data-chart-data="cash_flow_trends">
            <canvas id="cash-flow-trends-chart"></canvas>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Loading Indicator -->
  <div id="loading-indicator" class="loading-overlay" style="display: none;">
    <div class="loading-spinner">
      <i class="fa fa-spinner fa-spin fa-3x"></i>
      <p>{{ text_loading|default('جري التحميل...') }}</p>
    </div>
  </div>

  <!-- Alert Container -->
  <div class="alert-container"></div>

  <!-- Widget Gallery Modal -->
  <div id="widget-gallery-modal" class="modal-overlay" style="display: none;">
    <div class="modal">
      <div class="modal-header">
        <h3 class="modal-title">{{ text_add_widget|default('إضافة مكون') }}</h3>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="widget-gallery">
          <!-- Widget categories will be populated by JavaScript -->
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-default" onclick="closeModal()">{{ text_cancel|default('إلغء') }}</button>
      </div>
    </div>
  </div>

  <!-- Widget Settings Modal -->
  <div id="widget-settings-modal" class="modal-overlay" style="display: none;">
    <div class="modal">
      <div class="modal-header">
        <h3 class="modal-title">{{ text_widget_settings|default('عدادات المكونات') }}</h3>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="settings-section">
          <h4>{{ text_dashboard_layout|default('تخطيط لوحة المعلمات') }}</h4>
          <div class="form-group">
            <label>{{ text_layout_type|default('نوع التخطيط') }}</label>
            <select id="layout-type" class="form-control">
              <option value="grid">{{ text_grid_layout|default('تخطيط الشبكة') }}</option>
              <option value="flex">{{ text_flex_layout|default('التخطيط المرن') }}</option>
              <option value="masonry">{{ text_masonry_layout|default('التخيط المتراكب') }}</option>
            </select>
          </div>
        </div>
        <div class="settings-section">
          <h4>{{ text_auto_refresh|default('التحديث اتلقائي') }}</h4>
          <div class="form-group">
            <label>
              <input type="checkbox" id="auto-refresh-enabled" checked>
              {{ text_enable_auto_refresh|default('تفعيل التحدث التلقائي') }}
            </label>
          </div>
          <div class="form-group">
            <label>{{ text_refresh_interval|default('فترة التحديث') }}</label>
            <select id="refresh-interval" class="form-control">
              <option value="30">{{ text_30_seconds|default('30 ثنية') }}</option>
              <option value="60">{{ text_1_minute|default('دقيقة واحدة') }}</option>
              <option value="300" selected>{{ text_5_minutes|default('5 دائق') }}</option>
              <option value="600">{{ text_10_minutes|default('10 دقائ') }}</option>
            </select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" onclick="saveWidgetSettings()">{{ text_save|default('حفظ') }}</button>
        <button class="btn btn-default" onclick="closeModal()">{{ text_cancel|default('إغاء') }}</button>
      </div>
    </div>
  </div>

</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Include Dashboard Widgets JavaScript -->
<script src="view/javascript/dashboard/widgets.js"></script>

<!-- Include Dashboard Widgets CSS -->
<link rel="stylesheet" href="view/stylesheet/dashboard/widgets.css">

<style>
/* ETA & Tax Compliance Widget Styles */
.eta-status-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.eta-status-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 0 auto 10px;
    position: relative;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.status-indicator.connected {
    background: linear-gradient(45deg, #28a745, #20c997);
    animation: pulse-green 2s infinite;
}

.status-indicator.disconnected {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    animation: pulse-yellow 2s infinite;
}

.status-indicator.error {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    animation: pulse-red 2s infinite;
}

.status-indicator.unknown {
    background: linear-gradient(45deg, #6c757d, #adb5bd);
}

@keyframes pulse-green {
    0%, 100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
}

@keyframes pulse-yellow {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
}

@keyframes pulse-red {
    0%, 100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
}

.status-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.status-value {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.eta-actions {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.eta-actions .btn {
    margin: 0 5px;
    font-size: 12px;
    padding: 5px 10px;
}

/* ETA KPI Cards */
.eta-kpi {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.eta-kpi::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
    opacity: 0;
}

.eta-kpi:hover::before {
    animation: shine 0.5s ease-in-out;
    opacity: 1;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.eta-kpi .kpi-value {
    color: #fff;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.eta-kpi .kpi-label {
    color: rgba(255,255,255,0.9);
}

/* Tax Compliance Metrics */
.compliance-metric {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.compliance-metric:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 8px;
    display: block;
}

.metric-label {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.3;
    font-weight: 500;
}

/* ETA Recent Activity */
.eta-recent-activity {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.activity-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 14px;
    color: white;
}

.activity-icon.success { background: #28a745; }
.activity-icon.warning { background: #ffc107; }
.activity-icon.error { background: #dc3545; }
.activity-icon.info { background: #17a2b8; }

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.activity-time {
    font-size: 11px;
    color: #6c757d;
}

/* System Health Indicators */
.health-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.health-indicator.healthy {
    background: #28a745;
    box-shadow: 0 0 6px rgba(40, 167, 69, 0.6);
}

.health-indicator.warning {
    background: #ffc107;
    box-shadow: 0 0 6px rgba(255, 193, 7, 0.6);
}

.health-indicator.error {
    background: #dc3545;
    box-shadow: 0 0 6px rgba(220, 53, 69, 0.6);
}

.health-indicator.unknown {
    background: #6c757d;
}

/* Widget Type Specific Styles */
.dashboard-widget[data-widget-type="eta"] {
    border-left: 4px solid #007bff;
}

.dashboard-widget[data-widget-type="eta"] .widget-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.dashboard-widget[data-widget-type="eta"] .widget-header h5 {
    color: white;
}

.dashboard-widget[data-widget-type="eta"] .widget-controls .btn {
    border-color: rgba(255,255,255,0.3);
    color: rgba(255,255,255,0.8);
}

.dashboard-widget[data-widget-type="eta"] .widget-controls .btn:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

/* Responsive Design for ETA Widgets */
@media (max-width: 768px) {
    .eta-status-card {
        margin-bottom: 15px;
    }
    
    .compliance-metric {
        margin-bottom: 15px;
    }
    
    .eta-actions .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
    
    .metric-value {
        font-size: 20px;
    }
    
    .metric-label {
        font-size: 11px;
    }
}

/* Search Results Modal */
.search-results-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.search-results-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.search-results-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.search-results-list li {
    border-bottom: 1px solid #e9ecef;
    padding: 10px 0;
}

.search-results-list li:last-child {
    border-bottom: none;
}

.search-results-list a {
    text-decoration: none;
    color: #495057;
    display: block;
}

.search-results-list a:hover {
    color: #007bff;
}

.search-results-list small {
    display: block;
    color: #6c757d;
    margin-top: 5px;
}
</style>

<script>
// Enhanced Dashboard JavaScript with RTL/LTR Support
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();

    // Setup event handlers
    setupEventHandlers();

    // Initialize tooltips
    initializeTooltips();

    // Setup auto-refresh
    setupAutoRefresh();

    // Initialize filters
    initializeFilters();
});

function initializeDashboard() {
    // Set direction attribute
    const direction = '{{ direction }}';
    document.querySelector('.dashboard-container').setAttribute('dir', direction);

    // Initialize widgets system
    if (typeof DashboardWidgets !== 'undefined') {
        window.dashboardWidgets = new DashboardWidgets();
    }

    // Show loading state initially
    showLoadingState();

    // Load initial data
    setTimeout(() => {
        hideLoadingState();
        showSuccessMessage('{{ text_data_updated_successfully }}');
    }, 1000);
}

function setupEventHandlers() {
    // Refresh dashboard
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshDashboard();
        });
    }

    // Export dashboard
    const exportBtn = document.getElementById('export-dashboard');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportDashboard();
        });
    }

    // Print dashboard
    const printBtn = document.getElementById('print-dashboard');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            printDashboard();
        });
    }

    // Settings dashboard
    const settingsBtn = document.getElementById('settings-dashboard');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', function() {
            showDashboardSettings();
        });
    }

    // Toggle filters
    const toggleFiltersBtn = document.getElementById('toggle-filters');
    if (toggleFiltersBtn) {
        toggleFiltersBtn.addEventListener('click', function() {
            toggleFilters();
        });
    }

    // Form validation
    const filtersForm = document.getElementById('dashboard-filters');
    if (filtersForm) {
        filtersForm.addEventListener('submit', function(e) {
            if (!validateFiltersForm()) {
                e.preventDefault();
            }
        });
    }
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    if (typeof $ !== 'undefined' && $.fn.tooltip) {
        $('[data-toggle="tooltip"]').tooltip();
    }
}

function setupAutoRefresh() {
    // Auto-refresh every 5 minutes
    setInterval(() => {
        if (window.dashboardWidgets) {
            window.dashboardWidgets.loadAllWidgetData();
        }
    }, 300000); // 5 minutes
}

function initializeFilters() {
    // Set default date range if empty
    const dateFrom = document.querySelector('input[name="date_from"]');
    const dateTo = document.querySelector('input[name="date_to"]');

    if (dateFrom && !dateFrom.value) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        dateFrom.value = firstDay.toISOString().split('T')[0];
    }

    if (dateTo && !dateTo.value) {
        const today = new Date();
        dateTo.value = today.toISOString().split('T')[0];
    }
}

function refreshDashboard() {
    showLoadingState();

    // AJAX request to refresh data
    fetch('index.php?route=common/dashboard/refresh&user_token={{ user_token }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingState();
        updateDashboardData(data);
        showSuccessMessage('{{ text_data_updated_successfully }}');
        updateLastRefreshTime();
    })
    .catch(error => {
        hideLoadingState();
        showErrorMessage('{{ text_error_updating_data }}');
        console.error('Dashboard refresh error:', error);
    });
}

function exportDashboard() {
    showLoadingState();

    // Create export data
    const exportData = {
        filters: getFiltersData(),
        timestamp: new Date().toISOString(),
        user_token: '{{ user_token }}'
    };

    // Simulate export process
    setTimeout(() => {
        hideLoadingState();
        showInfoMessage('{{ text_export_feature }}');
    }, 1500);
}

function printDashboard() {
    // Hide non-printable elements
    const nonPrintable = document.querySelectorAll('.no-print, .dashboard-actions');
    nonPrintable.forEach(el => el.style.display = 'none');

    // Print
    window.print();

    // Restore elements
    nonPrintable.forEach(el => el.style.display = '');
}

function showDashboardSettings() {
    // Create settings modal dynamically
    const modal = createSettingsModal();
    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function toggleFilters() {
    const filtersBody = document.getElementById('filters-body');
    const toggleBtn = document.getElementById('toggle-filters');
    const icon = toggleBtn.querySelector('i');

    if (filtersBody.style.display === 'none') {
        filtersBody.style.display = 'block';
        icon.className = 'fa fa-chevron-up';
    } else {
        filtersBody.style.display = 'none';
        icon.className = 'fa fa-chevron-down';
    }
}

function validateFiltersForm() {
    const dateFrom = document.querySelector('input[name="date_from"]').value;
    const dateTo = document.querySelector('input[name="date_to"]').value;

    if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
        showErrorMessage('{{ text_invalid_date_range|default("نطاق التاريخ غير صحيح") }}');
        return false;
    }

    return true;
}

function showLoadingState() {
    const content = document.getElementById('content');
    content.classList.add('loading');
}

function hideLoadingState() {
    const content = document.getElementById('content');
    content.classList.remove('loading');
}

function showSuccessMessage(message) {
    showNotification(message, 'success');
}

function showErrorMessage(message) {
    showNotification(message, 'error');
}

function showInfoMessage(message) {
    showNotification(message, 'info');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" onclick="this.parentElement.remove()">
            <span>&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function updateDashboardData(data) {
    // Update widgets with new data
    if (window.dashboardWidgets && data) {
        window.dashboardWidgets.updateWidgets(data);
    }
}

function updateLastRefreshTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('{{ language_code }}');
    const lastUpdateElements = document.querySelectorAll('.last-update-time');

    lastUpdateElements.forEach(el => {
        el.textContent = '{{ text_last_update }}' + timeString;
    });
}

function getFiltersData() {
    const form = document.getElementById('dashboard-filters');
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    return data;
}

function createSettingsModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 9999;';

    modal.innerHTML = `
        <div class="modal-content bg-white rounded p-4" style="max-width: 500px; width: 90%;">
            <h5>{{ text_dashboard_settings }}</h5>
            <hr>
            <div class="form-group">
                <label>{{ text_auto_refresh }}</label>
                <select class="form-control" id="auto-refresh-interval">
                    <option value="0">{{ text_disabled|default("معطل") }}</option>
                    <option value="60">1 {{ text_minute|default("دقيقة") }}</option>
                    <option value="300" selected>5 {{ text_minutes|default("دقائق") }}</option>
                    <option value="600">10 {{ text_minutes|default("دقائق") }}</option>
                </select>
            </div>
            <div class="form-group">
                <label>{{ text_theme|default("المظهر") }}</label>
                <select class="form-control" id="dashboard-theme">
                    <option value="light">{{ text_light|default("فاتح") }}</option>
                    <option value="dark">{{ text_dark|default("داكن") }}</option>
                </select>
            </div>
            <hr>
            <div class="text-right">
                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    {{ text_cancel }}
                </button>
                <button type="button" class="btn btn-primary ml-2" onclick="saveDashboardSettings()">
                    {{ text_save }}
                </button>
            </div>
        </div>
    `;

    return modal;
}

function saveDashboardSettings() {
    // Save settings logic here
    showSuccessMessage('{{ text_settings_saved }}');
    document.querySelector('.modal-overlay').remove();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape key to close modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => modal.remove());
    }

    // Ctrl/Cmd + R to refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshDashboard();
    }

    // F5 to refresh
    if (e.key === 'F5') {
        e.preventDefault();
        refreshDashboard();
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    // Adjust layout for mobile
    if (window.innerWidth < 768) {
        document.body.classList.add('mobile-view');
    } else {
        document.body.classList.remove('mobile-view');
    }
});
</script>

{{ footer }}
