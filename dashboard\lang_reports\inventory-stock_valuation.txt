📄 Route: inventory/stock_valuation
📂 Controller: controller\inventory\stock_valuation.php
🧱 Models used (4):
   - common/central_service_manager
   - inventory/product
   - inventory/stock_valuation
   - inventory/warehouse
🎨 Twig templates (1):
   - view\template\inventory\stock_valuation.twig
🈯 Arabic Language Files (1):
   - language\ar\inventory\stock_valuation.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_recalculate_failed
   - heading_title
   - text_average_cost
   - text_fifo
   - text_home
   - text_latest_cost
   - text_lifo
   - text_pagination
   - text_recalculate_success
   - text_standard_cost
   - text_wac

❌ Missing in Arabic:
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_recalculate_failed
   - heading_title
   - text_average_cost
   - text_fifo
   - text_home
   - text_latest_cost
   - text_lifo
   - text_pagination
   - text_recalculate_success
   - text_standard_cost
   - text_wac

❌ Missing in English:
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_recalculate_failed
   - heading_title
   - text_average_cost
   - text_fifo
   - text_home
   - text_latest_cost
   - text_lifo
   - text_pagination
   - text_recalculate_success
   - text_standard_cost
   - text_wac

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_recalculate_failed = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_average_cost = ""  # TODO: ترجمة عربية
   - text_fifo = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_latest_cost = ""  # TODO: ترجمة عربية
   - text_lifo = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_recalculate_success = ""  # TODO: ترجمة عربية
   - text_standard_cost = ""  # TODO: ترجمة عربية
   - text_wac = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_recalculate_failed = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_average_cost = ""  # TODO: English translation
   - text_fifo = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_latest_cost = ""  # TODO: English translation
   - text_lifo = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_recalculate_success = ""  # TODO: English translation
   - text_standard_cost = ""  # TODO: English translation
   - text_wac = ""  # TODO: English translation
