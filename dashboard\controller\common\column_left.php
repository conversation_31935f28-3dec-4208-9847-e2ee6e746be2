<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP SYSTEM - The Final & Comprehensive Sidebar Controller v4.0 (Reference-Based)
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * @version 4.0.0 Final Build
 * <AUTHOR> Development Team (Synthesized by AI Agent)
 * @copyright 2025 AYM ERP Systems
 * @description هذا الملف هو الهيكل النهائي والموحد للعمود الجانبي، تم بناؤه بالاعتماد الكامل على
 * الهيكل المرجعي المتقدم (column_left5.php) والوثائق الاستراتيجية.
 * إنه يمثل المخطط النهائي لواجهة التنقل لجميع أنظمة AYM ERP المتكاملة.
 * 
 * المبادئ المتبعة في هذا الملف:
 * - بناء القوائم باستخدام مصفوفات متداخلة للحفاظ على الهيكلية.
 * - توثيق شامل باللغة العربية لكل قسم ونظام فرعي.
 * - فلترة نهائية للعناصر بناءً على صلاحيات المستخدم لضمان عدم ظهور قوائم فارغة.
 * - الكود منظم في دوال بناء منفصلة لكل نظام رئيسي لسهولة القراءة والصيانة.
 * - سنعيد صياغتها كليا بس نستقر على كل الشاشات والامكانيات
 ═══════════════════════════════════════════════════════════════════════════════
 */
 
class ControllerCommonColumnLeft extends Controller {
    public function index() {
        // 1) Check user_token and login
        if (!isset($this->request->get['user_token']) || !isset($this->session->data['user_token']) || ($this->request->get['user_token'] != $this->session->data['user_token'])) {
            $this->response->redirect($this->url->link('common/login'));
        }
        
        // 2) Load language files
        $this->load->language('common/column_left');

        // 3) Initialize main menus array
        $data['menus'] = [];

        // 4) Build comprehensive menu in logical order
        $this->buildCoreNavigation($data);

        // Core systems (by priority and dependencies)
        $this->buildAccountingSystem($data);           // First: Accounting (foundation of everything)
        $this->buildInventorySystem($data);            // Second: Inventory (depends on accounting)
        $this->buildPurchasingSystem($data);           // Third: Purchasing (depends on accounting and inventory)
        $this->buildSalesAndCrmSystem($data);          // Fourth: Sales (depends on accounting and inventory)
        $this->buildFinanceSystem($data);             // Fifth: Finance (depends on accounting)

        // Supporting systems
        $this->buildShippingSystem($data);            // Shipping and delivery
        $this->buildWebsiteManagementSystem($data);   // Website and e-commerce management
        $this->buildHrSystem($data);                  // Human resources
        $this->buildProjectManagementSystem($data);   // Project management
        $this->buildDocumentSystem($data);            // Document management
        $this->buildCommunicationSystem($data);       // Communication and notifications
        $this->buildWorkflowSystem($data);            // Visual workflow

        // Advanced systems
        $this->buildAiSystem($data);                  // Artificial intelligence
        $this->buildGovernanceSystem($data);          // Governance and risk
        $this->buildAdvancedMarketingSystem($data);   // Advanced marketing
        $this->buildEtaSystem($data);                 // Egyptian tax system

        // Reports and administration
        $this->buildReportsSystem($data);            // Reports and analytics
        $this->buildSystemAndSettingsMenu($data);    // System settings
        $this->buildSubscriptionMenu($data);         // Subscriptions
        
        // 5) Filter final menu based on user permissions
        $data['menus'] = $this->filterPermittedMenus($data['menus']);

        // 6) Send data to view file
        return $this->load->view('common/column_left', $data);
    }

    /**
     * Build core navigation links, dashboards and quick operations
     */
    private function buildCoreNavigation(&$data) {
        // (A) E-commerce store view (quick link)
        $data['menus'][] = ['id' => 'menu-webstore-link', 'icon' => 'fa-globe', 'name' => $this->language->get('text_show_website'), 'href' => HTTPS_CATALOG, 'target' => '_blank', 'children' => []];

        // (B) Dashboards
        $dashboards = [];
        if ($this->user->hasPermission('access', 'common/dashboard')) { $dashboards[] = ['name' => $this->language->get('text_main_dashboard'), 'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'common/dashboard']; }
        if ($this->user->hasPermission('access', 'dashboard/kpi')) { $dashboards[] = ['name' => $this->language->get('text_kpi_dashboard'), 'href' => $this->url->link('dashboard/kpi', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'dashboard/kpi']; }
        if ($this->user->hasPermission('access', 'dashboard/inventory_analytics')) { $dashboards[] = ['name' => $this->language->get('text_inventory_analytics_dashboard'), 'href' => $this->url->link('dashboard/inventory_analytics', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'dashboard/inventory_analytics']; }
        if (count($dashboards) > 1) {
            $data['menus'][] = ['id' => 'menu-dashboards', 'icon' => 'fa-dashboard', 'name' => $this->language->get('text_dashboards'), 'href' => '', 'children' => $dashboards];
        } elseif (count($dashboards) == 1) {
            $data['menus'][] = ['id' => 'menu-dashboard-main', 'icon' => 'fa-dashboard', 'name' => $this->language->get('text_main_dashboard'), 'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true), 'children' => []];
        }

        // (C) Daily quick operations
        $daily_operations = [];
        $quick_sales = [];
        if ($this->user->hasPermission('modify', 'sale/quote')) { $quick_sales[] = ['name' => $this->language->get('text_quick_add_quote'), 'href' => $this->url->link('sale/quote/add', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'sale/quote']; }
        if ($this->user->hasPermission('modify', 'sale/order')) { $quick_sales[] = ['name' => $this->language->get('text_quick_add_order'), 'href' => $this->url->link('sale/order/add', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'sale/order']; }
        if ($quick_sales) { $daily_operations[] = ['name' => $this->language->get('text_quick_sales_tasks'), 'children' => $quick_sales]; }
        if ($daily_operations) { $data['menus'][] = ['id' => 'menu-daily-operations', 'icon' => 'fa-flash', 'name' => $this->language->get('text_daily_operations'), 'href' => '', 'children' => $daily_operations]; }
    }

	/**
	 * =======================================================================
	 * (1) نظام المبيعات وإدارة علاقات العملاء (Sales & CRM)
	 * =======================================================================
	 * @Objective: إدارة دورة المبيعات كاملة من العميل المحتمل حتى التحصيل وخدمة ما بعد البيع.
	 * @Workflow: Sales Cycle, CRM Cycle, POS Cycle, Installment Cycle, After-Sales Cycle.
	 * @Accounting_Impact: Creates entries for Sales, COGS, AR, VAT, Commissions, and updates Inventory (WAC).
	 */
	private function buildSalesAndCrmSystem(&$data) {
		$sales_crm = [];

		//-----------------------------------------------------
		// (1.1) عمليات المبيعات
		//-----------------------------------------------------
		$sales_children = [];
		// أوامر البيع (order.php) - تسجيل طلبات البيع
		if ($this->user->hasPermission('access', 'sale/order')) {
			$sales_children[] = [
				'name' => $this->language->get('text_sale_order'),
				'href' => $this->url->link('sale/order', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/order.php - تسجيل أوامر البيع
			];
		}
		// مرتجعات المبيعات (return.php) - معالجة المرتجعات
		if ($this->user->hasPermission('access', 'sale/return')) {
			$sales_children[] = [
				'name' => $this->language->get('text_sale_return'),
				'href' => $this->url->link('sale/return', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/return.php - معالجة مرتجعات المبيعات
			];
		}
		// كوبونات (voucher.php) - إدارة الكوبونات
		if ($this->user->hasPermission('access', 'sale/voucher')) {
			$sales_children[] = [
				'name' => $this->language->get('text_sale_voucher'),
				'href' => $this->url->link('sale/voucher', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/voucher.php - إدارة كوبونات المبيعات
			];
		}
		// قوالب الكوبونات (voucher_theme.php) - قوالب الهدايا
		if ($this->user->hasPermission('access', 'sale/voucher_theme')) {
			$sales_children[] = [
				'name' => $this->language->get('text_sale_voucher_theme'),
				'href' => $this->url->link('sale/voucher_theme', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/voucher_theme.php - قوالب كوبونات الهدايا
			];
		}
		// التسعير الديناميكي (dynamic_pricing.php) - تسعير متغير
		if ($this->user->hasPermission('access', 'sale/dynamic_pricing')) {
			$sales_children[] = [
				'name' => $this->language->get('text_dynamic_pricing'),
				'href' => $this->url->link('sale/dynamic_pricing', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/dynamic_pricing.php - تسعير ديناميكي
			];
		}
		// البيع بالتقسيط (installment.php) - إدارة الأقساط
		if ($this->user->hasPermission('access', 'sale/installment')) {
			$sales_children[] = [
				'name' => $this->language->get('text_installment'),
				'href' => $this->url->link('sale/installment', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/installment.php - إدارة البيع بالتقسيط
			];
		}
		// سلات مهجورة وتحليل استرجاع العملاء (abandoned_cart.php)
		if ($this->user->hasPermission('access', 'sale/abandoned_cart')) {
			$sales_children[] = [
				'name' => $this->language->get('text_abandoned_carts'),
				'href' => $this->url->link('sale/abandoned_cart', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/abandoned_cart.php - سلات مهجورة وتحليل استرجاع العملاء
			];
		}
		// خطط التقسيط (installment_plan.php) - خطط العملاء
		if ($this->user->hasPermission('access', 'sale/installment_plan')) {
			$sales_children[] = [
				'name' => $this->language->get('text_installment_plan'),
				'href' => $this->url->link('sale/installment_plan', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/installment_plan.php - خطط تقسيط العملاء
			];
		}
		// معالجة الطلبات (order_processing.php) - معالجة الطلبات
		if ($this->user->hasPermission('access', 'sale/order_processing')) {
			$sales_children[] = [
				'name' => $this->language->get('text_order_processing'),
				'href' => $this->url->link('sale/order_processing', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/order_processing.php - معالجة الطلبات
			];
		}

		//-----------------------------------------------------
		// (1.3) العملاء
		//-----------------------------------------------------
		$customer_children = [];
		// العملاء (customer.php) - إدارة العملاء
		if ($this->user->hasPermission('access', 'customer/customer')) {
			$customer_children[] = [
				'name' => $this->language->get('text_customer'),
				'href' => $this->url->link('customer/customer', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: customer/customer.php - إدارة العملاء
			];
		}
		// مجموعات العملاء (customer_group.php) - تصنيفات العملاء
		if ($this->user->hasPermission('access', 'customer/customer_group')) {
			$customer_children[] = [
				'name' => $this->language->get('text_customer_group'),
				'href' => $this->url->link('customer/customer_group', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: customer/customer_group.php - مجموعات العملاء
			];
		}
		// الحقول المخصصة (custom_field.php) - حقول إضافية
		if ($this->user->hasPermission('access', 'customer/custom_field')) {
			$customer_children[] = [
				'name' => $this->language->get('text_custom_field'),
				'href' => $this->url->link('customer/custom_field', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: customer/custom_field.php - الحقول المخصصة للعملاء
			];
		}
		// تقييم العملاء (feedback.php) - ملاحظات العملاء
		if ($this->user->hasPermission('access', 'customer/feedback')) {
			$customer_children[] = [
				'name' => $this->language->get('text_customer_feedback'),
				'href' => $this->url->link('customer/feedback', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: customer/feedback.php - تقييم العملاء
			];
		}
		// موافقة العملاء (customer_approval.php) - موافقات العملاء
		if ($this->user->hasPermission('access', 'customer/customer_approval')) {
			$customer_children[] = [
				'name' => $this->language->get('text_customer_approval'),
				'href' => $this->url->link('customer/customer_approval', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: customer/customer_approval.php - موافقات العملاء
			];
		}
		// الولاء (loyalty.php) - برنامج الولاء
		if ($this->user->hasPermission('access', 'customer/loyalty')) {
			$customer_children[] = [
				'name' => $this->language->get('text_customer_loyalty'),
				'href' => $this->url->link('customer/loyalty', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: customer/loyalty.php - برنامج الولاء للعملاء
			];
		}

		//-----------------------------------------------------
		// (1.4) CRM
		//-----------------------------------------------------
		$crm_children = [];
		// العملاء المحتملون (lead.php) - إدارة العملاء المحتملين
		if ($this->user->hasPermission('access', 'crm/lead')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_lead'),
				'href' => $this->url->link('crm/lead', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/lead.php - إدارة العملاء المحتملين
			];
		}
		// الصفقات (deal.php) - إدارة الصفقات
		if ($this->user->hasPermission('access', 'crm/deal')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_deal'),
				'href' => $this->url->link('crm/deal', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/deal.php - إدارة الصفقات
			];
		}
		// جهات الاتصال (contact.php) - إدارة جهات الاتصال
		if ($this->user->hasPermission('access', 'crm/contact')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_contact'),
				'href' => $this->url->link('crm/contact', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/contact.php - إدارة جهات الاتصال
			];
		}
		// الحملات (campaign.php) - الحملات التسويقية
		if ($this->user->hasPermission('access', 'crm/campaign')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_campaign'),
				'href' => $this->url->link('crm/campaign', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/campaign.php - الحملات التسويقية
			];
		}
		// التحليلات (analytics.php) - تحليلات CRM
		if ($this->user->hasPermission('access', 'crm/analytics')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_analytics'),
				'href' => $this->url->link('crm/analytics', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/analytics.php - تحليلات CRM
			];
		}
		// الأنشطة (activity.php) - أنشطة العملاء
		if ($this->user->hasPermission('access', 'crm/activity')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_activity'),
				'href' => $this->url->link('crm/activity', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/activity.php - أنشطة العملاء
			];
		}
		// توقعات المبيعات (sales_forecast.php) - توقعات المبيعات
		if ($this->user->hasPermission('access', 'crm/sales_forecast')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_sales_forecast'),
				'href' => $this->url->link('crm/sales_forecast', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/sales_forecast.php - توقعات المبيعات
			];
		}
		// تقييم العملاء المحتملين (lead_scoring.php) - تقييم العملاء المحتملين
		if ($this->user->hasPermission('access', 'crm/lead_scoring')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_lead_scoring'),
				'href' => $this->url->link('crm/lead_scoring', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/lead_scoring.php - تقييم العملاء المحتملين
			];
		}
		// رحلة العميل (customer_journey.php) - تتبع رحلة العميل
		if ($this->user->hasPermission('access', 'crm/customer_journey')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_customer_journey'),
				'href' => $this->url->link('crm/customer_journey', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/customer_journey.php - تتبع رحلة العميل
			];
		}
		// الفرص (opportunity.php) - إدارة الفرص
		if ($this->user->hasPermission('access', 'crm/opportunity')) {
			$crm_children[] = [
				'name' => $this->language->get('text_crm_opportunity'),
				'href' => $this->url->link('crm/opportunity', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: crm/opportunity.php - إدارة الفرص
			];
		}

		//-----------------------------------------------------
		// (1.5) خدمات ما بعد البيع
		//-----------------------------------------------------
		$after_sales_children = [];
		// Placeholder: الضمان (warranty.php) - غير منفذة فعليًا
		// $after_sales_children[] = [
		//     'name' => $this->language->get('text_warranty'),
		//     'href' => '#',
		//     'comment' => 'Placeholder - غير منفذة فعليًا'
		// ];
		// Placeholder: الصيانة (maintenance.php) - غير منفذة فعليًا
		// $after_sales_children[] = [
		//     'name' => $this->language->get('text_maintenance'),
		//     'href' => '#',
		//     'comment' => 'Placeholder - غير منفذة فعليًا'
		// ];
		// Placeholder: عقود الخدمة (service_contracts.php) - غير منفذة فعليًا
		// $after_sales_children[] = [
		//     'name' => $this->language->get('text_service_contracts'),
		//     'href' => '#',
		//     'comment' => 'Placeholder - غير منفذة فعليًا'
		// ];

		//-----------------------------------------------------
		// (1.6) تحليلات وتقارير المبيعات
		//-----------------------------------------------------
		$sales_analytics_children = [];
		// تحليلات المبيعات (sales_analytics.php) - تحليلات متقدمة
		if ($this->user->hasPermission('access', 'sale/sales_analytics')) {
			$sales_analytics_children[] = [
				'name' => $this->language->get('text_sales_analytics'),
				'href' => $this->url->link('sale/sales_analytics', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/sales_analytics.php - تحليلات المبيعات
			];
		}
		// أهداف المبيعات (sales_target.php) - تتبع الأهداف
		if ($this->user->hasPermission('access', 'sale/sales_target')) {
			$sales_analytics_children[] = [
				'name' => $this->language->get('text_sales_target'),
				'href' => $this->url->link('sale/sales_target', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/sales_target.php - أهداف المبيعات
			];
		}
		// عمولات المبيعات (sales_commission.php) - إدارة العمولات
		if ($this->user->hasPermission('access', 'sale/sales_commission')) {
			$sales_analytics_children[] = [
				'name' => $this->language->get('text_sales_commission'),
				'href' => $this->url->link('sale/sales_commission', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/sales_commission.php - إدارة عمولات المبيعات
			];
		}
		// توقعات المبيعات (sales_forecast.php) - توقعات مستقبلية
		if ($this->user->hasPermission('access', 'sale/sales_forecast')) {
			$sales_analytics_children[] = [
				'name' => $this->language->get('text_sales_forecast'),
				'href' => $this->url->link('sale/sales_forecast', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: sale/sales_forecast.php - توقعات المبيعات
			];
		}

		//-----------------------------------------------------
		// (1.7) إعدادات المبيعات
		//-----------------------------------------------------
		$sales_settings_children = [];
		// Placeholder: إعدادات المبيعات (sales_settings.php) - غير منفذة فعليًا
		// $sales_settings_children[] = [
		//     'name' => $this->language->get('text_sales_settings'),
		//     'href' => '#',
		//     'comment' => 'Placeholder - غير منفذة فعليًا'
		// ];

		//-----------------------------------------------------
		// (1.1) نقاط البيع (POS) - العمود الفقري للبيع المباشر
		//-----------------------------------------------------
		$pos_children = [];

		// نقطة البيع الرئيسية (pos.php) - الأعقد في POS
		if ($this->user->hasPermission('access', 'pos/pos')) {
			$pos_children[] = [
				'name' => $this->language->get('text_pos_main'),
				'href' => $this->url->link('pos/pos', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: pos/pos.php - واجهة البيع التفاعلية (الأعقد في النظام)
			];
		}

		// تسليم واستلام الكاشير (cashier_handover.php)
		if ($this->user->hasPermission('access', 'pos/cashier_handover')) {
			$pos_children[] = [
				'name' => $this->language->get('text_pos_cashier_handover'),
				'href' => $this->url->link('pos/cashier_handover', 'user_token=' . $this->session->data['user_token'], true)
			];
		}

		// المناوبات (shift.php)
		if ($this->user->hasPermission('access', 'pos/shift')) {
			$pos_children[] = [
				'name' => $this->language->get('text_pos_shift'),
				'href' => $this->url->link('pos/shift', 'user_token=' . $this->session->data['user_token'], true)
			];
		}

		// الأجهزة (terminal.php)
		if ($this->user->hasPermission('access', 'pos/terminal')) {
			$pos_children[] = [
				'name' => $this->language->get('text_pos_terminal'),
				'href' => $this->url->link('pos/terminal', 'user_token=' . $this->session->data['user_token'], true)
			];
		}

		// التقارير (reports.php)
		if ($this->user->hasPermission('access', 'pos/reports')) {
			$pos_children[] = [
				'name' => $this->language->get('text_pos_reports'),
				'href' => $this->url->link('pos/reports', 'user_token=' . $this->session->data['user_token'], true)
			];
		}

		// الإعدادات (settings.php)
		if ($this->user->hasPermission('access', 'pos/settings')) {
			$pos_children[] = [
				'name' => $this->language->get('text_pos_settings'),
				'href' => $this->url->link('pos/settings', 'user_token=' . $this->session->data['user_token'], true)
			];
		}

		if ($pos_children) {
			$sales_crm[] = ['name' => $this->language->get('text_pos_management_section'), 'children' => $pos_children];
		}
		if ($sales_children) {
			$sales_crm[] = ['name' => $this->language->get('text_sales_operations_section'), 'children' => $sales_children];
		}
		if ($customer_children) {
			$sales_crm[] = ['name' => $this->language->get('text_customer_management_section'), 'children' => $customer_children];
		}
		if ($crm_children) {
			$sales_crm[] = ['name' => $this->language->get('text_crm_section'), 'children' => $crm_children];
		}
		if ($after_sales_children) {
			$sales_crm[] = ['name' => $this->language->get('text_after_sales_section'), 'children' => $after_sales_children];
		}
		if ($sales_analytics_children) {
			$sales_crm[] = ['name' => $this->language->get('text_sales_analytics_section'), 'children' => $sales_analytics_children];
		}
		if ($sales_settings_children) {
			$sales_crm[] = ['name' => $this->language->get('text_sales_pricing_settings_section'), 'children' => $sales_settings_children];
		}

		// إضافة القائمة الرئيسية للمبيعات إذا كانت تحتوي على عناصر
		if ($sales_crm) {
			$data['menus'][] = ['id' => 'menu-sales-crm', 'icon' => 'fa-shopping-cart', 'name' => $this->language->get('text_sales_and_crm'), 'href' => '', 'children' => $sales_crm];
		}
	}


	/**
	 * =======================================================================
	 * (2) نظام المشتريات والموردين (Purchasing & Suppliers)
	 * =======================================================================
	 * @Objective: إدارة دورة الشراء من الطلب الداخلي حتى الدفع للمورد وتقييم الموردين وإدارة تكاليف الاستيراد.
	 * @Workflow: Requisition -> RFQ -> PO -> Goods Receipt -> Supplier Invoice -> Payment.
	 * @Accounting_Impact: Creates entries for Purchases (as an intermediary account), AP, VAT, and updates Inventory (WAC) including landed costs.
	 */
	private function buildPurchasingSystem(&$data) {
		$purchasing = [];

		//-----------------------------------------------------
		// (2.1) قسم دورة الشراء الأساسية (Core Purchase Cycle)
		//-----------------------------------------------------
		$purchase_cycle = [];

		/**
		 * @Screen: طلبات الشراء الداخلية (Purchase Requisitions)
		 * @Users: جميع الأقسام, مدير المشتريات
		 * @Objective: تمكين الأقسام المختلفة من طلب احتياجاتها من المواد أو الخدمات بشكل رسمي وموثق.
		 * @Workflow: إنشاء طلب -> موافقات (حسب سير العمل) -> تحويل إلى أمر شراء.
		 */
		if ($this->user->hasPermission('access', 'purchase/requisition')) {
			$purchase_cycle[] = ['name' => $this->language->get('text_purchase_requisitions'), 'href' => $this->url->link('purchase/requisition', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'purchase/requisition'];
		}

		/**
		 * @Screen: أوامر الشراء (Purchase Orders - PO)
		 * @Users: فريق المشتريات, المدير المالي
		 * @Objective: إصدار أوامر شراء رسمية للموردين بالمنتجات والكميات والأسعار المتفق عليها.
		 * @Workflow: إنشاء من طلب شراء/عرض سعر -> إرسال للمورد -> تتبع الاستلام -> ربط بالفاتورة.
		 * @Accounting: لا يوجد قيد مباشر، لكنه يمثل التزامًا بالشراء.
		 */
		if ($this->user->hasPermission('access', 'purchase/order')) {
			$purchase_cycle[] = ['name' => $this->language->get('text_purchase_orders'), 'href' => $this->url->link('purchase/order', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'purchase/order'];
		}

		/**
		 * @Screen: إيصالات استلام البضائع (Goods Receipt Notes - GRN)
		 * @Users: أمناء المخازن, فريق فحص الجودة
		 * @Objective: توثيق استلام البضائع فعلياً في المستودعات ومطابقتها مع أمر الشراء.
		 * @Workflow: استلام فعلي -> تسجيل الكميات المستلمة -> فحص جودة (إن لزم) -> تحديث المخزون.
		 * @Accounting: من ح/المخزون (مدين) إلى ح/مشتريات تحت التسليم (دائن). تحديث كمية وتكلفة WAC للصنف.
		 */
		if ($this->user->hasPermission('access', 'purchase/goods_receipt')) {
			$purchase_cycle[] = ['name' => $this->language->get('text_goods_receipt_notes'), 'href' => $this->url->link('purchase/goods_receipt', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'purchase/goods_receipt'];
		}

		/**
		 * @Screen: فواتير الموردين (Supplier Invoices)
		 * @Users: المحاسبين, مدير المشتريات
		 * @Objective: تسجيل فواتير الموردين ومطابقتها مع أوامر الشراء والاستلامات قبل اعتمادها للدفع.
		 * @Workflow: استلام فاتورة -> مطابقة ثلاثية (PO, GRN) -> معالجة الفروقات -> اعتماد الدفع.
		 * @Accounting: من ح/مشتريات تحت التسليم (مدين) ومن ح/ضريبة القيمة المضافة (مدين) إلى ح/الموردين (دائن).
		 */
		if ($this->user->hasPermission('access', 'purchase/supplier_invoice')) {
			$purchase_cycle[] = ['name' => $this->language->get('text_supplier_invoices'), 'href' => $this->url->link('purchase/supplier_invoice', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'purchase/supplier_invoice'];
		}

		/**
		 * @Screen: مرتجعات المشتريات (Purchase Returns)
		 * @Users: أمناء المخازن, فريق المشتريات
		 * @Objective: إدارة عمليات إرجاع البضائع للموردين لأسباب مختلفة (تلف، عدم مطابقة).
		 * @Workflow: إنشاء طلب إرجاع -> موافقة -> تجهيز الشحنة للمورد -> استلام إشعار دائن.
		 * @Accounting: قيد عكسي للمشتريات والمخزون. من ح/الموردين (مدين) إلى ح/المخزون (دائن) بتكلفة WAC.
		 */
		if ($this->user->hasPermission('access', 'purchase/return')) {
			$purchase_cycle[] = ['name' => $this->language->get('text_purchase_returns'), 'href' => $this->url->link('purchase/purchase_return', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'purchase/purchase_return'];
		}
		
		if ($purchase_cycle) {
			$purchasing[] = ['name' => $this->language->get('text_purchase_cycle_section'), 'children' => $purchase_cycle];
		}
		
		//-----------------------------------------------------
		// (2.2) قسم إدارة الموردين (Supplier Management)
		//-----------------------------------------------------
		$supplier_mgmt = [];

		/**
		 * @Screen: الموردين (Suppliers)
		 * @Users: فريق المشتريات, المحاسبة
		 * @Objective: إدارة قاعدة بيانات الموردين، بما في ذلك بيانات الاتصال والشروط المالية والوثائق.
		 */
		if ($this->user->hasPermission('access', 'supplier/supplier')) {
			$supplier_mgmt[] = ['name' => $this->language->get('text_suppliers'), 'href' => $this->url->link('supplier/supplier', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'supplier/supplier'];
		}

		/**
		 * @Screen: تقييم الموردين (Supplier Evaluation)
		 * @Users: مدير المشتريات, مدير الجودة
		 * @Objective: تقييم أداء الموردين بناءً على معايير محددة (جودة، التزام بالمواعيد، سعر).
		 * @AI_Integration: تحليل بيانات الأداء واقتراح تقييمات تلقائية.
		 */
		if ($this->user->hasPermission('access', 'supplier/evaluation')) {
			$supplier_mgmt[] = ['name' => $this->language->get('text_supplier_evaluation'), 'href' => $this->url->link('supplier/evaluation', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'supplier/evaluation'];
		}

		/**
		 * @Screen: حسابات الموردين (Supplier Accounts)
		 * @Users: المحاسبين
		 * @Objective: عرض كشوف حسابات الموردين ومتابعة أرصدتهم وأعمار الديون.
		 */
		if ($this->user->hasPermission('access', 'supplier/account')) {
			$supplier_mgmt[] = ['name' => $this->language->get('text_supplier_accounts_ledger'), 'href' => $this->url->link('supplier/accounts', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'supplier/accounts'];
		}
		
		if ($supplier_mgmt) {
			$purchasing[] = ['name' => $this->language->get('text_supplier_management_section'), 'children' => $supplier_mgmt];
		}

		//-----------------------------------------------------
		// (2.3) قسم الاستيراد والتكاليف الإضافية (Import & Landed Costs)
		//-----------------------------------------------------
		$import_landed_costs = [];

		/**
		 * @Screen: إدارة شحنات الاستيراد (Import Shipments)
		 * @Users: مدير المشتريات, مدير اللوجستيات
		 * @Objective: تتبع شحنات الاستيراد من لحظة شحنها حتى وصولها للمستودعات.
		 */
		if ($this->user->hasPermission('access', 'import/shipment')) {
			$import_landed_costs[] = ['name' => $this->language->get('text_import_shipment_management'), 'href' => $this->url->link('import/shipment', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'import/shipment'];
		}

		/**
		 * @Screen: توزيع تكاليف الاستيراد (Landed Cost Allocation)
		 * @Users: المحاسبين, المدير المالي
		 * @Objective: تسجيل التكاليف الإضافية (شحن، جمارك، تخليص) وتوزيعها على تكلفة المنتجات المستوردة.
		 * @Workflow: تسجيل فواتير المصاريف -> ربطها بشحنة الاستيراد -> توزيعها على الأصناف (بالكمية/بالقيمة).
		 * @Accounting: من ح/المخزون (مدين) إلى ح/مصاريف الشحن والجمارك (دائن). يؤدي إلى زيادة تكلفة WAC.
		 */
		if ($this->user->hasPermission('modify', 'import/allocation')) {
			$import_landed_costs[] = ['name' => $this->language->get('text_allocate_landed_costs'), 'href' => $this->url->link('import/allocation', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'import/allocation'];
		}

		if ($import_landed_costs) {
			$purchasing[] = ['name' => $this->language->get('text_import_landed_costs_section'), 'children' => $import_landed_costs];
		}
		
		//-----------------------------------------------------
		// (2.4) شاشات متقدمة وتحليلات وتقارير المشتريات
		//-----------------------------------------------------
		$purchase_advanced = [];
		// عروض أسعار الموردين (quotation.php)
		if ($this->user->hasPermission('access', 'purchase/quotation')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_quotations'),
				'href' => $this->url->link('purchase/quotation', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/quotation'
			];
		}
		// مقارنة عروض الأسعار (quotation_comparison.php)
		if ($this->user->hasPermission('access', 'purchase/quotation_comparison')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_quotation_comparison'),
				'href' => $this->url->link('purchase/quotation_comparison', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/quotation_comparison'
			];
		}
		// تتبع الطلبات (order_tracking.php)
		if ($this->user->hasPermission('access', 'purchase/order_tracking')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_order_tracking'),
				'href' => $this->url->link('purchase/order_tracking', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/order_tracking'
			];
		}
		// تخطيط المشتريات (planning.php)
		if ($this->user->hasPermission('access', 'purchase/planning')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_purchase_planning'),
				'href' => $this->url->link('purchase/planning', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/planning'
			];
		}
		// تحليلات المشتريات (purchase_analytics.php)
		if ($this->user->hasPermission('access', 'purchase/purchase_analytics')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_purchase_analytics'),
				'href' => $this->url->link('purchase/purchase_analytics', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/purchase_analytics'
			];
		}
		// تحليلات الموردين المتقدمة (supplier_analytics_advanced.php)
		if ($this->user->hasPermission('access', 'purchase/supplier_analytics_advanced')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_analytics_advanced'),
				'href' => $this->url->link('purchase/supplier_analytics_advanced', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/supplier_analytics_advanced'
			];
		}
		// إدارة الجودة المتقدمة (quality_check.php)
		if ($this->user->hasPermission('access', 'purchase/quality_check')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_quality_check'),
				'href' => $this->url->link('purchase/quality_check', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/quality_check'
			];
		}
		// إدارة التكاليف المتقدمة (cost_management_advanced.php)
		if ($this->user->hasPermission('access', 'purchase/cost_management_advanced')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_cost_management_advanced'),
				'href' => $this->url->link('purchase/cost_management_advanced', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/cost_management_advanced'
			];
		}
		
		/**
		 * @Screen: عقود الموردين (Supplier Contracts)
		 * @Users: مدير المشتريات, المدير القانوني
		 * @Objective: إدارة عقود الشراء طويلة المدى مع الموردين وتتبع شروطها ومواعيدها.
		 * @Workflow: إنشاء عقد -> موافقات -> توقيع -> متابعة تنفيذ -> تجديد/إنهاء.
		 */
		if ($this->user->hasPermission('access', 'purchase/supplier_contracts')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_contracts'),
				'href' => $this->url->link('purchase/supplier_contracts', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/supplier_contracts'
			];
		}
		
		/**
		 * @Screen: مدفوعات الموردين (Supplier Payments)
		 * @Users: المحاسبين, المدير المالي
		 * @Objective: إدارة مدفوعات الموردين وتتبع سجل المدفوعات والمدفوعات المعلقة.
		 * @Workflow: استلام فاتورة -> مطابقة -> اعتماد دفع -> إصدار شيك/تحويل -> تسجيل الدفع.
		 * @Accounting: من ح/الموردين (مدين) إلى ح/البنك/النقدية (دائن).
		 */
		if ($this->user->hasPermission('access', 'purchase/supplier_payments')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_payments'),
				'href' => $this->url->link('purchase/supplier_payments', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/supplier_payments'
			];
		}
		
		/**
		 * @Screen: إعدادات الموافقات (Approval Settings)
		 * @Users: مدير النظام, مدير المشتريات
		 * @Objective: إعداد سير العمل للموافقات على المشتريات حسب القيم والأنواع.
		 * @Workflow: تحديد مستويات الموافقة -> تعيين الموظفين -> إعداد القواعد -> تفعيل.
		 */
		if ($this->user->hasPermission('access', 'purchase/approval_settings')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_approval_settings'),
				'href' => $this->url->link('purchase/approval_settings', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/approval_settings'
			];
		}
		
		/**
		 * @Screen: إعدادات الإشعارات (Notification Settings)
		 * @Users: مدير النظام, مدير المشتريات
		 * @Objective: إعداد نظام الإشعارات للمشتريات (تأخير طلبات، وصول بضائع، فواتير معلقة).
		 * @Workflow: تحديد أنواع الإشعارات -> تعيين المستلمين -> إعداد التوقيت -> تفعيل.
		 */
		if ($this->user->hasPermission('access', 'purchase/notification_settings')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_notification_settings'),
				'href' => $this->url->link('purchase/notification_settings', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/notification_settings'
			];
		}
		// تكامل محاسبي متقدم (accounting_integration_advanced.php)
		if ($this->user->hasPermission('access', 'purchase/accounting_integration_advanced')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_accounting_integration_advanced'),
				'href' => $this->url->link('purchase/accounting_integration_advanced', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/accounting_integration_advanced'
			];
		}
		// نظام الموافقات الذكي (smart_approval_system.php)
		if ($this->user->hasPermission('access', 'purchase/smart_approval_system')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_smart_approval_system'),
				'href' => $this->url->link('purchase/smart_approval_system', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/smart_approval_system'
			];
		}
		// إعدادات الموافقات (approval_settings.php)
		if ($this->user->hasPermission('access', 'purchase/approval_settings')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_approval_settings'),
				'href' => $this->url->link('purchase/approval_settings', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/approval_settings'
			];
		}
		// إعدادات الإشعارات (notification_settings.php)
		if ($this->user->hasPermission('access', 'purchase/notification_settings')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_notification_settings'),
				'href' => $this->url->link('purchase/notification_settings', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/notification_settings'
			];
		}
		// إعدادات المشتريات (settings.php)
		if ($this->user->hasPermission('access', 'purchase/settings')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_purchase_settings'),
				'href' => $this->url->link('purchase/settings', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/settings'
			];
		}
		// شاشة رئيسية (purchase.php)
		if ($this->user->hasPermission('access', 'purchase/purchase')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_purchase_main'),
				'href' => $this->url->link('purchase/purchase', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/purchase'
			];
		}
		// شاشة Excel للفواتير (supplier_invoice_excel.php)
		if ($this->user->hasPermission('access', 'purchase/supplier_invoice_excel')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_invoice_excel'),
				'href' => $this->url->link('purchase/supplier_invoice_excel', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/supplier_invoice_excel'
			];
		}
		// شاشة PDF للفواتير (supplier_invoice_pdf.php)
		if ($this->user->hasPermission('access', 'purchase/supplier_invoice_pdf')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_invoice_pdf'),
				'href' => $this->url->link('purchase/supplier_invoice_pdf', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/supplier_invoice_pdf'
			];
		}
		// مدفوعات الموردين (supplier_payments.php)
		if ($this->user->hasPermission('access', 'purchase/supplier_payments')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_payments'),
				'href' => $this->url->link('purchase/supplier_payments', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/supplier_payments'
			];
		}
		// عقود الموردين (supplier_contracts.php)
		if ($this->user->hasPermission('access', 'purchase/supplier_contracts')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_supplier_contracts'),
				'href' => $this->url->link('purchase/supplier_contracts', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/supplier_contracts'
			];
		}
		// شاشة مرتجعات المشتريات (purchase_return.php)
		if ($this->user->hasPermission('access', 'purchase/purchase_return')) {
			$purchase_advanced[] = [
				'name' => $this->language->get('text_purchase_return'),
				'href' => $this->url->link('purchase/purchase_return', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'purchase/purchase_return'
			];
		}
		if ($purchase_advanced) {
			$purchasing[] = ['name' => $this->language->get('text_purchase_advanced_section'), 'children' => $purchase_advanced];
		}

		//-----------------------------------------------------
		// (2.5) إدارة الموردين (جميع شاشات supplier/)
		//-----------------------------------------------------
		$supplier_mgmt_full = [];
		// الموردين
		if ($this->user->hasPermission('access', 'supplier/supplier')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_suppliers'),
				'href' => $this->url->link('supplier/supplier', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/supplier'
			];
		}
		// مجموعات الموردين
		if ($this->user->hasPermission('access', 'supplier/supplier_group')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_supplier_groups'),
				'href' => $this->url->link('supplier/supplier_group', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/supplier_group'
			];
		}
		// تقييم الموردين
		if ($this->user->hasPermission('access', 'supplier/evaluation')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_supplier_evaluation'),
				'href' => $this->url->link('supplier/evaluation', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/evaluation'
			];
		}
		// حسابات الموردين
		if ($this->user->hasPermission('access', 'supplier/accounts')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_supplier_accounts'),
				'href' => $this->url->link('supplier/accounts', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/accounts'
			];
		}
		// اتفاقيات الأسعار
		if ($this->user->hasPermission('access', 'supplier/price_agreement')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_supplier_price_agreement'),
				'href' => $this->url->link('supplier/price_agreement', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/price_agreement'
			];
		}
		// تحليل الأداء
		if ($this->user->hasPermission('access', 'supplier/performance')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_supplier_performance'),
				'href' => $this->url->link('supplier/performance', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/performance'
			];
		}
		// مستندات الموردين
		if ($this->user->hasPermission('access', 'supplier/documents')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_supplier_documents'),
				'href' => $this->url->link('supplier/documents', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/documents'
			];
		}
		// التواصل مع الموردين
		if ($this->user->hasPermission('access', 'supplier/communication')) {
			$supplier_mgmt_full[] = [
				'name' => $this->language->get('text_supplier_communication'),
				'href' => $this->url->link('supplier/communication', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'supplier/communication'
			];
		}
		if ($supplier_mgmt_full) {
			$purchasing[] = ['name' => $this->language->get('text_supplier_management_full_section'), 'children' => $supplier_mgmt_full];
		}
		
		if ($purchasing) {
			$data['menus'][] = ['id' => 'menu-purchasing', 'icon' => 'fa-truck', 'name' => $this->language->get('text_purchasing_and_suppliers'), 'href' => '', 'children' => $purchasing];
		}
	}

	/**
	 * =======================================================================
	 * (3) نظام المخزون والمستودعات (Inventory & Warehouse)
	 * =======================================================================
	 * @Objective: الإدارة الشاملة للأصناف، الكميات، التكاليف (WAC)، الجرد، التحويلات، والفروع المتعددة.
	 * @Workflow: Item Lifecycle, Receiving/Issuing, Stock Count/Adjustment, Transfers, WAC Calculation.
	 * @Accounting_Impact: This module is the core of cost management. All inventory movements (purchase, sale, adjustment, transfer) generate journal entries affecting Inventory, COGS, and other related accounts based on the Weighted Average Cost (WAC).
	 */
	 
	private function buildInventorySystem(&$data) {
		$inventory_warehouse = [];

		//-----------------------------------------------------
		// (3.1) قسم نظرة عامة وتقارير المخزون (Inventory Overview & Reports)
		//-----------------------------------------------------
		$inv_overview = [];
		

		/**
		 * @Screen: لوحة معلومات المخزون (Inventory Dashboard)
		 * @Users: مدير المخازن, مدير العمليات, الإدارة العليا
		 * @Objective: عرض مؤشرات الأداء الرئيسية للمخزون مثل معدل الدوران، قيمة المخزون، المنتجات بطيئة الحركة، والتنبيهات.
		 */
		if ($this->user->hasPermission('access', 'inventory/dashboard')) {
			$inv_overview[] = ['name' => $this->language->get('text_inventory_dashboard'), 'href' => $this->url->link('inventory/dashboard', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/dashboard'];
		}

		/**
		 * @Screen: استعلام الأرصدة الحالية (Current Stock Levels)
		 * @Users: أمناء المخازن, فريق المبيعات, مدير المشتريات
		 * @Objective: عرض الأرصدة الفعلية والمتاحة لكل صنف في كل فرع ومستودع.
		 */
		if ($this->user->hasPermission('access', 'inventory/current_stock')) {
			$inv_overview[] = ['name' => $this->language->get('text_current_stock_levels'), 'href' => $this->url->link('inventory/stock_levels', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/stock_levels'];
		}

		/**
		 * @Screen: سجل حركة المخزون (Stock Movement Ledger)
		 * @Users: المحاسبين, مراجعو الحسابات, مدير المخازن
		 * @Objective: تتبع تفصيلي لكل الحركات التي تمت على صنف معين (بيع، شراء، تحويل، تسوية).
		 * @Accounting: يوفر أساسًا لمراجعة قيود المخزون وتكلفة البضاعة المباعة.
		 */
		if ($this->user->hasPermission('access', 'inventory/movement_history')) {
			$inv_overview[] = ['name' => $this->language->get('text_stock_movement_ledger'), 'href' => $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/movement_history'];
		}

		/**
		 * @Screen: تقرير تقييم المخزون (Inventory Valuation Report)
		 * @Users: المدير المالي, المحاسبين
		 * @Objective: عرض القيمة المالية الإجمالية للمخزون بناءً على التكلفة المتوسطة المرجحة (WAC)، وهو تقرير أساسي للميزانية العمومية.
		 * @Accounting: القيمة النهائية في هذا التقرير يجب أن تطابق رصيد حساب المخزون في دليل الحسابات.
		 */
		if ($this->user->hasPermission('access', 'report/inventory_valuation')) {
			$inv_overview[] = ['name' => $this->language->get('text_inventory_valuation_report'), 'href' => $this->url->link('report/inventory_valuation', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'report/inventory_valuation'];
		}
		
		// تنبيهات إعادة الطلب التنبؤية وتحليلات المخزون الذكية (inventory_analytics.php)
		if ($this->user->hasPermission('access', 'dashboard/inventory_analytics')) {
			$inv_overview[] = ['name' => $this->language->get('text_predictive_reorder_alerts'), 'href' => $this->url->link('dashboard/inventory_analytics', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'dashboard/inventory_analytics'];
		}
		if (!empty($inv_overview)) {
			$data['menus'][] = ['id' => 'menu-inventory-overview', 'icon' => 'fa-eye', 'name' => $this->language->get('text_inventory_overview'), 'href' => '', 'children' => $inv_overview];
		}

		// إضافة إدارة المستودعات
		$warehouse_mgmt = [];
		if ($this->user->hasPermission('access', 'inventory/warehouse')) {
			$warehouse_mgmt[] = ['name' => $this->language->get('text_warehouse_management'), 'href' => $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/warehouse'];
		}
		if ($this->user->hasPermission('access', 'inventory/location_management')) {
			$warehouse_mgmt[] = ['name' => $this->language->get('text_location_management'), 'href' => $this->url->link('inventory/location_management', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/location_management'];
		}
		
		if ($inv_overview) {
			$inventory_warehouse[] = ['name' => $this->language->get('text_inventory_overview_reports_section'), 'children' => $inv_overview];
		}
		
		//-----------------------------------------------------
		// (3.2) قسم عمليات المخزون (Inventory Operations)
		//-----------------------------------------------------
		$inv_operations = [];

		/**
		 * @Screen: الجرد المخزني (Stock Counting / Physical Inventory)
		 * @Users: فرق الجرد, أمناء المخازن, مدير المخازن
		 * @Objective: إدارة وتنفيذ عمليات الجرد الدورية والمفاجئة لمطابقة الرصيد الفعلي مع الرصيد الدفتري.
		 * @Workflow: إنشاء جرد -> طباعة قوائم -> تسجيل الكميات الفعلية -> مراجعة الفروقات -> تطبيق التسويات.
		 * @Accounting: الفروقات (عجز أو زيادة) تؤدي إلى إنشاء قيود تسوية تلقائية.
		 */
		if ($this->user->hasPermission('access', 'inventory/stock_count')) {
			$inv_operations[] = ['name' => $this->language->get('text_stock_counting'), 'href' => $this->url->link('inventory/stock_count', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/stock_count'];
		}

		/**
		 * @Screen: تسويات المخزون (Stock Adjustments)
		 * @Users: مدير المخازن, المحاسبين
		 * @Objective: معالجة الزيادة أو النقص في المخزون بشكل يدوي لأسباب محددة (تلف، عينات، هدايا).
		 * @Accounting: من/إلى ح/المخزون (حسب نوع التسوية) ومن/إلى ح/تسويات المخزون.
		 */
		if ($this->user->hasPermission('access', 'inventory/adjustment')) {
			$inv_operations[] = ['name' => $this->language->get('text_stock_adjustments'), 'href' => $this->url->link('inventory/adjustment', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/adjustment'];
		}

		/**
		 * @Screen: التحويلات المخزنية (Stock Transfers)
		 * @Users: أمناء المخازن في الفروع المختلفة
		 * @Objective: إدارة نقل البضائع بين الفروع والمستودعات بشكل موثق.
		 * @Workflow: طلب تحويل -> موافقة الفرع المستلم -> إرسال البضاعة -> تأكيد الاستلام.
		 * @Accounting: من ح/المخزون-فرع مستلم (مدين) إلى ح/المخزون-فرع مرسل (دائن) بنفس قيمة WAC.
		 */
		if ($this->user->hasPermission('access', 'inventory/transfer')) {
			$inv_operations[] = ['name' => $this->language->get('text_stock_transfers'), 'href' => $this->url->link('inventory/transfer', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/transfer'];
		}
		
		if ($inv_operations) {
			$inventory_warehouse[] = ['name' => $this->language->get('text_inventory_operations_section'), 'children' => $inv_operations];
		}

		//-----------------------------------------------------
		// (3.3) قسم إدارة التكاليف والتتبع المتقدم (Costing & Advanced Tracking)
		//-----------------------------------------------------
		$cost_tracking = [];

		/**
		 * @Screen: سجل تكلفة الصنف (Item Cost History - WAC)
		 * @Users: المدير المالي, محلل التكاليف
		 * @Objective: تتبع التغيرات التاريخية في التكلفة المتوسطة المرجحة لكل صنف، مما يساعد في تحليل التكاليف.
		 */
		if ($this->user->hasPermission('access', 'inventory/cost_history')) {
			$cost_tracking[] = ['name' => $this->language->get('text_item_cost_history_wac'), 'href' => $this->url->link('inventory/cost_history', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/cost_history'];
		}

		/**
		 * @Screen: تتبع تاريخ الصلاحية (Expiry Date Tracking)
		 * @Users: أمناء المخازن, مدير الجودة
		 * @Objective: إدارة ومتابعة المنتجات حسب تاريخ انتهاء الصلاحية لتجنب الخسائر.
		 * @AI_Integration: التنبؤ بالمنتجات التي قد لا تباع قبل تاريخ انتهاء صلاحيتها.
		 */
		if ($this->user->hasPermission('access', 'inventory/expiry_tracking')) {
			$cost_tracking[] = ['name' => $this->language->get('text_expiry_date_tracking'), 'href' => $this->url->link('inventory/expiry_tracking', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/expiry_tracking'];
		}

		/**
		 * @Screen: تتبع التشغيلات/الدفعات (Batch/Lot Tracking)
		 * @Users: مدير الجودة, مدير المخازن
		 * @Objective: تتبع المنتجات حسب رقم التشغيلة، وهو أمر حيوي لعمليات الاستدعاء (Recall) ومراقبة الجودة.
		 */
		if ($this->user->hasPermission('access', 'inventory/batch_tracking')) {
			$cost_tracking[] = ['name' => $this->language->get('text_batch_lot_tracking'), 'href' => $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/batch_tracking'];
		}

		/**
		 * @Screen: تتبع الأرقام التسلسلية (Serial Number Tracking)
		 * @Users: فريق المبيعات, فريق الضمان
		 * @Objective: تتبع كل قطعة من منتج على حدة عبر رقمها التسلسلي، وهو ضروري للمنتجات الإلكترونية والأجهزة.
		 */
		if ($this->user->hasPermission('access', 'inventory/serial_tracking')) {
			$cost_tracking[] = ['name' => $this->language->get('text_serial_number_tracking'), 'href' => $this->url->link('inventory/serial_tracking', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/serial_tracking'];
		}
		
		if ($cost_tracking) {
			$inventory_warehouse[] = ['name' => $this->language->get('text_costing_tracking_section'), 'children' => $cost_tracking];
		}
		
		//-----------------------------------------------------
		// (3.4) قسم تخطيط وتحليل المخزون (Inventory Planning & Analysis)
		//-----------------------------------------------------
		$inv_planning = [];

		/**
		 * @Screen: تحليل ABC (ABC Analysis)
		 * @Users: مدير العمليات, مدير المشتريات
		 * @Objective: تصنيف الأصناف إلى فئات (A, B, C) بناءً على قيمتها أو حركتها لتحديد أولويات الإدارة.
		 */
		if ($this->user->hasPermission('access', 'inventory/abc_analysis')) {
			$inv_planning[] = ['name' => $this->language->get('text_abc_analysis'), 'href' => $this->url->link('inventory/abc_analysis', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/abc_analysis'];
		}
		
		/**
		 * @Screen: إدارة نقاط إعادة الطلب (Reorder Point Management)
		 * @Users: مدير المشتريات, مدير المخازن
		 * @Objective: تحديد الحد الأدنى والأقصى ونقطة إعادة الطلب لكل صنف لأتمتة اقتراحات الشراء.
		 * @AI_Integration: اقتراح نقاط إعادة طلب ديناميكية بناءً على التنبؤ بالطلب ومواسم البيع.
		 */
		if ($this->user->hasPermission('modify', 'inventory/reorder_points')) {
			$inv_planning[] = ['name' => $this->language->get('text_reorder_point_management'), 'href' => $this->url->link('inventory/reorder_points', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/reorder_points'];
		}
		
		if ($inv_planning) {
			$inventory_warehouse[] = ['name' => $this->language->get('text_inventory_planning_analysis_section'), 'children' => $inv_planning];
		}
		
		//-----------------------------------------------------
		// (3.5) قسم إعدادات المخزون (Inventory Settings)
		//-----------------------------------------------------
		$inv_settings = [];

		/**
		 * @Screen: ربط حسابات المخزون (Inventory Account Mapping)
		 * @Users: المدير المالي, محاسب التكاليف
		 * @Objective: تحديد حسابات الأستاذ العام التي تتأثر بعمليات المخزون المختلفة (شراء، بيع، تسوية) لضمان التكامل المحاسبي الصحيح.
		 */
		if ($this->user->hasPermission('modify', 'inventory/account_mapping')) {
			$inv_settings[] = ['name' => $this->language->get('text_inventory_account_mapping'), 'href' => $this->url->link('inventory/account_mapping', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'inventory/account_mapping'];
		}
		
		if ($inv_settings) {
			$inventory_warehouse[] = ['name' => $this->language->get('text_inventory_warehouse_settings_section'), 'children' => $inv_settings];
		}

		//-----------------------------------------------------
		// (3.6) قسم الشاشات المفقودة من tree.txt - يجب إضافتها
		//-----------------------------------------------------
		$missing_inventory_screens = [];

		// تحليل ABC (abc_analysis.php) - تصنيف المنتجات حسب الأهمية
		if ($this->user->hasPermission('access', 'inventory/abc_analysis')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_abc_analysis'),
				'href' => $this->url->link('inventory/abc_analysis', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/abc_analysis.php - تحليل ABC للمنتجات (A: عالية القيمة، B: متوسطة، C: منخفضة)
			];
		}

		// إدارة الباركود (barcode.php) - الباركود الأساسي
		if ($this->user->hasPermission('access', 'inventory/barcode')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_barcode_basic'),
				'href' => $this->url->link('inventory/barcode', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/barcode.php - إدارة الباركود الأساسي للمنتجات
			];
		}

		// إدارة الباركود المتقدمة (barcode_management.php) - إدارة شاملة
		if ($this->user->hasPermission('access', 'inventory/barcode_management')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_barcode_management'),
				'href' => $this->url->link('inventory/barcode_management', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/barcode_management.php - إدارة الباركود المتقدمة (إنشاء، طباعة، تتبع)
			];
		}

		// طباعة الباركود (barcode_print.php) - طباعة ملصقات
		if ($this->user->hasPermission('access', 'inventory/barcode_print')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_barcode_print'),
				'href' => $this->url->link('inventory/barcode_print', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/barcode_print.php - طباعة ملصقات الباركود بأحجام مختلفة
			];
		}

		// تتبع الدفعات (batch_tracking.php) - تتبع انتهاء الصلاحية
		if ($this->user->hasPermission('access', 'inventory/batch_tracking')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_batch_tracking'),
				'href' => $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/batch_tracking.php - تتبع الدفعات وانتهاء الصلاحية (FEFO: First Expired, First Out)
			];
		}

		// فئات المخزون (category.php) - تصنيف المنتجات للمخزون
		if ($this->user->hasPermission('access', 'inventory/category')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_category'),
				'href' => $this->url->link('inventory/category', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/category.php - تصنيف المنتجات لأغراض المخزون (مختلف عن catalog/category.php)
			];
		}

		// استلام البضائع (goods_receipt.php) - استلام من المشتريات
		if ($this->user->hasPermission('access', 'inventory/goods_receipt')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_goods_receipt'),
				'href' => $this->url->link('inventory/goods_receipt', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/goods_receipt.php - استلام البضائع في المخزون من المشتريات
			];
		}

		// لوحة المعلومات التفاعلية (interactive_dashboard.php) - لوحة متقدمة
		if ($this->user->hasPermission('access', 'inventory/interactive_dashboard')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_interactive_dashboard'),
				'href' => $this->url->link('inventory/interactive_dashboard', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/interactive_dashboard.php - لوحة معلومات تفاعلية متقدمة للمخزون
			];
		}

		// إدارة المخزون العامة (inventory.php) - الشاشة العامة
		if ($this->user->hasPermission('access', 'inventory/inventory')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_general'),
				'href' => $this->url->link('inventory/inventory', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/inventory.php - إدارة المخزون العامة (نظرة شاملة)
			];
		}

		// إدارة المخزون المتقدمة (inventory_management_advanced.php) - ميزات متقدمة
		if ($this->user->hasPermission('access', 'inventory/inventory_management_advanced')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_management_advanced'),
				'href' => $this->url->link('inventory/inventory_management_advanced', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/inventory_management_advanced.php - إدارة المخزون المتقدمة مع ميزات AI
			];
		}

		// تقييم المخزون (inventory_valuation.php) - طرق التقييم المختلفة
		if ($this->user->hasPermission('access', 'inventory/inventory_valuation')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_valuation'),
				'href' => $this->url->link('inventory/inventory_valuation', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/inventory_valuation.php - تقييم المخزون بطرق مختلفة (FIFO, LIFO, WAC, Standard, Latest, Average)
			];
		}

		// الشركات المصنعة (manufacturer.php) - إدارة المصنعين للمخزون
		if ($this->user->hasPermission('access', 'inventory/manufacturer')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_manufacturer'),
				'href' => $this->url->link('inventory/manufacturer', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/manufacturer.php - إدارة الشركات المصنعة لأغراض المخزون
			];
		}

		// إدارة المنتجات (product.php) - منتجات المخزون
		if ($this->user->hasPermission('access', 'inventory/product')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_product'),
				'href' => $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/product.php - إدارة المنتجات من ناحية المخزون (مختلف عن catalog/product.php)
			];
		}

		// إدارة المنتجات المتقدمة (product_management.php) - إدارة شاملة
		if ($this->user->hasPermission('access', 'inventory/product_management')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_product_management'),
				'href' => $this->url->link('inventory/product_management', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/product_management.php - إدارة المنتجات المتقدمة للمخزون
			];
		}

		// أوامر الشراء (purchase_order.php) - أوامر شراء المخزون
		if ($this->user->hasPermission('access', 'inventory/purchase_order')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_purchase_order'),
				'href' => $this->url->link('inventory/purchase_order', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/purchase_order.php - أوامر الشراء من ناحية المخزون
			];
		}

		// جرد المخزون (stocktake.php) - عمليات الجرد
		if ($this->user->hasPermission('access', 'inventory/stocktake')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_stocktake'),
				'href' => $this->url->link('inventory/stocktake', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/stocktake.php - عمليات جرد المخزون الدورية
			];
		}

		// عد المخزون (stock_count.php) - عد فعلي
		if ($this->user->hasPermission('access', 'inventory/stock_count')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_stock_count'),
				'href' => $this->url->link('inventory/stock_count', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/stock_count.php - عد المخزون الفعلي
			];
		}

		// عد المخزون المتقدم (stock_counting.php) - عد متقدم
		if ($this->user->hasPermission('access', 'inventory/stock_counting')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_stock_counting_advanced'),
				'href' => $this->url->link('inventory/stock_counting', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/stock_counting.php - عد المخزون المتقدم مع تقنيات حديثة
			];
		}

		// الوحدات (units.php) - وحدات القياس
		if ($this->user->hasPermission('access', 'inventory/units')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_inventory_units'),
				'href' => $this->url->link('inventory/units', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/units.php - إدارة وحدات القياس للمخزون
			];
		}

		// إدارة الوحدات (unit_management.php) - إدارة متقدمة للوحدات
		if ($this->user->hasPermission('access', 'inventory/unit_management')) {
			$missing_inventory_screens[] = [
				'name' => $this->language->get('text_unit_management'),
				'href' => $this->url->link('inventory/unit_management', 'user_token=' . $this->session->data['user_token'], true)
				// ملف: inventory/unit_management.php - إدارة الوحدات المتقدمة مع التحويلات
			];
		}

		if ($missing_inventory_screens) {
			$inventory_warehouse[] = ['name' => $this->language->get('text_missing_inventory_screens_section'), 'children' => $missing_inventory_screens];
		}

		if ($inventory_warehouse) {
			$data['menus'][] = ['id' => 'menu-inventory-warehouse', 'icon' => 'fa-archive', 'name' => $this->language->get('text_inventory_and_warehouse'), 'href' => '', 'children' => $inventory_warehouse];
		}
	}
		
	/**
	 * =======================================================================
	 * (5) نظام المحاسبة والمالية (Accounting & Finance)
	 * =======================================================================
	 * @Objective: إدارة جميع العمليات المحاسبية والمالية، بما في ذلك دفتر الأستاذ، الذمم، النقدية، الأصول، والموازنات.
	 * @Workflow: Journal Entry Cycle, AR/AP Cycle, Bank Reconciliation, Period Closing Cycle.
	 * @Accounting_Impact: This is the destination for all financial transactions generated by other modules. It ensures compliance and produces key financial statements.
	 */
	private function buildAccountingSystem(&$data) {
		$finance_accounting = [];

		//-----------------------------------------------------
		// (5.1) قسم المحاسبة الأساسية (Core Accounting)
		//-----------------------------------------------------
		$core_accounting = [];

		/**
		 * @Screen: دليل الحسابات (Chart of Accounts - CoA)
		 * @Users: المدير المالي, رئيس الحسابات
		 * @Objective: بناء وتخصيص الهيكل الشجري للحسابات الذي تستند إليه جميع العمليات المالية في النظام.
		 */
		if ($this->user->hasPermission('access', 'accounts/chart_account')) {
			$core_accounting[] = ['name' => $this->language->get('text_chart_of_accounts'), 'href' => $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/chartaccount'];
		}

		/**
		 * @Screen: قيود اليومية (Journal Entries / Vouchers)
		 * @Users: المحاسبون
		 * @Objective: تسجيل القيود المحاسبية اليدوية التي لا يتم إنشاؤها تلقائيًا من الوحدات الأخرى (مثل قيود التسوية والإهلاكات اليدوية).
		 */
		if ($this->user->hasPermission('access', 'accounts/journal')) {
			$core_accounting[] = ['name' => $this->language->get('text_journal_entries'), 'href' => $this->url->link('accounts/journal', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/journal'];
		}

		/**
		 * @Screen: كشوف الحسابات (Account Statements / Ledger)
		 * @Users: المحاسبون, المراجعون
		 * @Objective: عرض دفتر الأستاذ التفصيلي لأي حساب، وتتبع جميع الحركات المدينة والدائنة التي تمت عليه.
		 */
		if ($this->user->hasPermission('access', 'accounts/statement_account')) {
			$core_accounting[] = ['name' => $this->language->get('text_account_statements'), 'href' => $this->url->link('accounts/statement_account', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/statement_account'];
		}

		/**
		 * @Screen: إغلاق الفترة المحاسبية (Period Closing)
		 * @Users: رئيس الحسابات, المدير المالي
		 * @Objective: تنفيذ إجراءات إغلاق الفترة المالية (شهرية أو سنوية) وترحيل الأرباح والخسائر.
		 * @Workflow: مراجعة القيود -> إقفال الحسابات المؤقتة -> ترحيل الأرباح -> إصدار التقارير الختامية.
		 */
		if ($this->user->hasPermission('modify', 'accounts/period_closing')) {
			$core_accounting[] = ['name' => $this->language->get('text_accounting_period_closing'), 'href' => $this->url->link('accounts/period_closing', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/period_closing'];
		}
		
		if ($core_accounting) {
			$finance_accounting[] = ['name' => $this->language->get('text_core_accounting_section'), 'children' => $core_accounting];
		}
		
		//-----------------------------------------------------
		// (5.2) قسم الذمم (Receivables & Payables)
		//-----------------------------------------------------
		$receivables_payables = [];

		/**
		 * @Screen: حسابات العملاء (AR Ledger)
		 * @Users: محاسب العملاء, فريق التحصيل
		 * @Objective: متابعة أرصدة العملاء، أعمار الديون، وإدارة عمليات التحصيل.
		 */
		if ($this->user->hasPermission('access', 'customer/account_ledger')) {
			$receivables_payables[] = ['name' => $this->language->get('text_customer_accounts_ar_ledger'), 'href' => $this->url->link('customer/account_ledger', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'customer/account_ledger'];
		}

		/**
		 * @Screen: سندات القبض (Receipt Vouchers)
		 * @Users: أمين الصندوق, المحاسب
		 * @Objective: تسجيل المقبوضات النقدية أو الشيكات من العملاء أو أي مصادر أخرى.
		 * @Accounting: من ح/النقدية أو البنك (مدين) إلى ح/العملاء (دائن).
		 */
		if ($this->user->hasPermission('access', 'finance/receipt_voucher')) {
			$receivables_payables[] = ['name' => $this->language->get('text_receipt_vouchers'), 'href' => $this->url->link('finance/receipt_voucher', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/receipt_voucher'];
		}

		/**
		 * @Screen: سندات الصرف (Payment Vouchers)
		 * @Users: أمين الصندوق, المحاسب
		 * @Objective: تسجيل المدفوعات النقدية أو الشيكات للموردين أو للمصروفات المختلفة.
		 * @Accounting: من ح/الموردين أو المصروفات (مدين) إلى ح/النقدية أو البنك (دائن).
		 */
		if ($this->user->hasPermission('access', 'finance/payment_voucher')) {
			$receivables_payables[] = ['name' => $this->language->get('text_payment_vouchers'), 'href' => $this->url->link('finance/payment_voucher', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/payment_voucher'];
		}

		if ($receivables_payables) {
			$finance_accounting[] = ['name' => $this->language->get('text_receivables_payables_section'), 'children' => $receivables_payables];
		}

		//-----------------------------------------------------
		// (5.3) قسم النقدية والبنوك (Cash & Bank Management)
		//-----------------------------------------------------
		$cash_bank = [];

		/**
		 * @Screen: التسوية البنكية (Bank Reconciliation)
		 * @Users: المحاسبون
		 * @Objective: مطابقة كشف حساب البنك مع سجلات النظام لكشف وتسوية أي فروقات.
		 * @Workflow: استيراد كشف البنك -> مطابقة آلية ويدوية -> تسجيل الفروقات (مصاريف، فوائد).
		 * @AI_Integration: مطابقة ذكية للمعاملات واقتراح تسويات للفروقات.
		 */
		if ($this->user->hasPermission('access', 'finance/bank_reconciliation')) {
			$cash_bank[] = ['name' => $this->language->get('text_bank_reconciliation_screen'), 'href' => $this->url->link('finance/bank_reconciliation', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/bank_reconciliation'];
		}

		/**
		 * @Screen: إدارة الشيكات (Checks Management)
		 * @Users: المحاسبون
		 * @Objective: تتبع دورة حياة الشيكات الواردة والصادرة (تحت التحصيل، تم التحصيل، مرتجع، تم السداد).
		 * @Accounting: إنشاء قيود وسيطة مثل ح/شيكات تحت التحصيل و ح/شيكات مؤجلة الدفع.
		 */
		if ($this->user->hasPermission('access', 'finance/checks')) {
			$cash_bank[] = ['name' => $this->language->get('text_checks_management'), 'href' => $this->url->link('finance/checks', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/checks'];
		}

		if ($cash_bank) {
			$finance_accounting[] = ['name' => $this->language->get('text_cash_bank_gateways_section'), 'children' => $cash_bank];
		}
		
		//-----------------------------------------------------
		// (5.4) قسم الأصول الثابتة (Fixed Assets)
		//-----------------------------------------------------
		$fixed_assets = [];
		
		/**
		 * @Screen: سجل الأصول الثابتة (Fixed Assets Registry)
		 * @Users: محاسب الأصول
		 * @Objective: تسجيل وإدارة جميع الأصول الثابتة للشركة.
		 * @Accounting: قيد شراء الأصل: من ح/الأصول (مدين) إلى ح/الموردين أو النقدية (دائن).
		 */
		if ($this->user->hasPermission('access', 'accounts/fixed_assets')) {
			$fixed_assets[] = ['name' => $this->language->get('text_fixed_assets_registry'), 'href' => $this->url->link('accounts/fixed_assets', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/fixed_assets'];
		}

		/**
		 * @Screen: حساب وتسجيل الإهلاك (Depreciation Posting)
		 * @Users: محاسب الأصول
		 * @Objective: حساب الإهلاك الشهري أو السنوي للأصول وتسجيل القيود المحاسبية تلقائياً.
		 * @Accounting: قيد الإهلاك: من ح/مصروف إهلاك (مدين) إلى ح/مجمع إهلاك (دائن).
		 */
		if ($this->user->hasPermission('modify', 'accounts/depreciation')) {
			$fixed_assets[] = ['name' => $this->language->get('text_depreciation_calculation_posting'), 'href' => $this->url->link('accounts/depreciation', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/depreciation'];
		}
		
		if ($fixed_assets) {
			$finance_accounting[] = ['name' => $this->language->get('text_fixed_assets_section'), 'children' => $fixed_assets];
		}
		
		//-----------------------------------------------------
		// (5.5) قسم الموازنات والتخطيط المالي (Budgeting & Planning)
		//-----------------------------------------------------
		$budgeting = [];
		
		/**
		 * @Screen: إعداد الموازنات (Budget Setup)
		 * @Users: المدير المالي, مديرو الأقسام
		 * @Objective: إعداد الموازنات التقديرية للإيرادات والمصروفات للفترات القادمة.
		 */
		if ($this->user->hasPermission('modify', 'accounts/budget')) {
			$budgeting[] = ['name' => $this->language->get('text_budget_setup_management'), 'href' => $this->url->link('accounts/budget', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/budget'];
		}
		
		/**
		 * @Screen: متابعة الموازنة (Budget Monitoring)
		 * @Users: المدير المالي, الإدارة العليا
		 * @Objective: مقارنة الأداء الفعلي مع الموازنة التقديرية وتحليل الانحرافات.
		 */
		if ($this->user->hasPermission('access', 'accounts/budget_monitoring')) {
			$budgeting[] = ['name' => $this->language->get('text_budget_monitoring_variance'), 'href' => $this->url->link('accounts/budget_monitoring', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/budget_monitoring'];
		}

		if ($budgeting) {
			$finance_accounting[] = ['name' => $this->language->get('text_budgeting_planning_section'), 'children' => $budgeting];
		}

		//-----------------------------------------------------
		// (5.6) قسم التقارير المالية والضريبية (Financial & Tax Reports)
		//-----------------------------------------------------
		$fin_tax_reports = [];

		/**
		 * @Screen: التقارير المالية الرئيسية (P&L, Balance Sheet, Cash Flow)
		 * @Users: الإدارة العليا, المدير المالي, المستثمرون
		 * @Objective: توليد القوائم المالية الأساسية التي تعكس أداء الشركة ومركزها المالي.
		 */
		if ($this->user->hasPermission('access', 'accounts/income_statement')) {
			$fin_tax_reports[] = ['name' => $this->language->get('text_income_statement'), 'href' => $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/income_statement'];
		}
		if ($this->user->hasPermission('access', 'accounts/balance_sheet')) {
			$fin_tax_reports[] = ['name' => $this->language->get('text_balance_sheet'), 'href' => $this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/balance_sheet'];
		}
		if ($this->user->hasPermission('access', 'accounts/cash_flow')) {
			$fin_tax_reports[] = ['name' => $this->language->get('text_cash_flow_statement'), 'href' => $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/cash_flow'];
		}

		/**
		 * @Screen: تقرير ضريبة القيمة المضافة (VAT Report)
		 * @Users: المحاسبون, المدير المالي
		 * @Objective: تجهيز تقرير ضريبة القيمة المضافة (ضريبة المدخلات والمخرجات) لتقديمه للجهات الضريبية.
		 */
		if ($this->user->hasPermission('access', 'accounts/vat_report')) {
			$fin_tax_reports[] = ['name' => $this->language->get('text_vat_report'), 'href' => $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/vat_report'];
		}
		
		if ($fin_tax_reports) {
			$finance_accounting[] = ['name' => $this->language->get('text_financial_tax_reports_section'), 'children' => $fin_tax_reports];
		}

		if ($finance_accounting) {
			$data['menus'][] = ['id' => 'menu-finance-accounting', 'icon' => 'fa-money-check-alt', 'name' => $this->language->get('text_accounting_and_finance'), 'href' => '', 'children' => $finance_accounting];
		}
	}

	/**
	 * =======================================================================
	 * (5.5) نظام المالية والخزينة (Finance & Treasury)
	 * =======================================================================
	 * @Objective: إدارة السيولة النقدية، البنوك، الاستثمارات، والتمويل.
	 * @Workflow: Cash Management, Bank Operations, Investment Management, Financing.
	 * @Accounting_Impact: Creates entries for cash flows, bank transactions, investments, and financing activities.
	 */
	private function buildFinanceSystem(&$data) {
		$finance_treasury = [];

		//-----------------------------------------------------
		// (5.5.1) قسم إدارة البنوك (Bank Management)
		//-----------------------------------------------------
		$bank_management = [];

		/**
		 * @Screen: إدارة البنوك (Bank Management)
		 * @Users: المدير المالي, أمين الصندوق
		 * @Objective: إدارة حسابات البنوك وتتبع الأرصدة والحركات البنكية.
		 */
		if ($this->user->hasPermission('access', 'finance/bank')) {
			$bank_management[] = ['name' => $this->language->get('text_bank_management'), 'href' => $this->url->link('finance/bank', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/bank'];
		}

		/**
		 * @Screen: مطابقة البنوك (Bank Reconciliation)
		 * @Users: المحاسبون
		 * @Objective: مطابقة كشوف البنوك مع سجلات النظام.
		 */
		if ($this->user->hasPermission('access', 'finance/bank_reconciliation')) {
			$bank_management[] = ['name' => $this->language->get('text_bank_reconciliation'), 'href' => $this->url->link('finance/bank_reconciliation', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/bank_reconciliation'];
		}

		if ($bank_management) {
			$finance_treasury[] = ['name' => $this->language->get('text_bank_management_section'), 'children' => $bank_management];
		}

		//-----------------------------------------------------
		// (5.5.2) قسم إدارة النقدية (Cash Management)
		//-----------------------------------------------------
		$cash_management = [];

		/**
		 * @Screen: إدارة النقدية (Cash Management)
		 * @Users: أمين الصندوق
		 * @Objective: إدارة الصناديق النقدية وتتبع الحركات النقدية.
		 */
		if ($this->user->hasPermission('access', 'finance/cash')) {
			$cash_management[] = ['name' => $this->language->get('text_cash_management'), 'href' => $this->url->link('finance/cash', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/cash'];
		}

		/**
		 * @Screen: النقدية والبنوك (Cash & Bank)
		 * @Users: المدير المالي
		 * @Objective: عرض موحد للسيولة النقدية والبنكية.
		 */
		if ($this->user->hasPermission('access', 'finance/cash_bank')) {
			$cash_management[] = ['name' => $this->language->get('text_cash_bank_overview'), 'href' => $this->url->link('finance/cash_bank', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/cash_bank'];
		}

		if ($cash_management) {
			$finance_treasury[] = ['name' => $this->language->get('text_cash_management_section'), 'children' => $cash_management];
		}

		//-----------------------------------------------------
		// (5.5.3) قسم إدارة الشيكات (Checks Management)
		//-----------------------------------------------------
		$checks_management = [];

		/**
		 * @Screen: إدارة الشيكات (Checks Management)
		 * @Users: المحاسبون, أمين الصندوق
		 * @Objective: تتبع دورة حياة الشيكات الواردة والصادرة.
		 */
		if ($this->user->hasPermission('access', 'finance/checks')) {
			$checks_management[] = ['name' => $this->language->get('text_checks_management'), 'href' => $this->url->link('finance/checks', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/checks'];
		}

		if ($checks_management) {
			$finance_treasury[] = ['name' => $this->language->get('text_checks_management_section'), 'children' => $checks_management];
		}

		//-----------------------------------------------------
		// (5.5.4) قسم المحافظ الإلكترونية (E-Wallets)
		//-----------------------------------------------------
		$ewallet_management = [];

		/**
		 * @Screen: المحافظ الإلكترونية (E-Wallets)
		 * @Users: المدير المالي, أمين الصندوق
		 * @Objective: إدارة المحافظ الإلكترونية والمدفوعات الرقمية.
		 */
		if ($this->user->hasPermission('access', 'finance/ewallet')) {
			$ewallet_management[] = ['name' => $this->language->get('text_ewallet_management'), 'href' => $this->url->link('finance/ewallet', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/ewallet'];
		}

		if ($ewallet_management) {
			$finance_treasury[] = ['name' => $this->language->get('text_ewallet_management_section'), 'children' => $ewallet_management];
		}

		//-----------------------------------------------------
		// (5.5.5) قسم السندات المالية (Financial Vouchers)
		//-----------------------------------------------------
		$financial_vouchers = [];

		/**
		 * @Screen: سندات القبض (Receipt Vouchers)
		 * @Users: أمين الصندوق, المحاسب
		 * @Objective: تسجيل المقبوضات النقدية من العملاء أو الإيرادات الأخرى.
		 */
		if ($this->user->hasPermission('access', 'finance/receipt_voucher')) {
			$financial_vouchers[] = ['name' => $this->language->get('text_receipt_vouchers'), 'href' => $this->url->link('finance/receipt_voucher', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/receipt_voucher'];
		}

		/**
		 * @Screen: سندات الصرف (Payment Vouchers)
		 * @Users: أمين الصندوق, المحاسب
		 * @Objective: تسجيل المدفوعات النقدية للموردين أو المصروفات.
		 */
		if ($this->user->hasPermission('access', 'finance/payment_voucher')) {
			$financial_vouchers[] = ['name' => $this->language->get('text_payment_vouchers'), 'href' => $this->url->link('finance/payment_voucher', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'finance/payment_voucher'];
		}

		if ($financial_vouchers) {
			$finance_treasury[] = ['name' => $this->language->get('text_financial_vouchers_section'), 'children' => $financial_vouchers];
		}

		if ($finance_treasury) {
			$data['menus'][] = ['id' => 'menu-finance-treasury', 'icon' => 'fa-university', 'name' => $this->language->get('text_finance_treasury'), 'href' => '', 'children' => $finance_treasury];
		}
	}

	/**
	 * =======================================================================
	 * (6) نظام الفوترة الإلكترونية (مصر) - (E-Invoicing ETA)
	 * =======================================================================
	 * @Objective: إنشاء وإرسال الفواتير والإشعارات الإلكترونية لهيئة الضرائب المصرية (ETA) ومتابعة حالتها والامتثال للمتطلبات.
	 * @Workflow: Create Sales Invoice -> Send to ETA -> Monitor Status (Valid, Invalid) -> Handle Notifications from ETA.
	 * @Accounting_Impact: This module ensures that standard sales invoice accounting entries are formatted and transmitted correctly to the tax authority.
	 */
	private function buildEtaSystem(&$data) {
		$eta_module = [];

		/**
		 * @Screen: لوحة مراقبة الامتثال (ETA Compliance Dashboard)
		 * @Users: المحاسبون, المدير المالي, مسؤولو الامتثال الضريبي
		 * @Objective: عرض ملخص لحالة الفواتير المرسلة (صحيحة، غير صحيحة، مرفوضة) ومراقبة حالة الاتصال بمنظومة الضرائب.
		 */
		if ($this->user->hasPermission('access', 'eta/compliance_dashboard')) {
			$eta_module[] = [
				'name' => $this->language->get('text_eta_compliance_dashboard'),
				'href' => $this->url->link('eta/compliance_dashboard', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'eta/compliance_dashboard',
				'children' => [] // Direct link
			];
		}
		
		//-----------------------------------------------------
		// (6.1) قسم المستندات الإلكترونية (E-Documents)
		//-----------------------------------------------------
		$eta_documents = [];

		/**
		 * @Screen: الفواتير الإلكترونية (E-Invoices)
		 * @Users: المحاسبون
		 * @Objective: إدارة الفواتير الإلكترونية المرسلة، تتبع حالتها، وإعادة إرسال الفواتير المرفوضة بعد تصحيحها.
		 * @Workflow: الفواتير التي تم إنشاؤها في نظام المبيعات تظهر هنا -> إرسال إلى ETA -> تحديث الحالة بناءً على رد المنظومة.
		 */
		if ($this->user->hasPermission('access', 'eta/invoices')) {
			$eta_documents[] = ['name' => $this->language->get('text_eta_einvoices'), 'href' => $this->url->link('eta/invoices', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'eta/invoices'];
		}

		/**
		 * @Screen: الإشعارات الإلكترونية (Credit/Debit Notes)
		 * @Users: المحاسبون
		 * @Objective: إنشاء وإرسال إشعارات الخصم والإضافة الإلكترونية إلى منظومة الضرائب، وربطها بالفواتير الأصلية.
		 */
		if ($this->user->hasPermission('access', 'eta/notices')) {
			$eta_documents[] = ['name' => $this->language->get('text_eta_credit_debit_notes'), 'href' => $this->url->link('eta/notices', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'eta/notices'];
		}

		/**
		 * @Screen: الإيصالات الإلكترونية (E-Receipts)
		 * @Users: المحاسبون, الكاشير
		 * @Objective: إدارة وإرسال الإيصالات الإلكترونية الخاصة بنقاط البيع (B2C) إلى منظومة الضرائب.
		 */
		if ($this->user->hasPermission('access', 'eta/receipts')) {
			$eta_documents[] = ['name' => $this->language->get('text_eta_ereceipts'), 'href' => $this->url->link('eta/receipts', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'eta/receipts'];
		}
		
		if ($eta_documents) {
			$eta_module[] = ['name' => $this->language->get('text_eta_edocuments_section'), 'children' => $eta_documents];
		}

		//-----------------------------------------------------
		// (6.2) قسم إعدادات الفوترة الإلكترونية (E-Invoicing Settings)
		//-----------------------------------------------------
		$eta_settings = [];

		/**
		 * @Screen: أكواد الأصناف (GS1 / EGS Codes)
		 * @Users: مدير المنتجات, مدير النظام
		 * @Objective: إدارة وربط أكواد المنتجات والخدمات المستخدمة في النظام مع الأكواد المعتمدة من مصلحة الضرائب (GS1 أو EGS).
		 */
		if ($this->user->hasPermission('modify', 'eta/codes')) {
			$eta_settings[] = ['name' => $this->language->get('text_eta_item_codes_gs1_egs'), 'href' => $this->url->link('eta/codes', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'eta/codes'];
		}
		
		/**
		 * @Screen: إعدادات الربط مع ETA (Connection Settings)
		 * @Users: مدير النظام
		 * @Objective: إدخال وتخزين بيانات الربط الآمن مع منظومة الضرائب (Client ID, Client Secret, Token URL).
		 */
		if ($this->user->hasPermission('modify', 'eta/connection_settings')) {
			$eta_settings[] = ['name' => $this->language->get('text_eta_connection_settings'), 'href' => $this->url->link('eta/connection_settings', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'eta/connection_settings'];
		}

		if ($eta_settings) {
			$eta_module[] = ['name' => $this->language->get('text_eta_settings_section'), 'children' => $eta_settings];
		}

		if ($eta_module) {
			$data['menus'][] = ['id' => 'menu-eta-einvoicing', 'icon' => 'fa-file-signature', 'name' => $this->language->get('text_eta_einvoicing'), 'href' => '', 'children' => $eta_module];
		}
	}

	/**
	 * =======================================================================
	 * (7) نظام إدارة الموقع والمتجر الإلكتروني (Website & E-commerce)
	 * =======================================================================
	 * @Objective: إدارة كل ما يتعلق بواجهة المتجر الإلكتروني: المنتجات (البيانات التسويقية)، التصنيفات، المحتوى، التصميم، العروض، والتسويق الرقمي.
	 * @Note: This module manages the *presentation* layer. Core data like stock and costs are managed in the Inventory module, and orders are managed in the Sales module.
	 * @Workflow: Product Display Management, Content Management, Promotions Management, SEO Management.
	 */
	private function buildWebsiteManagementSystem(&$data) {
		$website_ecommerce = [];

		//-----------------------------------------------------
		// (7.1) قسم كتالوج المتجر (Store Catalog)
		//-----------------------------------------------------
		$store_catalog = [];

		/**
		 * @Screen: تصنيفات المتجر (Store Categories)
		 * @Users: مدير المتجر, مدير التسويق
		 * @Objective: تنظيم المنتجات في تصنيفات هرمية لعرضها بشكل منطقي وسهل التصفح في واجهة المتجر.
		 */
		if ($this->user->hasPermission('access', 'catalog/category')) {
			$store_catalog[] = ['name' => $this->language->get('text_store_categories'), 'href' => $this->url->link('catalog/category', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/category'];
		}

		/**
		 * @Screen: وحدات المنتجات (Product Units)
		 * @Users: مدير المخازن
		 * @Objective: إدارة وحدات قياس المنتجات (كجم، علب، قطع) والتحويل بينها
		 */
		if ($this->user->hasPermission('access', 'catalog/units')) {
			$store_catalog[] = ['name' => $this->language->get('text_product_units'), 'href' => $this->url->link('catalog/units', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/units'];
		}

		// تم حذف منتجات تجريبية - غير موجودة فعلياً

		/**
		 * @Screen: منتجات المتجر (Store Products)
		 * @Users: مدير المتجر, مدير المحتوى
		 * @Objective: إدارة البيانات الوصفية والتسويقية للمنتجات (الصور، الوصف، الكلمات المفتاحية) التي تظهر للعملاء.
		 * @Note: هذا القسم لا يدير الكميات أو التكاليف، بل فقط كيفية عرض المنتج.
		 */
		if ($this->user->hasPermission('access', 'catalog/product')) {
			$store_catalog[] = ['name' => $this->language->get('text_store_products_management'), 'href' => $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/product'];
		}

		/**
		 * @Screen: تقييمات المنتجات (Product Reviews)
		 * @Users: مدير المتجر, خدمة العملاء
		 * @Objective: مراجعة واعتماد أو رفض تقييمات العملاء على المنتجات.
		 */
		if ($this->user->hasPermission('access', 'catalog/review')) {
			$store_catalog[] = ['name' => $this->language->get('text_product_reviews'), 'href' => $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/review'];
		}

		if ($store_catalog) {
			$website_ecommerce[] = ['name' => $this->language->get('text_store_catalog_section'), 'children' => $store_catalog];
		}

		//-----------------------------------------------------
		// (7.2) قسم التجربة الذكية للمنتجات (AI Product Experience)
		//-----------------------------------------------------
		$ai_product_experience = [];

		// تم حذف توليد فيديو المنتج - غير موجود فعلياً

		// تم حذف توليد المحتوى بالذكاء الاصطناعي - غير موجود فعلياً

		/**
		 * @Screen: توليد محتوى صوتي آلي للمنتجات (AI Product Podcasts)
		 * @Users: مدير المحتوى, مدير التسويق
		 * @Objective: إنشاء محتوى صوتي آلي للمنتجات لتحسين تجربة العميل وتوفير معلومات صوتية.
		 * @AI_Integration: Text-to-Speech متقدم + تحسين الصوت
		 * @Use_Cases: وصف المنتج صوتي، نصائح الاستخدام، معلومات تقنية
		 */
		if ($this->user->hasPermission('access', 'catalog/product_podcast')) {
			$ai_product_experience[] = ['name' => $this->language->get('text_ai_product_podcast'), 'href' => $this->url->link('catalog/product_podcast', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/product_podcast'];
		}

		/**
		 * @Screen: الواقع المعزز للمنتجات (AR Product Viewer)
		 * @Users: مدير المتجر, مدير المحتوى
		 * @Objective: عرض المنتجات في الواقع المعزز لتمكين العملاء من رؤية المنتج في بيئتهم الحقيقية.
		 * @Technology: WebAR, AR.js, أو تكامل مع تطبيقات AR
		 * @Features: معاينة المنتج في الغرفة، تجربة الأثاث، تجربة الملابس
		 */
		if ($this->user->hasPermission('access', 'catalog/ar_product_viewer')) {
			$ai_product_experience[] = ['name' => $this->language->get('text_ar_product_viewer'), 'href' => $this->url->link('catalog/ar_product_viewer', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/ar_product_viewer'];
		}

		/**
		 * @Screen: المعاينة ثلاثية الأبعاد (3D Product Preview)
		 * @Users: مدير المتجر, مدير المحتوى
		 * @Objective: عرض المنتجات في نموذج ثلاثي الأبعاد قابل للدوران والتفاعل لتحسين تجربة المعاينة.
		 * @Technology: Three.js, WebGL, أو تكامل مع منصات 3D
		 * @Features: دوران 360 درجة، تكبير وتصغير، تفاصيل دقيقة
		 */
		if ($this->user->hasPermission('access', 'catalog/3d_product_preview')) {
			$ai_product_experience[] = ['name' => $this->language->get('text_3d_product_preview'), 'href' => $this->url->link('catalog/3d_product_preview', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/3d_product_preview'];
		}

		/**
		 * @Screen: التخصيص الذكي للمنتجات (AI Product Customization)
		 * @Users: مدير المنتجات, مدير التسويق
		 * @Objective: تمكين العملاء من تخصيص المنتجات باستخدام الذكاء الاصطناعي (الألوان، الأحجام، الإضافات).
		 * @AI_Integration: تحليل تفضيلات العميل، اقتراحات ذكية للتخصيص
		 * @Features: معاينة التخصيص في الوقت الفعلي، حساب السعر التلقائي
		 */
		if ($this->user->hasPermission('access', 'catalog/ai_product_customization')) {
			$ai_product_experience[] = ['name' => $this->language->get('text_ai_product_customization'), 'href' => $this->url->link('catalog/ai_product_customization', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/ai_product_customization'];
		}

		if ($ai_product_experience) {
			$website_ecommerce[] = ['name' => $this->language->get('text_ai_product_experience_section'), 'children' => $ai_product_experience];
		}

		//-----------------------------------------------------
		// (7.3) قسم تجربة العميل الذكية (AI Customer Experience)
		//-----------------------------------------------------
		$ai_customer_experience = [];

		/**
		 * @Screen: مساعد الدردشة الذكي (AI Chat Assistant)
		 * @Users: مدير خدمة العملاء, مدير المتجر
		 * @Objective: توفير مساعد ذكي للدردشة مع العملاء للإجابة على الاستفسارات وتقديم المساعدة.
		 * @AI_Integration: GPT أو نموذج محادثة متقدم
		 * @Features: إجابات فورية، توجيه للصفحات، معالجة الشكاوى البسيطة
		 */
		if ($this->user->hasPermission('access', 'catalog/ai_chat')) {
			$ai_customer_experience[] = ['name' => $this->language->get('text_ai_chat_assistant'), 'href' => $this->url->link('catalog/ai_chat', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/ai_chat'];
		}

		/**
		 * @Screen: التسوق بالصوت (Voice Shopping)
		 * @Users: مدير المتجر, مدير التقنية
		 * @Objective: تمكين العملاء من التسوق باستخدام الأوامر الصوتية للبحث والشراء.
		 * @Technology: Speech Recognition, Text-to-Speech, Voice Commands
		 * @Features: البحث الصوتي، إضافة للسلة، إتمام الشراء، تتبع الطلبات
		 */
		if ($this->user->hasPermission('access', 'catalog/voice_shopping')) {
			$ai_customer_experience[] = ['name' => $this->language->get('text_voice_shopping'), 'href' => $this->url->link('catalog/voice_shopping', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/voice_shopping'];
		}

		/**
		 * @Screen: البحث الذكي (AI Search)
		 * @Users: مدير المتجر, مدير التقنية
		 * @Objective: تحسين البحث في المتجر باستخدام الذكاء الاصطناعي لفهم نية المستخدم وتقديم نتائج أفضل.
		 * @AI_Integration: Semantic Search, Query Understanding, Auto-complete
		 * @Features: البحث باللغة الطبيعية، اقتراحات ذكية، تصفية ذكية
		 */
		if ($this->user->hasPermission('access', 'catalog/ai_search')) {
			$ai_customer_experience[] = ['name' => $this->language->get('text_ai_search'), 'href' => $this->url->link('catalog/ai_search', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/ai_search'];
		}

		/**
		 * @Screen: المساعد الشخصي للعميل (AI Personal Assistant)
		 * @Users: مدير خدمة العملاء, مدير التسويق
		 * @Objective: توفير مساعد شخصي ذكي لكل عميل يساعده في التسوق والتوصيات الشخصية.
		 * @AI_Integration: تحليل سلوك العميل، توصيات شخصية، تذكيرات ذكية
		 * @Features: تتبع الطلبات، توصيات مخصصة، تذكيرات للمنتجات المفضلة
		 */
		if ($this->user->hasPermission('access', 'catalog/ai_personal_assistant')) {
			$ai_customer_experience[] = ['name' => $this->language->get('text_ai_personal_assistant'), 'href' => $this->url->link('catalog/ai_personal_assistant', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/ai_personal_assistant'];
		}

		if ($ai_customer_experience) {
			$website_ecommerce[] = ['name' => $this->language->get('text_ai_customer_experience_section'), 'children' => $ai_customer_experience];
		}

		//-----------------------------------------------------
		// (7.4) قسم التسويق الذكي (AI Marketing)
		//-----------------------------------------------------
		$ai_marketing = [];

		/**
		 * @Screen: نظام التوصيات الذكي (AI Recommendations)
		 * @Users: مدير التسويق, محلل البيانات
		 * @Objective: تقديم توصيات ذكية للمنتجات بناءً على تحليل سلوك المستخدم وتفضيلاته.
		 * @AI_Integration: Collaborative Filtering, Content-Based Filtering, Hybrid Models
		 * @Features: توصيات "من قد يعجبك أيضاً"، توصيات "اشترى معاً"، توصيات موسمية
		 */
		if ($this->user->hasPermission('access', 'marketing/ai_recommendations')) {
			$ai_marketing[] = ['name' => $this->language->get('text_ai_recommendations'), 'href' => $this->url->link('marketing/ai_recommendations', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/ai_recommendations'];
		}

		/**
		 * @Screen: الحملات الذكية (AI Campaigns)
		 * @Users: مدير التسويق, محلل البيانات
		 * @Objective: إدارة الحملات التسويقية باستخدام الذكاء الاصطناعي لتحسين الأداء والاستهداف.
		 * @AI_Integration: Audience Segmentation, A/B Testing Automation, Performance Prediction
		 * @Features: استهداف ذكي، تحسين تلقائي للحملات، تحليل الأداء المتقدم
		 */
		if ($this->user->hasPermission('access', 'marketing/ai_campaigns')) {
			$ai_marketing[] = ['name' => $this->language->get('text_ai_campaigns'), 'href' => $this->url->link('marketing/ai_campaigns', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/ai_campaigns'];
		}

		/**
		 * @Screen: التسعير الديناميكي (Dynamic Pricing)
		 * @Users: مدير المبيعات, محلل البيانات
		 * @Objective: تعديل أسعار المنتجات تلقائياً بناءً على الطلب والمنافسة والمواسم.
		 * @AI_Integration: Demand Prediction, Competitor Analysis, Price Elasticity
		 * @Features: تعديل الأسعار التلقائي، مراقبة المنافسين، تحليل الربحية
		 */
		if ($this->user->hasPermission('access', 'marketing/dynamic_pricing')) {
			$ai_marketing[] = ['name' => $this->language->get('text_dynamic_pricing'), 'href' => $this->url->link('marketing/dynamic_pricing', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/dynamic_pricing'];
		}

		/**
		 * @Screen: العروض الشخصية (Personalized Offers)
		 * @Users: مدير التسويق, محلل البيانات
		 * @Objective: تقديم عروض مخصصة لكل عميل بناءً على سلوكه وتفضيلاته.
		 * @AI_Integration: Customer Segmentation, Behavioral Analysis, Offer Optimization
		 * @Features: عروض مخصصة، خصومات شخصية، عروض محدودة زمنياً
		 */
		if ($this->user->hasPermission('access', 'marketing/personalized_offers')) {
			$ai_marketing[] = ['name' => $this->language->get('text_personalized_offers'), 'href' => $this->url->link('marketing/personalized_offers', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/personalized_offers'];
		}

		if ($ai_marketing) {
			$website_ecommerce[] = ['name' => $this->language->get('text_ai_marketing_section'), 'children' => $ai_marketing];
		}

		//-----------------------------------------------------
		// (7.5) قسم التسعير والعروض الترويجية للمتجر (Pricing & Promotions)
		//-----------------------------------------------------
		$store_pricing_promo = [];

		/**
		 * @Screen: العروض الخاصة والكوبونات (Specials & Coupons)
		 * @Users: مدير التسويق, مدير المبيعات
		 * @Objective: إنشاء وإدارة العروض الخاصة على المنتجات، وإنشاء أكواد خصم (كوبونات) للحملات التسويقية.
		 */
		if ($this->user->hasPermission('access', 'marketing/coupon')) {
			$store_pricing_promo[] = ['name' => $this->language->get('text_coupons'), 'href' => $this->url->link('marketing/coupon', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/coupon'];
		}
		
		/**
		 * @Screen: بطاقات الهدايا (Gift Cards / Vouchers)
		 * @Users: مدير التسويق
		 * @Objective: إنشاء وإدارة بطاقات الهدايا التي يمكن للعملاء شراؤها أو الحصول عليها.
		 */
		if ($this->user->hasPermission('access', 'marketing/voucher')) {
			$store_pricing_promo[] = ['name' => $this->language->get('text_gift_vouchers'), 'href' => $this->url->link('marketing/voucher', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/voucher'];
		}

		if ($store_pricing_promo) {
			$website_ecommerce[] = ['name' => $this->language->get('text_store_pricing_promotions_section'), 'children' => $store_pricing_promo];
		}

		//-----------------------------------------------------
		// (7.6) قسم إدارة المحتوى (Content Management - CMS)
		//-----------------------------------------------------
		$cms = [];

		/**
		 * @Screen: الصفحات التعريفية (Information Pages)
		 * @Users: مدير المحتوى
		 * @Objective: إنشاء وإدارة الصفحات الثابتة في الموقع مثل "من نحن"، "سياسة الخصوصية"، "الشروط والأحكام".
		 */
		if ($this->user->hasPermission('access', 'catalog/information')) {
			$cms[] = ['name' => $this->language->get('text_information_pages'), 'href' => $this->url->link('catalog/information', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/information'];
		}

		/**
		 * @Screen: المدونة (Blog)
		 * @Users: مدير المحتوى, كتّاب المحتوى
		 * @Objective: إدارة مدونة الشركة (المقالات، التصنيفات، التعليقات) لزيادة التفاعل وتحسين محركات البحث.
		 */
		$blog_submenu = [];
		if ($this->user->hasPermission('access', 'catalog/blog_post')) { $blog_submenu[] = ['name' => $this->language->get('text_blog_posts'), 'href' => $this->url->link('catalog/blog_post', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/blog_post']; }
		if ($this->user->hasPermission('access', 'catalog/blog_category')) { $blog_submenu[] = ['name' => $this->language->get('text_blog_categories'), 'href' => $this->url->link('catalog/blog_category', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'catalog/blog_category']; }
		if ($blog_submenu) {
			$cms[] = ['name' => $this->language->get('text_blog_management_section'), 'children' => $blog_submenu];
		}
		
		if ($cms) {
			$website_ecommerce[] = ['name' => $this->language->get('text_content_management_cms_section'), 'children' => $cms];
		}

		//-----------------------------------------------------
		// (7.7) قسم تصميم المتجر وتخصيصه (Design & Customization)
		//-----------------------------------------------------
		$design_customization = [];

		/**
		 * @Screen: إدارة التخطيطات (Layouts)
		 * @Users: مصمم الواجهات, مدير المتجر
		 * @Objective: التحكم في كيفية عرض الوحدات (Modules) المختلفة على صفحات الموقع المختلفة (الصفحة الرئيسية، صفحة المنتج، إلخ).
		 */
		if ($this->user->hasPermission('access', 'design/layout')) {
			$design_customization[] = ['name' => $this->language->get('text_layout_management'), 'href' => $this->url->link('design/layout', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'design/layout'];
		}

		/**
		 * @Screen: إدارة البانرات (Banners)
		 * @Users: مدير التسويق
		 * @Objective: إنشاء وإدارة البانرات الإعلانية التي تظهر في أجزاء مختلفة من الموقع.
		 */
		if ($this->user->hasPermission('access', 'design/banner')) {
			$design_customization[] = ['name' => $this->language->get('text_banner_management'), 'href' => $this->url->link('design/banner', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'design/banner'];
		}

		if ($design_customization) {
			$website_ecommerce[] = ['name' => $this->language->get('text_design_customization_section'), 'children' => $design_customization];
		}
		
		//-----------------------------------------------------
		// (7.8) قسم التجارب والمعاينة (Testing & Preview)
		//-----------------------------------------------------
		$testing_preview = [];

		/**
		 * @Screen: معاينة المتجر (Store Preview)
		 * @Users: مدير المتجر
		 * @Objective: معاينة التغييرات على المتجر قبل نشرها للعملاء
		 */
		if ($this->user->hasPermission('access', 'testing/store_preview')) {
			$testing_preview[] = ['name' => $this->language->get('text_store_preview'), 'href' => $this->url->link('testing/store_preview', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'testing/store_preview'];
		}

		if ($testing_preview) {
			$website_ecommerce[] = ['name' => $this->language->get('text_testing_preview_section'), 'children' => $testing_preview];
		}

		//-----------------------------------------------------
		// (7.9) قسم التسويق الرقمي و SEO (Digital Marketing & SEO)
		//-----------------------------------------------------
		$digital_marketing_seo = [];
		
		/**
		 * @Screen: حملات التسويق (Marketing Campaigns)
		 * @Users: مدير التسويق
		 * @Objective: تتبع أداء الحملات التسويقية الخارجية (مثل إعلانات جوجل وفيسبوك) عبر روابط تتبع مخصصة.
		 */
		if ($this->user->hasPermission('access', 'marketing/marketing')) {
			$digital_marketing_seo[] = ['name' => $this->language->get('text_marketing_tracking'), 'href' => $this->url->link('marketing/marketing', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/marketing'];
		}
		
		/**
		 * @Screen: إدارة SEO (SEO Management)
		 * @Users: متخصص SEO, مدير التسويق
		 * @Objective: إدارة روابط SEO (SEO URLs) وتحسين ظهور الموقع في محركات البحث.
		 */
		if ($this->user->hasPermission('access', 'design/seo_url')) {
			$digital_marketing_seo[] = ['name' => $this->language->get('text_seo_urls'), 'href' => $this->url->link('design/seo_url', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'design/seo_url'];
		}

		if ($digital_marketing_seo) {
			$website_ecommerce[] = ['name' => $this->language->get('text_digital_marketing_seo_section'), 'children' => $digital_marketing_seo];
		}

		if ($website_ecommerce) {
			$data['menus'][] = ['id' => 'menu-website-ecommerce', 'icon' => 'fa-desktop', 'name' => $this->language->get('text_website_ecommerce'), 'href' => '', 'children' => $website_ecommerce];
		}
	}

	/**
	 * =======================================================================
	 * (7.5) نظام الشحن والتوصيل (Shipping & Delivery)
	 * =======================================================================
	 * @Objective: إدارة عمليات الشحن والتوصيل من تحضير الطلبات حتى التسليم للعميل.
	 * @Workflow: Order Fulfillment -> Shipment Preparation -> Tracking -> Delivery.
	 * @Accounting_Impact: Creates entries for shipping costs and delivery fees.
	 */
	private function buildShippingSystem(&$data) {
		$shipping_delivery = [];

		//-----------------------------------------------------
		// (7.5.1) قسم تنفيذ الطلبات (Order Fulfillment)
		//-----------------------------------------------------
		$order_fulfillment = [];

		/**
		 * @Screen: تنفيذ الطلبات (Order Fulfillment)
		 * @Users: مدير المستودع, فريق التحضير
		 * @Objective: إدارة عملية تحضير الطلبات للشحن.
		 */
		if ($this->user->hasPermission('access', 'shipping/order_fulfillment')) {
			$order_fulfillment[] = ['name' => $this->language->get('text_order_fulfillment'), 'href' => $this->url->link('shipping/order_fulfillment', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'shipping/order_fulfillment'];
		}

		/**
		 * @Screen: تحضير الطلبات (Prepare Orders)
		 * @Users: فريق التحضير
		 * @Objective: تحضير وتجهيز الطلبات للشحن.
		 */
		if ($this->user->hasPermission('access', 'shipping/prepare_orders')) {
			$order_fulfillment[] = ['name' => $this->language->get('text_prepare_orders'), 'href' => $this->url->link('shipping/prepare_orders', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'shipping/prepare_orders'];
		}

		if ($order_fulfillment) {
			$shipping_delivery[] = ['name' => $this->language->get('text_order_fulfillment_section'), 'children' => $order_fulfillment];
		}

		//-----------------------------------------------------
		// (7.5.2) قسم إدارة الشحنات (Shipment Management)
		//-----------------------------------------------------
		$shipment_management = [];

		/**
		 * @Screen: إدارة الشحنات (Shipment Management)
		 * @Users: مدير الشحن
		 * @Objective: إدارة الشحنات وتتبع حالتها.
		 */
		if ($this->user->hasPermission('access', 'shipping/shipment')) {
			$shipment_management[] = ['name' => $this->language->get('text_shipment_management'), 'href' => $this->url->link('shipping/shipment', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'shipping/shipment'];
		}

		/**
		 * @Screen: تتبع الشحنات (Tracking)
		 * @Users: خدمة العملاء, العملاء
		 * @Objective: تتبع حالة الشحنات ومعرفة موقعها.
		 */
		if ($this->user->hasPermission('access', 'shipping/tracking')) {
			$shipment_management[] = ['name' => $this->language->get('text_shipment_tracking'), 'href' => $this->url->link('shipping/tracking', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'shipping/tracking'];
		}

		if ($shipment_management) {
			$shipping_delivery[] = ['name' => $this->language->get('text_shipment_management_section'), 'children' => $shipment_management];
		}

		//-----------------------------------------------------
		// (7.5.3) قسم لوحة الشحن (Shipping Dashboard)
		//-----------------------------------------------------
		$shipping_dashboard = [];

		/**
		 * @Screen: لوحة الشحن (Shipping Dashboard)
		 * @Users: مدير الشحن, الإدارة
		 * @Objective: عرض إحصائيات ومؤشرات أداء الشحن.
		 */
		if ($this->user->hasPermission('access', 'shipping/shipping_dashboard')) {
			$shipping_dashboard[] = ['name' => $this->language->get('text_shipping_dashboard'), 'href' => $this->url->link('shipping/shipping_dashboard', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'shipping/shipping_dashboard'];
		}

		if ($shipping_dashboard) {
			$shipping_delivery[] = ['name' => $this->language->get('text_shipping_dashboard_section'), 'children' => $shipping_dashboard];
		}

		if ($shipping_delivery) {
			$data['menus'][] = ['id' => 'menu-shipping-delivery', 'icon' => 'fa-truck', 'name' => $this->language->get('text_shipping_delivery'), 'href' => '', 'children' => $shipping_delivery];
		}
	}

	/**
	 * =======================================================================
	 * (8) نظام الموارد البشرية (Human Resources - HR)
	 * =======================================================================
	 * @Objective: إدارة بيانات الموظفين، الحضور والانصراف، الإجازات، الرواتب، تقييم الأداء، التدريب، والسلف.
	 * @Workflow: Employee Lifecycle (Hiring -> ... -> Offboarding), Payroll Cycle, Performance Cycle, Leave Cycle.
	 * @Accounting_Impact: Generates significant accounting entries for Payroll (salaries, deductions, taxes) and Employee Advances.
	 */
	private function buildHrSystem(&$data) {
		$hr_management = [];

		//-----------------------------------------------------
		// (8.1) قسم إدارة شؤون الموظفين (Employee Affairs)
		//-----------------------------------------------------
		$employee_affairs = [];

		/**
		 * @Screen: ملفات الموظفين (Employee Profiles)
		 * @Users: مدير الموارد البشرية, موظف شؤون الموظفين
		 * @Objective: إنشاء وإدارة ملف شامل لكل موظف يحتوي على بياناته الشخصية، الوظيفية، الوثائق، وتاريخه في الشركة.
		 */
		if ($this->user->hasPermission('access', 'hr/employee')) {
			$employee_affairs[] = ['name' => $this->language->get('text_employee_profiles'), 'href' => $this->url->link('hr/employee', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/employee'];
		}

		/**
		 * @Screen: الحضور والانصراف (Attendance Management)
		 * @Users: موظف شؤون الموظفين, مديرو الأقسام
		 * @Objective: تسجيل ومتابعة سجلات الحضور والانصراف للموظفين، وحساب ساعات العمل الفعلية والإضافية.
		 * @Integration: يتكامل مع نظام الرواتب لحساب المستحقات والخصومات.
		 */
		if ($this->user->hasPermission('access', 'hr/attendance')) {
			$employee_affairs[] = ['name' => $this->language->get('text_attendance_management'), 'href' => $this->url->link('hr/attendance', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/attendance'];
		}

		/**
		 * @Screen: إدارة الإجازات (Leave Management)
		 * @Users: جميع الموظفين, مديرو الأقسام, مدير الموارد البشرية
		 * @Objective: إدارة طلبات الإجازات والموافقات عليها، ومتابعة رصيد الإجازات لكل موظف.
		 * @Workflow: طلب إجازة -> موافقة المدير المباشر -> موافقة الموارد البشرية -> تحديث رصيد الإجازات.
		 */
		if ($this->user->hasPermission('access', 'hr/leave')) {
			$employee_affairs[] = ['name' => $this->language->get('text_leave_management'), 'href' => $this->url->link('hr/leave', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/leave'];
		}

		/**
		 * @Screen: السلف والعهد للموظفين (Employee Advances & Loans)
		 * @Users: المحاسب, مدير الموارد البشرية
		 * @Objective: إدارة طلبات السلف والقروض للموظفين وجدولة سدادها.
		 * @Accounting: قيد صرف السلفة: من ح/سلف الموظفين (مدين) إلى ح/النقدية (دائن). وقيد التسوية عند خصمها من الراتب.
		 */
		if ($this->user->hasPermission('access', 'hr/employee_advance')) {
			$employee_affairs[] = ['name' => $this->language->get('text_employee_advances_loans'), 'href' => $this->url->link('hr/employee_advance', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/employee_advance'];
		}
		
		if ($employee_affairs) {
			$hr_management[] = ['name' => $this->language->get('text_employee_affairs_section'), 'children' => $employee_affairs];
		}
		
		//-----------------------------------------------------
		// (8.2) قسم الرواتب والأجور (Payroll)
		//-----------------------------------------------------
		$payroll_section = [];

		/**
		 * @Screen: إعداد الرواتب (Payroll Run)
		 * @Users: مسؤول الرواتب, المدير المالي
		 * @Objective: إعداد مسير الرواتب الشهري بناءً على بيانات الحضور، الإجازات، السلف، والبدلات.
		 * @Workflow: جمع البيانات -> حساب الرواتب -> مراجعة المسير -> اعتماد -> إنشاء قيد محاسبي.
		 * @AI_Integration: كشف أي أنماط غير طبيعية في ساعات العمل الإضافي أو الخصومات للتحقق منها.
		 * @Accounting: قيد إثبات الرواتب: من ح/مصروف الرواتب (مدين) إلى ح/الرواتب المستحقة، ح/التأمينات، ح/الضرائب (دائن).
		 */
		if ($this->user->hasPermission('modify', 'hr/payroll_run')) {
			$payroll_section[] = ['name' => $this->language->get('text_payroll_preparation_run'), 'href' => $this->url->link('hr/payroll_run', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/payroll_run'];
		}

		/**
		 * @Screen: صرف الرواتب (Payroll Disbursement)
		 * @Users: المدير المالي
		 * @Objective: تنفيذ عملية صرف الرواتب للموظفين عبر إنشاء ملف الدفع للبنك أو تسجيل الدفع النقدي.
		 * @Accounting: قيد صرف الرواتب: من ح/الرواتب المستحقة (مدين) إلى ح/البنك (دائن).
		 */
		if ($this->user->hasPermission('modify', 'hr/payroll_disbursement')) {
			$payroll_section[] = ['name' => $this->language->get('text_payroll_disbursement'), 'href' => $this->url->link('hr/payroll_disbursement', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/payroll_disbursement'];
		}

		/**
		 * @Screen: إعدادات الرواتب (Payroll Settings)
		 * @Users: مدير الموارد البشرية
		 * @Objective: تعريف عناصر الراتب المختلفة (بدلات، استقطاعات) وتحديد نسب التأمينات والضرائب.
		 */
		if ($this->user->hasPermission('modify', 'hr/payroll_settings')) {
			$payroll_section[] = ['name' => $this->language->get('text_payroll_settings'), 'href' => $this->url->link('hr/payroll_settings', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/payroll_settings'];
		}
		
		if ($payroll_section) {
			$hr_management[] = ['name' => $this->language->get('text_payroll_section'), 'children' => $payroll_section];
		}
		
		//-----------------------------------------------------
		// (8.3) قسم تقييم الأداء والتطوير (Performance & Development)
		//-----------------------------------------------------
		$performance_dev = [];

		/**
		 * @Screen: دورات التقييم (Evaluation Cycles)
		 * @Users: مدير الموارد البشرية, مديرو الأقسام
		 * @Objective: إطلاق وإدارة دورات تقييم أداء الموظفين بشكل دوري (سنوي، نصف سنوي).
		 * @Workflow: إطلاق دورة -> تعبئة التقييمات -> مراجعة النتائج -> ربط بالترقيات والمكافآت.
		 * @AI_Integration: تحليل النصوص في التقييمات لتحديد نقاط القوة والضعف، واقتراح خطط تطوير فردية.
		 */
		if ($this->user->hasPermission('access', 'hr/evaluation_process')) {
			$performance_dev[] = ['name' => $this->language->get('text_evaluation_cycles_process'), 'href' => $this->url->link('hr/evaluation_process', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/evaluation_process'];
		}

		/**
		 * @Screen: إدارة التدريب (Training Management)
		 * @Users: مدير الموارد البشرية, مديرو التدريب
		 * @Objective: تخطيط وتنفيذ وتتبع برامج التدريب للموظفين وقياس فعاليتها.
		 */
		if ($this->user->hasPermission('access', 'hr/training')) {
			$performance_dev[] = ['name' => $this->language->get('text_training_management'), 'href' => $this->url->link('hr/training', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'hr/training'];
		}

		if ($performance_dev) {
			$hr_management[] = ['name' => $this->language->get('text_performance_development_section'), 'children' => $performance_dev];
		}
		
		if ($hr_management) {
			$data['menus'][] = ['id' => 'menu-human-resources', 'icon' => 'fa-users', 'name' => $this->language->get('text_human_resources'), 'href' => '', 'children' => $hr_management];
		}
	}

	/**
	 * =======================================================================
	 * (9) نظام إدارة المشاريع والعمليات (Project & Operations Management)
	 * =======================================================================
	 * @Objective: تخطيط وتنفيذ ومتابعة المشاريع والمهام المرتبطة بها، وتتبع الوقت والموارد.
	 * @Workflow: Project Lifecycle (Create -> Plan -> Execute -> Monitor -> Close), Task Management.
	 * @Accounting_Impact: Can be linked to cost centers. Project costs (materials, wages) and revenues can generate journal entries, enabling project-based profitability analysis.
	 */
	private function buildProjectManagementSystem(&$data) {
		$project_management = [];

		/**
		 * @Screen: المشاريع (Projects)
		 * @Users: مديرو المشاريع, الإدارة العليا
		 * @Objective: إنشاء وإدارة المشاريع، تحديد الأهداف، الميزانيات، والجداول الزمنية.
		 */
		if ($this->user->hasPermission('access', 'project/project')) {
			$project_management[] = ['name' => $this->language->get('text_projects_list'), 'href' => $this->url->link('project/project', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'project/project'];
		}

		/**
		 * @Screen: المهام (Tasks)
		 * @Users: أعضاء فرق المشاريع, مديرو المشاريع
		 * @Objective: إنشاء المهام، إسنادها لأعضاء الفريق، تحديد المواعيد النهائية، ومتابعة التقدم.
		 */
		if ($this->user->hasPermission('access', 'project/task')) {
			$project_management[] = ['name' => $this->language->get('text_tasks_list'), 'href' => $this->url->link('project/task', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'project/task'];
		}

		/**
		 * @Screen: لوحة كانبان للمهام (Task Kanban Board)
		 * @Users: أعضاء فرق المشاريع
		 * @Objective: توفير عرض مرئي لحالة المهام (مثل: للتنفيذ، قيد التنفيذ، تم الإنجاز) لتسهيل متابعة سير العمل.
		 */
		if ($this->user->hasPermission('access', 'project/task_board')) {
			$project_management[] = ['name' => $this->language->get('text_task_kanban_board'), 'href' => $this->url->link('project/task_board', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'project/task_board'];
		}

		/**
		 * @Screen: مخطط جانت للمشاريع (Project Gantt Chart)
		 * @Users: مديرو المشاريع
		 * @Objective: توفير عرض زمني للمشاريع ومهامها واعتمادياتها، مما يساعد في تخطيط الموارد وتحديد المسار الحرج.
		 */
		if ($this->user->hasPermission('access', 'project/gantt')) {
			$project_management[] = ['name' => $this->language->get('text_project_gantt_chart'), 'href' => $this->url->link('project/gantt', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'project/gantt'];
		}

		/**
		 * @Screen: تتبع الوقت (Time Tracking / Timesheets)
		 * @Users: أعضاء فرق المشاريع
		 * @Objective: تسجيل الوقت الفعلي الذي يقضيه أعضاء الفريق على المهام والمشاريع المختلفة.
		 * @Accounting: يمكن استخدام هذه البيانات لحساب تكلفة العمالة للمشاريع.
		 */
		if ($this->user->hasPermission('access', 'project/timesheet')) {
			$project_management[] = ['name' => $this->language->get('text_time_tracking_timesheets'), 'href' => $this->url->link('project/timesheet', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'project/timesheet'];
		}

		/**
		 * @Screen: تقارير المشاريع (Project Reports)
		 * @Users: الإدارة العليا, مديرو المشاريع
		 * @Objective: إنشاء تقارير حول تقدم المشاريع، مقارنة التكلفة الفعلية بالميزانية، واستخدام الموارد.
		 */
		if ($this->user->hasPermission('access', 'report/project')) {
			$project_management[] = ['name' => $this->language->get('text_project_reports'), 'href' => $this->url->link('report/project', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'report/project'];
		}
		
		if ($project_management) {
			$data['menus'][] = ['id' => 'menu-project-management', 'icon' => 'fa-tasks', 'name' => $this->language->get('text_project_management'), 'href' => '', 'children' => $project_management];
		}
	}

	/**
	 * =======================================================================
	 * (9.5) نظام إدارة المستندات (Document Management)
	 * =======================================================================
	 * @Objective: إدارة المستندات الرقمية، الموافقات، الأرشفة، والتحكم في الإصدارات.
	 * @Workflow: Document Creation -> Approval -> Archive -> Version Control.
	 * @Accounting_Impact: No direct accounting entries, but supports audit trails for financial documents.
	 */
	private function buildDocumentSystem(&$data) {
		$document_management = [];

		//-----------------------------------------------------
		// (9.5.1) قسم إدارة المستندات (Document Management)
		//-----------------------------------------------------
		$document_core = [];

		/**
		 * @Screen: موافقات المستندات (Document Approval)
		 * @Users: المديرون, المحاسبون
		 * @Objective: إدارة موافقات المستندات المختلفة.
		 */
		if ($this->user->hasPermission('access', 'documents/approval')) {
			$document_core[] = ['name' => $this->language->get('text_document_approval'), 'href' => $this->url->link('documents/approval', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'documents/approval'];
		}

		/**
		 * @Screen: أرشيف المستندات (Document Archive)
		 * @Users: جميع المستخدمين
		 * @Objective: أرشفة وتخزين المستندات المهمة.
		 */
		if ($this->user->hasPermission('access', 'documents/archive')) {
			$document_core[] = ['name' => $this->language->get('text_document_archive'), 'href' => $this->url->link('documents/archive', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'documents/archive'];
		}

		/**
		 * @Screen: قوالب المستندات (Document Templates)
		 * @Users: مديرو النظام
		 * @Objective: إدارة قوالب المستندات المختلفة.
		 */
		if ($this->user->hasPermission('access', 'documents/templates')) {
			$document_core[] = ['name' => $this->language->get('text_document_templates'), 'href' => $this->url->link('documents/templates', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'documents/templates'];
		}

		/**
		 * @Screen: إصدارات المستندات (Document Versioning)
		 * @Users: جميع المستخدمين
		 * @Objective: تتبع إصدارات المستندات المختلفة.
		 */
		if ($this->user->hasPermission('access', 'documents/versioning')) {
			$document_core[] = ['name' => $this->language->get('text_document_versioning'), 'href' => $this->url->link('documents/versioning', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'documents/versioning'];
		}

		if ($document_core) {
			$document_management[] = ['name' => $this->language->get('text_document_management_section'), 'children' => $document_core];
		}

		if ($document_management) {
			$data['menus'][] = ['id' => 'menu-document-management', 'icon' => 'fa-file-alt', 'name' => $this->language->get('text_document_management'), 'href' => '', 'children' => $document_management];
		}
	}

	/**
	 * =======================================================================
	 * (10) نظام التواصل والإشعارات (Communication & Notifications)
	 * =======================================================================
	 * @Objective: إدارة التواصل الداخلي، الإشعارات، والرسائل بين المستخدمين.
	 * @Workflow: Internal Communication, Notification Management, Team Collaboration.
	 * @Accounting_Impact: No direct accounting entries, but supports communication for approval workflows.
	 */
	private function buildCommunicationSystem(&$data) {
		$communication_system = [];

		//-----------------------------------------------------
		// (10.1) قسم التواصل الداخلي (Internal Communication)
		//-----------------------------------------------------
		$internal_communication = [];

		/**
		 * @Screen: الإعلانات (Announcements)
		 * @Users: الإدارة, جميع الموظفين
		 * @Objective: نشر الإعلانات والأخبار المهمة لجميع الموظفين.
		 */
		if ($this->user->hasPermission('access', 'communication/announcements')) {
			$internal_communication[] = ['name' => $this->language->get('text_announcements'), 'href' => $this->url->link('communication/announcements', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'communication/announcements'];
		}

		/**
		 * @Screen: المحادثات (Chat)
		 * @Users: جميع الموظفين
		 * @Objective: نظام محادثات فوري بين الموظفين.
		 */
		if ($this->user->hasPermission('access', 'communication/chat')) {
			$internal_communication[] = ['name' => $this->language->get('text_chat_system'), 'href' => $this->url->link('communication/chat', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'communication/chat'];
		}

		/**
		 * @Screen: الرسائل (Messages)
		 * @Users: جميع الموظفين
		 * @Objective: نظام رسائل داخلي للتواصل الرسمي.
		 */
		if ($this->user->hasPermission('access', 'communication/messages')) {
			$internal_communication[] = ['name' => $this->language->get('text_internal_messages'), 'href' => $this->url->link('communication/messages', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'communication/messages'];
		}

		/**
		 * @Screen: الفرق (Teams)
		 * @Users: مديرو الفرق, أعضاء الفرق
		 * @Objective: إدارة الفرق والمجموعات العملية.
		 */
		if ($this->user->hasPermission('access', 'communication/teams')) {
			$internal_communication[] = ['name' => $this->language->get('text_teams_management'), 'href' => $this->url->link('communication/teams', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'communication/teams'];
		}

		if ($internal_communication) {
			$communication_system[] = ['name' => $this->language->get('text_internal_communication_section'), 'children' => $internal_communication];
		}

		//-----------------------------------------------------
		// (10.2) قسم إدارة الإشعارات (Notification Management)
		//-----------------------------------------------------
		$notification_management = [];

		/**
		 * @Screen: أتمتة الإشعارات (Notification Automation)
		 * @Users: مديرو النظام
		 * @Objective: إعداد قواعد الإشعارات التلقائية.
		 */
		if ($this->user->hasPermission('access', 'notification/automation')) {
			$notification_management[] = ['name' => $this->language->get('text_notification_automation'), 'href' => $this->url->link('notification/automation', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'notification/automation'];
		}

		/**
		 * @Screen: إعدادات الإشعارات (Notification Settings)
		 * @Users: جميع المستخدمين
		 * @Objective: إعدادات شخصية للإشعارات.
		 */
		if ($this->user->hasPermission('access', 'notification/settings')) {
			$notification_management[] = ['name' => $this->language->get('text_notification_settings'), 'href' => $this->url->link('notification/settings', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'notification/settings'];
		}

		/**
		 * @Screen: قوالب الإشعارات (Notification Templates)
		 * @Users: مديرو النظام
		 * @Objective: إدارة قوالب الإشعارات المختلفة.
		 */
		if ($this->user->hasPermission('access', 'notification/templates')) {
			$notification_management[] = ['name' => $this->language->get('text_notification_templates'), 'href' => $this->url->link('notification/templates', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'notification/templates'];
		}

		if ($notification_management) {
			$communication_system[] = ['name' => $this->language->get('text_notification_management_section'), 'children' => $notification_management];
		}

		if ($communication_system) {
			$data['menus'][] = ['id' => 'menu-communication-system', 'icon' => 'fa-comments', 'name' => $this->language->get('text_communication_system'), 'href' => '', 'children' => $communication_system];
		}
	}

	/**
	 * =======================================================================
	 * (11) نظام سير العمل المرئي (Visual Workflow System)
	 * =======================================================================
	 * @Objective: إدارة سير العمل المرئي والأتمتة، شبيه بـ n8n.
	 * @Workflow: Visual Workflow Designer, Process Automation, Task Management.
	 * @Accounting_Impact: Supports approval workflows that may trigger accounting entries.
	 */
	private function buildWorkflowSystem(&$data) {
		$workflow_system = [];

		//-----------------------------------------------------
		// (11.1) قسم المحرر المرئي (Visual Editor)
		//-----------------------------------------------------
		$visual_editor = [];

		/**
		 * @Screen: المحرر المرئي الأساسي (Visual Editor)
		 * @Users: مصممو سير العمل, مديرو العمليات
		 * @Objective: تصميم سير العمل بطريقة مرئية تفاعلية.
		 */
		if ($this->user->hasPermission('access', 'workflow/visual_editor')) {
			$visual_editor[] = ['name' => $this->language->get('text_visual_workflow_editor'), 'href' => $this->url->link('workflow/visual_editor', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/visual_editor'];
		}

		/**
		 * @Screen: المحرر المتقدم (Advanced Visual Editor)
		 * @Users: مطورو سير العمل المتقدم
		 * @Objective: تصميم سير عمل معقد مع شروط وإجراءات متقدمة.
		 */
		if ($this->user->hasPermission('access', 'workflow/advanced_visual_editor')) {
			$visual_editor[] = ['name' => $this->language->get('text_advanced_visual_editor'), 'href' => $this->url->link('workflow/advanced_visual_editor', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/advanced_visual_editor'];
		}

		/**
		 * @Screen: مصمم سير العمل (Workflow Designer)
		 * @Users: محللو العمليات, مديرو الأقسام
		 * @Objective: تصميم وتخصيص سير العمل حسب احتياجات كل قسم.
		 */
		if ($this->user->hasPermission('access', 'workflow/designer')) {
			$visual_editor[] = ['name' => $this->language->get('text_workflow_designer'), 'href' => $this->url->link('workflow/designer', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/designer'];
		}

		if ($visual_editor) {
			$workflow_system[] = ['name' => $this->language->get('text_visual_editor_section'), 'children' => $visual_editor];
		}

		//-----------------------------------------------------
		// (11.2) قسم إدارة سير العمل (Workflow Management)
		//-----------------------------------------------------
		$workflow_management = [];

		/**
		 * @Screen: إدارة سير العمل (Workflow Management)
		 * @Users: مديرو العمليات, مشرفو سير العمل
		 * @Objective: إدارة ومراقبة جميع سير العمل النشطة.
		 */
		if ($this->user->hasPermission('access', 'workflow/workflow')) {
			$workflow_management[] = ['name' => $this->language->get('text_workflow_management'), 'href' => $this->url->link('workflow/workflow', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/workflow'];
		}

		/**
		 * @Screen: إدارة المهام (Task Management)
		 * @Users: جميع الموظفين, مديرو المهام
		 * @Objective: إدارة المهام المرتبطة بسير العمل.
		 */
		if ($this->user->hasPermission('access', 'workflow/task')) {
			$workflow_management[] = ['name' => $this->language->get('text_task_management'), 'href' => $this->url->link('workflow/task', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/task'];
		}

		/**
		 * @Screen: طلبات الموافقة (Approval Requests)
		 * @Users: المعتمدون, مديرو الموافقات
		 * @Objective: إدارة طلبات الموافقة في سير العمل.
		 */
		if ($this->user->hasPermission('access', 'workflow/approval')) {
			$workflow_management[] = ['name' => $this->language->get('text_approval_requests'), 'href' => $this->url->link('workflow/approval', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/approval'];
		}

		if ($workflow_management) {
			$workflow_system[] = ['name' => $this->language->get('text_workflow_management_section'), 'children' => $workflow_management];
		}

		//-----------------------------------------------------
		// (11.3) قسم مكونات سير العمل (Workflow Components)
		//-----------------------------------------------------
		$workflow_components = [];

		/**
		 * @Screen: إدارة الإجراءات (Actions Management)
		 * @Users: مطورو سير العمل, مديرو النظام
		 * @Objective: إدارة الإجراءات المتاحة في سير العمل.
		 */
		if ($this->user->hasPermission('access', 'workflow/actions')) {
			$workflow_components[] = ['name' => $this->language->get('text_workflow_actions'), 'href' => $this->url->link('workflow/actions', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/actions'];
		}

		/**
		 * @Screen: إدارة الشروط (Conditions Management)
		 * @Users: مطورو سير العمل, محللو العمليات
		 * @Objective: إدارة الشروط والقواعد في سير العمل.
		 */
		if ($this->user->hasPermission('access', 'workflow/conditions')) {
			$workflow_components[] = ['name' => $this->language->get('text_workflow_conditions'), 'href' => $this->url->link('workflow/conditions', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/conditions'];
		}

		/**
		 * @Screen: إدارة المحفزات (Triggers Management)
		 * @Users: مطورو سير العمل, مديرو النظام
		 * @Objective: إدارة المحفزات التي تبدأ سير العمل.
		 */
		if ($this->user->hasPermission('access', 'workflow/triggers')) {
			$workflow_components[] = ['name' => $this->language->get('text_workflow_triggers'), 'href' => $this->url->link('workflow/triggers', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/triggers'];
		}

		/**
		 * @Screen: قوالب سير العمل (Workflow Templates)
		 * @Users: جميع مستخدمي سير العمل
		 * @Objective: إدارة واستخدام قوالب سير العمل الجاهزة.
		 */
		if ($this->user->hasPermission('access', 'workflow/templates')) {
			$workflow_components[] = ['name' => $this->language->get('text_workflow_templates'), 'href' => $this->url->link('workflow/templates', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/templates'];
		}

		if ($workflow_components) {
			$workflow_system[] = ['name' => $this->language->get('text_workflow_components_section'), 'children' => $workflow_components];
		}

		//-----------------------------------------------------
		// (11.4) قسم مراقبة الأداء (Performance Monitoring)
		//-----------------------------------------------------
		$workflow_monitoring = [];

		/**
		 * @Screen: مراقبة سير العمل (Workflow Monitoring)
		 * @Users: مديرو العمليات, محللو الأداء
		 * @Objective: مراقبة أداء سير العمل والاختناقات.
		 */
		if ($this->user->hasPermission('access', 'workflow/monitoring')) {
			$workflow_monitoring[] = ['name' => $this->language->get('text_workflow_monitoring'), 'href' => $this->url->link('workflow/monitoring', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/monitoring'];
		}

		/**
		 * @Screen: تحليل الأداء (Performance Analytics)
		 * @Users: محللو الأداء, الإدارة العليا
		 * @Objective: تحليل أداء سير العمل وتحديد نقاط التحسين.
		 */
		if ($this->user->hasPermission('access', 'workflow/analytics')) {
			$workflow_monitoring[] = ['name' => $this->language->get('text_workflow_analytics'), 'href' => $this->url->link('workflow/analytics', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'workflow/analytics'];
		}

		if ($workflow_monitoring) {
			$workflow_system[] = ['name' => $this->language->get('text_workflow_monitoring_section'), 'children' => $workflow_monitoring];
		}

		if ($workflow_system) {
			$data['menus'][] = ['id' => 'menu-workflow-system', 'icon' => 'fa-sitemap', 'name' => $this->language->get('text_workflow_system'), 'href' => '', 'children' => $workflow_system];
		}
	}

	/**
	 * =======================================================================
	 * (11) نظام الذكاء الاصطناعي (AI System)
	 * =======================================================================
	 * @Objective: تطبيق الذكاء الاصطناعي في جميع جوانب النظام لتحسين الأداء واتخاذ القرارات.
	 * @Workflow: AI Analysis, Predictions, Recommendations, Automation.
	 * @Accounting_Impact: Provides intelligent insights for financial decisions and cost optimization.
	 */
	private function buildAiSystem(&$data) {
		$ai_system = [];

		//-----------------------------------------------------
		// (11.1) قسم مساعد الذكاء الاصطناعي (AI Assistant)
		//-----------------------------------------------------
		$ai_assistant = [];

		/**
		 * @Screen: مساعد الذكاء الاصطناعي (AI Assistant)
		 * @Users: جميع المستخدمين
		 * @Objective: مساعد ذكي لتقديم المساعدة والإجابة على الاستفسارات.
		 */
		if ($this->user->hasPermission('access', 'ai/ai_assistant')) {
			$ai_assistant[] = ['name' => $this->language->get('text_ai_assistant'), 'href' => $this->url->link('ai/ai_assistant', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'ai/ai_assistant'];
		}

		/**
		 * @Screen: التحليلات الذكية (Smart Analytics)
		 * @Users: الإدارة, المحللون
		 * @Objective: تحليلات ذكية للبيانات والتنبؤات.
		 */
		if ($this->user->hasPermission('access', 'ai/smart_analytics')) {
			$ai_assistant[] = ['name' => $this->language->get('text_smart_analytics'), 'href' => $this->url->link('ai/smart_analytics', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'ai/smart_analytics'];
		}

		if ($ai_assistant) {
			$ai_system[] = ['name' => $this->language->get('text_ai_assistant_section'), 'children' => $ai_assistant];
		}

		if ($ai_system) {
			$data['menus'][] = ['id' => 'menu-ai-system', 'icon' => 'fa-brain', 'name' => $this->language->get('text_ai_system'), 'href' => '', 'children' => $ai_system];
		}
	}

	/**
	 * =======================================================================
	 * (12) نظام الحوكمة والمخاطر (Governance & Risk)
	 * =======================================================================
	 * @Objective: إدارة المخاطر، الرقابة الداخلية، والامتثال للقوانين واللوائح.
	 * @Workflow: Risk Assessment -> Control Implementation -> Compliance Monitoring.
	 * @Accounting_Impact: Ensures compliance with accounting standards and regulations.
	 */
	private function buildGovernanceSystem(&$data) {
		$governance_risk = [];

		//-----------------------------------------------------
		// (12.1) قسم إدارة المخاطر (Risk Management)
		//-----------------------------------------------------
		$risk_management = [];

		/**
		 * @Screen: سجل المخاطر (Risk Register)
		 * @Users: مديرو المخاطر, الإدارة العليا
		 * @Objective: تسجيل وتتبع المخاطر المختلفة التي تواجه المؤسسة.
		 */
		if ($this->user->hasPermission('access', 'governance/risk_register')) {
			$risk_management[] = ['name' => $this->language->get('text_risk_register'), 'href' => $this->url->link('governance/risk_register', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'governance/risk_register'];
		}

		/**
		 * @Screen: المراجعة الداخلية (Internal Audit)
		 * @Users: المراجعون الداخليون
		 * @Objective: إجراء المراجعات الداخلية وتتبع النتائج.
		 */
		if ($this->user->hasPermission('access', 'governance/internal_audit')) {
			$risk_management[] = ['name' => $this->language->get('text_internal_audit'), 'href' => $this->url->link('governance/internal_audit', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'governance/internal_audit'];
		}

		/**
		 * @Screen: الامتثال (Compliance)
		 * @Users: مسؤولو الامتثال
		 * @Objective: مراقبة الامتثال للقوانين واللوائح.
		 */
		if ($this->user->hasPermission('access', 'governance/compliance')) {
			$risk_management[] = ['name' => $this->language->get('text_compliance_management'), 'href' => $this->url->link('governance/compliance', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'governance/compliance'];
		}

		if ($risk_management) {
			$governance_risk[] = ['name' => $this->language->get('text_risk_management_section'), 'children' => $risk_management];
		}

		if ($governance_risk) {
			$data['menus'][] = ['id' => 'menu-governance-risk', 'icon' => 'fa-shield-alt', 'name' => $this->language->get('text_governance_risk'), 'href' => '', 'children' => $governance_risk];
		}
	}

	/**
	 * =======================================================================
	 * (12) نظام التقارير والتحليلات (Reports & Analytics)
	 * =======================================================================
	 * @Objective: توفير رؤى شاملة وقابلة للتخصيص حول أداء الشركة في جميع المجالات.
	 * @Workflow: Select Report -> Define Criteria (Date Range, Branch, etc.) -> View/Export Report -> Analyze Results.
	 * @Note: Many reports in this module, especially financial and inventory reports, rely heavily on the accuracy of the Weighted Average Cost (WAC) data.
	 */
	private function buildReportsSystem(&$data) {
		$reports_analytics = [];

		//-----------------------------------------------------
		// (12.1) قسم التقارير القياسية (Standard Reports)
		//-----------------------------------------------------
		$standard_reports = [];

		/**
		 * @Screen: تقارير المبيعات (Sales Reports)
		 * @Users: مديرو المبيعات, الإدارة العليا
		 * @Objective: عرض تقارير مفصلة عن المبيعات حسب المنتج، العميل، المنطقة، أو الفترة الزمنية.
		 */
		if ($this->user->hasPermission('access', 'report/sale')) {
			$standard_reports[] = ['name' => $this->language->get('text_standard_sales_reports'), 'href' => $this->url->link('report/sale', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'report/sale'];
		}

		/**
		 * @Screen: تقارير المشتريات (Purchase Reports)
		 * @Users: مديرو المشتريات, المدير المالي
		 * @Objective: عرض تقارير عن أوامر الشراء، أداء الموردين، وتكاليف الشراء.
		 */
		if ($this->user->hasPermission('access', 'report/purchase')) {
			$standard_reports[] = ['name' => $this->language->get('text_standard_purchase_reports'), 'href' => $this->url->link('report/purchase', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'report/purchase'];
		}

		/**
		 * @Screen: تقارير المخزون (Inventory Reports)
		 * @Users: مديرو المخازن, مديرو العمليات
		 * @Objective: عرض تقارير عن مستويات المخزون، معدل الدوران، المخزون الراكد، وتقييم المخزون.
		 */
		if ($this->user->hasPermission('access', 'report/inventory')) {
			$standard_reports[] = ['name' => $this->language->get('text_standard_inventory_reports'), 'href' => $this->url->link('report/inventory', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'report/inventory'];
		}

		/**
		 * @Screen: التقارير المالية (Financial Reports)
		 * @Users: المدير المالي, المحاسبون, الإدارة العليا
		 * @Objective: الوصول السريع إلى القوائم المالية الرئيسية (قائمة الدخل، الميزانية، إلخ).
		 */
		if ($this->user->hasPermission('access', 'accounts/financial_reports')) {
			$standard_reports[] = ['name' => $this->language->get('text_standard_financial_reports'), 'href' => $this->url->link('accounts/financial_reports', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'accounts/financial_reports'];
		}
		
		if ($standard_reports) {
			$reports_analytics[] = ['name' => $this->language->get('text_standard_reports_section'), 'children' => $standard_reports];
		}

		//-----------------------------------------------------
		// (12.2) قسم التقارير المخصصة والتحليلات (Custom Reports & Analytics)
		//-----------------------------------------------------
		
		/**
		 * @Screen: منشئ التقارير المخصصة (Custom Report Builder)
		 * @Users: محللو البيانات, مديرو الأقسام
		 * @Objective: تمكين المستخدمين من بناء تقاريرهم الخاصة عبر اختيار الحقول، المقاييس، والفلاتر المطلوبة.
		 * @AI_Integration: اقتراح حقول ومقاييس ذات صلة بالاستعلام، وتوليد رؤى تلقائية من البيانات المختارة.
		 */
		if ($this->user->hasPermission('access', 'report/custom_report_builder')) {
			$reports_analytics[] = [
				'name' => $this->language->get('text_custom_report_builder'),
				'href' => $this->url->link('report/custom_report_builder', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'report/custom_report_builder',
				'children' => []
			];
		}
		
		/**
		 * @Screen: التقارير المجدولة (Scheduled Reports)
		 * @Users: مديرو الأقسام, الإدارة العليا
		 * @Objective: جدولة إرسال التقارير الهامة بشكل دوري (يومي، أسبوعي، شهري) إلى البريد الإلكتروني للمستخدمين المعنيين.
		 */
		if ($this->user->hasPermission('access', 'report/scheduled')) {
			$reports_analytics[] = [
				'name' => $this->language->get('text_scheduled_reports'),
				'href' => $this->url->link('report/scheduled', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'report/scheduled',
				'children' => []
			];
		}
		
		//-----------------------------------------------------
		// (12.3) قسم الإحصاءات العامة (General Statistics)
		//-----------------------------------------------------
		$stats_tools = [];
		
		/**
		 * @Screen: قائمة المتصلين حالياً (Online Users)
		 * @Users: مديرو النظام
		 * @Objective: عرض قائمة بالمستخدمين المتصلين حالياً بالنظام.
		 */
		if ($this->user->hasPermission('access', 'report/online')) {
			$stats_tools[] = ['name' => $this->language->get('text_online_users'), 'href' => $this->url->link('report/online', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'report/online'];
		}

		/**
		 * @Screen: إحصائيات عامة (General Statistics)
		 * @Users: مديرو النظام, الإدارة
		 * @Objective: عرض إحصائيات عامة حول استخدام النظام وأداء العمليات الرئيسية.
		 */
		if ($this->user->hasPermission('access', 'report/statistics')) {
			$stats_tools[] = ['name' => $this->language->get('text_general_statistics'), 'href' => $this->url->link('report/statistics', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'report/statistics'];
		}
		
		if ($stats_tools) {
			$reports_analytics[] = ['name' => $this->language->get('text_statistics_tools_section'), 'children' => $stats_tools];
		}
		
		if ($reports_analytics) {
			$data['menus'][] = ['id' => 'menu-reports-analytics', 'icon' => 'fa-bar-chart', 'name' => $this->language->get('text_reports_and_analytics'), 'href' => '', 'children' => $reports_analytics];
		}
	}
		
	/**
	 * =======================================================================
	 * (13) نظام الإدارة والإعدادات (System & Settings)
	 * =======================================================================
	 * @Objective: إدارة الإعدادات العامة للنظام، المستخدمين والصلاحيات، التوطين، الأدوات المساعدة، والصيانة.
	 * @Users: مديرو النظام, مديرو تقنية المعلومات, الدعم الفني.
	 */
	private function buildSystemAndSettingsMenu(&$data) {
		$system_settings = [];

		//-----------------------------------------------------
		// (13.1) قسم الإعدادات العامة (General Settings)
		//-----------------------------------------------------
		
		/**
		 * @Screen: الإعدادات العامة للنظام (General System Settings)
		 * @Users: مديرو النظام
		 * @Objective: التحكم في الإعدادات الأساسية للشركة مثل الاسم، العنوان، البريد الإلكتروني، وشعار الشركة.
		 */
		if ($this->user->hasPermission('modify', 'setting/setting')) {
			$system_settings[] = [
				'name' => $this->language->get('text_general_system_settings'),
				'href' => $this->url->link('setting/setting', 'user_token=' . $this->session->data['user_token'], true),
				'permission' => 'setting/setting',
				'children' => []
			];
		}
		
		//-----------------------------------------------------
		// (13.2) قسم المستخدمين والصلاحيات (Users & Permissions)
		//-----------------------------------------------------
		$users_permissions = [];

		/**
		 * @Screen: المستخدمين (Users)
		 * @Users: مديرو النظام
		 * @Objective: إنشاء وتعديل وحذف حسابات المستخدمين الذين يمكنهم الوصول إلى لوحة التحكم.
		 */
		if ($this->user->hasPermission('access', 'user/user')) {
			$users_permissions[] = ['name' => $this->language->get('text_users_management'), 'href' => $this->url->link('user/user', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'user/user'];
		}

		/**
		 * @Screen: مجموعات المستخدمين (User Groups / Roles)
		 * @Users: مديرو النظام
		 * @Objective: إنشاء أدوار وظيفية (مثل محاسب، بائع) وتحديد الصلاحيات الممنوحة لكل دور.
		 */
		if ($this->user->hasPermission('access', 'user/user_group')) {
			$users_permissions[] = ['name' => $this->language->get('text_user_groups_roles'), 'href' => $this->url->link('user/user_group', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'user/user_group'];
		}

		// إضافة إعدادات المصادقة الثنائية
		if ($this->user->hasPermission('access', 'user/two_factor_setup')) {
			$users_permissions[] = ['name' => $this->language->get('text_two_factor_setup'), 'href' => $this->url->link('user/two_factor_setup', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'user/two_factor_setup'];
		}
		
		if ($users_permissions) {
			$system_settings[] = ['name' => $this->language->get('text_users_permissions_section'), 'children' => $users_permissions];
		}

		//-----------------------------------------------------
		// (13.3) قسم التوطين (Localisation)
		//-----------------------------------------------------
		$localisation = [];

		/**
		 * @Screen: اللغات والعملات (Languages & Currencies)
		 * @Users: مديرو النظام
		 * @Objective: إدارة اللغات والعملات المدعومة في النظام وتحديد أسعار الصرف.
		 */
		if ($this->user->hasPermission('access', 'localisation/language')) { $localisation[] = ['name' => $this->language->get('text_languages'), 'href' => $this->url->link('localisation/language', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'localisation/language']; }
		if ($this->user->hasPermission('access', 'localisation/currency')) { $localisation[] = ['name' => $this->language->get('text_currencies'), 'href' => $this->url->link('localisation/currency', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'localisation/currency']; }
		
		/**
		 * @Screen: حالات الطلبات والمخزون (Order & Stock Statuses)
		 * @Users: مديرو النظام
		 * @Objective: تخصيص الحالات التي يمكن أن تمر بها الطلبات والمخزون لتناسب سير عمل الشركة.
		 */
		if ($this->user->hasPermission('access', 'localisation/order_status')) { $localisation[] = ['name' => $this->language->get('text_order_statuses'), 'href' => $this->url->link('localisation/order_status', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'localisation/order_status']; }
		if ($this->user->hasPermission('access', 'localisation/stock_status')) { $localisation[] = ['name' => $this->language->get('text_stock_statuses'), 'href' => $this->url->link('localisation/stock_status', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'localisation/stock_status']; }

		if ($localisation) {
			$system_settings[] = ['name' => $this->language->get('text_localisation_section'), 'children' => $localisation];
		}

		//-----------------------------------------------------
		// (13.4) قسم إدارة الفروع (Branch Management)
		//-----------------------------------------------------
		$branch_management = [];

		/**
		 * @Screen: فروع الشركة (Company Branches)
		 * @Users: مدير النظام
		 * @Objective: إدارة مواقع الفروع والمنافذ التابعة للشركة
		 */
		if ($this->user->hasPermission('access', 'setting/branches')) {
			$branch_management[] = ['name' => $this->language->get('text_company_branches'), 'href' => $this->url->link('setting/branches', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'setting/branches'];
		}

		/**
		 * @Screen: إعدادات الفرع (Branch Settings)
		 * @Users: مدير الفرع
		 * @Objective: تخصيص إعدادات خاصة بكل فرع
		 */
		if ($this->user->hasPermission('access', 'setting/branch_settings')) {
			$branch_management[] = ['name' => $this->language->get('text_branch_settings'), 'href' => $this->url->link('setting/branch_settings', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'setting/branch_settings'];
		}

		if ($branch_management) {
			$system_settings[] = ['name' => $this->language->get('text_branch_management_section'), 'children' => $branch_management];
		}

		//-----------------------------------------------------
		// (13.5) قسم الأدوات والصيانة (Tools & Maintenance)
		//-----------------------------------------------------
		$tools_maintenance = [];

		/**
		 * @Screen: النسخ الاحتياطي والاستعادة (Backup & Restore)
		 * @Users: مديرو النظام
		 * @Objective: إنشاء نسخ احتياطية من قاعدة البيانات والملفات واستعادتها عند الحاجة.
		 */
		if ($this->user->hasPermission('access', 'tool/backup')) {
			$tools_maintenance[] = ['name' => $this->language->get('text_backup_restore'), 'href' => $this->url->link('tool/backup', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'tool/backup'];
		}

		/**
		 * @Screen: سجل أخطاء النظام (Error Logs)
		 * @Users: مديرو النظام, الدعم الفني
		 * @Objective: مراجعة سجلات الأخطاء التي تحدث في النظام لتشخيص المشاكل وحلها.
		 */
		if ($this->user->hasPermission('access', 'tool/log')) {
			$tools_maintenance[] = ['name' => $this->language->get('text_system_error_logs'), 'href' => $this->url->link('tool/log', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'tool/log'];
		}

		if ($tools_maintenance) {
			$system_settings[] = ['name' => $this->language->get('text_tools_maintenance_section'), 'children' => $tools_maintenance];
		}

		// إضافة أدوات الهجرة والاستيراد
		$migration_tools = [];
		if ($this->user->hasPermission('access', 'migration/excel')) {
			$migration_tools[] = ['name' => $this->language->get('text_excel_import_export'), 'href' => $this->url->link('migration/excel', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'migration/excel'];
		}
		if ($this->user->hasPermission('access', 'migration/odoo')) {
			$migration_tools[] = ['name' => $this->language->get('text_odoo_migration'), 'href' => $this->url->link('migration/odoo', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'migration/odoo'];
		}
		if ($this->user->hasPermission('access', 'migration/shopify')) {
			$migration_tools[] = ['name' => $this->language->get('text_shopify_migration'), 'href' => $this->url->link('migration/shopify', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'migration/shopify'];
		}
		if ($this->user->hasPermission('access', 'migration/woocommerce')) {
			$migration_tools[] = ['name' => $this->language->get('text_woocommerce_migration'), 'href' => $this->url->link('migration/woocommerce', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'migration/woocommerce'];
		}

		if (!empty($migration_tools)) {
			$system_settings[] = ['name' => $this->language->get('text_migration_tools_section'), 'children' => $migration_tools];
		}

		// إضافة إدارة الطوابير
		$queue_management = [];
		if ($this->user->hasPermission('access', 'queue/queue')) {
			$queue_management[] = ['name' => $this->language->get('text_queue_management'), 'href' => $this->url->link('queue/queue', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'queue/queue'];
		}

		if (!empty($queue_management)) {
			$system_settings[] = ['name' => $this->language->get('text_queue_management_section'), 'children' => $queue_management];
		}
		
		if ($system_settings) {
			$data['menus'][] = ['id' => 'menu-system-settings', 'icon' => 'fa-cogs', 'name' => $this->language->get('text_system_and_settings'), 'href' => '', 'children' => $system_settings];
		}
	}

	/**
	 * =======================================================================
	 * (14) نظام الاشتراك والدعم (Subscription & Support)
	 * =======================================================================
	 * @Objective: إدارة تفاصيل الاشتراك في النظام السحابي وطلب الدعم الفني.
	 * @Users: مدير النظام, المسؤول المالي.
	 */
	private function buildSubscriptionMenu(&$data) {
		$subscription_support = [];
		
		/**
		 * @Screen: معلومات الاشتراك (Subscription Information)
		 * @Users: مدير النظام
		 * @Objective: عرض تفاصيل الخطة الحالية، تاريخ التجديد، والاستخدام.
		 */
		if ($this->user->hasPermission('access', 'subscription/info')) {
			$subscription_support[] = ['name' => $this->language->get('text_subscription_information'), 'href' => $this->url->link('subscription/info', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'subscription/info'];
		}
		
		/**
		 * @Screen: الفواتير والمدفوعات (Billing & Payments)
		 * @Users: المسؤول المالي
		 * @Objective: عرض وإدارة فواتير الاشتراك وطرق الدفع.
		 */
		if ($this->user->hasPermission('access', 'subscription/billing')) {
			$subscription_support[] = ['name' => $this->language->get('text_subscription_billing_payments'), 'href' => $this->url->link('subscription/billing', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'subscription/billing'];
		}

		/**
		 * @Screen: طلب الدعم الفني (Request Support)
		 * @Users: جميع المستخدمين
		 * @Objective: فتح تذكرة دعم فني مباشرة مع فريق AYM ERP.
		 */
		if ($this->user->hasPermission('access', 'support/request')) {
			$subscription_support[] = ['name' => $this->language->get('text_request_technical_support'), 'href' => $this->url->link('support/request', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'support/request'];
		}

		/**
		 * @Screen: قاعدة المعرفة (Knowledge Base)
		 * @Users: جميع المستخدمين
		 * @Objective: تصفح المقالات والأدلة التعليمية لاستخدام النظام.
		 */
		if ($this->user->hasPermission('access', 'support/knowledge_base')) {
			$subscription_support[] = ['name' => $this->language->get('text_knowledge_base'), 'href' => $this->url->link('support/knowledge_base', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'support/knowledge_base'];
		}

		if ($subscription_support) {
			$data['menus'][] = ['id' => 'menu-subscription-support', 'icon' => 'fa-cloud', 'name' => $this->language->get('text_subscription_support'), 'href' => '', 'children' => $subscription_support];
		}
	}

	/**
	 * =======================================================================
	 * (13) نظام التسويق المتقدم (Advanced Marketing)
	 * =======================================================================
	 * @Objective: إدارة حملات التسويق الرقمي والتقليدي مع أدوات متقدمة للتحليل والاستهداف.
	 * @Workflow: Campaign Planning -> Execution -> Analysis -> Optimization.
	 * @Accounting_Impact: Creates entries for marketing expenses and tracks ROI.
	 */
	private function buildAdvancedMarketingSystem(&$data) {
		$advanced_marketing = [];

		//-----------------------------------------------------
		// (13.1) قسم الحملات الرقمية (Digital Campaigns)
		//-----------------------------------------------------
		$digital_campaigns = [];

		/**
		 * @Screen: حملات WhatsApp (WhatsApp Campaigns)
		 * @Users: مدير التسويق
		 * @Objective: إدارة حملات التسويق عبر WhatsApp.
		 */
		if ($this->user->hasPermission('access', 'marketing/whatsapp')) {
			$digital_campaigns[] = ['name' => $this->language->get('text_marketing_whatsapp'), 'href' => $this->url->link('marketing/whatsapp', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/whatsapp'];
		}

		/**
		 * @Screen: حملات البريد الإلكتروني (Email Campaigns)
		 * @Users: مدير التسويق
		 * @Objective: إدارة حملات التسويق عبر البريد الإلكتروني.
		 */
		if ($this->user->hasPermission('access', 'marketing/email')) {
			$digital_campaigns[] = ['name' => $this->language->get('text_marketing_email'), 'href' => $this->url->link('marketing/email', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/email'];
		}

		/**
		 * @Screen: تحليلات التسويق (Marketing Analytics)
		 * @Users: مدير التسويق, المحللون
		 * @Objective: تحليل أداء الحملات التسويقية.
		 */
		if ($this->user->hasPermission('access', 'marketing/analytics')) {
			$digital_campaigns[] = ['name' => $this->language->get('text_marketing_analytics'), 'href' => $this->url->link('marketing/analytics', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/analytics'];
		}

		if ($digital_campaigns) {
			$advanced_marketing[] = ['name' => $this->language->get('text_digital_campaigns_section'), 'children' => $digital_campaigns];
		}

		//-----------------------------------------------------
		// (13.2) قسم الكوبونات والعروض (Coupons & Offers)
		//-----------------------------------------------------
		$coupons_offers = [];

		/**
		 * @Screen: الكوبونات المتقدمة (Advanced Coupons)
		 * @Users: مدير التسويق
		 * @Objective: إدارة الكوبونات والخصومات المتقدمة.
		 */
		if ($this->user->hasPermission('access', 'marketing/coupon')) {
			$coupons_offers[] = ['name' => $this->language->get('text_advanced_coupons'), 'href' => $this->url->link('marketing/coupon', 'user_token=' . $this->session->data['user_token'], true), 'permission' => 'marketing/coupon'];
		}

		if ($coupons_offers) {
			$advanced_marketing[] = ['name' => $this->language->get('text_coupons_offers_section'), 'children' => $coupons_offers];
		}

		if ($advanced_marketing) {
			$data['menus'][] = ['id' => 'menu-advanced-marketing', 'icon' => 'fa-bullhorn', 'name' => $this->language->get('text_advanced_marketing'), 'href' => '', 'children' => $advanced_marketing];
		}
	}
	
    /**
     * دالة فلترة القائمة النهائية بناءً على صلاحيات المستخدم
     */
    private function filterPermittedMenus($menus) {
        $filtered_menus = [];
        foreach ($menus as $menu) {
            if (!empty($menu['children'])) {
                $children = $this->filterPermittedMenus($menu['children']);
                if (!empty($children)) {
                    $menu['children'] = $children;
                    $filtered_menus[] = $menu;
                }
            } else {
                if (isset($menu['permission']) && $this->user->hasPermission('access', $menu['permission'])) {
                    $filtered_menus[] = $menu;
                } elseif (!isset($menu['permission'])) {
                    $filtered_menus[] = $menu;
                }
            }
        }
        return $filtered_menus;
    }
}
