📄 Route: queue/queue
📂 Controller: controller\queue\queue.php
🧱 Models used (1):
   - queue/queue
🎨 Twig templates (1):
   - view\template\queue\queue.twig
🈯 Arabic Language Files (1):
   - language\ar\queue\queue.php
🇬🇧 English Language Files (1):
   - language\en-gb\queue\queue.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_cancel_success
   - text_cleanup_success
   - text_home
   - text_process_success
   - text_reset_success
   - text_retry_success

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_cancel_success
   - text_cleanup_success
   - text_home
   - text_process_success
   - text_reset_success
   - text_retry_success

❌ Missing in English:
   - error_permission
   - heading_title
   - text_cancel_success
   - text_cleanup_success
   - text_home
   - text_process_success
   - text_reset_success
   - text_retry_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_cancel_success = ""  # TODO: ترجمة عربية
   - text_cleanup_success = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_process_success = ""  # TODO: ترجمة عربية
   - text_reset_success = ""  # TODO: ترجمة عربية
   - text_retry_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_cancel_success = ""  # TODO: English translation
   - text_cleanup_success = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_process_success = ""  # TODO: English translation
   - text_reset_success = ""  # TODO: English translation
   - text_retry_success = ""  # TODO: English translation
