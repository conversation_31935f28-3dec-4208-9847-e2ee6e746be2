📄 Route: common/header_new
📂 Controller: controller\common\header_new.php
🧱 Models used (10):
   - common/security
   - communication/message
   - communication/unified_notification
   - core/central_service_manager
   - inventory/product
   - sale/order
   - tool/image
   - user/user
   - workflow/approval
   - workflow/task
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\common\header.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - code
   - direction
   - error_invalid_request
   - error_loading_header_data
   - error_marking_notification
   - error_user_not_logged
   - text_expiry_alert
   - text_logged
   - text_low_stock_alert
   - text_notification_marked_read
   - text_overdue_task
   - text_product_expiring
   - text_product_low_stock
   - text_security
   - text_task_overdue
   - text_task_upcoming
   - text_upcoming_task

❌ Missing in Arabic:
   - code
   - direction
   - error_invalid_request
   - error_loading_header_data
   - error_marking_notification
   - error_user_not_logged
   - text_expiry_alert
   - text_logged
   - text_low_stock_alert
   - text_notification_marked_read
   - text_overdue_task
   - text_product_expiring
   - text_product_low_stock
   - text_security
   - text_task_overdue
   - text_task_upcoming
   - text_upcoming_task

❌ Missing in English:
   - code
   - direction
   - error_invalid_request
   - error_loading_header_data
   - error_marking_notification
   - error_user_not_logged
   - text_expiry_alert
   - text_logged
   - text_low_stock_alert
   - text_notification_marked_read
   - text_overdue_task
   - text_product_expiring
   - text_product_low_stock
   - text_security
   - text_task_overdue
   - text_task_upcoming
   - text_upcoming_task

💡 Suggested Arabic Additions:
   - code = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_loading_header_data = ""  # TODO: ترجمة عربية
   - error_marking_notification = ""  # TODO: ترجمة عربية
   - error_user_not_logged = ""  # TODO: ترجمة عربية
   - text_expiry_alert = ""  # TODO: ترجمة عربية
   - text_logged = ""  # TODO: ترجمة عربية
   - text_low_stock_alert = ""  # TODO: ترجمة عربية
   - text_notification_marked_read = ""  # TODO: ترجمة عربية
   - text_overdue_task = ""  # TODO: ترجمة عربية
   - text_product_expiring = ""  # TODO: ترجمة عربية
   - text_product_low_stock = ""  # TODO: ترجمة عربية
   - text_security = ""  # TODO: ترجمة عربية
   - text_task_overdue = ""  # TODO: ترجمة عربية
   - text_task_upcoming = ""  # TODO: ترجمة عربية
   - text_upcoming_task = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - code = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_loading_header_data = ""  # TODO: English translation
   - error_marking_notification = ""  # TODO: English translation
   - error_user_not_logged = ""  # TODO: English translation
   - text_expiry_alert = ""  # TODO: English translation
   - text_logged = ""  # TODO: English translation
   - text_low_stock_alert = ""  # TODO: English translation
   - text_notification_marked_read = ""  # TODO: English translation
   - text_overdue_task = ""  # TODO: English translation
   - text_product_expiring = ""  # TODO: English translation
   - text_product_low_stock = ""  # TODO: English translation
   - text_security = ""  # TODO: English translation
   - text_task_overdue = ""  # TODO: English translation
   - text_task_upcoming = ""  # TODO: English translation
   - text_upcoming_task = ""  # TODO: English translation
