📄 Route: extension/module/pp_braintree_button
📂 Controller: controller\extension\module\pp_braintree_button.php
🧱 Models used (3):
   - setting/extension
   - setting/setting
   - user/user_group
🎨 Twig templates (1):
   - view\template\extension\module\pp_braintree_button.twig
🈯 Arabic Language Files (2):
   - language\ar\extension\extension\module.php
   - language\ar\extension\module\pp_braintree_button.php
🇬🇧 English Language Files (2):
   - language\en-gb\extension\extension\module.php
   - language\en-gb\extension\module\pp_braintree_button.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
