📄 Route: communication/announcements
📂 Controller: controller\communication\announcements.php
🧱 Models used (6):
   - communication/announcements
   - communication/unified_notification
   - core/central_service_manager
   - user/user
   - user/user_group
   - workflow/automation
🎨 Twig templates (1):
   - view\template\communication\announcements.twig
🈯 Arabic Language Files (1):
   - language\ar\communication\announcements.php
🇬🇧 English Language Files (1):
   - language\en-gb\communication\announcements.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_content
   - error_no_targets
   - error_permission
   - error_title
   - heading_title
   - text_add
   - text_analytics
   - text_bulk_send
   - text_bulk_success
   - text_home
   - text_success
   - text_type_catalog
   - text_type_general
   - text_type_inventory
   - text_type_maintenance
   - text_type_system
   - text_type_urgent

❌ Missing in Arabic:
   - error_content
   - error_no_targets
   - error_permission
   - error_title
   - heading_title
   - text_add
   - text_analytics
   - text_bulk_send
   - text_bulk_success
   - text_home
   - text_success
   - text_type_catalog
   - text_type_general
   - text_type_inventory
   - text_type_maintenance
   - text_type_system
   - text_type_urgent

❌ Missing in English:
   - error_content
   - error_no_targets
   - error_permission
   - error_title
   - heading_title
   - text_add
   - text_analytics
   - text_bulk_send
   - text_bulk_success
   - text_home
   - text_success
   - text_type_catalog
   - text_type_general
   - text_type_inventory
   - text_type_maintenance
   - text_type_system
   - text_type_urgent

💡 Suggested Arabic Additions:
   - error_content = ""  # TODO: ترجمة عربية
   - error_no_targets = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_title = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_analytics = ""  # TODO: ترجمة عربية
   - text_bulk_send = ""  # TODO: ترجمة عربية
   - text_bulk_success = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_type_catalog = ""  # TODO: ترجمة عربية
   - text_type_general = ""  # TODO: ترجمة عربية
   - text_type_inventory = ""  # TODO: ترجمة عربية
   - text_type_maintenance = ""  # TODO: ترجمة عربية
   - text_type_system = ""  # TODO: ترجمة عربية
   - text_type_urgent = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_content = ""  # TODO: English translation
   - error_no_targets = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_title = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_analytics = ""  # TODO: English translation
   - text_bulk_send = ""  # TODO: English translation
   - text_bulk_success = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_type_catalog = ""  # TODO: English translation
   - text_type_general = ""  # TODO: English translation
   - text_type_inventory = ""  # TODO: English translation
   - text_type_maintenance = ""  # TODO: English translation
   - text_type_system = ""  # TODO: English translation
   - text_type_urgent = ""  # TODO: English translation
