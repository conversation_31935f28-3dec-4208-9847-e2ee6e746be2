📄 Route: marketplace/modification
📂 Controller: controller\marketplace\modification.php
🧱 Models used (2):
   - setting/modification
   - setting/setting
🎨 Twig templates (1):
   - view\template\marketplace\modification.twig
🈯 Arabic Language Files (1):
   - language\ar\marketplace\modification.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketplace\modification.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - date_format_short
   - error_permission
   - heading_title
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
