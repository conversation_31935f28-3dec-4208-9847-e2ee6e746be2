📄 Route: workflow/actions
📂 Controller: controller\workflow\actions.php
🧱 Models used (6):
   ❌ workflow/actions (0 functions)
   ✅ communication/messages (14 functions)
   ✅ core/central_service_manager (60 functions)
   ❌ workflow/database (0 functions)
   ❌ ai/analysis (0 functions)
   ✅ logging/user_activity (13 functions)
🎨 Twig templates (1):
   ✅ view\template\workflow\actions.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\workflow\actions.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\workflow\actions.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (184):
   - action
   - column_left
   - error_add
   - error_permission
   - examples_add_product
   - examples_register_order
   - recent_actions
   - text_add
   - text_backup_data
   - text_business_logic
   - text_calculate_pricing
   - text_customer_classification
   - text_generate_pdf_desc
   - text_integration_actions
   - text_inventory_management
   - text_recent_actions
   - text_save_action
   - text_update_record
   - text_upload_product_image
   - text_webhook_send
   ... و 164 متغير آخر

❌ Missing in Arabic (184):
   - action
   - error_add
   - error_permission
   - examples_add_product
   - examples_register_order
   - text_backup_data
   - text_business_logic
   - text_calculate_pricing
   - text_customer_classification
   - text_generate_pdf_desc
   - text_integration_actions
   - text_inventory_management
   - text_recent_actions
   - text_upload_product_image
   - text_webhook_send
   ... و 169 متغير آخر

❌ Missing in English (184):
   - action
   - error_add
   - error_permission
   - examples_add_product
   - examples_register_order
   - text_backup_data
   - text_business_logic
   - text_calculate_pricing
   - text_customer_classification
   - text_generate_pdf_desc
   - text_integration_actions
   - text_inventory_management
   - text_recent_actions
   - text_upload_product_image
   - text_webhook_send
   ... و 169 متغير آخر

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 184 items
      - text_backup_data
      - error_add
      - text_webhook_send
      - text_generate_pdf_desc
      - action
   🟡 MISSING_ENGLISH_VARIABLES: 184 items
      - text_backup_data
      - error_add
      - text_webhook_send
      - text_generate_pdf_desc
      - action
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template
   🟢 MISSING_MODEL_FILES: 3 items
      - workflow/actions
      - workflow/database
      - ai/analysis

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 184 متغير عربي و 184 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 3 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:20
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.