📄 Route: purchase/supplier_invoice_pdf
📂 Controller: controller\purchase\supplier_invoice_pdf.php
🧱 Models used (1):
   - purchase/supplier_invoice
🎨 Twig templates (1):
   - view\template\purchase\supplier_invoice_pdf.twig
🈯 Arabic Language Files (1):
   - language\ar\purchase\supplier_invoice.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_product
   - column_quantity
   - column_total
   - column_unit
   - column_unit_price
   - date_format_short
   - error_delete_journal_linked
   - error_delete_paid_invoice
   - error_delete_status
   - error_invalid_status_approval
   - error_invalid_status_rejection
   - error_invoice_not_found
   - error_rejection_reason_required
   - text_due_date
   - text_history_approved
   - text_history_cancelled
   - text_history_created
   - text_history_paid
   - text_history_partially_paid
   - text_history_rejected
   - text_history_status_changed
   - text_history_updated
   - text_invoice
   - text_invoice_date
   - text_journal_supplier_invoice
   - text_notes
   - text_payment_recorded
   - text_payment_voided
   - text_po_number
   - text_status
   - text_status_approved
   - text_status_cancelled
   - text_status_paid
   - text_status_partially_paid
   - text_status_pending_approval
   - text_status_rejected
   - text_subtotal
   - text_supplier
   - text_tax
   - text_total

❌ Missing in Arabic:
   - column_product
   - column_quantity
   - column_total
   - column_unit
   - column_unit_price
   - date_format_short
   - error_delete_journal_linked
   - error_delete_paid_invoice
   - error_delete_status
   - error_invalid_status_approval
   - error_invalid_status_rejection
   - error_invoice_not_found
   - error_rejection_reason_required
   - text_due_date
   - text_history_approved
   - text_history_cancelled
   - text_history_created
   - text_history_paid
   - text_history_partially_paid
   - text_history_rejected
   - text_history_status_changed
   - text_history_updated
   - text_invoice
   - text_invoice_date
   - text_journal_supplier_invoice
   - text_notes
   - text_payment_recorded
   - text_payment_voided
   - text_po_number
   - text_status
   - text_status_approved
   - text_status_cancelled
   - text_status_paid
   - text_status_partially_paid
   - text_status_pending_approval
   - text_status_rejected
   - text_subtotal
   - text_supplier
   - text_tax
   - text_total

❌ Missing in English:
   - column_product
   - column_quantity
   - column_total
   - column_unit
   - column_unit_price
   - date_format_short
   - error_delete_journal_linked
   - error_delete_paid_invoice
   - error_delete_status
   - error_invalid_status_approval
   - error_invalid_status_rejection
   - error_invoice_not_found
   - error_rejection_reason_required
   - text_due_date
   - text_history_approved
   - text_history_cancelled
   - text_history_created
   - text_history_paid
   - text_history_partially_paid
   - text_history_rejected
   - text_history_status_changed
   - text_history_updated
   - text_invoice
   - text_invoice_date
   - text_journal_supplier_invoice
   - text_notes
   - text_payment_recorded
   - text_payment_voided
   - text_po_number
   - text_status
   - text_status_approved
   - text_status_cancelled
   - text_status_paid
   - text_status_partially_paid
   - text_status_pending_approval
   - text_status_rejected
   - text_subtotal
   - text_supplier
   - text_tax
   - text_total

💡 Suggested Arabic Additions:
   - column_product = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_total = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - column_unit_price = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_delete_journal_linked = ""  # TODO: ترجمة عربية
   - error_delete_paid_invoice = ""  # TODO: ترجمة عربية
   - error_delete_status = ""  # TODO: ترجمة عربية
   - error_invalid_status_approval = ""  # TODO: ترجمة عربية
   - error_invalid_status_rejection = ""  # TODO: ترجمة عربية
   - error_invoice_not_found = ""  # TODO: ترجمة عربية
   - error_rejection_reason_required = ""  # TODO: ترجمة عربية
   - text_due_date = ""  # TODO: ترجمة عربية
   - text_history_approved = ""  # TODO: ترجمة عربية
   - text_history_cancelled = ""  # TODO: ترجمة عربية
   - text_history_created = ""  # TODO: ترجمة عربية
   - text_history_paid = ""  # TODO: ترجمة عربية
   - text_history_partially_paid = ""  # TODO: ترجمة عربية
   - text_history_rejected = ""  # TODO: ترجمة عربية
   - text_history_status_changed = ""  # TODO: ترجمة عربية
   - text_history_updated = ""  # TODO: ترجمة عربية
   - text_invoice = ""  # TODO: ترجمة عربية
   - text_invoice_date = ""  # TODO: ترجمة عربية
   - text_journal_supplier_invoice = ""  # TODO: ترجمة عربية
   - text_notes = ""  # TODO: ترجمة عربية
   - text_payment_recorded = ""  # TODO: ترجمة عربية
   - text_payment_voided = ""  # TODO: ترجمة عربية
   - text_po_number = ""  # TODO: ترجمة عربية
   - text_status = ""  # TODO: ترجمة عربية
   - text_status_approved = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_paid = ""  # TODO: ترجمة عربية
   - text_status_partially_paid = ""  # TODO: ترجمة عربية
   - text_status_pending_approval = ""  # TODO: ترجمة عربية
   - text_status_rejected = ""  # TODO: ترجمة عربية
   - text_subtotal = ""  # TODO: ترجمة عربية
   - text_supplier = ""  # TODO: ترجمة عربية
   - text_tax = ""  # TODO: ترجمة عربية
   - text_total = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_product = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_total = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - column_unit_price = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_delete_journal_linked = ""  # TODO: English translation
   - error_delete_paid_invoice = ""  # TODO: English translation
   - error_delete_status = ""  # TODO: English translation
   - error_invalid_status_approval = ""  # TODO: English translation
   - error_invalid_status_rejection = ""  # TODO: English translation
   - error_invoice_not_found = ""  # TODO: English translation
   - error_rejection_reason_required = ""  # TODO: English translation
   - text_due_date = ""  # TODO: English translation
   - text_history_approved = ""  # TODO: English translation
   - text_history_cancelled = ""  # TODO: English translation
   - text_history_created = ""  # TODO: English translation
   - text_history_paid = ""  # TODO: English translation
   - text_history_partially_paid = ""  # TODO: English translation
   - text_history_rejected = ""  # TODO: English translation
   - text_history_status_changed = ""  # TODO: English translation
   - text_history_updated = ""  # TODO: English translation
   - text_invoice = ""  # TODO: English translation
   - text_invoice_date = ""  # TODO: English translation
   - text_journal_supplier_invoice = ""  # TODO: English translation
   - text_notes = ""  # TODO: English translation
   - text_payment_recorded = ""  # TODO: English translation
   - text_payment_voided = ""  # TODO: English translation
   - text_po_number = ""  # TODO: English translation
   - text_status = ""  # TODO: English translation
   - text_status_approved = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_paid = ""  # TODO: English translation
   - text_status_partially_paid = ""  # TODO: English translation
   - text_status_pending_approval = ""  # TODO: English translation
   - text_status_rejected = ""  # TODO: English translation
   - text_subtotal = ""  # TODO: English translation
   - text_supplier = ""  # TODO: English translation
   - text_tax = ""  # TODO: English translation
   - text_total = ""  # TODO: English translation
