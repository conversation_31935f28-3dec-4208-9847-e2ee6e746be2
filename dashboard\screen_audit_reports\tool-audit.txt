📄 Route: tool/audit
📂 Controller: controller\tool\audit.php
🧱 Models used (2):
   ✅ tool/audit (4 functions)
   ✅ user/user (47 functions)
🎨 Twig templates (1):
   ✅ view\template\tool\audit.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\tool\audit.php (13 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\tool\audit.php (13 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (12):
   - column_left
   - dashboard
   - error_permission
   - error_warning
   - filter_date_end
   - filter_date_start
   - footer
   - header
   - heading_title
   - success
   - text_home
   - user_token

❌ Missing in Arabic (10):
   - column_left
   - dashboard
   - error_warning
   - filter_date_end
   - filter_date_start
   - footer
   - header
   - success
   - text_home
   - user_token

❌ Missing in English (10):
   - column_left
   - dashboard
   - error_warning
   - filter_date_end
   - filter_date_start
   - footer
   - header
   - success
   - text_home
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 10 items
      - error_warning
      - user_token
      - column_left
      - filter_date_end
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 10 items
      - error_warning
      - user_token
      - column_left
      - filter_date_end
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 10 متغير عربي و 10 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.