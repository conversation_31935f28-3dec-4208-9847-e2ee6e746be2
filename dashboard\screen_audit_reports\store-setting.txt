📄 Route: store/setting
📂 Controller: controller\store\setting.php
🧱 Models used (2):
   ✅ setting/extension (11 functions)
   ✅ setting/module (7 functions)
🎨 Twig templates (1):
   ✅ view\template\store\setting.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\store\setting.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\store\setting.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (12):
   - button_add
   - button_delete
   - button_edit
   - column_action
   - column_left
   - column_name
   - error_permission
   - error_warning
   - footer
   - header
   - success
   - text_store_settingx

❌ Missing in Arabic (12):
   - button_add
   - button_delete
   - button_edit
   - column_action
   - column_left
   - column_name
   - error_permission
   - error_warning
   - footer
   - header
   - success
   - text_store_settingx

❌ Missing in English (12):
   - button_add
   - button_delete
   - button_edit
   - column_action
   - column_left
   - column_name
   - error_permission
   - error_warning
   - footer
   - header
   - success
   - text_store_settingx

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 12 items
      - error_warning
      - button_delete
      - column_left
      - button_add
      - column_name
   🟡 MISSING_ENGLISH_VARIABLES: 12 items
      - error_warning
      - button_delete
      - column_left
      - button_add
      - column_name

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 12 متغير عربي و 12 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.