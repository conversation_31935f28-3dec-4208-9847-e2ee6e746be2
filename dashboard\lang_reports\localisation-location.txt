📄 Route: localisation/location
📂 Controller: controller\localisation\location.php
🧱 Models used (3):
   - localisation/location
   - setting/store
   - tool/image
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\localisation\location.php
🇬🇧 English Language Files (1):
   - language\en-gb\localisation\location.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_address
   - error_name
   - error_permission
   - error_telephone
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_address
   - error_name
   - error_permission
   - error_telephone
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_address
   - error_name
   - error_permission
   - error_telephone
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_address = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_telephone = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_address = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_telephone = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
