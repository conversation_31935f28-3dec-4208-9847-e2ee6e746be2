📄 Route: common/login_new
📂 Controller: controller\common\login_new.php
🧱 Models used (3):
   - core/central_service_manager
   - user/two_factor
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\common\login.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\login.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_login
   - code
   - direction
   - error_login
   - heading_title
   - text_forgotten
   - text_home
   - text_login
   - text_password
   - text_remember
   - text_username

❌ Missing in Arabic:
   - button_login
   - code
   - direction
   - error_login
   - heading_title
   - text_forgotten
   - text_home
   - text_login
   - text_password
   - text_remember
   - text_username

❌ Missing in English:
   - button_login
   - code
   - direction
   - error_login
   - heading_title
   - text_forgotten
   - text_home
   - text_login
   - text_password
   - text_remember
   - text_username

💡 Suggested Arabic Additions:
   - button_login = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - error_login = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_forgotten = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_login = ""  # TODO: ترجمة عربية
   - text_password = ""  # TODO: ترجمة عربية
   - text_remember = ""  # TODO: ترجمة عربية
   - text_username = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_login = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - error_login = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_forgotten = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_login = ""  # TODO: English translation
   - text_password = ""  # TODO: English translation
   - text_remember = ""  # TODO: English translation
   - text_username = ""  # TODO: English translation
