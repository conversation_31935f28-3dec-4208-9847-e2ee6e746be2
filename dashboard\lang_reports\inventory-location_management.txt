📄 Route: inventory/location_management
📂 Controller: controller\inventory\location_management.php
🧱 Models used (7):
   - branch/branch
   - core/central_service_manager
   - inventory/location_management
   - inventory/location_management_enhanced
   - inventory/warehouse
   - setting/setting
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (2):
   - language\ar\common\header.php
   - language\ar\inventory\location_management.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_location_code
   - error_location_code_exists
   - error_location_has_products
   - error_location_type
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_all
   - text_barcode_scanner
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_location_map
   - text_none
   - text_pagination
   - text_success
   - text_usage_report

❌ Missing in Arabic:
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_location_code
   - error_location_code_exists
   - error_location_has_products
   - error_location_type
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_all
   - text_barcode_scanner
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_location_map
   - text_none
   - text_pagination
   - text_success
   - text_usage_report

❌ Missing in English:
   - date_format_short
   - error_advanced_permission
   - error_exception
   - error_location_code
   - error_location_code_exists
   - error_location_has_products
   - error_location_type
   - error_name
   - error_permission
   - heading_title
   - text_add
   - text_all
   - text_barcode_scanner
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_location_map
   - text_none
   - text_pagination
   - text_success
   - text_usage_report

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_location_code = ""  # TODO: ترجمة عربية
   - error_location_code_exists = ""  # TODO: ترجمة عربية
   - error_location_has_products = ""  # TODO: ترجمة عربية
   - error_location_type = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_barcode_scanner = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_location_map = ""  # TODO: ترجمة عربية
   - text_none = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_usage_report = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_location_code = ""  # TODO: English translation
   - error_location_code_exists = ""  # TODO: English translation
   - error_location_has_products = ""  # TODO: English translation
   - error_location_type = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_barcode_scanner = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_location_map = ""  # TODO: English translation
   - text_none = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_usage_report = ""  # TODO: English translation
