📄 Route: governance/risk_register
📂 Controller: controller\governance\risk_register.php
🧱 Models used (2):
   - governance/risk_register
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\governance\risk_register.php
🇬🇧 English Language Files (1):
   - language\en-gb\governance\risk_register.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_no_permission
   - heading_title
   - text_home
   - text_list

❌ Missing in Arabic:
   - error_no_permission
   - heading_title
   - text_home
   - text_list

❌ Missing in English:
   - error_no_permission
   - heading_title
   - text_home
   - text_list

💡 Suggested Arabic Additions:
   - error_no_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_no_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
