📄 Route: finance/checks
📂 Controller: controller\finance\checks.php
🧱 Models used (4):
   - bank/bank
   - customer/customer
   - finance/checks
   - supplier/supplier
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_amount
   - error_bank
   - error_bank_account
   - error_bounce_date
   - error_bounce_reason
   - error_check
   - error_check_date
   - error_check_number
   - error_collection_date
   - error_deposit_date
   - error_drawer_name
   - error_due_date
   - error_permission
   - error_selected_checks
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_success
   - text_success_bounced
   - text_success_collected
   - text_success_deposited

❌ Missing in Arabic:
   - date_format_short
   - error_amount
   - error_bank
   - error_bank_account
   - error_bounce_date
   - error_bounce_reason
   - error_check
   - error_check_date
   - error_check_number
   - error_collection_date
   - error_deposit_date
   - error_drawer_name
   - error_due_date
   - error_permission
   - error_selected_checks
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_success
   - text_success_bounced
   - text_success_collected
   - text_success_deposited

❌ Missing in English:
   - date_format_short
   - error_amount
   - error_bank
   - error_bank_account
   - error_bounce_date
   - error_bounce_reason
   - error_check
   - error_check_date
   - error_check_number
   - error_collection_date
   - error_deposit_date
   - error_drawer_name
   - error_due_date
   - error_permission
   - error_selected_checks
   - heading_title
   - text_add
   - text_edit
   - text_home
   - text_success
   - text_success_bounced
   - text_success_collected
   - text_success_deposited

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_amount = ""  # TODO: ترجمة عربية
   - error_bank = ""  # TODO: ترجمة عربية
   - error_bank_account = ""  # TODO: ترجمة عربية
   - error_bounce_date = ""  # TODO: ترجمة عربية
   - error_bounce_reason = ""  # TODO: ترجمة عربية
   - error_check = ""  # TODO: ترجمة عربية
   - error_check_date = ""  # TODO: ترجمة عربية
   - error_check_number = ""  # TODO: ترجمة عربية
   - error_collection_date = ""  # TODO: ترجمة عربية
   - error_deposit_date = ""  # TODO: ترجمة عربية
   - error_drawer_name = ""  # TODO: ترجمة عربية
   - error_due_date = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_selected_checks = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_success_bounced = ""  # TODO: ترجمة عربية
   - text_success_collected = ""  # TODO: ترجمة عربية
   - text_success_deposited = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_amount = ""  # TODO: English translation
   - error_bank = ""  # TODO: English translation
   - error_bank_account = ""  # TODO: English translation
   - error_bounce_date = ""  # TODO: English translation
   - error_bounce_reason = ""  # TODO: English translation
   - error_check = ""  # TODO: English translation
   - error_check_date = ""  # TODO: English translation
   - error_check_number = ""  # TODO: English translation
   - error_collection_date = ""  # TODO: English translation
   - error_deposit_date = ""  # TODO: English translation
   - error_drawer_name = ""  # TODO: English translation
   - error_due_date = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_selected_checks = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_success_bounced = ""  # TODO: English translation
   - text_success_collected = ""  # TODO: English translation
   - text_success_deposited = ""  # TODO: English translation
