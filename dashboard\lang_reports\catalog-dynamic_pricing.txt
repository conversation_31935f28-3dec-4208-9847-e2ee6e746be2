📄 Route: catalog/dynamic_pricing
📂 Controller: controller\catalog\dynamic_pricing.php
🧱 Models used (1):
   - catalog/product
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\catalog\dynamic_pricing.php
🇬🇧 English Language Files (1):
   - language\en-gb\catalog\dynamic_pricing.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_cancel
   - button_save
   - entry_condition_type
   - entry_condition_value
   - entry_date_end
   - entry_date_start
   - entry_formula
   - entry_name
   - entry_priority
   - entry_status
   - entry_type
   - entry_value
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_name
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in Arabic:
   - button_cancel
   - button_save
   - entry_condition_type
   - entry_condition_value
   - entry_date_end
   - entry_date_start
   - entry_formula
   - entry_name
   - entry_priority
   - entry_status
   - entry_type
   - entry_value
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_name
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in English:
   - button_cancel
   - button_save
   - entry_condition_type
   - entry_condition_value
   - entry_date_end
   - entry_date_start
   - entry_formula
   - entry_name
   - entry_priority
   - entry_status
   - entry_type
   - entry_value
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_name
   - error_permission
   - error_quantity_must_be_positive
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - entry_condition_type = ""  # TODO: ترجمة عربية
   - entry_condition_value = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - entry_date_start = ""  # TODO: ترجمة عربية
   - entry_formula = ""  # TODO: ترجمة عربية
   - entry_name = ""  # TODO: ترجمة عربية
   - entry_priority = ""  # TODO: ترجمة عربية
   - entry_status = ""  # TODO: ترجمة عربية
   - entry_type = ""  # TODO: ترجمة عربية
   - entry_value = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_cancel = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - entry_condition_type = ""  # TODO: English translation
   - entry_condition_value = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - entry_date_start = ""  # TODO: English translation
   - entry_formula = ""  # TODO: English translation
   - entry_name = ""  # TODO: English translation
   - entry_priority = ""  # TODO: English translation
   - entry_status = ""  # TODO: English translation
   - entry_type = ""  # TODO: English translation
   - entry_value = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
