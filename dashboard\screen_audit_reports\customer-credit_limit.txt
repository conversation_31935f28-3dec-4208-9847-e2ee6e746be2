📄 Route: customer/credit_limit
📂 Controller: controller\customer\credit_limit.php
🧱 Models used (1):
   ✅ customer/customer (51 functions)
🎨 Twig templates (1):
   ✅ view\template\customer\credit_limit.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\customer\credit_limit.php (25 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\customer\credit_limit.php (25 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (26):
   - add
   - button_add
   - column_available_credit
   - column_credit_limit
   - column_current_balance
   - column_customer
   - column_email
   - column_left
   - column_telephone
   - error_credit_limit
   - error_permission
   - error_warning
   - footer
   - header
   - success
   - text_add
   - text_home
   - text_list
   - text_success
   - user_token
   ... و 6 متغير آخر

❌ Missing in Arabic (11):
   - add
   - button_add
   - button_edit
   - column_left
   - error_warning
   - footer
   - header
   - success
   - text_customer
   - text_home
   - user_token

❌ Missing in English (11):
   - add
   - button_add
   - button_edit
   - column_left
   - error_warning
   - footer
   - header
   - success
   - text_customer
   - text_home
   - user_token

🗄️ Database Tables Used (1):
   ❌ credit_limit

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 11 items
      - error_warning
      - text_customer
      - user_token
      - column_left
      - button_add
   🟡 MISSING_ENGLISH_VARIABLES: 11 items
      - error_warning
      - text_customer
      - user_token
      - column_left
      - button_add
   🔴 INVALID_DATABASE_TABLES: 1 items
      - credit_limit

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 11 متغير عربي و 11 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:59
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.