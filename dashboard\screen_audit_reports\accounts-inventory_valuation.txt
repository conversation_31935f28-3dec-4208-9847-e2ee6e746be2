📄 Route: accounts/inventory_valuation
📂 Controller: controller\accounts\inventory_valuation.php
🧱 Models used (2):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/inventory_valuation (1 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\inventory_valuation.php (19 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\inventory_valuation.php (19 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (22):
   - button_filter
   - code
   - date_format_short
   - entry_date_end
   - entry_date_start
   - error_no_data
   - print_title
   - text_average_cost
   - text_closing_qty
   - text_form
   - text_from
   - text_in_qty
   - text_inventory_valuation
   - text_inventory_value
   - text_opening_qty
   - text_out_qty
   - text_period
   - text_product_name
   - text_to
   - text_total_value
   ... و 2 متغير آخر

❌ Missing in Arabic (3):
   - code
   - date_format_short
   - direction

❌ Missing in English (3):
   - code
   - date_format_short
   - direction

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - code
      - direction
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - code
      - direction
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:41
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.