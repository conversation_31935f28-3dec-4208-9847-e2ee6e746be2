📄 Route: common/profile
📂 Controller: controller\common\profile.php
🧱 Models used (2):
   - tool/image
   - user/user
🎨 Twig templates (1):
   - view\template\common\profile.twig
🈯 Arabic Language Files (1):
   - language\ar\common\profile.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\profile.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_confirm
   - error_email
   - error_exists_email
   - error_exists_username
   - error_firstname
   - error_lastname
   - error_password
   - error_permission
   - error_username
   - heading_title
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_confirm
   - error_email
   - error_exists_email
   - error_exists_username
   - error_firstname
   - error_lastname
   - error_password
   - error_permission
   - error_username
   - heading_title
   - text_home
   - text_success

❌ Missing in English:
   - error_confirm
   - error_email
   - error_exists_email
   - error_exists_username
   - error_firstname
   - error_lastname
   - error_password
   - error_permission
   - error_username
   - heading_title
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_confirm = ""  # TODO: ترجمة عربية
   - error_email = ""  # TODO: ترجمة عربية
   - error_exists_email = ""  # TODO: ترجمة عربية
   - error_exists_username = ""  # TODO: ترجمة عربية
   - error_firstname = ""  # TODO: ترجمة عربية
   - error_lastname = ""  # TODO: ترجمة عربية
   - error_password = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_username = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_confirm = ""  # TODO: English translation
   - error_email = ""  # TODO: English translation
   - error_exists_email = ""  # TODO: English translation
   - error_exists_username = ""  # TODO: English translation
   - error_firstname = ""  # TODO: English translation
   - error_lastname = ""  # TODO: English translation
   - error_password = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_username = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
