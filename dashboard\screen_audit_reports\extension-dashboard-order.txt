📄 Route: extension/dashboard/order
📂 Controller: controller\extension\dashboard\order.php
🧱 Models used (2):
   ✅ setting/setting (5 functions)
   ✅ sale/order (27 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\dashboard\order.php (9 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\dashboard\order.php (9 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (5):
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic (1):
   - text_home

❌ Missing in English (1):
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 1 items
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 1 items
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 1 متغير عربي و 1 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:21
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.