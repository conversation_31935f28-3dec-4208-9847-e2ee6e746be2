📄 Route: logging/performance
📂 Controller: controller\logging\performance.php
🧱 Models used (1):
   - logging/performance
🎨 Twig templates (1):
   - view\template\logging\performance.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_cache_optimization
   - text_cache_optimization_desc
   - text_catalog_optimization
   - text_catalog_optimization_desc
   - text_database_optimization
   - text_database_optimization_desc
   - text_detailed_analysis
   - text_home
   - text_index_optimization
   - text_index_optimization_desc
   - text_inventory_optimization
   - text_inventory_optimization_desc
   - text_module_catalog
   - text_module_communication
   - text_module_inventory
   - text_module_purchase
   - text_module_sales
   - text_module_workflow
   - text_operation_inventory_update
   - text_operation_order_processing
   - text_operation_product_search
   - text_operation_workflow_execution
   - text_optimization
   - text_optimization_completed
   - text_realtime_monitoring

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_cache_optimization
   - text_cache_optimization_desc
   - text_catalog_optimization
   - text_catalog_optimization_desc
   - text_database_optimization
   - text_database_optimization_desc
   - text_detailed_analysis
   - text_home
   - text_index_optimization
   - text_index_optimization_desc
   - text_inventory_optimization
   - text_inventory_optimization_desc
   - text_module_catalog
   - text_module_communication
   - text_module_inventory
   - text_module_purchase
   - text_module_sales
   - text_module_workflow
   - text_operation_inventory_update
   - text_operation_order_processing
   - text_operation_product_search
   - text_operation_workflow_execution
   - text_optimization
   - text_optimization_completed
   - text_realtime_monitoring

❌ Missing in English:
   - error_permission
   - heading_title
   - text_cache_optimization
   - text_cache_optimization_desc
   - text_catalog_optimization
   - text_catalog_optimization_desc
   - text_database_optimization
   - text_database_optimization_desc
   - text_detailed_analysis
   - text_home
   - text_index_optimization
   - text_index_optimization_desc
   - text_inventory_optimization
   - text_inventory_optimization_desc
   - text_module_catalog
   - text_module_communication
   - text_module_inventory
   - text_module_purchase
   - text_module_sales
   - text_module_workflow
   - text_operation_inventory_update
   - text_operation_order_processing
   - text_operation_product_search
   - text_operation_workflow_execution
   - text_optimization
   - text_optimization_completed
   - text_realtime_monitoring

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_cache_optimization = ""  # TODO: ترجمة عربية
   - text_cache_optimization_desc = ""  # TODO: ترجمة عربية
   - text_catalog_optimization = ""  # TODO: ترجمة عربية
   - text_catalog_optimization_desc = ""  # TODO: ترجمة عربية
   - text_database_optimization = ""  # TODO: ترجمة عربية
   - text_database_optimization_desc = ""  # TODO: ترجمة عربية
   - text_detailed_analysis = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_index_optimization = ""  # TODO: ترجمة عربية
   - text_index_optimization_desc = ""  # TODO: ترجمة عربية
   - text_inventory_optimization = ""  # TODO: ترجمة عربية
   - text_inventory_optimization_desc = ""  # TODO: ترجمة عربية
   - text_module_catalog = ""  # TODO: ترجمة عربية
   - text_module_communication = ""  # TODO: ترجمة عربية
   - text_module_inventory = ""  # TODO: ترجمة عربية
   - text_module_purchase = ""  # TODO: ترجمة عربية
   - text_module_sales = ""  # TODO: ترجمة عربية
   - text_module_workflow = ""  # TODO: ترجمة عربية
   - text_operation_inventory_update = ""  # TODO: ترجمة عربية
   - text_operation_order_processing = ""  # TODO: ترجمة عربية
   - text_operation_product_search = ""  # TODO: ترجمة عربية
   - text_operation_workflow_execution = ""  # TODO: ترجمة عربية
   - text_optimization = ""  # TODO: ترجمة عربية
   - text_optimization_completed = ""  # TODO: ترجمة عربية
   - text_realtime_monitoring = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_cache_optimization = ""  # TODO: English translation
   - text_cache_optimization_desc = ""  # TODO: English translation
   - text_catalog_optimization = ""  # TODO: English translation
   - text_catalog_optimization_desc = ""  # TODO: English translation
   - text_database_optimization = ""  # TODO: English translation
   - text_database_optimization_desc = ""  # TODO: English translation
   - text_detailed_analysis = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_index_optimization = ""  # TODO: English translation
   - text_index_optimization_desc = ""  # TODO: English translation
   - text_inventory_optimization = ""  # TODO: English translation
   - text_inventory_optimization_desc = ""  # TODO: English translation
   - text_module_catalog = ""  # TODO: English translation
   - text_module_communication = ""  # TODO: English translation
   - text_module_inventory = ""  # TODO: English translation
   - text_module_purchase = ""  # TODO: English translation
   - text_module_sales = ""  # TODO: English translation
   - text_module_workflow = ""  # TODO: English translation
   - text_operation_inventory_update = ""  # TODO: English translation
   - text_operation_order_processing = ""  # TODO: English translation
   - text_operation_product_search = ""  # TODO: English translation
   - text_operation_workflow_execution = ""  # TODO: English translation
   - text_optimization = ""  # TODO: English translation
   - text_optimization_completed = ""  # TODO: English translation
   - text_realtime_monitoring = ""  # TODO: English translation
