📄 Route: accounts/fixed_assets_advanced
📂 Controller: controller\accounts\fixed_assets_advanced.php
🧱 Models used (2):
   - accounts/audit_trail
   - accounts/fixed_assets_advanced
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - heading_title
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - heading_title
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - date_format_short
   - heading_title
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
