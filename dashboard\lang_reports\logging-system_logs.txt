📄 Route: logging/system_logs
📂 Controller: controller\logging\system_logs.php
🧱 Models used (1):
   - logging/system_logs
🎨 Twig templates (1):
   - view\template\logging\system_logs.twig
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_home
   - text_level_alert
   - text_level_critical
   - text_level_debug
   - text_level_emergency
   - text_level_error
   - text_level_info
   - text_level_notice
   - text_level_warning
   - text_module_accounts
   - text_module_ai
   - text_module_api
   - text_module_catalog
   - text_module_communication
   - text_module_crm
   - text_module_database
   - text_module_finance
   - text_module_hr
   - text_module_inventory
   - text_module_notification
   - text_module_pos
   - text_module_purchase
   - text_module_sales
   - text_module_security
   - text_module_shipping
   - text_module_system
   - text_module_workflow
   - text_realtime_monitoring
   - text_view_log

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_home
   - text_level_alert
   - text_level_critical
   - text_level_debug
   - text_level_emergency
   - text_level_error
   - text_level_info
   - text_level_notice
   - text_level_warning
   - text_module_accounts
   - text_module_ai
   - text_module_api
   - text_module_catalog
   - text_module_communication
   - text_module_crm
   - text_module_database
   - text_module_finance
   - text_module_hr
   - text_module_inventory
   - text_module_notification
   - text_module_pos
   - text_module_purchase
   - text_module_sales
   - text_module_security
   - text_module_shipping
   - text_module_system
   - text_module_workflow
   - text_realtime_monitoring
   - text_view_log

❌ Missing in English:
   - error_permission
   - heading_title
   - text_home
   - text_level_alert
   - text_level_critical
   - text_level_debug
   - text_level_emergency
   - text_level_error
   - text_level_info
   - text_level_notice
   - text_level_warning
   - text_module_accounts
   - text_module_ai
   - text_module_api
   - text_module_catalog
   - text_module_communication
   - text_module_crm
   - text_module_database
   - text_module_finance
   - text_module_hr
   - text_module_inventory
   - text_module_notification
   - text_module_pos
   - text_module_purchase
   - text_module_sales
   - text_module_security
   - text_module_shipping
   - text_module_system
   - text_module_workflow
   - text_realtime_monitoring
   - text_view_log

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_level_alert = ""  # TODO: ترجمة عربية
   - text_level_critical = ""  # TODO: ترجمة عربية
   - text_level_debug = ""  # TODO: ترجمة عربية
   - text_level_emergency = ""  # TODO: ترجمة عربية
   - text_level_error = ""  # TODO: ترجمة عربية
   - text_level_info = ""  # TODO: ترجمة عربية
   - text_level_notice = ""  # TODO: ترجمة عربية
   - text_level_warning = ""  # TODO: ترجمة عربية
   - text_module_accounts = ""  # TODO: ترجمة عربية
   - text_module_ai = ""  # TODO: ترجمة عربية
   - text_module_api = ""  # TODO: ترجمة عربية
   - text_module_catalog = ""  # TODO: ترجمة عربية
   - text_module_communication = ""  # TODO: ترجمة عربية
   - text_module_crm = ""  # TODO: ترجمة عربية
   - text_module_database = ""  # TODO: ترجمة عربية
   - text_module_finance = ""  # TODO: ترجمة عربية
   - text_module_hr = ""  # TODO: ترجمة عربية
   - text_module_inventory = ""  # TODO: ترجمة عربية
   - text_module_notification = ""  # TODO: ترجمة عربية
   - text_module_pos = ""  # TODO: ترجمة عربية
   - text_module_purchase = ""  # TODO: ترجمة عربية
   - text_module_sales = ""  # TODO: ترجمة عربية
   - text_module_security = ""  # TODO: ترجمة عربية
   - text_module_shipping = ""  # TODO: ترجمة عربية
   - text_module_system = ""  # TODO: ترجمة عربية
   - text_module_workflow = ""  # TODO: ترجمة عربية
   - text_realtime_monitoring = ""  # TODO: ترجمة عربية
   - text_view_log = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_level_alert = ""  # TODO: English translation
   - text_level_critical = ""  # TODO: English translation
   - text_level_debug = ""  # TODO: English translation
   - text_level_emergency = ""  # TODO: English translation
   - text_level_error = ""  # TODO: English translation
   - text_level_info = ""  # TODO: English translation
   - text_level_notice = ""  # TODO: English translation
   - text_level_warning = ""  # TODO: English translation
   - text_module_accounts = ""  # TODO: English translation
   - text_module_ai = ""  # TODO: English translation
   - text_module_api = ""  # TODO: English translation
   - text_module_catalog = ""  # TODO: English translation
   - text_module_communication = ""  # TODO: English translation
   - text_module_crm = ""  # TODO: English translation
   - text_module_database = ""  # TODO: English translation
   - text_module_finance = ""  # TODO: English translation
   - text_module_hr = ""  # TODO: English translation
   - text_module_inventory = ""  # TODO: English translation
   - text_module_notification = ""  # TODO: English translation
   - text_module_pos = ""  # TODO: English translation
   - text_module_purchase = ""  # TODO: English translation
   - text_module_sales = ""  # TODO: English translation
   - text_module_security = ""  # TODO: English translation
   - text_module_shipping = ""  # TODO: English translation
   - text_module_system = ""  # TODO: English translation
   - text_module_workflow = ""  # TODO: English translation
   - text_realtime_monitoring = ""  # TODO: English translation
   - text_view_log = ""  # TODO: English translation
