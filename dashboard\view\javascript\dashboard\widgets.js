/**
 * AYM ERP Dashboard Widgets JavaScript
 * JavaScript للمكونات الذكية للوحة المعلومات
 * 
 * Handles widget interaction, customization, and advanced dashboard features
 * يتعامل مع تفاعل المكونات والتخصيص والميزات المتقدمة للوحة المعلومات
 */

class DashboardWidgets {
    constructor() {
        this.widgets = new Map();
        this.filters = {};
        this.userPermissions = {};
        this.widgetConfig = {};
        this.isLoading = false;
        this.refreshInterval = null;
        this.websocket = null;
        
        this.init();
    }
    
    /**
     * Initialize the dashboard widgets system
     * تهيئة نظام مكونات لوحة المعلومات
     */
    init() {
        this.loadUserPermissions();
        this.loadWidgetConfiguration();
        this.setupEventListeners();
        this.initializeWidgets();
        this.setupRealTimeUpdates();
        this.setupGlobalFilters();
        this.setupDragAndDrop();
        this.setupResponsiveDesign();
        this.setupAccessibility();
        this.setupPerformanceMonitoring();
    }
    
    /**
     * Load user permissions for widget access
     * تحميل صلاحيات المستخدم للوصول للمكونات
     */
    async loadUserPermissions() {
        try {
            const response = await fetch('index.php?route=common/dashboard/getUserPermissions&user_token=' + encodeURIComponent(this.getUserToken()));
            const data = await response.json();
            
            if (data.success) {
                this.userPermissions = data.widget_config.user_preferences || {};
                this.widgetConfig = data.widget_config;
            }
        } catch (error) {
            console.error('Error loading user permissions:', error);
        }
    }
    
    /**
     * Load widget configuration
     * تحميل إعدادات المكونات
     */
    async loadWidgetConfiguration() {
        try {
            const response = await fetch('index.php?route=common/dashboard/getWidgetConfiguration&user_token=' + encodeURIComponent(this.getUserToken()));
            const data = await response.json();
            
            if (data.success) {
                this.widgetConfig = data.widget_config;
                this.renderAvailableWidgets();
            }
        } catch (error) {
            console.error('Error loading widget configuration:', error);
        }
    }
    
    /**
     * Setup event listeners for widget interactions
     * إعداد مستمعي الأحداث لتفاعلات المكونات
     */
    setupEventListeners() {
        // Widget controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('.refresh-widget')) {
                this.refreshWidget(e.target.closest('.dashboard-widget'));
            }
            
            if (e.target.matches('.remove-widget')) {
                this.removeWidget(e.target.closest('.dashboard-widget'));
            }
            
            if (e.target.matches('.minimize-widget')) {
                this.minimizeWidget(e.target.closest('.dashboard-widget'));
            }
            
            if (e.target.matches('.maximize-widget')) {
                this.maximizeWidget(e.target.closest('.dashboard-widget'));
            }
            
            if (e.target.matches('.fullscreen-widget')) {
                this.fullscreenWidget(e.target.closest('.dashboard-widget'));
            }
        });
        
        // Global filters
        document.addEventListener('change', (e) => {
            if (e.target.matches('#date-range-filter, #branch-filter, #channel-filter, #category-filter')) {
                this.updateFilters();
            }
        });
        
        // Apply filters button
        document.addEventListener('click', (e) => {
            if (e.target.matches('#apply-filters')) {
                this.applyFilters();
            }
            
            if (e.target.matches('#clear-filters')) {
                this.clearFilters();
            }
        });
        
        // Add widget button
        document.addEventListener('click', (e) => {
            if (e.target.matches('#add-widget-btn')) {
                this.showWidgetGallery();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // Window resize
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
        
        // Visibility change (for auto-refresh)
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }
    
    /**
     * Initialize all widgets on the dashboard
     * تهيئة جميع المكونات في لوحة المعلومات
     */
    initializeWidgets() {
        const widgetElements = document.querySelectorAll('.dashboard-widget');
        
        widgetElements.forEach(widgetElement => {
            const widgetId = widgetElement.dataset.widget;
            const widgetType = widgetElement.dataset.widgetType;
            
            if (widgetId && widgetType) {
                this.initializeWidget(widgetElement, widgetId, widgetType);
            }
        });
        
        // Load initial data
        this.loadAllWidgetData();
    }
    
    /**
     * Initialize a single widget
     * تهيئة مكون واحد
     */
    initializeWidget(widgetElement, widgetId, widgetType) {
        const widget = {
            element: widgetElement,
            id: widgetId,
            type: widgetType,
            data: null,
            chart: null,
            isLoaded: false,
            isMinimized: false,
            isMaximized: false,
            isFullscreen: false,
            refreshInterval: null
        };
        
        this.widgets.set(widgetId, widget);
        
        // Setup widget-specific event listeners
        this.setupWidgetEventListeners(widget);
        
        // Load widget data
        this.loadWidgetData(widget);
    }
    
    /**
     * Setup event listeners for a specific widget
     * إعداد مستمعي الأحداث لمكون محدد
     */
    setupWidgetEventListeners(widget) {
        const element = widget.element;
        
        // Widget header interactions
        const header = element.querySelector('.widget-header');
        if (header) {
            header.addEventListener('dblclick', () => {
                this.maximizeWidget(element);
            });
            
            header.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showWidgetContextMenu(e, widget);
            });
        }
        
        // Widget body interactions
        const body = element.querySelector('.widget-body');
        if (body) {
            body.addEventListener('click', (e) => {
                this.handleWidgetBodyClick(e, widget);
            });
        }
        
        // Chart interactions
        const chartContainer = element.querySelector('.chart-container');
        if (chartContainer) {
            chartContainer.addEventListener('click', (e) => {
                this.handleChartClick(e, widget);
            });
        }
    }
    
    /**
     * Load data for all widgets
     * تحميل بيانات جميع المكونات
     */
    async loadAllWidgetData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingIndicator();
        
        try {
            const response = await fetch('index.php?route=common/dashboard/getWidgetData&user_token=' + encodeURIComponent(this.getUserToken()) + '&' + new URLSearchParams(this.filters));
            const data = await response.json();
            
            if (data.success) {
                this.updateAllWidgets(data.widgets);
                this.updateMetadata(data.metadata);
                this.updatePerformanceMetrics(data.performance);
            } else {
                this.showError('Error loading widget data: ' + data.error);
            }
        } catch (error) {
            console.error('Error loading widget data:', error);
            this.showError('Network error while loading widget data');
        } finally {
            this.isLoading = false;
            this.hideLoadingIndicator();
        }
    }
    
    /**
     * Load data for a specific widget
     * تحميل بيانات لمكون محدد
     */
    async loadWidgetData(widget) {
        try {
            const response = await fetch('index.php?route=common/dashboard/getWidgetData&user_token=' + encodeURIComponent(this.getUserToken()) + '&type=' + widget.type + '&' + new URLSearchParams(this.filters));
            const data = await response.json();
            
            if (data.success) {
                this.updateWidget(widget, data.widget_data);
            } else {
                this.showWidgetError(widget, data.error);
            }
        } catch (error) {
            console.error('Error loading widget data:', error);
            this.showWidgetError(widget, 'Network error');
        }
    }
    
    /**
     * Update all widgets with new data
     * تحديث جميع المكونات ببيانات جديدة
     */
    updateAllWidgets(widgetsData) {
        this.widgets.forEach((widget, widgetId) => {
            const data = this.getWidgetData(widgetsData, widget.type, widgetId);
            if (data) {
                this.updateWidget(widget, data);
            }
        });
    }
    
    /**
     * Update a specific widget with new data
     * تحديث مكون محدد ببيانات جديدة
     */
    updateWidget(widget, data) {
        widget.data = data;
        widget.isLoaded = true;
        
        // Update widget content
        this.updateWidgetContent(widget, data);
        
        // Update charts if present
        this.updateWidgetCharts(widget, data);
        
        // Update metrics
        this.updateWidgetMetrics(widget, data);
        
        // Trigger custom update events
        this.triggerWidgetUpdateEvent(widget, data);
        
        // Mark widget as updated
        widget.element.classList.add('widget-updated');
        setTimeout(() => {
            widget.element.classList.remove('widget-updated');
        }, 2000);
    }
    
    /**
     * Update widget content based on data
     * تحديث محتوى المكون بناءً على البيانات
     */
    updateWidgetContent(widget, data) {
        const element = widget.element;
        
        // Update KPI values
        this.updateKPIValues(element, data);
        
        // Update tables
        this.updateTables(element, data);
        
        // Update lists
        this.updateLists(element, data);
        
        // Update progress bars
        this.updateProgressBars(element, data);
        
        // Update status indicators
        this.updateStatusIndicators(element, data);
    }
    
    /**
     * Update KPI values in widget
     * تحديث قيم المؤشرات الرئيسية في المكون
     */
    updateKPIValues(element, data) {
        const kpiElements = element.querySelectorAll('.kpi-value');
        
        kpiElements.forEach(kpiElement => {
            const dataKey = kpiElement.dataset.kpi;
            if (dataKey && data[dataKey] !== undefined) {
                const value = data[dataKey];
                const formattedValue = this.formatValue(value, kpiElement.dataset.format);
                
                // Animate value change
                this.animateValueChange(kpiElement, formattedValue);
            }
        });
    }
    
    /**
     * Update tables in widget
     * تحديث الجداول في المكون
     */
    updateTables(element, data) {
        const tables = element.querySelectorAll('.widget-table');
        
        tables.forEach(table => {
            const dataKey = table.dataset.table;
            if (dataKey && data[dataKey]) {
                this.renderTable(table, data[dataKey]);
            }
        });
    }
    
    /**
     * Update lists in widget
     * تحديث القوائم في المكون
     */
    updateLists(element, data) {
        const lists = element.querySelectorAll('.widget-list');
        
        lists.forEach(list => {
            const dataKey = list.dataset.list;
            if (dataKey && data[dataKey]) {
                this.renderList(list, data[dataKey]);
            }
        });
    }
    
    /**
     * Update progress bars in widget
     * تحديث أشرطة التقدم في المكون
     */
    updateProgressBars(element, data) {
        const progressBars = element.querySelectorAll('.progress-bar');
        
        progressBars.forEach(bar => {
            const dataKey = bar.dataset.progress;
            if (dataKey && data[dataKey] !== undefined) {
                const value = data[dataKey];
                this.animateProgressBar(bar, value);
            }
        });
    }
    
    /**
     * Update status indicators in widget
     * تحديث مؤشرات الحالة في المكون
     */
    updateStatusIndicators(element, data) {
        const indicators = element.querySelectorAll('.status-indicator');
        
        indicators.forEach(indicator => {
            const dataKey = indicator.dataset.status;
            if (dataKey && data[dataKey] !== undefined) {
                const status = data[dataKey];
                this.updateStatusIndicator(indicator, status);
            }
        });
    }
    
    /**
     * Update widget charts
     * تحديث رسوم المكون البيانية
     */
    updateWidgetCharts(widget, data) {
        const chartContainers = widget.element.querySelectorAll('.chart-container');
        
        chartContainers.forEach(container => {
            const chartType = container.dataset.chartType;
            const dataKey = container.dataset.chartData;
            
            if (chartType && dataKey && data[dataKey]) {
                this.updateChart(container, chartType, data[dataKey]);
            }
        });
    }
    
    /**
     * Update widget metrics
     * تحديث مقاييس المكون
     */
    updateWidgetMetrics(widget, data) {
        const metrics = widget.element.querySelectorAll('.metric');
        
        metrics.forEach(metric => {
            const dataKey = metric.dataset.metric;
            if (dataKey && data[dataKey] !== undefined) {
                const value = data[dataKey];
                const formattedValue = this.formatMetric(value, metric.dataset.format);
                
                metric.textContent = formattedValue;
                
                // Add trend indicator if available
                const trendKey = dataKey + '_trend';
                if (data[trendKey] !== undefined) {
                    this.updateTrendIndicator(metric, data[trendKey]);
                }
            }
        });
    }
    
    /**
     * Setup real-time updates
     * إعداد التحديثات الفورية
     */
    setupRealTimeUpdates() {
        // Setup WebSocket connection
        this.setupWebSocket();
        
        // Setup polling for real-time updates
        this.setupPolling();
        
        // Setup auto-refresh
        this.setupAutoRefresh();
    }
    
    /**
     * Setup WebSocket connection for real-time updates
     * إعداد اتصال WebSocket للتحديثات الفورية
     */
    setupWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = protocol + '//' + window.location.host + '/socket/notification_server.php';
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected');
                this.sendWebSocketMessage({
                    type: 'subscribe',
                    user_id: this.getUserId(),
                    dashboard: true
                });
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    this.setupWebSocket();
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
            
        } catch (error) {
            console.error('Error setting up WebSocket:', error);
        }
    }
    
    /**
     * Setup polling for real-time updates
     * إعداد الاستطلاع للتحديثات الفورية
     */
    setupPolling() {
        // Poll for real-time updates every 30 seconds
        setInterval(() => {
            this.loadRealTimeUpdates();
        }, 30000);
    }
    
    /**
     * Setup auto-refresh functionality
     * إعداد وظيفة التحديث التلقائي
     */
    setupAutoRefresh() {
        const refreshInterval = this.getUserPreference('refresh_interval', 300); // 5 minutes default
        
        if (refreshInterval > 0) {
            this.refreshInterval = setInterval(() => {
                if (!document.hidden) {
                    this.loadAllWidgetData();
                }
            }, refreshInterval * 1000);
        }
    }
    
    /**
     * Load real-time updates
     * تحميل التحديثات الفورية
     */
    async loadRealTimeUpdates() {
        try {
            const response = await fetch('index.php?route=common/dashboard/refresh&user_token=' + encodeURIComponent(this.getUserToken()));
            const data = await response.json();
            
            if (data.success) {
                this.handleRealTimeUpdates(data.updates);
            }
        } catch (error) {
            console.error('Error loading real-time updates:', error);
        }
    }
    
    /**
     * Handle real-time updates
     * التعامل مع التحديثات الفورية
     */
    handleRealTimeUpdates(updates) {
        // Handle alerts
        if (updates.alerts) {
            this.showAlerts(updates.alerts);
        }
        
        // Handle notifications
        if (updates.notifications) {
            this.updateNotifications(updates.notifications);
        }
        
        // Handle metrics
        if (updates.metrics) {
            this.updateRealTimeMetrics(updates.metrics);
        }
        
        // Handle activities
        if (updates.activities) {
            this.updateActivityFeed(updates.activities);
        }
    }
    
    /**
     * Setup global filters
     * إعداد الفلاتر الشاملة
     */
    setupGlobalFilters() {
        // Initialize filter values
        this.filters = {
            date_start: this.getDateRangeStart(),
            date_end: this.getDateRangeEnd(),
            branch_id: this.getSelectedBranch(),
            channel: this.getSelectedChannel(),
            category: this.getSelectedCategory(),
            period: this.getSelectedPeriod()
        };

        // Setup filter change handlers
        this.setupFilterChangeHandlers();
    }

    /**
     * Get date range start
     * الحصول على بداية النطاق الزمني
     */
    getDateRangeStart() {
        const dateInput = document.getElementById('filter_date_start');
        if (dateInput && dateInput.value) {
            return dateInput.value;
        }
        // Default to 30 days ago
        const date = new Date();
        date.setDate(date.getDate() - 30);
        return date.toISOString().split('T')[0];
    }

    /**
     * Get date range end
     * الحصول على نهاية النطاق الزمني
     */
    getDateRangeEnd() {
        const dateInput = document.getElementById('filter_date_end');
        if (dateInput && dateInput.value) {
            return dateInput.value;
        }
        // Default to today
        return new Date().toISOString().split('T')[0];
    }

    /**
     * Get selected branch
     * الحصول على الفرع المحدد
     */
    getSelectedBranch() {
        const branchSelect = document.getElementById('filter_branch');
        return branchSelect ? branchSelect.value : '';
    }

    /**
     * Get selected channel
     * الحصول على القناة المحددة
     */
    getSelectedChannel() {
        const channelSelect = document.getElementById('filter_channel');
        return channelSelect ? channelSelect.value : '';
    }

    /**
     * Get selected category
     * الحصول على الفئة المحددة
     */
    getSelectedCategory() {
        const categorySelect = document.getElementById('filter_category');
        return categorySelect ? categorySelect.value : '';
    }

    /**
     * Get selected period
     * الحصول على الفترة المحددة
     */
    getSelectedPeriod() {
        const periodSelect = document.getElementById('filter_period');
        return periodSelect ? periodSelect.value : 'month';
    }
    
    /**
     * Setup filter change handlers
     * إعداد معالجات تغيير الفلاتر
     */
    setupFilterChangeHandlers() {
        const filterElements = document.querySelectorAll('#global-filters select, #global-filters input');
        
        filterElements.forEach(element => {
            element.addEventListener('change', () => {
                this.updateFilters();
            });
        });
    }
    
    /**
     * Update filters based on current selections
     * تحديث الفلاتر بناءً على الاختيارات الحالية
     */
    updateFilters() {
        this.filters = {
            date_start: this.getDateRangeStart(),
            date_end: this.getDateRangeEnd(),
            branch_id: this.getSelectedBranch(),
            channel: this.getSelectedChannel(),
            category: this.getSelectedCategory(),
            period: this.getSelectedPeriod()
        };
        
        // Store filters in session storage
        sessionStorage.setItem('dashboard_filters', JSON.stringify(this.filters));
    }
    
    /**
     * Apply filters and reload widget data
     * تطبيق الفلاتر وإعادة تحميل بيانات المكونات
     */
    applyFilters() {
        this.updateFilters();
        this.loadAllWidgetData();
        this.showSuccessMessage('Filters applied successfully');
    }
    
    /**
     * Clear all filters
     * مسح جميع الفلاتر
     */
    clearFilters() {
        // Reset filter elements
        document.getElementById('date-range-filter').value = 'month';
        document.getElementById('branch-filter').value = '';
        document.getElementById('channel-filter').value = '';
        document.getElementById('category-filter').value = '';
        
        // Update filters
        this.updateFilters();
        
        // Reload data
        this.loadAllWidgetData();
        
        this.showSuccessMessage('Filters cleared successfully');
    }
    
    /**
     * Setup drag and drop functionality
     * إعداد وظيفة السحب والإفلات
     */
    setupDragAndDrop() {
        // Make widgets draggable
        this.widgets.forEach((widget, widgetId) => {
            this.makeWidgetDraggable(widget);
        });
        
        // Setup drop zones
        this.setupDropZones();
        
        // Setup grid snapping
        this.setupGridSnapping();
    }
    
    /**
     * Make a widget draggable
     * جعل المكون قابل للسحب
     */
    makeWidgetDraggable(widget) {
        const element = widget.element;
        const header = element.querySelector('.widget-header');
        
        if (header) {
            header.draggable = true;
            
            header.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', widget.id);
                element.classList.add('dragging');
            });
            
            header.addEventListener('dragend', () => {
                element.classList.remove('dragging');
            });
        }
    }
    
    /**
     * Setup drop zones for widget placement
     * إعداد مناطق الإفلات لتحديد موقع المكونات
     */
    setupDropZones() {
        const dashboardContainer = document.querySelector('.dashboard-container');
        
        if (dashboardContainer) {
            dashboardContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            });
            
            dashboardContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                const widgetId = e.dataTransfer.getData('text/plain');
                const widget = this.widgets.get(widgetId);
                
                if (widget) {
                    this.moveWidgetToPosition(widget, e.clientX, e.clientY);
                }
            });
        }
    }
    
    /**
     * Move widget to new position
     * نقل المكون إلى موقع جديد
     */
    moveWidgetToPosition(widget, x, y) {
        const container = document.querySelector('.dashboard-container');
        const containerRect = container.getBoundingClientRect();
        
        // Calculate grid position
        const gridX = Math.floor((x - containerRect.left) / 100);
        const gridY = Math.floor((y - containerRect.top) / 100);
        
        // Update widget position
        widget.element.style.gridColumn = `${gridX + 1} / span ${widget.element.dataset.width || 1}`;
        widget.element.style.gridRow = `${gridY + 1} / span ${widget.element.dataset.height || 1}`;
        
        // Save layout
        this.saveWidgetLayout();
    }
    
    /**
     * Setup responsive design
     * إعداد التصميم المتجاوب
     */
    setupResponsiveDesign() {
        // Handle different screen sizes
        this.handleScreenSizeChange();
        
        // Setup responsive breakpoints
        this.setupResponsiveBreakpoints();
        
        // Setup touch interactions
        this.setupTouchInteractions();
    }
    
    /**
     * Handle screen size changes
     * التعامل مع تغييرات حجم الشاشة
     */
    handleScreenSizeChange() {
        const mediaQueries = {
            mobile: window.matchMedia('(max-width: 768px)'),
            tablet: window.matchMedia('(min-width: 769px) and (max-width: 1024px)'),
            desktop: window.matchMedia('(min-width: 1025px)')
        };
        
        Object.entries(mediaQueries).forEach(([size, mediaQuery]) => {
            mediaQuery.addListener(() => {
                this.adjustLayoutForScreenSize(size);
            });
        });
        
        // Initial adjustment
        this.adjustLayoutForScreenSize(this.getCurrentScreenSize());
    }
    
    /**
     * Setup accessibility features
     * إعداد ميزات إمكانية الوصول
     */
    setupAccessibility() {
        // Add ARIA labels
        this.addAriaLabels();
        
        // Setup keyboard navigation
        this.setupKeyboardNavigation();
        
        // Setup screen reader support
        this.setupScreenReaderSupport();
        
        // Setup high contrast mode
        this.setupHighContrastMode();
    }
    
    /**
     * Setup performance monitoring
     * إعداد مراقبة الأداء
     */
    setupPerformanceMonitoring() {
        // Monitor widget load times
        this.monitorWidgetLoadTimes();
        
        // Monitor memory usage
        this.monitorMemoryUsage();
        
        // Monitor network performance
        this.monitorNetworkPerformance();
        
        // Setup performance alerts
        this.setupPerformanceAlerts();
    }
    
    /**
     * Show widget gallery for adding new widgets
     * عرض معرض المكونات لإضافة مكونات جديدة
     */
    showWidgetGallery() {
        const modal = this.createModal('widget-gallery', 'Add Widgets');
        
        // Populate with available widgets
        const availableWidgets = this.getAvailableWidgets();
        
        availableWidgets.forEach(category => {
            const categorySection = this.createCategorySection(category);
            modal.querySelector('.modal-body').appendChild(categorySection);
        });
        
        // Show modal
        this.showModal(modal);
    }
    
    /**
     * Get available widgets based on user permissions
     * الحصول على المكونات المتاحة بناءً على صلاحيات المستخدم
     */
    getAvailableWidgets() {
        const availableWidgets = [];
        
        if (this.userPermissions.executive) {
            availableWidgets.push({
                name: 'Executive & Strategic',
                widgets: this.widgetConfig.available_widgets?.executive || []
            });
        }
        
        if (this.userPermissions.sales) {
            availableWidgets.push({
                name: 'Sales & CRM',
                widgets: this.widgetConfig.available_widgets?.sales || []
            });
        }
        
        if (this.userPermissions.inventory) {
            availableWidgets.push({
                name: 'Inventory & Warehouse',
                widgets: this.widgetConfig.available_widgets?.inventory || []
            });
        }
        
        if (this.userPermissions.finance) {
            availableWidgets.push({
                name: 'Finance & Accounting',
                widgets: this.widgetConfig.available_widgets?.finance || []
            });
        }
        
        return availableWidgets;
    }
    
    /**
     * Create category section for widget gallery
     * إنشاء قسم فئة لمعرض المكونات
     */
    createCategorySection(category) {
        const section = document.createElement('div');
        section.className = 'widget-category';
        
        const title = document.createElement('h4');
        title.textContent = category.name;
        section.appendChild(title);
        
        const grid = document.createElement('div');
        grid.className = 'widget-grid';
        
        category.widgets.forEach(widget => {
            const widgetCard = this.createWidgetCard(widget);
            grid.appendChild(widgetCard);
        });
        
        section.appendChild(grid);
        return section;
    }
    
    /**
     * Create widget card for gallery
     * إنشاء بطاقة مكون للمعرض
     */
    createWidgetCard(widget) {
        const card = document.createElement('div');
        card.className = 'widget-card';
        card.dataset.widgetId = widget.id;
        card.dataset.widgetType = widget.type;
        
        card.innerHTML = `
            <div class="widget-card-icon">
                <i class="fa ${widget.icon}"></i>
            </div>
            <div class="widget-card-content">
                <h5>${widget.name}</h5>
                <p>${widget.description || ''}</p>
            </div>
            <div class="widget-card-actions">
                <button class="btn btn-sm btn-primary add-widget-btn" data-widget-id="${widget.id}" data-widget-type="${widget.type}">
                    Add Widget
                </button>
            </div>
        `;
        
        // Add click handler
        card.querySelector('.add-widget-btn').addEventListener('click', () => {
            this.addWidget(widget.id, widget.type);
        });
        
        return card;
    }
    
    /**
     * Add a new widget to the dashboard
     * إضافة مكون جديد للوحة المعلومات
     */
    addWidget(widgetId, widgetType) {
        // Create widget element
        const widgetElement = this.createWidgetElement(widgetId, widgetType);
        
        // Add to dashboard
        const dashboardContainer = document.querySelector('.dashboard-container');
        dashboardContainer.appendChild(widgetElement);
        
        // Initialize widget
        this.initializeWidget(widgetElement, widgetId, widgetType);
        
        // Save layout
        this.saveWidgetLayout();
        
        // Close modal
        this.closeModal();
        
        this.showSuccessMessage('Widget added successfully');
    }
    
    /**
     * Create widget element
     * إنشاء عنصر المكون
     */
    createWidgetElement(widgetId, widgetType) {
        const element = document.createElement('div');
        element.className = 'dashboard-widget';
        element.dataset.widget = widgetId;
        element.dataset.widgetType = widgetType;
        element.dataset.width = '1';
        element.dataset.height = '1';
        
        // Get widget template
        const template = this.getWidgetTemplate(widgetId, widgetType);
        element.innerHTML = template;
        
        return element;
    }
    
    /**
     * Get widget template
     * الحصول على قالب المكون
     */
    getWidgetTemplate(widgetId, widgetType) {
        // This would load from a template system
        // For now, return a basic template
        return `
            <div class="widget-header">
                <h5><i class="fa fa-chart-bar"></i> ${this.getWidgetTitle(widgetId)}</h5>
                <div class="widget-controls">
                    <button class="btn btn-sm btn-outline-secondary refresh-widget" title="Refresh">
                        <i class="fa fa-refresh"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger remove-widget" title="Remove">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body">
                <div class="widget-loading">
                    <i class="fa fa-spinner fa-spin"></i> Loading...
                </div>
            </div>
        `;
    }
    
    /**
     * Get widget title
     * الحصول على عنوان المكون
     */
    getWidgetTitle(widgetId) {
        const titles = {
            'executive_summary': 'Executive Summary',
            'sales_performance': 'Sales Performance',
            'inventory_overview': 'Inventory Overview',
            'financial_overview': 'Financial Overview'
            // Add more titles as needed
        };
        
        return titles[widgetId] || 'Widget';
    }
    
    /**
     * Remove widget from dashboard
     * إزالة المكون من لوحة المعلومات
     */
    removeWidget(widgetElement) {
        const widgetId = widgetElement.dataset.widget;
        const widget = this.widgets.get(widgetId);
        
        if (widget) {
            // Clean up widget
            this.cleanupWidget(widget);
            
            // Remove from DOM
            widgetElement.remove();
            
            // Remove from widgets map
            this.widgets.delete(widgetId);
            
            // Save layout
            this.saveWidgetLayout();
            
            this.showSuccessMessage('Widget removed successfully');
        }
    }
    
    /**
     * Cleanup widget resources
     * تنظيف موارد المكون
     */
    cleanupWidget(widget) {
        // Clear refresh interval
        if (widget.refreshInterval) {
            clearInterval(widget.refreshInterval);
        }
        
        // Destroy charts
        if (widget.chart) {
            widget.chart.destroy();
        }
        
        // Remove event listeners
        this.removeWidgetEventListeners(widget);
    }
    
    /**
     * Refresh a specific widget
     * تحديث مكون محدد
     */
    refreshWidget(widgetElement) {
        const widgetId = widgetElement.dataset.widget;
        const widget = this.widgets.get(widgetId);
        
        if (widget) {
            this.loadWidgetData(widget);
        }
    }
    
    /**
     * Minimize widget
     * تصغير المكون
     */
    minimizeWidget(widgetElement) {
        widgetElement.classList.add('minimized');
        widgetElement.querySelector('.widget-body').style.display = 'none';
    }
    
    /**
     * Maximize widget
     * تكبير المكون
     */
    maximizeWidget(widgetElement) {
        widgetElement.classList.remove('minimized');
        widgetElement.querySelector('.widget-body').style.display = 'block';
    }
    
    /**
     * Fullscreen widget
     * ملء الشاشة للمكون
     */
    fullscreenWidget(widgetElement) {
        if (widgetElement.classList.contains('fullscreen')) {
            widgetElement.classList.remove('fullscreen');
            document.body.classList.remove('widget-fullscreen');
        } else {
            widgetElement.classList.add('fullscreen');
            document.body.classList.add('widget-fullscreen');
        }
    }
    
    /**
     * Handle keyboard shortcuts
     * التعامل مع اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + R: Refresh all widgets
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.loadAllWidgetData();
        }
        
        // Ctrl/Cmd + F: Focus on filters
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            document.getElementById('date-range-filter').focus();
        }
        
        // Ctrl/Cmd + A: Add widget
        if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
            e.preventDefault();
            this.showWidgetGallery();
        }
        
        // Escape: Close modals, fullscreen widgets
        if (e.key === 'Escape') {
            this.closeModals();
            this.exitFullscreenWidgets();
        }
    }
    
    /**
     * Handle window resize
     * التعامل مع تغيير حجم النافذة
     */
    handleWindowResize() {
        // Adjust widget layouts
        this.adjustWidgetLayouts();
        
        // Update chart sizes
        this.updateChartSizes();
        
        // Adjust responsive elements
        this.adjustResponsiveElements();
    }
    
    /**
     * Handle visibility change
     * التعامل مع تغيير الرؤية
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // Pause auto-refresh when tab is not visible
            this.pauseAutoRefresh();
        } else {
            // Resume auto-refresh when tab becomes visible
            this.resumeAutoRefresh();
            
            // Refresh data if needed
            this.refreshIfNeeded();
        }
    }
    
    /**
     * Utility methods
     * طرق مساعدة
     */
    getUserToken() {
        return document.querySelector('meta[name="user_token"]')?.content || '';
    }
    
    getUserId() {
        return document.querySelector('meta[name="user_id"]')?.content || '';
    }
    
    getUserPreference(key, defaultValue) {
        return this.userPermissions[key] || defaultValue;
    }
    
    formatValue(value, format) {
        if (typeof value === 'number') {
            if (format === 'currency') {
                return new Intl.NumberFormat('ar-EG', { style: 'currency', currency: 'EGP' }).format(value);
            } else if (format === 'percentage') {
                return value.toFixed(2) + '%';
            } else if (format === 'number') {
                return new Intl.NumberFormat('ar-EG').format(value);
            }
        }
        return value;
    }
    
    formatMetric(value, format) {
        return this.formatValue(value, format);
    }
    
    showLoadingIndicator() {
        const indicator = document.getElementById('loading-indicator');
        if (indicator) {
            indicator.style.display = 'block';
        }
    }
    
    hideLoadingIndicator() {
        const indicator = document.getElementById('loading-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    showSuccessMessage(message) {
        this.showNotification(message, 'success');
    }

    showErrorMessage(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Show general error
     * عرض خطأ عام
     */
    showError(message) {
        this.showNotification(message, 'error');
        console.error('Dashboard Error:', message);
    }

    /**
     * Show widget-specific error
     * عرض خطأ خاص بالويدجت
     */
    showWidgetError(widget, message) {
        const widgetElement = document.getElementById(widget);
        if (widgetElement) {
            const errorHtml = `
                <div class="widget-error alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <strong>خطأ في تحميل البيانات:</strong>
                    <p>${message}</p>
                    <button class="btn btn-sm btn-primary" onclick="dashboardWidgets.loadWidgetData('${widget}')">
                        <i class="fa fa-refresh"></i> إعادة المحاولة
                    </button>
                </div>
            `;
            widgetElement.innerHTML = errorHtml;
        }
        console.error(`Widget ${widget} Error:`, message);
    }

    /**
     * Adjust widget layouts for responsive design
     * تعديل تخطيط الويدجت للتصميم المتجاوب
     */
    adjustWidgetLayouts() {
        const widgets = document.querySelectorAll('.dashboard-widget');
        const screenWidth = window.innerWidth;

        widgets.forEach(widget => {
            if (screenWidth < 768) {
                // Mobile layout
                widget.classList.add('widget-mobile');
                widget.classList.remove('widget-desktop', 'widget-tablet');
            } else if (screenWidth < 1024) {
                // Tablet layout
                widget.classList.add('widget-tablet');
                widget.classList.remove('widget-mobile', 'widget-desktop');
            } else {
                // Desktop layout
                widget.classList.add('widget-desktop');
                widget.classList.remove('widget-mobile', 'widget-tablet');
            }
        });
    }
    
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    // Additional utility methods would be implemented here...
}

// Initialize dashboard widgets when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardWidgets = new DashboardWidgets();
}); 