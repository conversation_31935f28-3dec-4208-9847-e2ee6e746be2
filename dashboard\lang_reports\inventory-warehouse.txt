📄 Route: inventory/warehouse
📂 Controller: controller\inventory\warehouse.php
🧱 Models used (7):
   - accounting/chartaccount
   - core/central_service_manager
   - inventory/warehouse
   - inventory/warehouse_enhanced
   - setting/setting
   - user/user
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\common\header.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\header.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add
   - button_clear
   - button_delete
   - button_edit
   - button_export
   - button_print
   - button_search
   - column_action
   - column_address
   - column_code
   - column_manager
   - column_name
   - column_product_count
   - column_status
   - column_total_value
   - date_format_short
   - error_adjustments_required
   - error_advanced_permission
   - error_barcode_required
   - error_children
   - error_code
   - error_code_exists
   - error_exception
   - error_from_warehouse_required
   - error_invalid_action
   - error_invalid_request
   - error_movement_failed
   - error_movement_type_required
   - error_name
   - error_permission
   - error_product
   - error_product_not_found
   - error_product_required
   - error_products_required
   - error_quantity_required
   - error_required_fields
   - error_same_warehouse
   - error_system_error
   - error_to_warehouse_required
   - error_transfer_failed
   - error_warehouse_create
   - error_warehouse_id_required
   - error_warehouse_not_found
   - error_warehouse_required
   - error_warning
   - heading_title
   - text_add
   - text_adjustment_success
   - text_confirm
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_inventory
   - text_list
   - text_loading
   - text_location_branch
   - text_location_factory
   - text_location_office
   - text_location_showroom
   - text_location_store
   - text_location_warehouse
   - text_movement_success
   - text_no_results
   - text_pagination
   - text_status_updated
   - text_stock_adjustment
   - text_success
   - text_transfer_success
   - text_warehouse_dashboard

❌ Missing in Arabic:
   - button_add
   - button_clear
   - button_delete
   - button_edit
   - button_export
   - button_print
   - button_search
   - column_action
   - column_address
   - column_code
   - column_manager
   - column_name
   - column_product_count
   - column_status
   - column_total_value
   - date_format_short
   - error_adjustments_required
   - error_advanced_permission
   - error_barcode_required
   - error_children
   - error_code
   - error_code_exists
   - error_exception
   - error_from_warehouse_required
   - error_invalid_action
   - error_invalid_request
   - error_movement_failed
   - error_movement_type_required
   - error_name
   - error_permission
   - error_product
   - error_product_not_found
   - error_product_required
   - error_products_required
   - error_quantity_required
   - error_required_fields
   - error_same_warehouse
   - error_system_error
   - error_to_warehouse_required
   - error_transfer_failed
   - error_warehouse_create
   - error_warehouse_id_required
   - error_warehouse_not_found
   - error_warehouse_required
   - error_warning
   - heading_title
   - text_add
   - text_adjustment_success
   - text_confirm
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_inventory
   - text_list
   - text_loading
   - text_location_branch
   - text_location_factory
   - text_location_office
   - text_location_showroom
   - text_location_store
   - text_location_warehouse
   - text_movement_success
   - text_no_results
   - text_pagination
   - text_status_updated
   - text_stock_adjustment
   - text_success
   - text_transfer_success
   - text_warehouse_dashboard

❌ Missing in English:
   - button_add
   - button_clear
   - button_delete
   - button_edit
   - button_export
   - button_print
   - button_search
   - column_action
   - column_address
   - column_code
   - column_manager
   - column_name
   - column_product_count
   - column_status
   - column_total_value
   - date_format_short
   - error_adjustments_required
   - error_advanced_permission
   - error_barcode_required
   - error_children
   - error_code
   - error_code_exists
   - error_exception
   - error_from_warehouse_required
   - error_invalid_action
   - error_invalid_request
   - error_movement_failed
   - error_movement_type_required
   - error_name
   - error_permission
   - error_product
   - error_product_not_found
   - error_product_required
   - error_products_required
   - error_quantity_required
   - error_required_fields
   - error_same_warehouse
   - error_system_error
   - error_to_warehouse_required
   - error_transfer_failed
   - error_warehouse_create
   - error_warehouse_id_required
   - error_warehouse_not_found
   - error_warehouse_required
   - error_warning
   - heading_title
   - text_add
   - text_adjustment_success
   - text_confirm
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_inventory
   - text_list
   - text_loading
   - text_location_branch
   - text_location_factory
   - text_location_office
   - text_location_showroom
   - text_location_store
   - text_location_warehouse
   - text_movement_success
   - text_no_results
   - text_pagination
   - text_status_updated
   - text_stock_adjustment
   - text_success
   - text_transfer_success
   - text_warehouse_dashboard

💡 Suggested Arabic Additions:
   - button_add = ""  # TODO: ترجمة عربية
   - button_clear = ""  # TODO: ترجمة عربية
   - button_delete = ""  # TODO: ترجمة عربية
   - button_edit = ""  # TODO: ترجمة عربية
   - button_export = ""  # TODO: ترجمة عربية
   - button_print = ""  # TODO: ترجمة عربية
   - button_search = ""  # TODO: ترجمة عربية
   - column_action = ""  # TODO: ترجمة عربية
   - column_address = ""  # TODO: ترجمة عربية
   - column_code = ""  # TODO: ترجمة عربية
   - column_manager = ""  # TODO: ترجمة عربية
   - column_name = ""  # TODO: ترجمة عربية
   - column_product_count = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_total_value = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_adjustments_required = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_barcode_required = ""  # TODO: ترجمة عربية
   - error_children = ""  # TODO: ترجمة عربية
   - error_code = ""  # TODO: ترجمة عربية
   - error_code_exists = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_from_warehouse_required = ""  # TODO: ترجمة عربية
   - error_invalid_action = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_movement_failed = ""  # TODO: ترجمة عربية
   - error_movement_type_required = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_product = ""  # TODO: ترجمة عربية
   - error_product_not_found = ""  # TODO: ترجمة عربية
   - error_product_required = ""  # TODO: ترجمة عربية
   - error_products_required = ""  # TODO: ترجمة عربية
   - error_quantity_required = ""  # TODO: ترجمة عربية
   - error_required_fields = ""  # TODO: ترجمة عربية
   - error_same_warehouse = ""  # TODO: ترجمة عربية
   - error_system_error = ""  # TODO: ترجمة عربية
   - error_to_warehouse_required = ""  # TODO: ترجمة عربية
   - error_transfer_failed = ""  # TODO: ترجمة عربية
   - error_warehouse_create = ""  # TODO: ترجمة عربية
   - error_warehouse_id_required = ""  # TODO: ترجمة عربية
   - error_warehouse_not_found = ""  # TODO: ترجمة عربية
   - error_warehouse_required = ""  # TODO: ترجمة عربية
   - error_warning = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_adjustment_success = ""  # TODO: ترجمة عربية
   - text_confirm = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_inventory = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية
   - text_loading = ""  # TODO: ترجمة عربية
   - text_location_branch = ""  # TODO: ترجمة عربية
   - text_location_factory = ""  # TODO: ترجمة عربية
   - text_location_office = ""  # TODO: ترجمة عربية
   - text_location_showroom = ""  # TODO: ترجمة عربية
   - text_location_store = ""  # TODO: ترجمة عربية
   - text_location_warehouse = ""  # TODO: ترجمة عربية
   - text_movement_success = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_status_updated = ""  # TODO: ترجمة عربية
   - text_stock_adjustment = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_transfer_success = ""  # TODO: ترجمة عربية
   - text_warehouse_dashboard = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add = ""  # TODO: English translation
   - button_clear = ""  # TODO: English translation
   - button_delete = ""  # TODO: English translation
   - button_edit = ""  # TODO: English translation
   - button_export = ""  # TODO: English translation
   - button_print = ""  # TODO: English translation
   - button_search = ""  # TODO: English translation
   - column_action = ""  # TODO: English translation
   - column_address = ""  # TODO: English translation
   - column_code = ""  # TODO: English translation
   - column_manager = ""  # TODO: English translation
   - column_name = ""  # TODO: English translation
   - column_product_count = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_total_value = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_adjustments_required = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_barcode_required = ""  # TODO: English translation
   - error_children = ""  # TODO: English translation
   - error_code = ""  # TODO: English translation
   - error_code_exists = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_from_warehouse_required = ""  # TODO: English translation
   - error_invalid_action = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_movement_failed = ""  # TODO: English translation
   - error_movement_type_required = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_product = ""  # TODO: English translation
   - error_product_not_found = ""  # TODO: English translation
   - error_product_required = ""  # TODO: English translation
   - error_products_required = ""  # TODO: English translation
   - error_quantity_required = ""  # TODO: English translation
   - error_required_fields = ""  # TODO: English translation
   - error_same_warehouse = ""  # TODO: English translation
   - error_system_error = ""  # TODO: English translation
   - error_to_warehouse_required = ""  # TODO: English translation
   - error_transfer_failed = ""  # TODO: English translation
   - error_warehouse_create = ""  # TODO: English translation
   - error_warehouse_id_required = ""  # TODO: English translation
   - error_warehouse_not_found = ""  # TODO: English translation
   - error_warehouse_required = ""  # TODO: English translation
   - error_warning = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_adjustment_success = ""  # TODO: English translation
   - text_confirm = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_inventory = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
   - text_loading = ""  # TODO: English translation
   - text_location_branch = ""  # TODO: English translation
   - text_location_factory = ""  # TODO: English translation
   - text_location_office = ""  # TODO: English translation
   - text_location_showroom = ""  # TODO: English translation
   - text_location_store = ""  # TODO: English translation
   - text_location_warehouse = ""  # TODO: English translation
   - text_movement_success = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_status_updated = ""  # TODO: English translation
   - text_stock_adjustment = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_transfer_success = ""  # TODO: English translation
   - text_warehouse_dashboard = ""  # TODO: English translation
