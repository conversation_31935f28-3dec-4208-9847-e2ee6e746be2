📄 Route: extension/shipping/weight
📂 Controller: controller\extension\shipping\weight.php
🧱 Models used (3):
   - localisation/geo_zone
   - localisation/tax_class
   - setting/setting
🎨 Twig templates (1):
   - view\template\extension\shipping\weight.twig
🈯 Arabic Language Files (1):
   - language\ar\extension\shipping\weight.php
🇬🇧 English Language Files (1):
   - language\en-gb\extension\shipping\weight.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

❌ Missing in English:
   - error_permission
   - heading_title
   - text_extension
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_extension = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_extension = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
