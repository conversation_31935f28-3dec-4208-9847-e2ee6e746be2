# 1️⃣1️⃣ قوالب الهجرة (Excel Templates)

## 🎯 الهدف من قوالب الهجرة
تسهيل انتقال العملاء من الأنظمة المنافسة إلى AYM ERP من خلال قوالب Excel محسنة وأدوات تحويل ذكية.

## 📊 قوالب الاستيراد الأساسية

### **👥 1. قالب العملاء (Customers Template)**

#### **الملف:** `templates/customers_import.xlsx`

#### **الأعمدة المطلوبة:**
```excel
A: customer_id (اختياري - للتحديث)
B: customer_type (فرد/شركة)
C: first_name (الاسم الأول)*
D: last_name (اسم العائلة)
E: company_name (اسم الشركة)
F: email (البريد الإلكتروني)*
G: telephone (رقم الهاتف)*
H: mobile (رقم الموبايل)
I: tax_number (الرقم الضريبي)
J: commercial_register (السجل التجاري)
K: address_1 (العنوان الأول)*
L: address_2 (العنوان الثاني)
M: city (المدينة)*
N: governorate (المحافظة)*
O: postal_code (الرمز البريدي)
P: country (الدولة)*
Q: customer_group (مجموعة العملاء)*
R: credit_limit (حد الائتمان)
S: payment_terms (شروط الدفع)
T: sales_rep (المندوب)
U: notes (ملاحظات)
V: status (الحالة: نشط/غير نشط)
```

#### **قواعد التحقق:**
- **الحقول الإجبارية:** مميزة بـ *
- **تنسيق البريد الإلكتروني:** يجب أن يكون صحيح
- **رقم الهاتف:** يجب أن يبدأ بـ +20 للأرقام المصرية
- **مجموعة العملاء:** يجب أن تكون موجودة في النظام
- **حد الائتمان:** رقم موجب أو صفر

#### **مثال على البيانات:**
```excel
Row 2: 
A: (فارغ)
B: شركة
C: أحمد
D: محمد
E: شركة النصر للتجارة
F: <EMAIL>
G: +201234567890
H: +201234567891
I: 123456789
J: 98765
K: 15 شارع التحرير
L: الدور الثالث
M: القاهرة
N: القاهرة
O: 11511
P: مصر
Q: عملاء الجملة
R: 50000
S: 30 يوم
T: محمد علي
U: عميل مميز
V: نشط
```

### **📦 2. قالب المنتجات (Products Template)**

#### **الملف:** `templates/products_import.xlsx`

#### **الأعمدة المطلوبة:**
```excel
A: product_id (اختياري - للتحديث)
B: name (اسم المنتج)*
C: description (الوصف)
D: model (الموديل/الكود)*
E: sku (رمز المنتج)*
F: barcode (الباركود)
G: category (الفئة)*
H: manufacturer (الشركة المصنعة)
I: unit (الوحدة)*
J: purchase_price (سعر الشراء)*
K: selling_price (سعر البيع)*
L: wholesale_price (سعر الجملة)
M: minimum_quantity (الحد الأدنى)
N: maximum_quantity (الحد الأقصى)
O: reorder_level (نقطة إعادة الطلب)
P: weight (الوزن)
Q: dimensions (الأبعاد)
R: warranty_period (فترة الضمان)
S: expiry_tracking (تتبع انتهاء الصلاحية)
T: batch_tracking (تتبع الدفعات)
U: tax_rate (معدل الضريبة)
V: status (الحالة)*
W: image_url (رابط الصورة)
```

#### **مثال على البيانات:**
```excel
Row 2:
A: (فارغ)
B: لابتوب ديل انسبايرون 15
C: لابتوب للاستخدام المكتبي والشخصي
D: DELL-INS-15-3000
E: DELL15INS3000
F: *************
G: أجهزة كمبيوتر
H: ديل
I: قطعة
J: 15000
K: 18000
L: 16500
M: 1
N: 100
O: 5
P: 2.5
Q: 35x25x2 سم
R: 12 شهر
S: لا
T: نعم
U: 14
V: نشط
W: https://example.com/laptop.jpg
```

### **💰 3. قالب المعاملات المالية (Financial Transactions)**

#### **الملف:** `templates/transactions_import.xlsx`

#### **الأعمدة المطلوبة:**
```excel
A: transaction_id (اختياري)
B: date (التاريخ)*
C: reference (المرجع)*
D: account_code (كود الحساب)*
E: account_name (اسم الحساب)
F: debit (مدين)
G: credit (دائن)
H: description (الوصف)*
I: customer_supplier (العميل/المورد)
J: invoice_number (رقم الفاتورة)
K: due_date (تاريخ الاستحقاق)
L: currency (العملة)
M: exchange_rate (سعر الصرف)
N: branch (الفرع)
O: cost_center (مركز التكلفة)
P: project (المشروع)
Q: notes (ملاحظات)
```

### **📋 4. قالب المخزون (Inventory Template)**

#### **الملف:** `templates/inventory_import.xlsx`

#### **الأعمدة المطلوبة:**
```excel
A: product_code (كود المنتج)*
B: product_name (اسم المنتج)
C: warehouse (المستودع)*
D: location (الموقع)
E: quantity (الكمية)*
F: unit_cost (تكلفة الوحدة)*
G: total_value (القيمة الإجمالية)
H: batch_number (رقم الدفعة)
I: expiry_date (تاريخ الانتهاء)
J: manufacturing_date (تاريخ الإنتاج)
K: supplier (المورد)
L: last_updated (آخر تحديث)
M: notes (ملاحظات)
```

### **🏢 5. قالب الموردين (Suppliers Template)**

#### **الملف:** `templates/suppliers_import.xlsx`

#### **الأعمدة المطلوبة:**
```excel
A: supplier_id (اختياري)
B: supplier_name (اسم المورد)*
C: contact_person (جهة الاتصال)*
D: email (البريد الإلكتروني)*
E: telephone (الهاتف)*
F: mobile (الموبايل)
G: fax (الفاكس)
H: website (الموقع الإلكتروني)
I: tax_number (الرقم الضريبي)
J: commercial_register (السجل التجاري)
K: address (العنوان)*
L: city (المدينة)*
M: country (الدولة)*
N: payment_terms (شروط الدفع)
O: credit_limit (حد الائتمان)
P: currency (العملة)
Q: category (الفئة)
R: rating (التقييم)
S: notes (ملاحظات)
T: status (الحالة)*
```

## 🔄 أدوات التحويل الذكية

### **🧠 1. Data Mapper (مطابق البيانات)**

#### **الملف:** `tools/DataMapper.php`

```php
<?php
/**
 * أداة مطابقة البيانات الذكية
 */
class DataMapper {
    
    private $mapping_rules = [];
    
    public function __construct() {
        $this->loadMappingRules();
    }
    
    public function mapData($source_data, $source_system) {
        $mapped_data = [];
        
        foreach ($source_data as $field => $value) {
            $aym_field = $this->mapField($field, $source_system);
            if ($aym_field) {
                $mapped_data[$aym_field] = $this->transformValue($value, $aym_field);
            }
        }
        
        return $mapped_data;
    }
    
    private function mapField($source_field, $source_system) {
        $mapping_key = "{$source_system}.{$source_field}";
        return $this->mapping_rules[$mapping_key] ?? null;
    }
    
    private function transformValue($value, $target_field) {
        switch ($target_field) {
            case 'telephone':
                return $this->formatPhoneNumber($value);
            case 'email':
                return strtolower(trim($value));
            case 'price':
                return floatval($value);
            case 'status':
                return $this->mapStatus($value);
            default:
                return $value;
        }
    }
    
    private function formatPhoneNumber($phone) {
        // تنسيق أرقام الهاتف المصرية
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        if (substr($phone, 0, 2) == '01') {
            return '+2' . $phone;
        } elseif (substr($phone, 0, 3) == '201') {
            return '+' . $phone;
        }
        
        return $phone;
    }
    
    private function loadMappingRules() {
        // قواعد مطابقة Odoo
        $this->mapping_rules['odoo.name'] = 'firstname';
        $this->mapping_rules['odoo.email'] = 'email';
        $this->mapping_rules['odoo.phone'] = 'telephone';
        $this->mapping_rules['odoo.street'] = 'address_1';
        $this->mapping_rules['odoo.city'] = 'city';
        
        // قواعد مطابقة WooCommerce
        $this->mapping_rules['woocommerce.first_name'] = 'firstname';
        $this->mapping_rules['woocommerce.last_name'] = 'lastname';
        $this->mapping_rules['woocommerce.email'] = 'email';
        $this->mapping_rules['woocommerce.phone'] = 'telephone';
        
        // قواعد مطابقة Shopify
        $this->mapping_rules['shopify.first_name'] = 'firstname';
        $this->mapping_rules['shopify.last_name'] = 'lastname';
        $this->mapping_rules['shopify.email'] = 'email';
        $this->mapping_rules['shopify.phone'] = 'telephone';
    }
}
```

### **✅ 2. Data Validator (مدقق البيانات)**

#### **الملف:** `tools/DataValidator.php`

```php
<?php
/**
 * أداة التحقق من صحة البيانات
 */
class DataValidator {
    
    private $errors = [];
    
    public function validateCustomer($customer_data, $row_number) {
        $this->errors = [];
        
        // التحقق من الحقول الإجبارية
        $required_fields = ['firstname', 'email', 'telephone', 'address_1', 'city'];
        foreach ($required_fields as $field) {
            if (empty($customer_data[$field])) {
                $this->errors[] = "Row {$row_number}: {$field} is required";
            }
        }
        
        // التحقق من تنسيق البريد الإلكتروني
        if (!empty($customer_data['email']) && !filter_var($customer_data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->errors[] = "Row {$row_number}: Invalid email format";
        }
        
        // التحقق من رقم الهاتف
        if (!empty($customer_data['telephone']) && !$this->isValidPhoneNumber($customer_data['telephone'])) {
            $this->errors[] = "Row {$row_number}: Invalid phone number format";
        }
        
        // التحقق من حد الائتمان
        if (!empty($customer_data['credit_limit']) && !is_numeric($customer_data['credit_limit'])) {
            $this->errors[] = "Row {$row_number}: Credit limit must be numeric";
        }
        
        return empty($this->errors);
    }
    
    public function validateProduct($product_data, $row_number) {
        $this->errors = [];
        
        // التحقق من الحقول الإجبارية
        $required_fields = ['name', 'model', 'sku', 'category', 'unit', 'purchase_price', 'selling_price'];
        foreach ($required_fields as $field) {
            if (empty($product_data[$field])) {
                $this->errors[] = "Row {$row_number}: {$field} is required";
            }
        }
        
        // التحقق من الأسعار
        if (!empty($product_data['purchase_price']) && !is_numeric($product_data['purchase_price'])) {
            $this->errors[] = "Row {$row_number}: Purchase price must be numeric";
        }
        
        if (!empty($product_data['selling_price']) && !is_numeric($product_data['selling_price'])) {
            $this->errors[] = "Row {$row_number}: Selling price must be numeric";
        }
        
        // التحقق من منطقية الأسعار
        if (!empty($product_data['purchase_price']) && !empty($product_data['selling_price'])) {
            if (floatval($product_data['selling_price']) < floatval($product_data['purchase_price'])) {
                $this->errors[] = "Row {$row_number}: Selling price should be higher than purchase price";
            }
        }
        
        return empty($this->errors);
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    private function isValidPhoneNumber($phone) {
        // التحقق من أرقام الهاتف المصرية
        $pattern = '/^(\+2|002)?01[0-9]{9}$/';
        return preg_match($pattern, $phone);
    }
}
```

### **🔄 3. Data Converter (محول البيانات)**

#### **الملف:** `tools/DataConverter.php`

```php
<?php
/**
 * أداة تحويل البيانات من تنسيقات مختلفة
 */
class DataConverter {
    
    public function convertFromOdoo($odoo_export_file) {
        $odoo_data = $this->readCSV($odoo_export_file);
        $aym_data = [];
        
        foreach ($odoo_data as $row) {
            $aym_row = [
                'firstname' => $row['name'],
                'email' => $row['email'],
                'telephone' => $this->formatPhone($row['phone']),
                'address_1' => $row['street'],
                'city' => $row['city'],
                'country' => $this->mapCountry($row['country_id']),
                'customer_group' => $this->mapCustomerGroup($row['category_id'])
            ];
            $aym_data[] = $aym_row;
        }
        
        return $this->writeExcel($aym_data, 'customers_from_odoo.xlsx');
    }
    
    public function convertFromWooCommerce($woo_export_file) {
        $woo_data = $this->readCSV($woo_export_file);
        $aym_data = [];
        
        foreach ($woo_data as $row) {
            $aym_row = [
                'firstname' => $row['billing_first_name'],
                'lastname' => $row['billing_last_name'],
                'email' => $row['billing_email'],
                'telephone' => $this->formatPhone($row['billing_phone']),
                'address_1' => $row['billing_address_1'],
                'address_2' => $row['billing_address_2'],
                'city' => $row['billing_city'],
                'postal_code' => $row['billing_postcode'],
                'country' => $row['billing_country']
            ];
            $aym_data[] = $aym_row;
        }
        
        return $this->writeExcel($aym_data, 'customers_from_woocommerce.xlsx');
    }
    
    public function convertFromShopify($shopify_export_file) {
        $shopify_data = $this->readCSV($shopify_export_file);
        $aym_data = [];
        
        foreach ($shopify_data as $row) {
            $aym_row = [
                'firstname' => $row['First Name'],
                'lastname' => $row['Last Name'],
                'email' => $row['Email'],
                'telephone' => $this->formatPhone($row['Phone']),
                'address_1' => $row['Default Address Address1'],
                'address_2' => $row['Default Address Address2'],
                'city' => $row['Default Address City'],
                'postal_code' => $row['Default Address Zip'],
                'country' => $row['Default Address Country']
            ];
            $aym_data[] = $aym_row;
        }
        
        return $this->writeExcel($aym_data, 'customers_from_shopify.xlsx');
    }
}
```

## 📋 دليل الاستخدام للعملاء

### **📖 1. دليل الهجرة من Odoo**

#### **الخطوات:**
1. **تصدير البيانات من Odoo:**
   - اذهب إلى Contacts → Export
   - اختر الحقول المطلوبة
   - صدر كملف CSV

2. **استخدام أداة التحويل:**
   - ارفع ملف CSV إلى أداة التحويل
   - اختر "Convert from Odoo"
   - حمل ملف Excel المحول

3. **مراجعة البيانات:**
   - افتح ملف Excel
   - راجع البيانات وصحح الأخطاء
   - احفظ الملف

4. **استيراد إلى AYM ERP:**
   - اذهب إلى Tools → Import Data
   - اختر "Customers Import"
   - ارفع ملف Excel
   - اتبع معالج الاستيراد

### **📖 2. دليل الهجرة من WooCommerce**

#### **الخطوات:**
1. **تصدير البيانات من WooCommerce:**
   - استخدم plugin "WooCommerce Customer/Order/Coupon Export"
   - صدر العملاء والطلبات والمنتجات

2. **استخدام أداة التحويل:**
   - ارفع ملفات CSV
   - اختر "Convert from WooCommerce"
   - حمل ملفات Excel المحولة

3. **الاستيراد المتدرج:**
   - ابدأ بالعملاء
   - ثم المنتجات
   - أخيراً الطلبات

### **📖 3. دليل الهجرة من Shopify**

#### **الخطوات:**
1. **تصدير البيانات من Shopify:**
   - اذهب إلى Settings → Data Export
   - صدر العملاء والمنتجات والطلبات

2. **استخدام أداة التحويل:**
   - ارفع ملفات CSV
   - اختر "Convert from Shopify"
   - حمل ملفات Excel المحولة

3. **التحقق والاستيراد:**
   - راجع البيانات المحولة
   - استورد البيانات بالترتيب الصحيح

## 🎯 خطة تطوير قوالب الهجرة

### **📅 الأسبوع الأول:**
1. **إنشاء القوالب الأساسية:** العملاء، المنتجات، الموردين
2. **تطوير أدوات التحويل:** DataMapper, DataValidator
3. **اختبار القوالب:** مع بيانات تجريبية

### **📅 الأسبوع الثاني:**
1. **تطوير محولات المنافسين:** Odoo, WooCommerce, Shopify
2. **إنشاء واجهة المستخدم:** لرفع وتحويل الملفات
3. **كتابة الأدلة:** دليل المستخدم لكل نظام

### **📅 الأسبوع الثالث:**
1. **اختبار شامل:** مع عملاء حقيقيين
2. **تحسين الأدوات:** بناءً على التغذية الراجعة
3. **إطلاق الخدمة:** للعملاء

### **📊 معايير النجاح:**
- **دقة التحويل:** 99%+ للبيانات الأساسية
- **سرعة الهجرة:** أقل من يوم واحد لـ 10,000 سجل
- **سهولة الاستخدام:** دليل واضح لكل خطوة
- **دعم شامل:** لجميع الأنظمة المنافسة الرئيسية
