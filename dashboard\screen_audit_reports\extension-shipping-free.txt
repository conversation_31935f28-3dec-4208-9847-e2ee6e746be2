📄 Route: extension/shipping/free
📂 Controller: controller\extension\shipping\free.php
🧱 Models used (2):
   ✅ setting/setting (5 functions)
   ✅ localisation/geo_zone (11 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\shipping\free.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\shipping\free.php (10 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\shipping\free.php (10 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (24):
   - action
   - button_save
   - column_left
   - entry_geo_zone
   - entry_sort_order
   - entry_status
   - entry_total
   - error_permission
   - error_warning
   - footer
   - header
   - help_total
   - shipping_free_sort_order
   - shipping_free_total
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_success
   ... و 4 متغير آخر

❌ Missing in Arabic (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_free_sort_order
   - shipping_free_total
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home

❌ Missing in English (14):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_free_sort_order
   - shipping_free_total
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 14 items
      - shipping_free_total
      - button_save
      - error_warning
      - column_left
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 14 items
      - shipping_free_total
      - button_save
      - error_warning
      - column_left
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 14 متغير عربي و 14 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:29
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.