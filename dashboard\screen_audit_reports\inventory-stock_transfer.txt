📄 Route: inventory/stock_transfer
📂 Controller: controller\inventory\stock_transfer.php
🧱 Models used (6):
   ❌ common/central_service_manager (0 functions)
   ✅ inventory/stock_transfer (35 functions)
   ❌ inventory/branch (0 functions)
   ✅ user/user (47 functions)
   ✅ inventory/stock_transfer_enhanced (21 functions)
   ✅ catalog/product (128 functions)
🎨 Twig templates (1):
   ✅ view\template\inventory\stock_transfer.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\inventory\stock_transfer.php (387 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\stock_transfer.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (139):
   - button_add_transfer
   - button_approve
   - button_bulk_print
   - button_filter
   - button_refresh
   - column_from_warehouse
   - column_status
   - column_transfer_name
   - datetime_format
   - error_permission
   - error_product_required
   - error_transfer_not_found
   - filter_transfer_number
   - pagination
   - sort_date_created
   - success_approve
   - text_approved_success
   - text_pending_transfers
   - text_shipped_success
   - text_transfer_type_return
   ... و 119 متغير آخر

❌ Missing in Arabic (64):
   - button_close
   - column_from_warehouse
   - error_invalid_item
   - error_movement_failed_for_product
   - error_no_transfers_selected
   - filter_date_end
   - header
   - pagination
   - sort_from_warehouse
   - success_approve
   - success_ship
   - text_bulk_actions
   - text_filter
   - text_pending_transfers
   - text_select_action
   ... و 49 متغير آخر

❌ Missing in English (139):
   - button_bulk_print
   - button_refresh
   - column_from_warehouse
   - datetime_format
   - error_permission
   - error_product_required
   - error_transfer_not_found
   - filter_transfer_number
   - pagination
   - sort_date_created
   - success_approve
   - text_approved_success
   - text_pending_transfers
   - text_shipped_success
   - text_transfer_type_return
   ... و 124 متغير آخر

🗄️ Database Tables Used (2):
   ❌ statements
   ❌ stock

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 64 items
      - success_approve
      - success_ship
      - button_close
      - filter_date_end
      - pagination
   🟡 MISSING_ENGLISH_VARIABLES: 139 items
      - text_shipped_success
      - success_approve
      - pagination
      - button_refresh
      - text_pending_transfers
   🔴 INVALID_DATABASE_TABLES: 2 items
      - stock
      - statements
   🟢 MISSING_MODEL_FILES: 2 items
      - common/central_service_manager
      - inventory/branch

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 64 متغير عربي و 139 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:07
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.