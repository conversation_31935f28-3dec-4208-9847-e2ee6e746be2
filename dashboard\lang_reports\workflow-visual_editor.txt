📄 Route: workflow/visual_editor
📂 Controller: controller\workflow\visual_editor.php
🧱 Models used (4):
   - hr/department
   - user/user
   - user/user_group
   - workflow/workflow
🎨 Twig templates (1):
   - view\template\workflow\visual_editor.twig
🈯 Arabic Language Files (1):
   - language\ar\workflow\visual_editor.php
🇬🇧 English Language Files (1):
   - language\en-gb\workflow\visual_editor.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_cancel
   - button_save
   - entry_department
   - entry_description
   - entry_escalation_after_days
   - entry_escalation_enabled
   - entry_name
   - entry_notify_creator
   - entry_status
   - entry_workflow_type
   - error_invalid_node
   - error_name
   - error_permission
   - heading_title
   - tab_general
   - tab_visual_editor
   - text_active
   - text_add
   - text_approval
   - text_approver
   - text_archived
   - text_click_to_configure
   - text_condition
   - text_configure
   - text_configure_node
   - text_delay
   - text_document_approval
   - text_edit
   - text_email
   - text_expense_claim
   - text_function
   - text_home
   - text_inactive
   - text_leave_request
   - text_no
   - text_node
   - text_nodes
   - text_none
   - text_notification
   - text_other
   - text_payment_approval
   - text_purchase_approval
   - text_recipient
   - text_success
   - text_task
   - text_trigger
   - text_webhook
   - text_workflow
   - text_yes

❌ Missing in Arabic:
   - button_cancel
   - button_save
   - entry_department
   - entry_description
   - entry_escalation_after_days
   - entry_escalation_enabled
   - entry_name
   - entry_notify_creator
   - entry_status
   - entry_workflow_type
   - error_invalid_node
   - error_name
   - error_permission
   - heading_title
   - tab_general
   - tab_visual_editor
   - text_active
   - text_add
   - text_approval
   - text_approver
   - text_archived
   - text_click_to_configure
   - text_condition
   - text_configure
   - text_configure_node
   - text_delay
   - text_document_approval
   - text_edit
   - text_email
   - text_expense_claim
   - text_function
   - text_home
   - text_inactive
   - text_leave_request
   - text_no
   - text_node
   - text_nodes
   - text_none
   - text_notification
   - text_other
   - text_payment_approval
   - text_purchase_approval
   - text_recipient
   - text_success
   - text_task
   - text_trigger
   - text_webhook
   - text_workflow
   - text_yes

❌ Missing in English:
   - button_cancel
   - button_save
   - entry_department
   - entry_description
   - entry_escalation_after_days
   - entry_escalation_enabled
   - entry_name
   - entry_notify_creator
   - entry_status
   - entry_workflow_type
   - error_invalid_node
   - error_name
   - error_permission
   - heading_title
   - tab_general
   - tab_visual_editor
   - text_active
   - text_add
   - text_approval
   - text_approver
   - text_archived
   - text_click_to_configure
   - text_condition
   - text_configure
   - text_configure_node
   - text_delay
   - text_document_approval
   - text_edit
   - text_email
   - text_expense_claim
   - text_function
   - text_home
   - text_inactive
   - text_leave_request
   - text_no
   - text_node
   - text_nodes
   - text_none
   - text_notification
   - text_other
   - text_payment_approval
   - text_purchase_approval
   - text_recipient
   - text_success
   - text_task
   - text_trigger
   - text_webhook
   - text_workflow
   - text_yes

💡 Suggested Arabic Additions:
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - entry_department = ""  # TODO: ترجمة عربية
   - entry_description = ""  # TODO: ترجمة عربية
   - entry_escalation_after_days = ""  # TODO: ترجمة عربية
   - entry_escalation_enabled = ""  # TODO: ترجمة عربية
   - entry_name = ""  # TODO: ترجمة عربية
   - entry_notify_creator = ""  # TODO: ترجمة عربية
   - entry_status = ""  # TODO: ترجمة عربية
   - entry_workflow_type = ""  # TODO: ترجمة عربية
   - error_invalid_node = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - tab_general = ""  # TODO: ترجمة عربية
   - tab_visual_editor = ""  # TODO: ترجمة عربية
   - text_active = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_approval = ""  # TODO: ترجمة عربية
   - text_approver = ""  # TODO: ترجمة عربية
   - text_archived = ""  # TODO: ترجمة عربية
   - text_click_to_configure = ""  # TODO: ترجمة عربية
   - text_condition = ""  # TODO: ترجمة عربية
   - text_configure = ""  # TODO: ترجمة عربية
   - text_configure_node = ""  # TODO: ترجمة عربية
   - text_delay = ""  # TODO: ترجمة عربية
   - text_document_approval = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_email = ""  # TODO: ترجمة عربية
   - text_expense_claim = ""  # TODO: ترجمة عربية
   - text_function = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_inactive = ""  # TODO: ترجمة عربية
   - text_leave_request = ""  # TODO: ترجمة عربية
   - text_no = ""  # TODO: ترجمة عربية
   - text_node = ""  # TODO: ترجمة عربية
   - text_nodes = ""  # TODO: ترجمة عربية
   - text_none = ""  # TODO: ترجمة عربية
   - text_notification = ""  # TODO: ترجمة عربية
   - text_other = ""  # TODO: ترجمة عربية
   - text_payment_approval = ""  # TODO: ترجمة عربية
   - text_purchase_approval = ""  # TODO: ترجمة عربية
   - text_recipient = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_task = ""  # TODO: ترجمة عربية
   - text_trigger = ""  # TODO: ترجمة عربية
   - text_webhook = ""  # TODO: ترجمة عربية
   - text_workflow = ""  # TODO: ترجمة عربية
   - text_yes = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_cancel = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - entry_department = ""  # TODO: English translation
   - entry_description = ""  # TODO: English translation
   - entry_escalation_after_days = ""  # TODO: English translation
   - entry_escalation_enabled = ""  # TODO: English translation
   - entry_name = ""  # TODO: English translation
   - entry_notify_creator = ""  # TODO: English translation
   - entry_status = ""  # TODO: English translation
   - entry_workflow_type = ""  # TODO: English translation
   - error_invalid_node = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - tab_general = ""  # TODO: English translation
   - tab_visual_editor = ""  # TODO: English translation
   - text_active = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_approval = ""  # TODO: English translation
   - text_approver = ""  # TODO: English translation
   - text_archived = ""  # TODO: English translation
   - text_click_to_configure = ""  # TODO: English translation
   - text_condition = ""  # TODO: English translation
   - text_configure = ""  # TODO: English translation
   - text_configure_node = ""  # TODO: English translation
   - text_delay = ""  # TODO: English translation
   - text_document_approval = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_email = ""  # TODO: English translation
   - text_expense_claim = ""  # TODO: English translation
   - text_function = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_inactive = ""  # TODO: English translation
   - text_leave_request = ""  # TODO: English translation
   - text_no = ""  # TODO: English translation
   - text_node = ""  # TODO: English translation
   - text_nodes = ""  # TODO: English translation
   - text_none = ""  # TODO: English translation
   - text_notification = ""  # TODO: English translation
   - text_other = ""  # TODO: English translation
   - text_payment_approval = ""  # TODO: English translation
   - text_purchase_approval = ""  # TODO: English translation
   - text_recipient = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_task = ""  # TODO: English translation
   - text_trigger = ""  # TODO: English translation
   - text_webhook = ""  # TODO: English translation
   - text_workflow = ""  # TODO: English translation
   - text_yes = ""  # TODO: English translation
