📄 Route: marketplace/marketplace
📂 Controller: controller\marketplace\marketplace.php
🧱 Models used (1):
   ✅ setting/extension (11 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\marketplace\marketplace.php (62 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\marketplace\marketplace.php (62 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (32):
   - button_reply
   - date_format_short
   - error_comment
   - error_install
   - error_opencart
   - error_permission
   - error_pin
   - error_purchase
   - text_install
   - text_language
   - text_marketplace
   - text_name
   - text_other
   - text_payment
   - text_price
   - text_rating
   - text_report
   - text_shipping
   - text_theme
   - text_total
   ... و 12 متغير آخر

❌ Missing in Arabic (5):
   - button_more
   - date_format_short
   - error_comment
   - error_pin
   - text_home

❌ Missing in English (5):
   - button_more
   - date_format_short
   - error_comment
   - error_pin
   - text_home

🗄️ Database Tables Used (1):
   ❌ the

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 5 items
      - error_comment
      - text_home
      - error_pin
      - button_more
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 5 items
      - error_comment
      - text_home
      - error_pin
      - button_more
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 1 items
      - the

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 5 متغير عربي و 5 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.