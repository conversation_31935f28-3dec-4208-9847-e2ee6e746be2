📄 Route: accounts/aging_report
📂 Controller: controller\accounts\aging_report.php
🧱 Models used (5):
   - accounts/aging_report
   - branch/branch
   - core/central_service_manager
   - customer/customer
   - supplier/supplier
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\aging_report.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\aging_report.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - error_date_end
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_0_30
   - text_0_30_days
   - text_31_60
   - text_31_60_days
   - text_61_90
   - text_61_90_days
   - text_aging_report
   - text_buckets
   - text_customer
   - text_customer_details
   - text_customer_name
   - text_form
   - text_home
   - text_no_results
   - text_over_90
   - text_over_90_days
   - text_period_end
   - text_success_generate
   - text_total

❌ Missing in Arabic:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - error_date_end
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_0_30
   - text_0_30_days
   - text_31_60
   - text_31_60_days
   - text_61_90
   - text_61_90_days
   - text_aging_report
   - text_buckets
   - text_customer
   - text_customer_details
   - text_customer_name
   - text_form
   - text_home
   - text_no_results
   - text_over_90
   - text_over_90_days
   - text_period_end
   - text_success_generate
   - text_total

❌ Missing in English:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - error_date_end
   - error_no_data
   - error_permission
   - heading_title
   - print_title
   - text_0_30
   - text_0_30_days
   - text_31_60
   - text_31_60_days
   - text_61_90
   - text_61_90_days
   - text_aging_report
   - text_buckets
   - text_customer
   - text_customer_details
   - text_customer_name
   - text_form
   - text_home
   - text_no_results
   - text_over_90
   - text_over_90_days
   - text_period_end
   - text_success_generate
   - text_total

💡 Suggested Arabic Additions:
   - button_filter = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - error_date_end = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_0_30 = ""  # TODO: ترجمة عربية
   - text_0_30_days = ""  # TODO: ترجمة عربية
   - text_31_60 = ""  # TODO: ترجمة عربية
   - text_31_60_days = ""  # TODO: ترجمة عربية
   - text_61_90 = ""  # TODO: ترجمة عربية
   - text_61_90_days = ""  # TODO: ترجمة عربية
   - text_aging_report = ""  # TODO: ترجمة عربية
   - text_buckets = ""  # TODO: ترجمة عربية
   - text_customer = ""  # TODO: ترجمة عربية
   - text_customer_details = ""  # TODO: ترجمة عربية
   - text_customer_name = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_over_90 = ""  # TODO: ترجمة عربية
   - text_over_90_days = ""  # TODO: ترجمة عربية
   - text_period_end = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_total = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_filter = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - error_date_end = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_0_30 = ""  # TODO: English translation
   - text_0_30_days = ""  # TODO: English translation
   - text_31_60 = ""  # TODO: English translation
   - text_31_60_days = ""  # TODO: English translation
   - text_61_90 = ""  # TODO: English translation
   - text_61_90_days = ""  # TODO: English translation
   - text_aging_report = ""  # TODO: English translation
   - text_buckets = ""  # TODO: English translation
   - text_customer = ""  # TODO: English translation
   - text_customer_details = ""  # TODO: English translation
   - text_customer_name = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_over_90 = ""  # TODO: English translation
   - text_over_90_days = ""  # TODO: English translation
   - text_period_end = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_total = ""  # TODO: English translation
