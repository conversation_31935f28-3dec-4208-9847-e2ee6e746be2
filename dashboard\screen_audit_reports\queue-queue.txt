📄 Route: queue/queue
📂 Controller: controller\queue\queue.php
🧱 Models used (1):
   ✅ queue/queue (19 functions)
🎨 Twig templates (1):
   ✅ view\template\queue\queue.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\queue\queue.php (656 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\queue\queue.php (476 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (55):
   - button_close
   - button_retry
   - button_view
   - column_attempts
   - column_job_type
   - delete
   - entry_date_from
   - error_permission
   - pagination
   - text_cleanup_success
   - text_confirm_cancel
   - text_confirm_retry
   - text_failed
   - text_filter
   - text_list
   - text_low
   - text_pending
   - text_process_success
   - text_processing
   - text_reset_success
   ... و 35 متغير آخر

❌ Missing in Arabic (14):
   - datepicker
   - delete
   - filter_date_from
   - filter_date_to
   - heading
   - pagination
   - results
   - text_cancel_success
   - text_cleanup_success
   - text_home
   - text_process_success
   - text_reset_success
   - text_retry_success
   - user_token

❌ Missing in English (14):
   - datepicker
   - delete
   - filter_date_from
   - filter_date_to
   - heading
   - pagination
   - results
   - text_cancel_success
   - text_cleanup_success
   - text_home
   - text_process_success
   - text_reset_success
   - text_retry_success
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 14 items
      - user_token
      - filter_date_to
      - pagination
      - text_home
      - text_cleanup_success
   🟡 MISSING_ENGLISH_VARIABLES: 14 items
      - user_token
      - filter_date_to
      - pagination
      - text_home
      - text_cleanup_success

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 14 متغير عربي و 14 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.