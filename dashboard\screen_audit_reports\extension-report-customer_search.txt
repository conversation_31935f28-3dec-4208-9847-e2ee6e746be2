📄 Route: extension/report/customer_search
📂 Controller: controller\extension\report\customer_search.php
🧱 Models used (3):
   ✅ setting/setting (5 functions)
   ✅ extension/report/customer (12 functions)
   ✅ catalog/category (15 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\report\customer_search.php (21 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\report\customer_search.php (21 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (9):
   - datetime_format
   - error_permission
   - heading_title
   - text_customer
   - text_extension
   - text_guest
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (3):
   - datetime_format
   - text_home
   - text_pagination

❌ Missing in English (3):
   - datetime_format
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - datetime_format
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - datetime_format

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:28
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.