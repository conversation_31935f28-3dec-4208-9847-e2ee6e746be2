📄 Route: finance/cash_bank
📂 Controller: controller\finance\cash_bank.php
🧱 Models used (1):
   - finance/cash_bank
🎨 Twig templates (1):
   - view\template\finance\cash_bank.twig
🈯 Arabic Language Files (1):
   - language\ar\finance\cash_bank.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - error_permission
   - heading_title
   - text_home

❌ Missing in Arabic:
   - datetime_format
   - error_permission
   - heading_title
   - text_home

❌ Missing in English:
   - datetime_format
   - error_permission
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
