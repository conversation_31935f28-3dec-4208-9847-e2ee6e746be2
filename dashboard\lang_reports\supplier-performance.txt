📄 Route: supplier/performance
📂 Controller: controller\supplier\performance.php
🧱 Models used (2):
   - supplier/performance
   - supplier/supplier
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\supplier\performance.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_period
   - error_permission
   - error_scores
   - error_supplier
   - heading_title
   - text_evaluate
   - text_home
   - text_never
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - error_period
   - error_permission
   - error_scores
   - error_supplier
   - heading_title
   - text_evaluate
   - text_home
   - text_never
   - text_pagination
   - text_success

❌ Missing in English:
   - date_format_short
   - error_period
   - error_permission
   - error_scores
   - error_supplier
   - heading_title
   - text_evaluate
   - text_home
   - text_never
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_period = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_scores = ""  # TODO: ترجمة عربية
   - error_supplier = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_evaluate = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_never = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_period = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_scores = ""  # TODO: English translation
   - error_supplier = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_evaluate = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_never = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
