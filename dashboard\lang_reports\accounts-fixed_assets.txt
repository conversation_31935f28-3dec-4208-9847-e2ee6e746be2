📄 Route: accounts/fixed_assets
📂 Controller: controller\accounts\fixed_assets.php
🧱 Models used (2):
   - accounts/fixed_assets
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\fixed_assets.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\fixed_assets.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - error_asset_id_required
   - error_category_required
   - error_cost_required
   - error_depreciation_method_required
   - error_name_required
   - heading_title
   - print_title
   - text_accum_depr
   - text_assets
   - text_buildings
   - text_computers
   - text_declining_balance
   - text_end_date
   - text_equipment
   - text_fixed_assets_report
   - text_form
   - text_furniture
   - text_home
   - text_net_value
   - text_no_results
   - text_other
   - text_straight_line
   - text_success_add
   - text_success_dispose
   - text_sum_of_years
   - text_vehicles

❌ Missing in Arabic:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - error_asset_id_required
   - error_category_required
   - error_cost_required
   - error_depreciation_method_required
   - error_name_required
   - heading_title
   - print_title
   - text_accum_depr
   - text_assets
   - text_buildings
   - text_computers
   - text_declining_balance
   - text_end_date
   - text_equipment
   - text_fixed_assets_report
   - text_form
   - text_furniture
   - text_home
   - text_net_value
   - text_no_results
   - text_other
   - text_straight_line
   - text_success_add
   - text_success_dispose
   - text_sum_of_years
   - text_vehicles

❌ Missing in English:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - error_asset_id_required
   - error_category_required
   - error_cost_required
   - error_depreciation_method_required
   - error_name_required
   - heading_title
   - print_title
   - text_accum_depr
   - text_assets
   - text_buildings
   - text_computers
   - text_declining_balance
   - text_end_date
   - text_equipment
   - text_fixed_assets_report
   - text_form
   - text_furniture
   - text_home
   - text_net_value
   - text_no_results
   - text_other
   - text_straight_line
   - text_success_add
   - text_success_dispose
   - text_sum_of_years
   - text_vehicles

💡 Suggested Arabic Additions:
   - button_filter = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - error_asset_id_required = ""  # TODO: ترجمة عربية
   - error_category_required = ""  # TODO: ترجمة عربية
   - error_cost_required = ""  # TODO: ترجمة عربية
   - error_depreciation_method_required = ""  # TODO: ترجمة عربية
   - error_name_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_accum_depr = ""  # TODO: ترجمة عربية
   - text_assets = ""  # TODO: ترجمة عربية
   - text_buildings = ""  # TODO: ترجمة عربية
   - text_computers = ""  # TODO: ترجمة عربية
   - text_declining_balance = ""  # TODO: ترجمة عربية
   - text_end_date = ""  # TODO: ترجمة عربية
   - text_equipment = ""  # TODO: ترجمة عربية
   - text_fixed_assets_report = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_furniture = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_net_value = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_other = ""  # TODO: ترجمة عربية
   - text_straight_line = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_dispose = ""  # TODO: ترجمة عربية
   - text_sum_of_years = ""  # TODO: ترجمة عربية
   - text_vehicles = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_filter = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - error_asset_id_required = ""  # TODO: English translation
   - error_category_required = ""  # TODO: English translation
   - error_cost_required = ""  # TODO: English translation
   - error_depreciation_method_required = ""  # TODO: English translation
   - error_name_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_accum_depr = ""  # TODO: English translation
   - text_assets = ""  # TODO: English translation
   - text_buildings = ""  # TODO: English translation
   - text_computers = ""  # TODO: English translation
   - text_declining_balance = ""  # TODO: English translation
   - text_end_date = ""  # TODO: English translation
   - text_equipment = ""  # TODO: English translation
   - text_fixed_assets_report = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_furniture = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_net_value = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_other = ""  # TODO: English translation
   - text_straight_line = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_dispose = ""  # TODO: English translation
   - text_sum_of_years = ""  # TODO: English translation
   - text_vehicles = ""  # TODO: English translation
