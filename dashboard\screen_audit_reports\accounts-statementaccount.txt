📄 Route: accounts/statementaccount
📂 Controller: controller\accounts\statementaccount.php
🧱 Models used (3):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/chartaccount (19 functions)
   ✅ accounts/statementaccount (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\statementaccount.php (9 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\statementaccount.php (7 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - button_submit
   - code
   - date_format_short
   - direction
   - heading_title
   - print_title
   - text_account_end
   - text_account_start
   - text_date_end
   - text_date_start
   - text_no_results

❌ Missing in Arabic (4):
   - code
   - date_format_short
   - direction
   - print_title

❌ Missing in English (4):
   - code
   - date_format_short
   - direction
   - print_title

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - direction
      - code
      - print_title
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 4 items
      - direction
      - code
      - print_title
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 4 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:44
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.