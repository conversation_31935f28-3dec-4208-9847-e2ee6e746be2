📄 Route: accounts/aging_report
📂 Controller: controller\accounts\aging_report.php
🧱 Models used (5):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/aging_report (10 functions)
   ✅ customer/customer (51 functions)
   ✅ supplier/supplier (21 functions)
   ✅ branch/branch (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\aging_report.php (134 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\aging_report.php (134 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (29):
   - button_filter
   - code
   - date_format_short
   - entry_date_end
   - error_no_data
   - error_permission
   - print_title
   - text_0_30
   - text_0_30_days
   - text_31_60
   - text_31_60_days
   - text_61_90
   - text_buckets
   - text_customer
   - text_customer_name
   - text_home
   - text_no_results
   - text_over_90
   - text_success_generate
   - text_total
   ... و 9 متغير آخر

❌ Missing in Arabic (4):
   - code
   - date_format_short
   - direction
   - text_home

❌ Missing in English (4):
   - code
   - date_format_short
   - direction
   - text_home

🗄️ Database Tables Used (3):
   ❌ credit_limit
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - text_home
      - code
      - direction
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 4 items
      - text_home
      - code
      - direction
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 3 items
      - workflow
      - template
      - credit_limit

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 4 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 3 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:31
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.