📄 Route: common/developer
📂 Controller: controller\common\developer.php
🧱 Models used (1):
   - setting/setting
🎨 Twig templates (1):
   - view\template\common\developer.twig
🈯 Arabic Language Files (1):
   - language\ar\common\developer.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\developer.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_permission
   - text_cache
   - text_sass
   - text_success
   - text_theme

❌ Missing in Arabic:
   - error_permission
   - text_cache
   - text_sass
   - text_success
   - text_theme

❌ Missing in English:
   - error_permission
   - text_cache
   - text_sass
   - text_success
   - text_theme

💡 Suggested Arabic Additions:
   - error_permission = ""  # TODO: ترجمة عربية
   - text_cache = ""  # TODO: ترجمة عربية
   - text_sass = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_theme = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_permission = ""  # TODO: English translation
   - text_cache = ""  # TODO: English translation
   - text_sass = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_theme = ""  # TODO: English translation
