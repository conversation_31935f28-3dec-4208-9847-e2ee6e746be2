📄 Route: report/inventory_trends
📂 Controller: controller\report\inventory_trends.php
🧱 Models used (2):
   - branch/branch
   - report/inventory_trends
🎨 Twig templates (1):
   - view\template\report\inventory_trends.twig
🈯 Arabic Language Files (1):
   - language\ar\report\inventory_trends.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_movement
   - heading_title
   - text_adjustment
   - text_all_movements
   - text_day
   - text_forecast_analysis
   - text_forecast_analysis_desc
   - text_home
   - text_in
   - text_month
   - text_movement_analysis
   - text_movement_analysis_desc
   - text_out
   - text_pagination
   - text_quarter
   - text_seasonality_analysis
   - text_seasonality_analysis_desc
   - text_transfer
   - text_turnover_analysis
   - text_turnover_analysis_desc
   - text_week
   - text_year

❌ Missing in Arabic:
   - heading_movement
   - heading_title
   - text_adjustment
   - text_all_movements
   - text_day
   - text_forecast_analysis
   - text_forecast_analysis_desc
   - text_home
   - text_in
   - text_month
   - text_movement_analysis
   - text_movement_analysis_desc
   - text_out
   - text_pagination
   - text_quarter
   - text_seasonality_analysis
   - text_seasonality_analysis_desc
   - text_transfer
   - text_turnover_analysis
   - text_turnover_analysis_desc
   - text_week
   - text_year

❌ Missing in English:
   - heading_movement
   - heading_title
   - text_adjustment
   - text_all_movements
   - text_day
   - text_forecast_analysis
   - text_forecast_analysis_desc
   - text_home
   - text_in
   - text_month
   - text_movement_analysis
   - text_movement_analysis_desc
   - text_out
   - text_pagination
   - text_quarter
   - text_seasonality_analysis
   - text_seasonality_analysis_desc
   - text_transfer
   - text_turnover_analysis
   - text_turnover_analysis_desc
   - text_week
   - text_year

💡 Suggested Arabic Additions:
   - heading_movement = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_adjustment = ""  # TODO: ترجمة عربية
   - text_all_movements = ""  # TODO: ترجمة عربية
   - text_day = ""  # TODO: ترجمة عربية
   - text_forecast_analysis = ""  # TODO: ترجمة عربية
   - text_forecast_analysis_desc = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_in = ""  # TODO: ترجمة عربية
   - text_month = ""  # TODO: ترجمة عربية
   - text_movement_analysis = ""  # TODO: ترجمة عربية
   - text_movement_analysis_desc = ""  # TODO: ترجمة عربية
   - text_out = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_quarter = ""  # TODO: ترجمة عربية
   - text_seasonality_analysis = ""  # TODO: ترجمة عربية
   - text_seasonality_analysis_desc = ""  # TODO: ترجمة عربية
   - text_transfer = ""  # TODO: ترجمة عربية
   - text_turnover_analysis = ""  # TODO: ترجمة عربية
   - text_turnover_analysis_desc = ""  # TODO: ترجمة عربية
   - text_week = ""  # TODO: ترجمة عربية
   - text_year = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_movement = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_adjustment = ""  # TODO: English translation
   - text_all_movements = ""  # TODO: English translation
   - text_day = ""  # TODO: English translation
   - text_forecast_analysis = ""  # TODO: English translation
   - text_forecast_analysis_desc = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_in = ""  # TODO: English translation
   - text_month = ""  # TODO: English translation
   - text_movement_analysis = ""  # TODO: English translation
   - text_movement_analysis_desc = ""  # TODO: English translation
   - text_out = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_quarter = ""  # TODO: English translation
   - text_seasonality_analysis = ""  # TODO: English translation
   - text_seasonality_analysis_desc = ""  # TODO: English translation
   - text_transfer = ""  # TODO: English translation
   - text_turnover_analysis = ""  # TODO: English translation
   - text_turnover_analysis_desc = ""  # TODO: English translation
   - text_week = ""  # TODO: English translation
   - text_year = ""  # TODO: English translation
