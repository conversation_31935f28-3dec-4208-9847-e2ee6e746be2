📄 Route: service/warranty
📂 Controller: controller\service\warranty.php
🧱 Models used (1):
   ✅ service/warranty (14 functions)
🎨 Twig templates (1):
   ✅ view\template\service\warranty.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\service\warranty.php (39 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\service\warranty.php (62 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (90):
   - button_add
   - button_filter
   - column_start_date
   - column_warranty_period
   - column_warranty_status
   - entry_customer
   - entry_warranty_number
   - error_not_found
   - error_permission
   - filter_product
   - sort_end_date
   - sort_order_id
   - sort_warranty_number
   - text_active
   - text_ajax_error
   - text_confirm_delete
   - text_list
   - text_product_id
   - text_warranty_list
   - text_warranty_status
   ... و 70 متغير آخر

❌ Missing in Arabic (51):
   - button_add
   - button_delete
   - column_warranty_period
   - column_warranty_type
   - date_format_short
   - delete
   - entry_customer
   - error_warranty_period
   - header
   - sort_end_date
   - sort_order_id
   - text_confirm
   - text_list
   - text_status_cancelled
   - text_view_warranty
   ... و 36 متغير آخر

❌ Missing in English (52):
   - button_add_warranty
   - button_close
   - column_warranty_status
   - date_format_short
   - delete
   - error_not_found
   - header
   - sort_end_date
   - sort_order_id
   - text_ajax_error
   - text_claimed
   - text_filter
   - text_product_id
   - text_warranty_list
   - text_warranty_status
   ... و 37 متغير آخر

🗄️ Database Tables Used (3):
   ✅ cod_customer
   ✅ cod_product
   ✅ cod_warranty

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 51 items
      - text_view_warranty
      - button_delete
      - sort_end_date
      - button_add
      - entry_customer
   🟡 MISSING_ENGLISH_VARIABLES: 52 items
      - button_add_warranty
      - button_close
      - sort_end_date
      - text_warranty_list
      - text_ajax_error

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 51 متغير عربي و 52 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:18
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.