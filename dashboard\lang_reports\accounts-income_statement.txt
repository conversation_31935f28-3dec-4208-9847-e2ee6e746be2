📄 Route: accounts/income_statement
📂 Controller: controller\accounts\income_statement.php
🧱 Models used (5):
   - accounts/cost_center
   - accounts/income_statement
   - core/central_service_manager
   - localisation/branch
   - setting/branch
🎨 Twig templates (1):
   - view\template\accounts\income_statement.twig
🈯 Arabic Language Files (1):
   - language\ar\accounts\income_statement.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\income_statement.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - code
   - date_format_short
   - direction
   - error_date_from_required
   - error_date_range
   - error_date_to_required
   - error_form
   - error_generate
   - error_invalid_data
   - error_missing_parameters
   - error_no_analysis_data
   - error_no_data
   - error_period1_required
   - error_period2_required
   - error_permission
   - heading_title
   - print_title
   - text_advanced_analysis
   - text_all_branches
   - text_all_cost_centers
   - text_analysis_view
   - text_expenses
   - text_from
   - text_home
   - text_income_statement
   - text_net_income
   - text_period
   - text_revenues
   - text_success_analysis
   - text_success_compare
   - text_success_generate
   - text_to
   - text_total_expenses
   - text_total_revenues

❌ Missing in Arabic:
   - code
   - date_format_short
   - direction
   - error_date_from_required
   - error_date_range
   - error_date_to_required
   - error_form
   - error_generate
   - error_invalid_data
   - error_missing_parameters
   - error_no_analysis_data
   - error_no_data
   - error_period1_required
   - error_period2_required
   - error_permission
   - heading_title
   - print_title
   - text_advanced_analysis
   - text_all_branches
   - text_all_cost_centers
   - text_analysis_view
   - text_expenses
   - text_from
   - text_home
   - text_income_statement
   - text_net_income
   - text_period
   - text_revenues
   - text_success_analysis
   - text_success_compare
   - text_success_generate
   - text_to
   - text_total_expenses
   - text_total_revenues

❌ Missing in English:
   - code
   - date_format_short
   - direction
   - error_date_from_required
   - error_date_range
   - error_date_to_required
   - error_form
   - error_generate
   - error_invalid_data
   - error_missing_parameters
   - error_no_analysis_data
   - error_no_data
   - error_period1_required
   - error_period2_required
   - error_permission
   - heading_title
   - print_title
   - text_advanced_analysis
   - text_all_branches
   - text_all_cost_centers
   - text_analysis_view
   - text_expenses
   - text_from
   - text_home
   - text_income_statement
   - text_net_income
   - text_period
   - text_revenues
   - text_success_analysis
   - text_success_compare
   - text_success_generate
   - text_to
   - text_total_expenses
   - text_total_revenues

💡 Suggested Arabic Additions:
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - error_date_from_required = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_date_to_required = ""  # TODO: ترجمة عربية
   - error_form = ""  # TODO: ترجمة عربية
   - error_generate = ""  # TODO: ترجمة عربية
   - error_invalid_data = ""  # TODO: ترجمة عربية
   - error_missing_parameters = ""  # TODO: ترجمة عربية
   - error_no_analysis_data = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_period1_required = ""  # TODO: ترجمة عربية
   - error_period2_required = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_advanced_analysis = ""  # TODO: ترجمة عربية
   - text_all_branches = ""  # TODO: ترجمة عربية
   - text_all_cost_centers = ""  # TODO: ترجمة عربية
   - text_analysis_view = ""  # TODO: ترجمة عربية
   - text_expenses = ""  # TODO: ترجمة عربية
   - text_from = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_income_statement = ""  # TODO: ترجمة عربية
   - text_net_income = ""  # TODO: ترجمة عربية
   - text_period = ""  # TODO: ترجمة عربية
   - text_revenues = ""  # TODO: ترجمة عربية
   - text_success_analysis = ""  # TODO: ترجمة عربية
   - text_success_compare = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_to = ""  # TODO: ترجمة عربية
   - text_total_expenses = ""  # TODO: ترجمة عربية
   - text_total_revenues = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - error_date_from_required = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_date_to_required = ""  # TODO: English translation
   - error_form = ""  # TODO: English translation
   - error_generate = ""  # TODO: English translation
   - error_invalid_data = ""  # TODO: English translation
   - error_missing_parameters = ""  # TODO: English translation
   - error_no_analysis_data = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_period1_required = ""  # TODO: English translation
   - error_period2_required = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_advanced_analysis = ""  # TODO: English translation
   - text_all_branches = ""  # TODO: English translation
   - text_all_cost_centers = ""  # TODO: English translation
   - text_analysis_view = ""  # TODO: English translation
   - text_expenses = ""  # TODO: English translation
   - text_from = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_income_statement = ""  # TODO: English translation
   - text_net_income = ""  # TODO: English translation
   - text_period = ""  # TODO: English translation
   - text_revenues = ""  # TODO: English translation
   - text_success_analysis = ""  # TODO: English translation
   - text_success_compare = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_to = ""  # TODO: English translation
   - text_total_expenses = ""  # TODO: English translation
   - text_total_revenues = ""  # TODO: English translation
