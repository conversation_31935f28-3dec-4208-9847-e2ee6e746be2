📄 Route: customer/custom_field
📂 Controller: controller\customer\custom_field.php
🧱 Models used (3):
   ✅ customer/custom_field (11 functions)
   ✅ localisation/language (7 functions)
   ✅ customer/customer_group (7 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\customer\custom_field.php (43 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\customer\custom_field.php (43 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (20):
   - error_custom_value
   - error_name
   - error_permission
   - error_type
   - heading_title
   - text_add
   - text_checkbox
   - text_date
   - text_datetime
   - text_edit
   - text_file
   - text_home
   - text_input
   - text_pagination
   - text_radio
   - text_select
   - text_success
   - text_text
   - text_textarea
   - text_time

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (2):
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - text_home
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:59
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.