📄 Route: customer/customer
📂 Controller: controller\customer\customer.php
🧱 Models used (6):
   - customer/custom_field
   - customer/customer
   - customer/customer_group
   - localisation/country
   - setting/store
   - tool/upload
🎨 Twig templates (0):
🈯 Arabic Language Files (2):
   - language\ar\customer\customer.php
   - language\ar\error\not_found.php
🇬🇧 English Language Files (2):
   - language\en-gb\customer\customer.php
   - language\en-gb\error\not_found.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_address_1
   - error_bank_account_name
   - error_bank_account_number
   - error_cheque
   - error_city
   - error_confirm
   - error_country
   - error_custom_field
   - error_email
   - error_exists
   - error_firstname
   - error_lastname
   - error_password
   - error_paypal
   - error_permission
   - error_postcode
   - error_telephone
   - error_tracking
   - error_tracking_exists
   - error_warning
   - error_zone
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - error_address_1
   - error_bank_account_name
   - error_bank_account_number
   - error_cheque
   - error_city
   - error_confirm
   - error_country
   - error_custom_field
   - error_email
   - error_exists
   - error_firstname
   - error_lastname
   - error_password
   - error_paypal
   - error_permission
   - error_postcode
   - error_telephone
   - error_tracking
   - error_tracking_exists
   - error_warning
   - error_zone
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - date_format_short
   - error_address_1
   - error_bank_account_name
   - error_bank_account_number
   - error_cheque
   - error_city
   - error_confirm
   - error_country
   - error_custom_field
   - error_email
   - error_exists
   - error_firstname
   - error_lastname
   - error_password
   - error_paypal
   - error_permission
   - error_postcode
   - error_telephone
   - error_tracking
   - error_tracking_exists
   - error_warning
   - error_zone
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_address_1 = ""  # TODO: ترجمة عربية
   - error_bank_account_name = ""  # TODO: ترجمة عربية
   - error_bank_account_number = ""  # TODO: ترجمة عربية
   - error_cheque = ""  # TODO: ترجمة عربية
   - error_city = ""  # TODO: ترجمة عربية
   - error_confirm = ""  # TODO: ترجمة عربية
   - error_country = ""  # TODO: ترجمة عربية
   - error_custom_field = ""  # TODO: ترجمة عربية
   - error_email = ""  # TODO: ترجمة عربية
   - error_exists = ""  # TODO: ترجمة عربية
   - error_firstname = ""  # TODO: ترجمة عربية
   - error_lastname = ""  # TODO: ترجمة عربية
   - error_password = ""  # TODO: ترجمة عربية
   - error_paypal = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_postcode = ""  # TODO: ترجمة عربية
   - error_telephone = ""  # TODO: ترجمة عربية
   - error_tracking = ""  # TODO: ترجمة عربية
   - error_tracking_exists = ""  # TODO: ترجمة عربية
   - error_warning = ""  # TODO: ترجمة عربية
   - error_zone = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_address_1 = ""  # TODO: English translation
   - error_bank_account_name = ""  # TODO: English translation
   - error_bank_account_number = ""  # TODO: English translation
   - error_cheque = ""  # TODO: English translation
   - error_city = ""  # TODO: English translation
   - error_confirm = ""  # TODO: English translation
   - error_country = ""  # TODO: English translation
   - error_custom_field = ""  # TODO: English translation
   - error_email = ""  # TODO: English translation
   - error_exists = ""  # TODO: English translation
   - error_firstname = ""  # TODO: English translation
   - error_lastname = ""  # TODO: English translation
   - error_password = ""  # TODO: English translation
   - error_paypal = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_postcode = ""  # TODO: English translation
   - error_telephone = ""  # TODO: English translation
   - error_tracking = ""  # TODO: English translation
   - error_tracking_exists = ""  # TODO: English translation
   - error_warning = ""  # TODO: English translation
   - error_zone = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
