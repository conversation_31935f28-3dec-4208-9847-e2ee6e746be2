📄 Route: purchase/smart_approval_system
📂 Controller: controller\purchase\smart_approval_system.php
🧱 Models used (4):
   ❌ purchase/smart_approval_system (0 functions)
   ✅ accounts/audit_trail (13 functions)
   ✅ user/user (47 functions)
   ❌ setting/department (0 functions)
🎨 Twig templates (1):
   ✅ view\template\purchase\smart_approval_system.twig
🈯 Arabic Language Files (1):
   ❌ language\ar\purchase\smart_approval_system.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\purchase\smart_approval_system.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (55):
   - action
   - can_bulk_approval
   - can_update_rules
   - error_bulk_approval_url
   - error_can_update_rules
   - error_escalate_url
   - error_heading_title
   - error_pending_approvals_url
   - error_user_token
   - error_users
   - error_workflow_url
   - header
   - text_bulk_approval_url
   - text_can_bulk_approval
   - text_escalate_url
   - text_pending_approvals_url
   - text_update_rules_url
   - text_workflow_url
   - update_rules_url
   - workflow_url
   ... و 35 متغير آخر

❌ Missing in Arabic (55):
   - action
   - can_update_rules
   - error_can_update_rules
   - error_escalate_url
   - error_heading_title
   - error_pending_approvals_url
   - error_user_token
   - error_users
   - error_workflow_url
   - header
   - text_bulk_approval_url
   - text_escalate_url
   - text_update_rules_url
   - update_rules_url
   - workflow_url
   ... و 40 متغير آخر

❌ Missing in English (55):
   - action
   - can_update_rules
   - error_can_update_rules
   - error_escalate_url
   - error_heading_title
   - error_pending_approvals_url
   - error_user_token
   - error_users
   - error_workflow_url
   - header
   - text_bulk_approval_url
   - text_escalate_url
   - text_update_rules_url
   - update_rules_url
   - workflow_url
   ... و 40 متغير آخر

🗄️ Database Tables Used (1):
   ❌ journal_entry

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 55 items
      - error_escalate_url
      - action
      - error_users
      - error_can_update_rules
      - text_update_rules_url
   🟡 MISSING_ENGLISH_VARIABLES: 55 items
      - error_escalate_url
      - action
      - error_users
      - error_can_update_rules
      - text_update_rules_url
   🔴 INVALID_DATABASE_TABLES: 1 items
      - journal_entry
   🟢 MISSING_MODEL_FILES: 2 items
      - purchase/smart_approval_system
      - setting/department

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 55 متغير عربي و 55 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.