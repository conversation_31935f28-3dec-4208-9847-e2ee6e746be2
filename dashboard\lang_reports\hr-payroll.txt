📄 Route: hr/payroll
📂 Controller: controller\hr\payroll.php
🧱 Models used (2):
   - hr/employee
   - hr/payroll
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\hr\payroll.php
🇬🇧 English Language Files (1):
   - language\en-gb\hr\payroll.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add_period
   - button_close
   - button_filter
   - button_mark_paid
   - button_reset
   - button_save
   - button_view_entries
   - column_actions
   - column_allowances
   - column_base_salary
   - column_deductions
   - column_employee
   - column_end_date
   - column_net_salary
   - column_payment_status
   - column_period_name
   - column_start_date
   - column_status
   - error_calculation_failed
   - error_comparison_failed
   - error_delete_failed
   - error_employee_email_not_found
   - error_export_failed
   - error_invalid_format
   - error_invalid_request
   - error_invalid_tax_rate
   - error_no_employees
   - error_not_found
   - error_permission
   - error_report_failed
   - error_required
   - error_update_failed
   - heading_title
   - text_add_period
   - text_ajax_error
   - text_all_statuses
   - text_confirm_delete
   - text_edit_period
   - text_end_date
   - text_enter_period_name
   - text_filter
   - text_payroll_list
   - text_start_date
   - text_status_closed
   - text_status_open
   - text_success_add
   - text_success_bulk_update
   - text_success_delete
   - text_success_delete_entry
   - text_success_edit
   - text_success_email_sent
   - text_success_generate
   - text_success_mark_paid
   - text_success_settings
   - text_success_update_entry
   - text_view_entries
   - text_view_entries_for_period

❌ Missing in Arabic:
   - button_add_period
   - button_close
   - button_filter
   - button_mark_paid
   - button_reset
   - button_save
   - button_view_entries
   - column_actions
   - column_allowances
   - column_base_salary
   - column_deductions
   - column_employee
   - column_end_date
   - column_net_salary
   - column_payment_status
   - column_period_name
   - column_start_date
   - column_status
   - error_calculation_failed
   - error_comparison_failed
   - error_delete_failed
   - error_employee_email_not_found
   - error_export_failed
   - error_invalid_format
   - error_invalid_request
   - error_invalid_tax_rate
   - error_no_employees
   - error_not_found
   - error_permission
   - error_report_failed
   - error_required
   - error_update_failed
   - heading_title
   - text_add_period
   - text_ajax_error
   - text_all_statuses
   - text_confirm_delete
   - text_edit_period
   - text_end_date
   - text_enter_period_name
   - text_filter
   - text_payroll_list
   - text_start_date
   - text_status_closed
   - text_status_open
   - text_success_add
   - text_success_bulk_update
   - text_success_delete
   - text_success_delete_entry
   - text_success_edit
   - text_success_email_sent
   - text_success_generate
   - text_success_mark_paid
   - text_success_settings
   - text_success_update_entry
   - text_view_entries
   - text_view_entries_for_period

❌ Missing in English:
   - button_add_period
   - button_close
   - button_filter
   - button_mark_paid
   - button_reset
   - button_save
   - button_view_entries
   - column_actions
   - column_allowances
   - column_base_salary
   - column_deductions
   - column_employee
   - column_end_date
   - column_net_salary
   - column_payment_status
   - column_period_name
   - column_start_date
   - column_status
   - error_calculation_failed
   - error_comparison_failed
   - error_delete_failed
   - error_employee_email_not_found
   - error_export_failed
   - error_invalid_format
   - error_invalid_request
   - error_invalid_tax_rate
   - error_no_employees
   - error_not_found
   - error_permission
   - error_report_failed
   - error_required
   - error_update_failed
   - heading_title
   - text_add_period
   - text_ajax_error
   - text_all_statuses
   - text_confirm_delete
   - text_edit_period
   - text_end_date
   - text_enter_period_name
   - text_filter
   - text_payroll_list
   - text_start_date
   - text_status_closed
   - text_status_open
   - text_success_add
   - text_success_bulk_update
   - text_success_delete
   - text_success_delete_entry
   - text_success_edit
   - text_success_email_sent
   - text_success_generate
   - text_success_mark_paid
   - text_success_settings
   - text_success_update_entry
   - text_view_entries
   - text_view_entries_for_period

💡 Suggested Arabic Additions:
   - button_add_period = ""  # TODO: ترجمة عربية
   - button_close = ""  # TODO: ترجمة عربية
   - button_filter = ""  # TODO: ترجمة عربية
   - button_mark_paid = ""  # TODO: ترجمة عربية
   - button_reset = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - button_view_entries = ""  # TODO: ترجمة عربية
   - column_actions = ""  # TODO: ترجمة عربية
   - column_allowances = ""  # TODO: ترجمة عربية
   - column_base_salary = ""  # TODO: ترجمة عربية
   - column_deductions = ""  # TODO: ترجمة عربية
   - column_employee = ""  # TODO: ترجمة عربية
   - column_end_date = ""  # TODO: ترجمة عربية
   - column_net_salary = ""  # TODO: ترجمة عربية
   - column_payment_status = ""  # TODO: ترجمة عربية
   - column_period_name = ""  # TODO: ترجمة عربية
   - column_start_date = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - error_calculation_failed = ""  # TODO: ترجمة عربية
   - error_comparison_failed = ""  # TODO: ترجمة عربية
   - error_delete_failed = ""  # TODO: ترجمة عربية
   - error_employee_email_not_found = ""  # TODO: ترجمة عربية
   - error_export_failed = ""  # TODO: ترجمة عربية
   - error_invalid_format = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_invalid_tax_rate = ""  # TODO: ترجمة عربية
   - error_no_employees = ""  # TODO: ترجمة عربية
   - error_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_report_failed = ""  # TODO: ترجمة عربية
   - error_required = ""  # TODO: ترجمة عربية
   - error_update_failed = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add_period = ""  # TODO: ترجمة عربية
   - text_ajax_error = ""  # TODO: ترجمة عربية
   - text_all_statuses = ""  # TODO: ترجمة عربية
   - text_confirm_delete = ""  # TODO: ترجمة عربية
   - text_edit_period = ""  # TODO: ترجمة عربية
   - text_end_date = ""  # TODO: ترجمة عربية
   - text_enter_period_name = ""  # TODO: ترجمة عربية
   - text_filter = ""  # TODO: ترجمة عربية
   - text_payroll_list = ""  # TODO: ترجمة عربية
   - text_start_date = ""  # TODO: ترجمة عربية
   - text_status_closed = ""  # TODO: ترجمة عربية
   - text_status_open = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_bulk_update = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_delete_entry = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية
   - text_success_email_sent = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_success_mark_paid = ""  # TODO: ترجمة عربية
   - text_success_settings = ""  # TODO: ترجمة عربية
   - text_success_update_entry = ""  # TODO: ترجمة عربية
   - text_view_entries = ""  # TODO: ترجمة عربية
   - text_view_entries_for_period = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add_period = ""  # TODO: English translation
   - button_close = ""  # TODO: English translation
   - button_filter = ""  # TODO: English translation
   - button_mark_paid = ""  # TODO: English translation
   - button_reset = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - button_view_entries = ""  # TODO: English translation
   - column_actions = ""  # TODO: English translation
   - column_allowances = ""  # TODO: English translation
   - column_base_salary = ""  # TODO: English translation
   - column_deductions = ""  # TODO: English translation
   - column_employee = ""  # TODO: English translation
   - column_end_date = ""  # TODO: English translation
   - column_net_salary = ""  # TODO: English translation
   - column_payment_status = ""  # TODO: English translation
   - column_period_name = ""  # TODO: English translation
   - column_start_date = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - error_calculation_failed = ""  # TODO: English translation
   - error_comparison_failed = ""  # TODO: English translation
   - error_delete_failed = ""  # TODO: English translation
   - error_employee_email_not_found = ""  # TODO: English translation
   - error_export_failed = ""  # TODO: English translation
   - error_invalid_format = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_invalid_tax_rate = ""  # TODO: English translation
   - error_no_employees = ""  # TODO: English translation
   - error_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_report_failed = ""  # TODO: English translation
   - error_required = ""  # TODO: English translation
   - error_update_failed = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add_period = ""  # TODO: English translation
   - text_ajax_error = ""  # TODO: English translation
   - text_all_statuses = ""  # TODO: English translation
   - text_confirm_delete = ""  # TODO: English translation
   - text_edit_period = ""  # TODO: English translation
   - text_end_date = ""  # TODO: English translation
   - text_enter_period_name = ""  # TODO: English translation
   - text_filter = ""  # TODO: English translation
   - text_payroll_list = ""  # TODO: English translation
   - text_start_date = ""  # TODO: English translation
   - text_status_closed = ""  # TODO: English translation
   - text_status_open = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_bulk_update = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_delete_entry = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
   - text_success_email_sent = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_success_mark_paid = ""  # TODO: English translation
   - text_success_settings = ""  # TODO: English translation
   - text_success_update_entry = ""  # TODO: English translation
   - text_view_entries = ""  # TODO: English translation
   - text_view_entries_for_period = ""  # TODO: English translation
