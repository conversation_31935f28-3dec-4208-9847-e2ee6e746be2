📄 Route: common/developer
📂 Controller: controller\common\developer.php
🧱 Models used (1):
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\common\developer.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\common\developer.php (13 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\common\developer.php (13 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - button_off
   - button_on
   - button_refresh
   - column_action
   - column_component
   - entry_cache
   - entry_sass
   - entry_theme
   - error_permission
   - heading_title
   - text_cache
   - text_sass
   - text_success
   - text_theme
   - user_token

❌ Missing in Arabic (2):
   - button_refresh
   - user_token

❌ Missing in English (2):
   - button_refresh
   - user_token

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - button_refresh
      - user_token
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - button_refresh
      - user_token

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:52
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.