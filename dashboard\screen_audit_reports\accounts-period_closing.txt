📄 Route: accounts/period_closing
📂 Controller: controller\accounts\period_closing.php
🧱 Models used (2):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/period_closing (26 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\period_closing.php (97 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\period_closing.php (97 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (14):
   - date_format_short
   - datetime_format
   - error_date_range
   - error_end_date
   - error_period_name
   - error_permission
   - error_start_date
   - heading_title
   - text_backup_failed
   - text_close_period
   - text_home
   - text_preview_closing
   - text_success_closed
   - text_success_reopened

❌ Missing in Arabic (4):
   - date_format_short
   - datetime_format
   - text_close_period
   - text_preview_closing

❌ Missing in English (4):
   - date_format_short
   - datetime_format
   - text_close_period
   - text_preview_closing

🗄️ Database Tables Used (2):
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 4 items
      - date_format_short
      - text_close_period
      - datetime_format
      - text_preview_closing
   🟡 MISSING_ENGLISH_VARIABLES: 4 items
      - date_format_short
      - text_close_period
      - datetime_format
      - text_preview_closing
   🔴 INVALID_DATABASE_TABLES: 2 items
      - workflow
      - template

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 4 متغير عربي و 4 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:44
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.