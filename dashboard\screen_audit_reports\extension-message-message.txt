📄 Route: extension/message/message
📂 Controller: controller\extension\message\message.php
🧱 Models used (4):
   ✅ extension/message (12 functions)
   ✅ user/user (47 functions)
   ✅ extension/message/message (20 functions)
   ✅ user/user_group (9 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\message\message.php (50 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\message\message.php (51 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - date_format_short
   - error_file
   - error_message
   - error_permission
   - error_subject
   - error_to
   - heading_title
   - text_compose
   - text_home
   - text_pagination
   - text_reply
   - text_success_delete
   - text_success_mark
   - text_success_send
   - text_view

❌ Missing in Arabic (3):
   - date_format_short
   - error_file
   - text_home

❌ Missing in English (3):
   - date_format_short
   - error_file
   - text_home

🗄️ Database Tables Used (2):
   ❌ database
   ❌ disk

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - error_file
      - text_home
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - error_file
      - text_home
      - date_format_short
   🔴 INVALID_DATABASE_TABLES: 2 items
      - disk
      - database

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:23
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.