📄 Route: purchase/supplier_payments
📂 Controller: controller\purchase\supplier_payments.php
🧱 Models used (3):
   ✅ purchase/supplier_payments (14 functions)
   ✅ supplier/supplier (21 functions)
   ❌ localisation/payment_method (0 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\purchase\supplier_payments.php (160 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\purchase\supplier_payments.php (160 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (22):
   - date_format_short
   - error_approve_payment
   - error_cancel_payment
   - error_payment_date
   - error_payment_id
   - error_payment_method
   - error_permission
   - error_supplier
   - heading_title
   - text_add
   - text_home
   - text_pagination
   - text_payment_approved
   - text_payment_cancelled
   - text_payment_report
   - text_status_approved
   - text_status_cancelled
   - text_status_paid
   - text_status_pending
   - text_success
   ... و 2 متغير آخر

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (3):
   - date_format_short
   - text_home
   - text_pagination

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟢 MISSING_MODEL_FILES: 1 items
      - localisation/payment_method

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ⚠️ 75%
📅 Analysis Date: 2025-07-21 18:33:16
🔧 Total Issues: 3

⚠️ جيد، لكن يحتاج بعض التحسينات.