📄 Route: extension/shipping/item
📂 Controller: controller\extension\shipping\item.php
🧱 Models used (3):
   ✅ setting/setting (5 functions)
   ✅ localisation/tax_class (8 functions)
   ✅ localisation/geo_zone (11 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\shipping\item.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\shipping\item.php (10 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\shipping\item.php (10 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (25):
   - action
   - button_save
   - column_left
   - entry_cost
   - entry_geo_zone
   - entry_sort_order
   - entry_status
   - entry_tax_class
   - error_permission
   - error_warning
   - footer
   - header
   - shipping_item_sort_order
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_extension
   - text_home
   - text_none
   - text_success
   ... و 5 متغير آخر

❌ Missing in Arabic (15):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_item_cost
   - shipping_item_sort_order
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home
   - text_none

❌ Missing in English (15):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - shipping_item_cost
   - shipping_item_sort_order
   - text_all_zones
   - text_disabled
   - text_enabled
   - text_home
   - text_none

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 15 items
      - button_save
      - shipping_item_sort_order
      - error_warning
      - column_left
      - text_home
   🟡 MISSING_ENGLISH_VARIABLES: 15 items
      - button_save
      - shipping_item_sort_order
      - error_warning
      - column_left
      - text_home

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 15 متغير عربي و 15 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:30
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.