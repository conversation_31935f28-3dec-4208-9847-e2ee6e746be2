📄 Route: design/translation
📂 Controller: controller\design\translation.php
🧱 Models used (3):
   ✅ design/translation (6 functions)
   ✅ localisation/language (7 functions)
   ✅ setting/store (14 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\design\translation.php (22 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\design\translation.php (22 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (9):
   - error_key
   - error_permission
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (2):
   - text_home
   - text_pagination

❌ Missing in English (2):
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - text_pagination
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - text_home
      - text_pagination

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:00
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.