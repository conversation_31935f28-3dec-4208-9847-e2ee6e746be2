📄 Route: marketplace/marketplace
📂 Controller: controller\marketplace\marketplace.php
🧱 Models used (1):
   - setting/extension
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\marketplace\marketplace.php
🇬🇧 English Language Files (1):
   - language\en-gb\marketplace\marketplace.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_more
   - button_reply
   - date_format_short
   - error_comment
   - error_download
   - error_install
   - error_opencart
   - error_permission
   - error_pin
   - error_purchase
   - heading_title
   - text_all
   - text_date_added
   - text_date_modified
   - text_feed
   - text_free
   - text_home
   - text_install
   - text_language
   - text_marketplace
   - text_module
   - text_name
   - text_other
   - text_paid
   - text_payment
   - text_price
   - text_purchased
   - text_rating
   - text_report
   - text_shipping
   - text_theme
   - text_total

❌ Missing in Arabic:
   - button_more
   - button_reply
   - date_format_short
   - error_comment
   - error_download
   - error_install
   - error_opencart
   - error_permission
   - error_pin
   - error_purchase
   - heading_title
   - text_all
   - text_date_added
   - text_date_modified
   - text_feed
   - text_free
   - text_home
   - text_install
   - text_language
   - text_marketplace
   - text_module
   - text_name
   - text_other
   - text_paid
   - text_payment
   - text_price
   - text_purchased
   - text_rating
   - text_report
   - text_shipping
   - text_theme
   - text_total

❌ Missing in English:
   - button_more
   - button_reply
   - date_format_short
   - error_comment
   - error_download
   - error_install
   - error_opencart
   - error_permission
   - error_pin
   - error_purchase
   - heading_title
   - text_all
   - text_date_added
   - text_date_modified
   - text_feed
   - text_free
   - text_home
   - text_install
   - text_language
   - text_marketplace
   - text_module
   - text_name
   - text_other
   - text_paid
   - text_payment
   - text_price
   - text_purchased
   - text_rating
   - text_report
   - text_shipping
   - text_theme
   - text_total

💡 Suggested Arabic Additions:
   - button_more = ""  # TODO: ترجمة عربية
   - button_reply = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_comment = ""  # TODO: ترجمة عربية
   - error_download = ""  # TODO: ترجمة عربية
   - error_install = ""  # TODO: ترجمة عربية
   - error_opencart = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_pin = ""  # TODO: ترجمة عربية
   - error_purchase = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_date_added = ""  # TODO: ترجمة عربية
   - text_date_modified = ""  # TODO: ترجمة عربية
   - text_feed = ""  # TODO: ترجمة عربية
   - text_free = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_install = ""  # TODO: ترجمة عربية
   - text_language = ""  # TODO: ترجمة عربية
   - text_marketplace = ""  # TODO: ترجمة عربية
   - text_module = ""  # TODO: ترجمة عربية
   - text_name = ""  # TODO: ترجمة عربية
   - text_other = ""  # TODO: ترجمة عربية
   - text_paid = ""  # TODO: ترجمة عربية
   - text_payment = ""  # TODO: ترجمة عربية
   - text_price = ""  # TODO: ترجمة عربية
   - text_purchased = ""  # TODO: ترجمة عربية
   - text_rating = ""  # TODO: ترجمة عربية
   - text_report = ""  # TODO: ترجمة عربية
   - text_shipping = ""  # TODO: ترجمة عربية
   - text_theme = ""  # TODO: ترجمة عربية
   - text_total = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_more = ""  # TODO: English translation
   - button_reply = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_comment = ""  # TODO: English translation
   - error_download = ""  # TODO: English translation
   - error_install = ""  # TODO: English translation
   - error_opencart = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_pin = ""  # TODO: English translation
   - error_purchase = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_date_added = ""  # TODO: English translation
   - text_date_modified = ""  # TODO: English translation
   - text_feed = ""  # TODO: English translation
   - text_free = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_install = ""  # TODO: English translation
   - text_language = ""  # TODO: English translation
   - text_marketplace = ""  # TODO: English translation
   - text_module = ""  # TODO: English translation
   - text_name = ""  # TODO: English translation
   - text_other = ""  # TODO: English translation
   - text_paid = ""  # TODO: English translation
   - text_payment = ""  # TODO: English translation
   - text_price = ""  # TODO: English translation
   - text_purchased = ""  # TODO: English translation
   - text_rating = ""  # TODO: English translation
   - text_report = ""  # TODO: English translation
   - text_shipping = ""  # TODO: English translation
   - text_theme = ""  # TODO: English translation
   - text_total = ""  # TODO: English translation
