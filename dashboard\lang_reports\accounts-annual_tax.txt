📄 Route: accounts/annual_tax
📂 Controller: controller\accounts\annual_tax.php
🧱 Models used (2):
   - accounts/annual_tax
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\annual_tax.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\annual_tax.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_no_data
   - error_permission
   - error_report_type
   - error_year
   - heading_title
   - text_all_taxes
   - text_comparative_report
   - text_detailed_report
   - text_home
   - text_income_tax
   - text_stamp_tax
   - text_success_generate
   - text_summary_report
   - text_vat
   - text_view
   - text_withholding_tax

❌ Missing in Arabic:
   - error_no_data
   - error_permission
   - error_report_type
   - error_year
   - heading_title
   - text_all_taxes
   - text_comparative_report
   - text_detailed_report
   - text_home
   - text_income_tax
   - text_stamp_tax
   - text_success_generate
   - text_summary_report
   - text_vat
   - text_view
   - text_withholding_tax

❌ Missing in English:
   - error_no_data
   - error_permission
   - error_report_type
   - error_year
   - heading_title
   - text_all_taxes
   - text_comparative_report
   - text_detailed_report
   - text_home
   - text_income_tax
   - text_stamp_tax
   - text_success_generate
   - text_summary_report
   - text_vat
   - text_view
   - text_withholding_tax

💡 Suggested Arabic Additions:
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_report_type = ""  # TODO: ترجمة عربية
   - error_year = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_all_taxes = ""  # TODO: ترجمة عربية
   - text_comparative_report = ""  # TODO: ترجمة عربية
   - text_detailed_report = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_income_tax = ""  # TODO: ترجمة عربية
   - text_stamp_tax = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_summary_report = ""  # TODO: ترجمة عربية
   - text_vat = ""  # TODO: ترجمة عربية
   - text_view = ""  # TODO: ترجمة عربية
   - text_withholding_tax = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_no_data = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_report_type = ""  # TODO: English translation
   - error_year = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_all_taxes = ""  # TODO: English translation
   - text_comparative_report = ""  # TODO: English translation
   - text_detailed_report = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_income_tax = ""  # TODO: English translation
   - text_stamp_tax = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_summary_report = ""  # TODO: English translation
   - text_vat = ""  # TODO: English translation
   - text_view = ""  # TODO: English translation
   - text_withholding_tax = ""  # TODO: English translation
