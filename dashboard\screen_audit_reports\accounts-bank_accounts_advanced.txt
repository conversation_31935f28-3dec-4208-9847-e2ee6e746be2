📄 Route: accounts/bank_accounts_advanced
📂 Controller: controller\accounts\bank_accounts_advanced.php
🧱 Models used (3):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/bank_accounts_advanced (29 functions)
   ✅ accounts/audit_trail (13 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\accounts\bank_accounts_advanced.php (183 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\accounts\bank_accounts_advanced.php (183 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (9):
   - date_format_short
   - error_account_name
   - error_account_number
   - error_bank_name
   - error_permission
   - heading_title
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (6):
   - date_format_short
   - error_account_name
   - error_account_number
   - error_bank_name
   - text_pagination
   - text_success

❌ Missing in English (6):
   - date_format_short
   - error_account_name
   - error_account_number
   - error_bank_name
   - text_pagination
   - text_success

🗄️ Database Tables Used (3):
   ❌ journal_entry
   ❌ template
   ❌ workflow

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 6 items
      - text_success
      - error_account_number
      - error_bank_name
      - text_pagination
      - error_account_name
   🟡 MISSING_ENGLISH_VARIABLES: 6 items
      - text_success
      - error_account_number
      - error_bank_name
      - text_pagination
      - error_account_name
   🔴 INVALID_DATABASE_TABLES: 3 items
      - workflow
      - template
      - journal_entry

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 6 متغير عربي و 6 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 3 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:32:32
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.