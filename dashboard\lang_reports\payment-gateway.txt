📄 Route: payment/gateway
📂 Controller: controller\payment\gateway.php
🧱 Models used (2):
   - accounts/chartaccount
   - payment/gateway
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - datetime_format
   - error_account
   - error_api_endpoint
   - error_api_key
   - error_commission_account
   - error_commission_rate
   - error_fixed_fee
   - error_gateway
   - error_gateway_type
   - error_merchant_id
   - error_name
   - error_permission
   - error_provider
   - heading_title
   - text_add
   - text_bank_transfer
   - text_buy_now_pay_later
   - text_credit_card
   - text_cryptocurrency
   - text_debit_card
   - text_digital_wallet
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_installment
   - text_mobile_payment
   - text_never
   - text_success
   - text_sync_success
   - text_test_success

❌ Missing in Arabic:
   - datetime_format
   - error_account
   - error_api_endpoint
   - error_api_key
   - error_commission_account
   - error_commission_rate
   - error_fixed_fee
   - error_gateway
   - error_gateway_type
   - error_merchant_id
   - error_name
   - error_permission
   - error_provider
   - heading_title
   - text_add
   - text_bank_transfer
   - text_buy_now_pay_later
   - text_credit_card
   - text_cryptocurrency
   - text_debit_card
   - text_digital_wallet
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_installment
   - text_mobile_payment
   - text_never
   - text_success
   - text_sync_success
   - text_test_success

❌ Missing in English:
   - datetime_format
   - error_account
   - error_api_endpoint
   - error_api_key
   - error_commission_account
   - error_commission_rate
   - error_fixed_fee
   - error_gateway
   - error_gateway_type
   - error_merchant_id
   - error_name
   - error_permission
   - error_provider
   - heading_title
   - text_add
   - text_bank_transfer
   - text_buy_now_pay_later
   - text_credit_card
   - text_cryptocurrency
   - text_debit_card
   - text_digital_wallet
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_installment
   - text_mobile_payment
   - text_never
   - text_success
   - text_sync_success
   - text_test_success

💡 Suggested Arabic Additions:
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_account = ""  # TODO: ترجمة عربية
   - error_api_endpoint = ""  # TODO: ترجمة عربية
   - error_api_key = ""  # TODO: ترجمة عربية
   - error_commission_account = ""  # TODO: ترجمة عربية
   - error_commission_rate = ""  # TODO: ترجمة عربية
   - error_fixed_fee = ""  # TODO: ترجمة عربية
   - error_gateway = ""  # TODO: ترجمة عربية
   - error_gateway_type = ""  # TODO: ترجمة عربية
   - error_merchant_id = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_provider = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_bank_transfer = ""  # TODO: ترجمة عربية
   - text_buy_now_pay_later = ""  # TODO: ترجمة عربية
   - text_credit_card = ""  # TODO: ترجمة عربية
   - text_cryptocurrency = ""  # TODO: ترجمة عربية
   - text_debit_card = ""  # TODO: ترجمة عربية
   - text_digital_wallet = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_installment = ""  # TODO: ترجمة عربية
   - text_mobile_payment = ""  # TODO: ترجمة عربية
   - text_never = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_sync_success = ""  # TODO: ترجمة عربية
   - text_test_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - datetime_format = ""  # TODO: English translation
   - error_account = ""  # TODO: English translation
   - error_api_endpoint = ""  # TODO: English translation
   - error_api_key = ""  # TODO: English translation
   - error_commission_account = ""  # TODO: English translation
   - error_commission_rate = ""  # TODO: English translation
   - error_fixed_fee = ""  # TODO: English translation
   - error_gateway = ""  # TODO: English translation
   - error_gateway_type = ""  # TODO: English translation
   - error_merchant_id = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_provider = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_bank_transfer = ""  # TODO: English translation
   - text_buy_now_pay_later = ""  # TODO: English translation
   - text_credit_card = ""  # TODO: English translation
   - text_cryptocurrency = ""  # TODO: English translation
   - text_debit_card = ""  # TODO: English translation
   - text_digital_wallet = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_installment = ""  # TODO: English translation
   - text_mobile_payment = ""  # TODO: English translation
   - text_never = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_sync_success = ""  # TODO: English translation
   - text_test_success = ""  # TODO: English translation
