<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
<meta charset="UTF-8" />
<title>{{ title }}</title>
<base href="{{ base }}" />
{% if description %}
<meta name="description" content="{{ description }}" />
{% endif %}
{% if keywords %}
<meta name="keywords" content="{{ keywords }}" />
{% endif %}
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<script type="text/javascript" src="view/javascript/jquery/jquery-3.7.0.min.js"></script>
<script type="text/javascript" src="view/javascript/bootstrap/js/bootstrap.min.js"></script>
<!-- bootstrap-a.css?ver=3.3.7 ش هينفع نرقيها عشان القوالب مستخدمينها فيها-->
{% if direction == 'rtl' %}
<link href="view/stylesheet/bootstrap-a.css?ver=3.3.7" rel="stylesheet" media="screen" />
{% else %}
<link href="view/stylesheet/bootstrap.css?ver=3.3.7" rel="stylesheet" media="screen" />
{% endif %}
<link href="view/javascript/font-awesome/css/font-awesome.min.css?ver=4.7.0" type="text/css" rel="stylesheet" />
<script src="view/javascript/jquery/datetimepicker/moment/moment.min.js?ver=2.18.1" type="text/javascript"></script>
<script src="view/javascript/jquery/datetimepicker/moment/moment-with-locales.min.js" type="text/javascript"></script>
<script src="view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
<link href="view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.css?ver=3.1.3" type="text/css" rel="stylesheet" media="screen" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.14.1/jquery-ui.min.js" integrity="sha512-MSOo1aY+3pXCOCdGAYoBZ6YGI0aragoQsg1mKKBHXCYPIWxamwOE7Drh+N5CPgGI5SA9IEKJiPjdfqWFWmZtRA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    {#  bootstrap 3.3.7 , jquery 3.7.0, font-awesome 4.7.0 , moment 2.18.1, bootstrap-datetimepicker 3.1.3, select2 4.1.0-rc.0, dataTables.bootstrap 1.10.21, toastr 2.1.3, chart.js 4.4.8, sweetalert2 11.17.2,jspdf 2.5.1 ,vue 3.5.13      #}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" 
          href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/css/dataTables.bootstrap.min.css" 
          integrity="sha512-BMbq2It2D3J17/C7aRklzOODG1IQ3+MHw3ifzBHMBwGO/0yUqYmsStgBjI0z5EYlaDEFnvYV7gNYdD3vFLRKsA==" 
          crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />
<!-- تحميل bootstrap-daterangepicker CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.14.1/themes/base/jquery-ui.min.css" integrity="sha512-TFee0335YRJoyiqz8hA8KV3P0tXa5CpRBSoM0Wnkn7JoJx1kaq1yXL/rb8YFpWXkMOjRcv5txv+C6UluttluCQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<!-- تحميل bootstrap-daterangepicker JS -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
{% if direction == 'rtl' %}
<link type="text/css" href="view/stylesheet/stylesheet-a.css?ver=1.0.0" rel="stylesheet" media="screen" />
{% else %}
<link type="text/css" href="view/stylesheet/stylesheet.css?ver=1.0.0" rel="stylesheet" media="screen" />
{% endif %}
<!-- مركز الإشعارات المتطور -->
<link type="text/css" href="view/stylesheet/notifications-panel.css?ver=2.0.0" rel="stylesheet" media="screen" />
<!-- Dashboard Enhanced Styles -->
<link type="text/css" href="view/stylesheet/dashboard-enhanced.css?ver=1.0.0" rel="stylesheet" media="screen" />

{% for style in styles %}
<link type="text/css" href="{{ style.href }}" rel="{{ style.rel }}" media="{{ style.media }}" />
{% endfor %}
{% for link in links %}
<link href="{{ link.href }}" rel="{{ link.rel }}" />
{% endfor %}
<script src="view/javascript/common.js" type="text/javascript"></script>
{% for script in scripts %}
<script type="text/javascript" src="{{ script }}"></script>
{% endfor %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0-rc.0/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.21/js/dataTables.bootstrap.min.js"></script>
<!-- النسخة الحديثة (ES Module) للمتصفحات الداعمة لـ type="module" -->
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/polyfills.umd.js"></script>
<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
<!-- مركز الإشعارات المتطور -->
<script src="view/javascript/notifications-panel.js?ver=2.0.0" type="text/javascript"></script>

<style>
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear,.select2-container--default[dir="ltr"] .select2-selection--single .select2-selection__clear {
    padding-inline-end: 25px;
}    
.modal-dialog {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.modal-content {
  height: auto;
  min-height: 100%;
  border-radius: 0;
}
</style>
</head>
<body>
    
<div id="container">
<header id="header" class="navbar navbar-static-top">
  <div class="container-fluid">
    <div id="header-logo" class="navbar-header"><a href="{{ home }}" class="navbar-brand"><img width="120" src="view/image/logo.png" alt="CODAYM" title="CODAYM" /></a></div>
    {% if logged %}<a href="#" id="button-menu" class="hidden-md hidden-lg"><span class="fa fa-bars"></span></a>
    <ul class="nav navbar-nav navbar-right">
      <!-- مركز الإشعارات المتطور - عينك على النظام -->
      <li class="dropdown unified-notifications-menu">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown" id="unified-notifications-trigger">
          <i class="fa fa-bell notification-bell"></i>
          <span id="unified-notifications-count" class="notification-badge">0</span>
          <span id="critical-indicator" class="critical-pulse" style="display: none;"></span>
          <span id="system-health-indicator" class="system-health-dot" title="حالة النظام"></span>
        </a>

        <!-- البانل المتطور الجديد -->
        <div class="dropdown-menu unified-notifications-panel">
          <!-- هيدر البانل المحسن -->
          <div class="panel-header">
            <div class="header-left">
              <div class="header-title">
                <i class="fa fa-eye"></i>
                <span>عينك على النظام</span>
              </div>
              <div class="system-status" id="system-status-indicator">
                <i class="fa fa-circle text-success"></i>
                <span>النظام يعمل بكفاءة</span>
              </div>
            </div>
            <div class="header-right">
              <div class="header-stats">
                <span id="total-count-display">0</span> إشعار
              </div>
              <div class="header-actions">
                <button class="header-btn" id="refresh-notifications" title="تحديث">
                  <i class="fa fa-refresh"></i>
                </button>
                <button class="header-btn" id="notification-settings" title="الإعدادات">
                  <i class="fa fa-cog"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- شريط المؤشرات السريعة -->
          <div class="quick-indicators">
            <div class="indicator-item" data-type="performance">
              <div class="indicator-icon">
                <i class="fa fa-tachometer-alt"></i>
              </div>
              <div class="indicator-info">
                <span class="indicator-value" id="system-performance">95%</span>
                <span class="indicator-label">الأداء</span>
              </div>
            </div>
            <div class="indicator-item" data-type="users">
              <div class="indicator-icon">
                <i class="fa fa-users"></i>
              </div>
              <div class="indicator-info">
                <span class="indicator-value" id="active-users-count">15</span>
                <span class="indicator-label">نشط</span>
              </div>
            </div>
            <div class="indicator-item" data-type="sales">
              <div class="indicator-icon">
                <i class="fa fa-chart-line"></i>
              </div>
              <div class="indicator-info">
                <span class="indicator-value" id="today-sales-amount">45.2K</span>
                <span class="indicator-label">مبيعات اليوم</span>
              </div>
            </div>
            <div class="indicator-item" data-type="tasks">
              <div class="indicator-icon">
                <i class="fa fa-tasks"></i>
              </div>
              <div class="indicator-info">
                <span class="indicator-value" id="pending-tasks-count">8</span>
                <span class="indicator-label">مهام معلقة</span>
              </div>
            </div>
          </div>

          <!-- تبويبات التصنيف المحسنة -->
          <div class="panel-tabs">
            <ul class="nav nav-tabs" role="tablist">
              <li role="presentation" class="active">
                <a href="#all-notifications" data-toggle="tab" role="tab" data-tab="all">
                  <i class="fa fa-list"></i>
                  <span class="tab-text">الكل</span>
                  <span id="all-count" class="tab-badge">0</span>
                </a>
              </li>
              <li role="presentation">
                <a href="#critical-notifications" data-toggle="tab" role="tab" data-tab="critical">
                  <i class="fa fa-exclamation-triangle"></i>
                  <span class="tab-text">حرجة</span>
                  <span id="critical-count" class="tab-badge critical">0</span>
                </a>
              </li>
              <li role="presentation">
                <a href="#approvals-notifications" data-toggle="tab" role="tab" data-tab="approvals">
                  <i class="fa fa-check-circle"></i>
                  <span class="tab-text">موافقات</span>
                  <span id="approvals-count-tab" class="tab-badge warning">0</span>
                </a>
              </li>
              <li role="presentation">
                <a href="#workflow-notifications" data-toggle="tab" role="tab" data-tab="workflow">
                  <i class="fa fa-sitemap"></i>
                  <span class="tab-text">سير العمل</span>
                  <span id="workflow-count-tab" class="tab-badge info">0</span>
                </a>
              </li>
              <li role="presentation">
                <a href="#documents-notifications" data-toggle="tab" role="tab" data-tab="documents">
                  <i class="fa fa-file-alt"></i>
                  <span class="tab-text">مستندات</span>
                  <span id="documents-count-tab" class="tab-badge">0</span>
                </a>
              </li>
              <li role="presentation">
                <a href="#security-notifications" data-toggle="tab" role="tab" data-tab="security">
                  <i class="fa fa-shield-alt"></i>
                  <span class="tab-text">أمان</span>
                  <span id="security-count-tab" class="tab-badge danger">0</span>
                </a>
              </li>
            </ul>
          </div>

          <!-- محتوى التبويبات المحسن -->
          <div class="panel-content">
            <div class="tab-content">
              <!-- تبويب الكل -->
              <div role="tabpanel" class="tab-pane active" id="all-notifications">
                <div class="notifications-list" id="all-notifications-list">
                  <div class="loading-placeholder">
                    <div class="loading-spinner">
                      <i class="fa fa-spinner fa-spin"></i>
                    </div>
                    <span>جاري تحميل آخر التحديثات...</span>
                  </div>
                </div>
              </div>

              <!-- تبويب الحرجة -->
              <div role="tabpanel" class="tab-pane" id="critical-notifications">
                <div class="tab-header">
                  <h6><i class="fa fa-exclamation-triangle text-danger"></i> الإشعارات الحرجة</h6>
                  <p class="tab-description">المشاكل التي تحتاج تدخل فوري</p>
                </div>
                <div class="notifications-list" id="critical-notifications-list">
                  <div class="empty-state">
                    <i class="fa fa-check-circle text-success"></i>
                    <p>لا توجد مشاكل حرجة حالياً</p>
                  </div>
                </div>
              </div>

              <!-- تبويب الموافقات -->
              <div role="tabpanel" class="tab-pane" id="approvals-notifications">
                <div class="tab-header">
                  <h6><i class="fa fa-check-circle text-warning"></i> طلبات الموافقة</h6>
                  <p class="tab-description">المعاملات التي تحتاج موافقتك</p>
                </div>
                <div class="notifications-list" id="approvals-notifications-list">
                  <div class="empty-state">
                    <i class="fa fa-inbox"></i>
                    <p>لا توجد طلبات موافقة معلقة</p>
                  </div>
                </div>
              </div>

              <!-- تبويب سير العمل -->
              <div role="tabpanel" class="tab-pane" id="workflow-notifications">
                <div class="tab-header">
                  <h6><i class="fa fa-sitemap text-info"></i> سير العمل</h6>
                  <p class="tab-description">المهام والعمليات الجارية</p>
                </div>
                <div class="notifications-list" id="workflow-notifications-list">
                  <div class="empty-state">
                    <i class="fa fa-tasks"></i>
                    <p>جميع المهام مكتملة</p>
                  </div>
                </div>
              </div>

              <!-- تبويب المستندات -->
              <div role="tabpanel" class="tab-pane" id="documents-notifications">
                <div class="tab-header">
                  <h6><i class="fa fa-file-alt text-primary"></i> المستندات</h6>
                  <p class="tab-description">المستندات المشتركة والمراجعات</p>
                </div>
                <div class="notifications-list" id="documents-notifications-list">
                  <div class="empty-state">
                    <i class="fa fa-folder-open"></i>
                    <p>لا توجد مستندات جديدة</p>
                  </div>
                </div>
              </div>

              <!-- تبويب الأمان -->
              <div role="tabpanel" class="tab-pane" id="security-notifications">
                <div class="tab-header">
                  <h6><i class="fa fa-shield-alt text-danger"></i> التنبيهات الأمنية</h6>
                  <p class="tab-description">محاولات الدخول والأنشطة المشبوهة</p>
                </div>
                <div class="notifications-list" id="security-notifications-list">
                  <div class="empty-state">
                    <i class="fa fa-shield-check text-success"></i>
                    <p>النظام آمن - لا توجد تهديدات</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- فوتر البانل المحسن -->
          <div class="panel-footer">
            <div class="footer-stats">
              <div class="stat-item">
                <i class="fa fa-clock"></i>
                <span>آخر تحديث: <span id="last-update-time">الآن</span></span>
              </div>
              <div class="stat-item">
                <i class="fa fa-sync-alt"></i>
                <span>تحديث تلقائي: <span id="auto-refresh-status">مفعل</span></span>
              </div>
            </div>

            <div class="footer-actions">
              <div class="action-group">
                <button class="footer-btn" id="mark-all-read" title="تحديد الكل كمقروء">
                  <i class="fa fa-check-double"></i>
                  <span>تحديد الكل كمقروء</span>
                </button>
                <button class="footer-btn" id="clear-read" title="مسح المقروءة">
                  <i class="fa fa-trash-alt"></i>
                  <span>مسح المقروءة</span>
                </button>
              </div>

              <div class="action-group">
                <button class="footer-btn primary" id="view-all-notifications" title="عرض جميع الإشعارات">
                  <i class="fa fa-external-link-alt"></i>
                  <span>عرض الكل</span>
                </button>
                <button class="footer-btn" id="notification-preferences" title="تفضيلات الإشعارات">
                  <i class="fa fa-cog"></i>
                  <span>الإعدادات</span>
                </button>
              </div>
            </div>

            <!-- شريط التحكم السريع -->
            <div class="quick-controls">
              <div class="control-group">
                <label class="control-label">التحديث التلقائي:</label>
                <div class="toggle-switch">
                  <input type="checkbox" id="auto-refresh-toggle" checked>
                  <label for="auto-refresh-toggle" class="toggle-label"></label>
                </div>
              </div>

              <div class="control-group">
                <label class="control-label">الأصوات:</label>
                <div class="toggle-switch">
                  <input type="checkbox" id="sound-notifications-toggle" checked>
                  <label for="sound-notifications-toggle" class="toggle-label"></label>
                </div>
              </div>

              <div class="control-group">
                <label class="control-label">إشعارات سطح المكتب:</label>
                <div class="toggle-switch">
                  <input type="checkbox" id="desktop-notifications-toggle">
                  <label for="desktop-notifications-toggle" class="toggle-label"></label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>
      
              
      <li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown"><img style="margin-inline-end: 5px;" src="{{ image }}" alt="{{ firstname }} {{ lastname }}" title="{{ username }}" id="user-profile" class="img-circle" />{{ firstname }}<i class="fa fa-caret-down fa-fw"></i></a>
        <ul class="dropdown-menu dropdown-menu-right">
          <li><a style="height: 40px;line-height: 30px;font-size: 13px;color: #6D6D6D;font-weight: 500;" href="{{ profile }}"><i class="fa fa-user fa-fw"></i>   {{ text_profile }}</a></li>
           <li role="separator" class="divider" style="margin:2px 0"></li>
          {% for store in stores %}
          <li><a style="height: 40px;line-height: 30px;font-size: 13px;color: #6D6D6D;font-weight: 500;" href="{{ store.href }}" target="_blank"><i class="fa fa-globe fa-fw"></i> {{ store.name }}</a></li>
          {% endfor %}
          <li role="separator" class="divider" style="margin:2px 0"></li>
          <li><a href="https://codaym.com" target="_blank"><img width="30" src="view/image/dlogo.png" alt="CODAYM" title="CODAYM" /> COD AYM </a></li>
        </ul>
      </li>
      <li><a href="{{ logout }}"><i class="fa fa-sign-out"></i> <span class="hidden-xs hidden-sm hidden-md">{{ text_logout }}</span></a></li>
    </ul>
    {% endif %} </div>
</header>





<!-- CSS للبانل المتطور الجديد - عينك على النظام -->
<style>
  /* أيقونة الإشعارات المحسنة */
  .notification-bell {
    font-size: 18px;
    transition: all 0.3s ease;
  }

  .notification-bell:hover {
    transform: rotate(15deg);
    color: #667eea;
  }

  .notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 9px;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    padding: 2px 6px;
    min-width: 18px;
    text-align: center;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
    animation: bounce 2s infinite;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-2px); }
  }

  /* مؤشر الحالة الحرجة */
  .critical-pulse {
    position: absolute;
    top: 1px;
    right: 1px;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, #ff4444, #cc0000);
    border-radius: 50%;
    animation: criticalPulse 1s infinite;
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);
  }

  /* مؤشر حالة النظام */
  .system-health-dot {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 6px;
    height: 6px;
    background: #28a745;
    border-radius: 50%;
    animation: healthPulse 2s infinite;
  }

  @keyframes criticalPulse {
    0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
    70% { transform: scale(1.1); box-shadow: 0 0 0 8px rgba(255, 68, 68, 0); }
    100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
  }

  @keyframes healthPulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  /* البانل الرئيسي المحسن - دعم RTL/LTR كامل */
  .unified-notifications-panel {
    width: 520px;
    max-height: 650px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0,0,0,.15), 0 0 0 1px rgba(255,255,255,.1);
    position: absolute !important;
    top: 100% !important;
    z-index: 9999 !important;
  }

  /* دعم RTL */
  [dir="rtl"] .unified-notifications-panel {
    left: -480px !important;
    right: auto !important;
  }

  /* دعم LTR */
  [dir="ltr"] .unified-notifications-panel {
    right: -480px !important;
    left: auto !important;
  }
    background: #fff;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  /* هيدر البانل */
  .panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
  }

  .header-title i {
    margin-left: 8px;
    font-size: 18px;
  }

  .header-left, .header-right {
    display: flex;
    flex-direction: column;
    gap: 4px;
    position: relative;
    z-index: 1;
  }

  .system-status {
    font-size: 12px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .header-stats {
    font-size: 14px;
    font-weight: 600;
    text-align: right;
  }

  .header-actions {
    display: flex;
    gap: 8px;
    margin-top: 4px;
  }

  .header-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
  }

  .header-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  /* المؤشرات السريعة */
  .quick-indicators {
    display: flex;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    gap: 8px;
  }

  .indicator-item {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 10px;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  .indicator-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .indicator-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
  }

  .indicator-item[data-type="performance"] .indicator-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
  }

  .indicator-item[data-type="users"] .indicator-icon {
    background: linear-gradient(135deg, #007bff, #6610f2);
  }

  .indicator-item[data-type="sales"] .indicator-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
  }

  .indicator-item[data-type="tasks"] .indicator-icon {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
  }

  .indicator-info {
    display: flex;
    flex-direction: column;
  }

  .indicator-value {
    font-size: 16px;
    font-weight: 700;
    color: #333;
    line-height: 1;
  }

  .indicator-label {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
  }

  /* تبويبات التصنيف */
  .panel-tabs {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
  }

  .panel-tabs .nav-tabs {
    border: none;
    margin: 0;
  }

  .panel-tabs .nav-tabs > li {
    margin-bottom: 0;
  }

  .panel-tabs .nav-tabs > li > a {
    border: none;
    border-radius: 0;
    padding: 12px 15px;
    font-size: 12px;
    color: #6c757d;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    min-width: 80px;
  }

  .panel-tabs .nav-tabs > li > a i {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .panel-tabs .nav-tabs > li.active > a,
  .panel-tabs .nav-tabs > li.active > a:hover,
  .panel-tabs .nav-tabs > li.active > a:focus {
    color: #495057;
    background: #fff;
    border-bottom: 2px solid #667eea;
  }

  .panel-tabs .nav-tabs > li > a:hover {
    background: #e9ecef;
    border-color: transparent;
  }

  /* شارات العدد في التبويبات المحسنة */
  .tab-badge {
    font-size: 8px;
    padding: 1px 4px;
    border-radius: 50%;
    margin-top: 2px;
    min-width: 14px;
    height: 14px;
    line-height: 12px;
    text-align: center;
    display: inline-block;
    font-weight: 700;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }

  .tab-badge:not(.critical):not(.warning):not(.info):not(.danger) {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
  }

  .tab-badge.critical {
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: white;
    animation: badgePulse 2s infinite;
  }

  .tab-badge.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
  }

  .tab-badge.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
  }

  .tab-badge.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
  }

  @keyframes badgePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }

  /* هيدر التبويبات */
  .tab-header {
    padding: 12px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
  }

  .tab-header h6 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .tab-header .tab-description {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
  }

  /* حالة فارغة */
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
  }

  .empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-state p {
    margin: 0;
    font-size: 14px;
  }

  /* محتوى البانل */
  .panel-content {
    max-height: 400px;
    overflow-y: auto;
  }

  .notifications-list {
    padding: 0;
  }

  /* عناصر الإشعارات */
  .notification-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
  }

  .notification-item:hover {
    background: #f8f9fa;
  }

  .notification-item:last-child {
    border-bottom: none;
  }

  .notification-item.unread {
    background: #fff3cd;
    border-right: 4px solid #ffc107;
  }

  .notification-item.critical {
    background: #f8d7da;
    border-right: 4px solid #dc3545;
  }

  .notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 16px;
    flex-shrink: 0;
  }

  .notification-icon.info { background: #d1ecf1; color: #0c5460; }
  .notification-icon.success { background: #d4edda; color: #155724; }
  .notification-icon.warning { background: #fff3cd; color: #856404; }
  .notification-icon.danger { background: #f8d7da; color: #721c24; }

  .notification-content {
    flex: 1;
    min-width: 0;
  }

  .notification-title {
    font-weight: 600;
    font-size: 14px;
    color: #212529;
    margin-bottom: 4px;
    line-height: 1.3;
  }

  .notification-message {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 6px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .notification-time {
    font-size: 11px;
    color: #adb5bd;
    display: flex;
    align-items: center;
  }

  .notification-time i {
    margin-left: 4px;
  }

  /* فوتر البانل */
  .panel-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 12px 20px;
  }

  .footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .footer-actions .btn {
    font-size: 12px;
    padding: 6px 12px;
  }

  /* حالة التحميل */
  .loading-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
  }

  .loading-placeholder i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
  }

  /* حالة فارغة */
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
  }

  .empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
    opacity: 0.5;
  }

  /* الوضع المستجيب */
  @media (max-width: 767px) {
    .unified-notifications-panel {
      width: 100vw;
      max-width: 100vw;
      left: 0 !important;
      right: 0 !important;
      margin: 0;
      border-radius: 0;
      max-height: 80vh;
    }

    .panel-tabs .nav-tabs > li > a {
      padding: 10px 8px;
      font-size: 11px;
      min-width: 60px;
    }

    .notification-item {
      padding: 12px 15px;
    }

    .notification-icon {
      width: 35px;
      height: 35px;
      margin-left: 10px;
    }
  }

  /* فوتر البانل المحسن */
  .panel-footer {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-top: 1px solid #dee2e6;
    padding: 16px 20px;
  }

  .footer-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 11px;
    color: #6c757d;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .action-group {
    display: flex;
    gap: 8px;
  }

  .footer-btn {
    background: white;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .footer-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    transform: translateY(-1px);
  }

  .footer-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
  }

  .footer-btn.primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4c93);
    transform: translateY(-1px);
  }

  /* التحكم السريع */
  .quick-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #dee2e6;
  }

  .control-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .control-label {
    font-size: 11px;
    color: #6c757d;
    font-weight: 500;
  }

  /* مفتاح التبديل */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
  }

  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
  }

  .toggle-label:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + .toggle-label {
    background: linear-gradient(135deg, #667eea, #764ba2);
  }

  input:checked + .toggle-label:before {
    transform: translateX(20px);
  }

  /* تحسينات الموبايل للبانل */
  @media (max-width: 768px) {
    .unified-notifications-panel {
      width: 95vw;
      max-width: 400px;
      max-height: 80vh;
    }

    .quick-indicators {
      flex-wrap: wrap;
      gap: 6px;
    }

    .indicator-item {
      flex: 1 1 calc(50% - 3px);
      min-width: 0;
    }

    .panel-tabs .nav-tabs > li > a {
      padding: 8px 4px;
      font-size: 10px;
    }

    .panel-tabs .nav-tabs > li > a .tab-text {
      display: none;
    }

    .footer-actions {
      flex-direction: column;
      gap: 8px;
    }

    .action-group {
      width: 100%;
      justify-content: center;
    }

    .quick-controls {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;
    }

    .control-group {
      justify-content: space-between;
    }
  }
</style>

<!-- كود جافاسكريبت المحسن للبانل المتطور -->
<script type="text/javascript">
$(document).ready(function() {
  // متغيرات النظام
  var autoRefreshEnabled = true;
  var soundNotificationsEnabled = true;
  var desktopNotificationsEnabled = false;
  var refreshInterval;

  // تحميل بيانات الهيدر عند تحميل الصفحة
  loadHeaderData();

  // بدء التحديث التلقائي
  startAutoRefresh();

  // طلب إذن الإشعارات
  requestNotificationPermission();

  // معالجات الأحداث
  initEventHandlers();

  // تحديث الوقت المباشر
  updateLiveTime();
  setInterval(updateLiveTime, 1000);
});

// تهيئة معالجات الأحداث
function initEventHandlers() {
  // وضع علامة قراءة على الإشعار عند النقر عليه
  $(document).on('click', '.notification-item', function() {
    var notificationId = $(this).data('notification-id');
    markNotificationAsRead(notificationId);
  });

  // النقر على المؤشرات السريعة
  $(document).on('click', '.indicator-item', function() {
    var type = $(this).data('type');
    handleIndicatorClick(type);
  });

  // أزرار الهيدر
  $('#refresh-notifications').on('click', function() {
    $(this).find('i').addClass('fa-spin');
    loadHeaderData();
    setTimeout(() => {
      $(this).find('i').removeClass('fa-spin');
    }, 1000);
  });

  $('#notification-settings').on('click', function() {
    openNotificationSettings();
  });

  // أزرار الفوتر
  $('#mark-all-read').on('click', function() {
    markAllNotificationsAsRead();
  });

  $('#clear-read').on('click', function() {
    clearReadNotifications();
  });

  $('#view-all-notifications').on('click', function() {
    window.open('index.php?route=common/notifications&user_token={{ user_token }}', '_blank');
  });

  // مفاتيح التبديل
  $('#auto-refresh-toggle').on('change', function() {
    autoRefreshEnabled = $(this).is(':checked');
    if (autoRefreshEnabled) {
      startAutoRefresh();
      $('#auto-refresh-status').text('مفعل');
    } else {
      stopAutoRefresh();
      $('#auto-refresh-status').text('معطل');
    }
  });

  $('#sound-notifications-toggle').on('change', function() {
    soundNotificationsEnabled = $(this).is(':checked');
    localStorage.setItem('soundNotifications', soundNotificationsEnabled);
  });

  $('#desktop-notifications-toggle').on('change', function() {
    desktopNotificationsEnabled = $(this).is(':checked');
    localStorage.setItem('desktopNotifications', desktopNotificationsEnabled);
  });

  // تبديل التبويبات
  $('.panel-tabs a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
    var tabType = $(e.target).data('tab');
    loadTabData(tabType);
  });
}

// تحميل بيانات الهيدر المحسن (جميع الأنواع)
function loadHeaderData() {
  $.ajax({
    url: 'index.php?route=common/header/getHeaderData&user_token={{ user_token }}',
    type: 'GET',
    dataType: 'json',
    success: function(response) {
      if (response.success) {
        updateNotifications(response.data.notifications);
        updateSystemIndicators(response.data.indicators);
        updateSystemStatus(response.data.system_status);
        updateLastUpdateTime();

        // تشغيل صوت التنبيه للإشعارات الجديدة
        if (soundNotificationsEnabled && response.data.new_notifications > 0) {
          playNotificationSound();
        }

        // إشعارات سطح المكتب
        if (desktopNotificationsEnabled && response.data.critical_notifications > 0) {
          showDesktopNotification(response.data.critical_notifications);
        }
      }
    },
    error: function(xhr, status, error) {
      console.error('خطأ في تحميل بيانات الهيدر: ' + error);
    }
  });
}

// تحديث قسم الإشعارات
function updateNotifications(data) {
  // تحديث العداد
  $('#notifications-count, #notifications-header-count').text(data.count);
  
  // إخفاء العداد إذا كان صفر
  if (data.count == 0) {
    $('#notifications-count').hide();
  } else {
    $('#notifications-count').show();
  }
  
  // تحديث قائمة الإشعارات
  var notificationsList = $('#notifications-list');
  notificationsList.empty();
  
  if (data.items.length === 0) {
    notificationsList.append('<li class="text-center">لا توجد إشعارات جديدة</li>');
  } else {
    $.each(data.items, function(index, notification) {
      var priorityClass = '';
      var iconClass = 'fa-bell';
      
      // تحديد لون الإشعار حسب الأولوية
      switch(notification.priority) {
        case 'high':
          priorityClass = 'text-danger';
          iconClass = 'fa-exclamation-circle';
          break;
        case 'medium':
          priorityClass = 'text-warning';
          break;
        case 'low':
          priorityClass = 'text-muted';
          break;
      }
      
      // إضافة عنصر الإشعار
      var notificationItem = $('<li>')
        .addClass('notification-item')
        .attr('data-notification-id', notification.notification_id);
      
      if (notification.action_url) {
        notificationItem.attr('data-url', notification.action_url);
      }
      
      var html = '<div class="notification-item">' +
                  '<div class="notification-icon"><i class="fa ' + iconClass + ' ' + priorityClass + '"></i></div>' +
                  '<div class="notification-content">' +
                    '<span class="notification-title">' + notification.title + '</span>' +
                    '<span class="notification-text">' + notification.content + '</span>' +
                    '<span class="notification-time">' + notification.time_ago + '</span>' +
                  '</div>' +
                '</div>';
      
      notificationItem.html(html);
      notificationsList.append(notificationItem);
    });
  }
}

// تحديث قسم الرسائل
function updateMessages(data) {
  // تحديث العداد
  $('#messages-count, #messages-header-count').text(data.count);
  
  // إخفاء العداد إذا كان صفر
  if (data.count == 0) {
    $('#messages-count').hide();
  } else {
    $('#messages-count').show();
  }
  
  // تحديث قائمة الرسائل
  var messagesList = $('#messages-list');
  messagesList.empty();
  
  if (data.items.length === 0) {
    messagesList.append('<li class="text-center">لا توجد رسائل جديدة</li>');
  } else {
    $.each(data.items, function(index, message) {
      var messageItem = $('<li>')
        .addClass('message-item')
        .attr('data-conversation-id', message.conversation_id)
        .attr('data-message-id', message.message_id);
      
      var html = '<div class="notification-item">' +
                  '<div class="notification-icon"><i class="fa fa-envelope"></i></div>' +
                  '<div class="notification-content">' +
                    '<span class="notification-title">من: ' + message.sender_name + '</span>' +
                    '<span class="notification-text">' + message.message_text + '</span>' +
                    '<span class="notification-time">' + message.time_ago + '</span>' +
                  '</div>' +
                '</div>';
      
      messageItem.html(html);
      messagesList.append(messageItem);
    });
  }
}

// تحديث قسم طلبات الموافقة
function updateApprovals(data) {
  // تحديث العداد
  $('#approvals-count, #approvals-header-count').text(data.count);
  
  // إخفاء العداد إذا كان صفر
  if (data.count == 0) {
    $('#approvals-count').hide();
  } else {
    $('#approvals-count').show();
  }
  
  // تحديث قائمة طلبات الموافقة
  var approvalsList = $('#approvals-list');
  approvalsList.empty();
  
  if (data.items.length === 0) {
    approvalsList.append('<li class="text-center">لا توجد طلبات موافقة معلقة</li>');
  } else {
    $.each(data.items, function(index, approval) {
      var priorityClass = '';
      var iconClass = 'fa-flag';
      
      // تحديد لون الطلب حسب الأولوية
      switch(approval.priority) {
        case 'high':
          priorityClass = 'text-danger';
          break;
        case 'medium':
          priorityClass = 'text-warning';
          break;
        case 'low':
          priorityClass = 'text-muted';
          break;
      }
      
      var approvalItem = $('<li>')
        .addClass('approval-item')
        .attr('data-request-id', approval.request_id);
      
      var html = '<div class="notification-item">' +
                  '<div class="notification-icon"><i class="fa ' + iconClass + ' ' + priorityClass + '"></i></div>' +
                  '<div class="notification-content">' +
                    '<span class="notification-title">' + approval.title + '</span>' +
                    '<span class="notification-text">من: ' + approval.requester_name + '</span>' +
                    '<span class="notification-time">' + approval.time_ago + '</span>' +
                  '</div>' +
                '</div>';
      
      approvalItem.html(html);
      approvalsList.append(approvalItem);
    });
  }
}

// وضع علامة قراءة على الإشعار
function markNotificationAsRead(notificationId) {
  $.ajax({
    url: 'index.php?route=common/header/markNotificationAsRead&user_token={{ user_token }}',
    type: 'POST',
    data: {
      notification_id: notificationId
    },
    dataType: 'json',
    success: function(response) {
      if (response.success) {
        // إعادة تحميل بيانات الهيدر
        loadHeaderData();
        
        // إذا كان هناك رابط، الانتقال إليه
        var item = $('.notification-item[data-notification-id="' + notificationId + '"]');
        var url = item.data('url');
        if (url) {
          window.location.href = url;
        }
      }
    }
  });
}

// وضع علامة قراءة على الرسالة
function markMessageAsRead(conversationId, messageId) {
  $.ajax({
    url: 'index.php?route=common/header/markMessageAsRead&user_token={{ user_token }}',
    type: 'POST',
    data: {
      conversation_id: conversationId,
      message_id: messageId
    },
    dataType: 'json',
    success: function(response) {
      if (response.success) {
        // إعادة تحميل بيانات الهيدر
        loadHeaderData();
        
        // الانتقال إلى صفحة المحادثة
        window.location.href = 'index.php?route=messaging/conversation&user_token={{ user_token }}&conversation_id=' + conversationId;
      }
    }
  });
}

// الانتقال إلى صفحة طلب الموافقة عند النقر
$(document).on('click', '.approval-item', function() {
  var requestId = $(this).data('request-id');
  window.location.href = 'index.php?route=workflow/approval&user_token={{ user_token }}&request_id=' + requestId;
});

// تحديث البانل الموحد
function updateUnifiedNotificationsPanel(data) {
  // تحديث العداد الرئيسي
  var totalCount = data.total_count || 0;
  $('#unified-notifications-count').text(totalCount);
  $('#total-count-display').text(totalCount);

  // إظهار/إخفاء العداد
  if (totalCount == 0) {
    $('#unified-notifications-count').hide();
  } else {
    $('#unified-notifications-count').show();
  }

  // إظهار مؤشر الحالة الحرجة
  if (data.has_critical) {
    $('#critical-indicator').show();
    $('#unified-notifications-count').removeClass('label-warning').addClass('label-danger');
  } else if (data.has_urgent) {
    $('#critical-indicator').hide();
    $('#unified-notifications-count').removeClass('label-danger').addClass('label-warning');
  } else {
    $('#critical-indicator').hide();
    $('#unified-notifications-count').removeClass('label-danger').addClass('label-warning');
  }

  // تحديث إحصائيات التبويبات
  updateTabCounts(data.stats);

  // تحديث قائمة الإشعارات
  updateNotificationsList(data.notifications);

  // تخزين البيانات للتصفية
  window.allNotifications = data.notifications;
}

// تحديث عدادات التبويبات
function updateTabCounts(stats) {
  $('#all-count').text(stats.total || 0);
  $('#critical-count').text(stats.critical || 0);

  // حساب عدد الرسائل والموافقات والتنبيهات
  var messagesCount = stats.by_type.message || 0;
  var approvalsCount = stats.by_type.approval || 0;
  var alertsCount = (stats.by_type.inventory || 0) + (stats.by_type.expiry || 0) + (stats.by_type.task || 0);

  $('#messages-count-tab').text(messagesCount);
  $('#approvals-count-tab').text(approvalsCount);
  $('#alerts-count-tab').text(alertsCount);

  // إخفاء العدادات الصفرية
  $('.tab-badge').each(function() {
    if ($(this).text() == '0') {
      $(this).hide();
    } else {
      $(this).show();
    }
  });
}

// تحديث قائمة الإشعارات
function updateNotificationsList(notifications) {
  var container = $('#all-notifications-list');
  container.empty();

  if (notifications.length === 0) {
    container.html(getEmptyState('لا توجد إشعارات جديدة', 'fa-bell-slash'));
    return;
  }

  $.each(notifications, function(index, notification) {
    var notificationHtml = createNotificationItem(notification);
    container.append(notificationHtml);
  });
}

// إنشاء عنصر إشعار
function createNotificationItem(notification) {
  var priorityClass = notification.priority === 'critical' ? 'critical' :
                     (notification.priority === 'urgent' ? 'urgent' : '');

  var iconColorClass = notification.color || 'info';

  var html = '<div class="notification-item ' + priorityClass + '" ' +
             'data-notification-id="' + notification.id + '" ' +
             'data-notification-type="' + notification.type + '" ' +
             'data-action-url="' + (notification.action_url || '') + '">' +

             '<div class="notification-icon ' + iconColorClass + '">' +
               '<i class="fa ' + notification.icon + '"></i>' +
             '</div>' +

             '<div class="notification-content">' +
               '<div class="notification-title">' + notification.title + '</div>' +
               '<div class="notification-message">' + notification.message + '</div>' +
               '<div class="notification-time">' +
                 '<i class="fa fa-clock-o"></i> ' + notification.time_ago +
               '</div>' +
             '</div>' +
           '</div>';

  return html;
}

// تصفية الإشعارات حسب التبويب
function filterNotificationsByTab(tabId) {
  if (!window.allNotifications) return;

  var filteredNotifications = [];

  switch(tabId) {
    case '#all-notifications':
      filteredNotifications = window.allNotifications;
      break;
    case '#critical-notifications':
      filteredNotifications = window.allNotifications.filter(function(n) {
        return n.priority === 'critical' || n.priority === 'urgent';
      });
      break;
    case '#messages-notifications':
      filteredNotifications = window.allNotifications.filter(function(n) {
        return n.type === 'message';
      });
      break;
    case '#approvals-notifications':
      filteredNotifications = window.allNotifications.filter(function(n) {
        return n.type === 'approval';
      });
      break;
    case '#alerts-notifications':
      filteredNotifications = window.allNotifications.filter(function(n) {
        return n.type === 'inventory' || n.type === 'expiry' || n.type === 'task';
      });
      break;
  }

  var container = $(tabId + '-list');
  container.empty();

  if (filteredNotifications.length === 0) {
    container.html(getEmptyState('لا توجد عناصر في هذا القسم', 'fa-inbox'));
    return;
  }

  $.each(filteredNotifications, function(index, notification) {
    var notificationHtml = createNotificationItem(notification);
    container.append(notificationHtml);
  });
}

// تحديد إشعار كمقروء
function markNotificationAsRead(notificationId, notificationType) {
  $.ajax({
    url: 'index.php?route=common/header/markAsRead&user_token={{ user_token }}',
    type: 'POST',
    data: {
      notification_id: notificationId,
      notification_type: notificationType
    },
    dataType: 'json',
    success: function(response) {
      if (response.success) {
        // إعادة تحميل الإشعارات
        loadUnifiedNotifications();
      }
    }
  });
}

// تحديد جميع الإشعارات كمقروءة
function markAllNotificationsAsRead() {
  $.ajax({
    url: 'index.php?route=common/header/markAllAsRead&user_token={{ user_token }}',
    type: 'POST',
    dataType: 'json',
    success: function(response) {
      if (response.success) {
        loadUnifiedNotifications();
      }
    }
  });
}

// حالة فارغة
function getEmptyState(message, icon) {
  return '<div class="empty-state">' +
           '<i class="fa ' + icon + '"></i>' +
           '<p>' + message + '</p>' +
         '</div>';
}

// عرض خطأ في التحميل
function showNotificationError() {
  $('#all-notifications-list').html(
    '<div class="empty-state">' +
      '<i class="fa fa-exclamation-triangle"></i>' +
      '<p>خطأ في تحميل الإشعارات</p>' +
    '</div>'
  );
}

// الدوال الجديدة للبانل المحسن

// تحديث المؤشرات السريعة
function updateSystemIndicators(indicators) {
  if (indicators) {
    $('#system-performance').text(indicators.performance + '%');
    $('#active-users-count').text(indicators.active_users);
    $('#today-sales-amount').text(indicators.today_sales);
    $('#pending-tasks-count').text(indicators.pending_tasks);
  }
}

// تحديث حالة النظام
function updateSystemStatus(status) {
  if (status) {
    var statusIndicator = $('#system-status-indicator');
    statusIndicator.find('i').removeClass('text-success text-warning text-danger');

    switch(status.level) {
      case 'healthy':
        statusIndicator.find('i').addClass('text-success');
        statusIndicator.find('span').text('النظام يعمل بكفاءة');
        break;
      case 'warning':
        statusIndicator.find('i').addClass('text-warning');
        statusIndicator.find('span').text('تحذير: ' + status.message);
        break;
      case 'critical':
        statusIndicator.find('i').addClass('text-danger');
        statusIndicator.find('span').text('خطر: ' + status.message);
        break;
    }
  }
}

// تحديث وقت آخر تحديث
function updateLastUpdateTime() {
  $('#last-update-time').text('الآن');
}

// تحديث الوقت المباشر
function updateLiveTime() {
  var now = new Date();
  var timeString = now.toLocaleTimeString('ar-EG', {
    hour: '2-digit',
    minute: '2-digit'
  });
  // تحديث أي عنصر يعرض الوقت في الهيدر
}

// بدء التحديث التلقائي
function startAutoRefresh() {
  if (window.refreshInterval) {
    clearInterval(window.refreshInterval);
  }
  window.refreshInterval = setInterval(loadHeaderData, 30000); // كل 30 ثانية
}

// إيقاف التحديث التلقائي
function stopAutoRefresh() {
  if (window.refreshInterval) {
    clearInterval(window.refreshInterval);
  }
}

// معالجة النقر على المؤشرات
function handleIndicatorClick(type) {
  switch(type) {
    case 'performance':
      console.log('فتح مراقب الأداء');
      break;
    case 'users':
      console.log('فتح المستخدمين النشطين');
      break;
    case 'sales':
      console.log('فتح تقرير المبيعات');
      break;
    case 'tasks':
      console.log('فتح مدير المهام');
      break;
  }
}

// تشغيل صوت التنبيه
function playNotificationSound() {
  // يمكن إضافة ملف صوتي لاحقاً
  console.log('تشغيل صوت التنبيه');
}

// إشعارات سطح المكتب
function showDesktopNotification(count) {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('AYM ERP - إشعار هام', {
      body: 'لديك ' + count + ' إشعار حرج يحتاج انتباهك',
      icon: 'view/image/favicon.png'
    });
  }
}

// طلب إذن الإشعارات
function requestNotificationPermission() {
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
  }
}

// تحديد جميع الإشعارات كمقروءة
function markAllNotificationsAsRead() {
  $.ajax({
    url: 'index.php?route=common/header/markAllAsRead&user_token={{ user_token }}',
    type: 'POST',
    success: function() {
      loadHeaderData();
      alert('تم تحديد جميع الإشعارات كمقروءة');
    }
  });
}

// مسح الإشعارات المقروءة
function clearReadNotifications() {
  $.ajax({
    url: 'index.php?route=common/header/clearRead&user_token={{ user_token }}',
    type: 'POST',
    success: function() {
      loadHeaderData();
      alert('تم مسح الإشعارات المقروءة');
    }
  });
}

// فتح إعدادات الإشعارات
function openNotificationSettings() {
  console.log('فتح إعدادات الإشعارات');
}
</script>