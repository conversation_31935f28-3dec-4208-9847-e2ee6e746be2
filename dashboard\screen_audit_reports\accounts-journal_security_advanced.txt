📄 Route: accounts/journal_security_advanced
📂 Controller: controller\accounts\journal_security_advanced.php
🧱 Models used (3):
   ✅ accounts/journal_security_advanced (12 functions)
   ✅ accounts/audit_trail (13 functions)
   ❌ user/user_permission (0 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\accounts\journal_security_advanced.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\accounts\journal_security_advanced.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (2):
   - heading_title
   - text_home

❌ Missing in Arabic (2):
   - heading_title
   - text_home

❌ Missing in English (2):
   - heading_title
   - text_home

🗄️ Database Tables Used (1):
   ❌ journal_entry

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 2 items
      - text_home
      - heading_title
   🟡 MISSING_ENGLISH_VARIABLES: 2 items
      - text_home
      - heading_title
   🔴 INVALID_DATABASE_TABLES: 1 items
      - journal_entry
   🟢 MISSING_MODEL_FILES: 1 items
      - user/user_permission

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 2 متغير عربي و 2 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 1 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:42
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.