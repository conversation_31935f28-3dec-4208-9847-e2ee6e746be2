📄 Route: inventory/barcode
📂 Controller: controller\inventory\barcode.php
🧱 Models used (3):
   ✅ inventory/barcode (30 functions)
   ✅ inventory/units (14 functions)
   ✅ catalog/option (12 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\inventory\barcode.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\inventory\barcode.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (17):
   - error_barcode
   - error_barcode_exists
   - error_barcode_generation
   - error_barcode_invalid
   - error_barcode_not_found
   - error_permission
   - error_product
   - heading_title
   - text_add
   - text_barcode_generated
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_no
   - text_success
   - text_yes

❌ Missing in Arabic (17):
   - error_barcode
   - error_barcode_exists
   - error_barcode_generation
   - error_barcode_not_found
   - error_permission
   - error_product
   - heading_title
   - text_add
   - text_barcode_generated
   - text_disabled
   - text_enabled
   - text_home
   - text_no
   - text_success
   - text_yes
   ... و 2 متغير آخر

❌ Missing in English (17):
   - error_barcode
   - error_barcode_exists
   - error_barcode_generation
   - error_barcode_not_found
   - error_permission
   - error_product
   - heading_title
   - text_add
   - text_barcode_generated
   - text_disabled
   - text_enabled
   - text_home
   - text_no
   - text_success
   - text_yes
   ... و 2 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 17 items
      - text_no
      - text_success
      - text_add
      - text_home
      - error_barcode
   🟡 MISSING_ENGLISH_VARIABLES: 17 items
      - text_no
      - text_success
      - text_add
      - text_home
      - error_barcode

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 17 متغير عربي و 17 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:04
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.