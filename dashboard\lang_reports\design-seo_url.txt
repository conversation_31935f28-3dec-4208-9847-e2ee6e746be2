📄 Route: design/seo_url
📂 Controller: controller\design\seo_url.php
🧱 Models used (3):
   - design/seo_url
   - localisation/language
   - setting/store
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\design\seo_url.php
🇬🇧 English Language Files (1):
   - language\en-gb\design\seo_url.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_exists
   - error_keyword
   - error_permission
   - error_query
   - error_query_exists
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic:
   - error_exists
   - error_keyword
   - error_permission
   - error_query
   - error_query_exists
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in English:
   - error_exists
   - error_keyword
   - error_permission
   - error_query
   - error_query_exists
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

💡 Suggested Arabic Additions:
   - error_exists = ""  # TODO: ترجمة عربية
   - error_keyword = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_query = ""  # TODO: ترجمة عربية
   - error_query_exists = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_default = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_exists = ""  # TODO: English translation
   - error_keyword = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_query = ""  # TODO: English translation
   - error_query_exists = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_default = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
