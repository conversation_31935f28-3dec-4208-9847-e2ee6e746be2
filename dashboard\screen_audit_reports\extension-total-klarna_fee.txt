📄 Route: extension/total/klarna_fee
📂 Controller: controller\extension\total\klarna_fee.php
🧱 Models used (2):
   ✅ setting/setting (5 functions)
   ✅ localisation/tax_class (8 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\total\klarna_fee.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\total\klarna_fee.php (16 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\total\klarna_fee.php (16 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (28):
   - action
   - button_save
   - column_left
   - entry_status
   - entry_tax_class
   - entry_total
   - error_permission
   - error_warning
   - footer
   - header
   - text_denmark
   - text_disabled
   - text_enabled
   - text_extension
   - text_finland
   - text_germany
   - text_home
   - text_netherlands
   - text_success
   - text_sweden
   ... و 8 متغير آخر

❌ Missing in Arabic (12):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home
   - text_none

❌ Missing in English (12):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home
   - text_none

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 12 items
      - button_save
      - error_warning
      - column_left
      - text_home
      - action
   🟡 MISSING_ENGLISH_VARIABLES: 12 items
      - button_save
      - error_warning
      - column_left
      - text_home
      - action

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 12 متغير عربي و 12 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:30
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.