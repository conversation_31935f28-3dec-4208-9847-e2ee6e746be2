📄 Route: user/api
📂 Controller: controller\user\api.php
🧱 Models used (1):
   ✅ user/api (12 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\user\api.php (21 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\user\api.php (21 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (15):
   - date_format_short
   - datetime_format
   - error_ip
   - error_key
   - error_permission
   - error_username
   - heading_title
   - text_add
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_ip
   - text_pagination
   - text_success

❌ Missing in Arabic (6):
   - date_format_short
   - datetime_format
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

❌ Missing in English (6):
   - date_format_short
   - datetime_format
   - text_disabled
   - text_enabled
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 6 items
      - text_home
      - text_disabled
      - datetime_format
      - text_pagination
      - text_enabled
   🟡 MISSING_ENGLISH_VARIABLES: 6 items
      - text_home
      - text_disabled
      - datetime_format
      - text_pagination
      - text_enabled

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 6 متغير عربي و 6 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:19
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.