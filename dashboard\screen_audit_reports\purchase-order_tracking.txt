📄 Route: purchase/order_tracking
📂 Controller: controller\purchase\order_tracking.php
🧱 Models used (2):
   ✅ purchase/order_tracking (13 functions)
   ❌ purchase/supplier (0 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\purchase\order_tracking.php (110 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\purchase\order_tracking.php (110 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (42):
   - button_filter
   - column_current_status
   - column_expected_delivery
   - column_po_number
   - column_supplier
   - column_total
   - date_format_short
   - entry_po_number
   - error_not_found
   - error_permission
   - error_status
   - success_delivery_updated
   - text_confirm
   - text_list
   - text_status_cancelled
   - text_status_confirmed_by_vendor
   - text_status_created
   - text_status_fully_received
   - text_status_partially_received
   - text_status_sent_to_vendor
   ... و 22 متغير آخر

❌ Missing in Arabic (3):
   - date_format_short
   - text_home
   - text_pagination

❌ Missing in English (3):
   - date_format_short
   - text_home
   - text_pagination

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - date_format_short
   🟢 MISSING_MODEL_FILES: 1 items
      - purchase/supplier

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 1 ملف موديل

📈 Screen Health Score: ⚠️ 75%
📅 Analysis Date: 2025-07-21 18:33:15
🔧 Total Issues: 3

⚠️ جيد، لكن يحتاج بعض التحسينات.