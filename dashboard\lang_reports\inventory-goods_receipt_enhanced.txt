📄 Route: inventory/goods_receipt_enhanced
📂 Controller: controller\inventory\goods_receipt_enhanced.php
🧱 Models used (5):
   - core/central_service_manager
   - inventory/goods_receipt_enhanced
   - inventory/warehouse
   - purchase/purchase_order
   - setting/setting
🎨 Twig templates (0):
🈯 Arabic Language Files (0):
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_no_items
   - error_permission
   - error_receipt_date
   - error_receipt_number
   - heading_title
   - text_add
   - text_edit
   - text_list
   - text_success

❌ Missing in Arabic:
   - date_format_short
   - error_no_items
   - error_permission
   - error_receipt_date
   - error_receipt_number
   - heading_title
   - text_add
   - text_edit
   - text_list
   - text_success

❌ Missing in English:
   - date_format_short
   - error_no_items
   - error_permission
   - error_receipt_date
   - error_receipt_number
   - heading_title
   - text_add
   - text_edit
   - text_list
   - text_success

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_no_items = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_receipt_date = ""  # TODO: ترجمة عربية
   - error_receipt_number = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_no_items = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_receipt_date = ""  # TODO: English translation
   - error_receipt_number = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
