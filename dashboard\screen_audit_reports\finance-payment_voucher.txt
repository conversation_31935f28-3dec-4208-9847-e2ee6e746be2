📄 Route: finance/payment_voucher
📂 Controller: controller\finance\payment_voucher.php
🧱 Models used (6):
   ✅ finance/payment_voucher (33 functions)
   ✅ accounts/audit_trail (13 functions)
   ✅ accounts/journal_security_advanced (12 functions)
   ✅ supplier/supplier (21 functions)
   ✅ hr/employee (26 functions)
   ✅ accounts/chartaccount (19 functions)
🎨 Twig templates (1):
   ✅ view\template\finance\payment_voucher.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\finance\payment_voucher.php (286 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\finance\payment_voucher.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (122):
   - approved_by_name
   - bank_name
   - button_load_bills
   - button_post
   - button_reports
   - column_allocation_amount
   - column_voucher_number
   - created_date
   - entry_bank_account
   - entry_currency
   - entry_filter_amount_from
   - entry_filter_payment_method
   - entry_filter_supplier
   - text_actions
   - text_advanced_search
   - text_ajax_error
   - text_expense_payment
   - text_select
   - text_status_approved
   - text_status_posted
   ... و 102 متغير آخر

❌ Missing in Arabic (30):
   - amount
   - approved_by_name
   - bank_name
   - check_date
   - column_left
   - created_by_name
   - created_date
   - date_format_short
   - entry_filter_payment_method
   - header
   - journal_id
   - journal_link
   - reference_number
   - user_token
   - voucher_id
   ... و 15 متغير آخر

❌ Missing in English (122):
   - approved_by_name
   - button_load_bills
   - button_post
   - button_reports
   - column_allocation_amount
   - created_date
   - entry_currency
   - entry_filter_amount_from
   - entry_filter_payment_method
   - entry_filter_supplier
   - text_advanced_search
   - text_ajax_error
   - text_expense_payment
   - text_select
   - text_status_approved
   ... و 107 متغير آخر

🗄️ Database Tables Used (5):
   ✅ cod_employee_documents
   ✅ cod_employee_profile
   ✅ cod_user
   ❌ existing
   ❌ journal_entry

🚨 Issues Found (3):
   🟡 MISSING_ARABIC_VARIABLES: 30 items
      - journal_id
      - header
      - entry_filter_payment_method
      - date_format_short
      - journal_link
   🟡 MISSING_ENGLISH_VARIABLES: 122 items
      - button_post
      - button_load_bills
      - text_ajax_error
      - text_select
      - text_advanced_search
   🔴 INVALID_DATABASE_TABLES: 2 items
      - existing
      - journal_entry

💡 Recommendations (2):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 30 متغير عربي و 122 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 2 جدول غير موجود

📈 Screen Health Score: ❌ 65%
📅 Analysis Date: 2025-07-21 18:33:03
🔧 Total Issues: 3

❌ يحتاج إصلاح عاجل لعدة مشاكل.