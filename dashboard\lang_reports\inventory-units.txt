📄 Route: inventory/units
📂 Controller: controller\inventory\units.php
🧱 Models used (2):
   - inventory/units
   - localisation/language
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\inventory\units.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_name
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_add
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in Arabic:
   - error_name
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_add
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

❌ Missing in English:
   - error_name
   - error_permission
   - error_unit_in_use
   - heading_title
   - text_add
   - text_defaults_created
   - text_disabled
   - text_edit
   - text_enabled
   - text_home
   - text_success

💡 Suggested Arabic Additions:
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_unit_in_use = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_defaults_created = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_unit_in_use = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_defaults_created = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
