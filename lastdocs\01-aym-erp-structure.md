# 1️⃣ ما هو AYM ERP والوحدات الأساسية

## 🎯 التعريف الشامل
AYM ERP هو أول نظام ERP متكامل بالذكاء الاصطناعي + التجارة الإلكترونية في مصر والشرق الأوسط، مبني على OpenCart 3.0.3.x مع تعديلات جذرية شاملة. يهدف لمنافسة Odoo + WooCommerce/Shopify والتفوق على SAP/Microsoft/Oracle.

## 🏗️ البنية التقنية الأساسية
- **الأساس:** OpenCart 3.0.3.x (ليس الإصدار الرابع)
- **الهيكل:** MVC Pattern مع قوالب Twig
- **قاعدة البيانات:** 340+ جدول متخصص بادئة `cod_` بدلاً من `oc_`
- **الإدارة:** مجلد `dashboard` بدلاً من `admin`
- **الملفات:** 3,263 سطر في column_left.php + 3,793 سطر في tree.txt

## 📊 الهيكل الكامل للوحدات (من العمود الجانبي)

### **🏠 1. التنقل الأساسي (Core Navigation)**
#### **1.1 الروابط السريعة**
- **عرض المتجر:** رابط مباشر للواجهة الأمامية
- **اللوحات الرئيسية:**
  - لوحة التحكم الرئيسية (`common/dashboard`)
  - لوحة مؤشرات الأداء (`dashboard/kpi`)
  - لوحة تحليلات المخزون (`dashboard/inventory_analytics`)

#### **1.2 العمليات اليومية السريعة**
- **مهام المبيعات السريعة:**
  - إضافة عرض سعر سريع (`sale/quote/add`)
  - إضافة طلب سريع (`sale/order/add`)

### **🧮 2. النظام المحاسبي (Accounting System) - الأساس**
#### **2.1 المحاسبة الأساسية (Core Accounting)**
- **دليل الحسابات** (`accounts/chartaccount`)
  - بناء الهيكل الشجري للحسابات
  - أساس جميع العمليات المالية
- **قيود اليومية** (`accounts/journal`)
  - تسجيل القيود المحاسبية اليدوية
  - قيود التسوية والإهلاكات
- **كشوف الحسابات** (`accounts/statement_account`)
  - دفتر الأستاذ التفصيلي
  - تتبع الحركات المدينة والدائنة
- **إغلاق الفترة المحاسبية** (`accounts/period_closing`)
  - إجراءات إغلاق الفترة المالية
  - ترحيل الأرباح والخسائر

#### **2.2 الذمم (Receivables & Payables)**
- **حسابات العملاء** (`customer/account_ledger`)
  - متابعة أرصدة العملاء
  - أعمار الديون وإدارة التحصيل
- **سندات القبض** (`finance/receipt_voucher`)
  - تسجيل المقبوضات النقدية والشيكات
  - من ح/النقدية إلى ح/العملاء
- **سندات الصرف** (`finance/payment_voucher`)
  - تسجيل المدفوعات للموردين والمصروفات
  - من ح/الموردين إلى ح/النقدية

#### **2.3 النقدية والبنوك (Cash & Bank Management)**
- **التسوية البنكية** (`finance/bank_reconciliation`)
  - مطابقة كشف البنك مع النظام
  - مطابقة ذكية بالذكاء الاصطناعي
- **إدارة الشيكات** (`finance/checks`)
  - تتبع دورة حياة الشيكات
  - حسابات وسيطة للشيكات

#### **2.4 الأصول الثابتة (Fixed Assets)**
- **سجل الأصول الثابتة** (`accounts/fixed_assets`)
  - تسجيل وإدارة الأصول
  - قيود شراء الأصول
- **حساب الإهلاك** (`accounts/depreciation`)
  - حساب الإهلاك التلقائي
  - قيود الإهلاك الشهرية/السنوية

#### **2.5 الموازنات والتخطيط المالي (Budgeting & Planning)**
- **إعداد الموازنات** (`accounts/budget`)
  - موازنات الإيرادات والمصروفات
  - التخطيط للفترات القادمة
- **متابعة الموازنة** (`accounts/budget_monitoring`)
  - مقارنة الأداء الفعلي مع المخطط
  - تحليل الانحرافات

#### **2.6 التقارير المالية والضريبية (Financial & Tax Reports)**
- **الميزانية العمومية** (`accounts/balance_sheet`)
- **قائمة الدخل** (`accounts/income_statement`)
- **قائمة التدفق النقدي** (`accounts/cash_flow`)
- **ميزان المراجعة** (`accounts/trial_balance`)
- **تقارير ضريبة القيمة المضافة** (`accounts/vat_report`)
- **الإقرارات الضريبية** (`accounts/tax_return`)

### **📦 3. نظام المخزون (Inventory System)**
#### **3.1 إدارة المنتجات الأساسية**
- **المنتجات** (`inventory/product`)
- **فئات المنتجات** (`inventory/category`)
- **الشركات المصنعة** (`inventory/manufacturer`)
- **الوحدات** (`inventory/units`)

#### **3.2 إدارة المخزون المتقدمة**
- **المخزون الحالي** (`inventory/current_stock`)
- **مستويات المخزون** (`inventory/stock_levels`)
- **حركة المخزون** (`inventory/stock_movement`)
- **تسوية المخزون** (`inventory/stock_adjustment`)
- **نقل المخزون** (`inventory/stock_transfer`)

#### **3.3 المستودعات والمواقع**
- **إدارة المستودعات** (`inventory/warehouse`)
- **إدارة المواقع** (`inventory/location_management`)
- **تتبع الدفعات** (`inventory/batch_tracking`)

#### **3.4 الجرد والتقييم**
- **جرد المخزون** (`inventory/stocktake`)
- **عد المخزون** (`inventory/stock_count`)
- **تقييم المخزون** (`inventory/stock_valuation`)
- **تحليل ABC** (`inventory/abc_analysis`)

#### **3.5 التنبيهات والتقارير**
- **تنبيهات المخزون** (`inventory/stock_alerts`)
- **تقارير المخزون** (`inventory/inventory_reports`)
- **لوحة المخزون التفاعلية** (`inventory/interactive_dashboard`)

### **🛒 4. نظام المشتريات (Purchasing System)**
#### **4.1 دورة الشراء الأساسية (Core Purchase Cycle)**
- **طلبات الشراء الداخلية** (`purchase/requisition`)
  - طلبات الأقسام للمواد والخدمات
  - سير عمل الموافقات
- **أوامر الشراء** (`purchase/order`)
  - أوامر شراء رسمية للموردين
  - تتبع الاستلام وربط الفواتير
- **استلام البضائع** (`purchase/goods_receipt`)
  - توثيق الاستلام الفعلي
  - فحص الجودة وتحديث المخزون
- **فواتير الموردين** (`purchase/supplier_invoice`)
  - مطابقة ثلاثية (PO, GRN, Invoice)
  - اعتماد الدفع

#### **4.2 إدارة الموردين**
- **الموردين** (`supplier/supplier`)
- **مجموعات الموردين** (`supplier/supplier_group`)
- **تقييم الموردين** (`supplier/evaluation`)
- **عقود الموردين** (`purchase/supplier_contracts`)

#### **4.3 العمليات المتقدمة**
- **عروض الأسعار** (`purchase/quotation`)
- **مقارنة العروض** (`purchase/quotation_comparison`)
- **تتبع الطلبات** (`purchase/order_tracking`)
- **تخطيط المشتريات** (`purchase/planning`)
- **تحليلات المشتريات** (`purchase/purchase_analytics`)

### **💰 5. نظام المبيعات وإدارة علاقات العملاء (Sales & CRM)**
#### **5.1 عمليات المبيعات**
- **أوامر البيع** (`sale/order`)
- **مرتجعات المبيعات** (`sale/return`)
- **كوبونات الهدايا** (`sale/voucher`)
- **قوالب الكوبونات** (`sale/voucher_theme`)
- **التسعير الديناميكي** (`sale/dynamic_pricing`)
- **البيع بالتقسيط** (`sale/installment`)
- **السلات المهجورة** (`sale/abandoned_cart`)
- **خطط التقسيط** (`sale/installment_plan`)
- **معالجة الطلبات** (`sale/order_processing`)

#### **5.2 إدارة العملاء**
- **العملاء** (`customer/customer`)
- **مجموعات العملاء** (`customer/customer_group`)
- **الحقول المخصصة** (`customer/custom_field`)
- **تقييم العملاء** (`customer/feedback`)
- **موافقة العملاء** (`customer/customer_approval`)
- **برنامج الولاء** (`customer/loyalty`)

#### **5.3 نظام CRM**
- **العملاء المحتملون** (`crm/lead`)
- **الصفقات** (`crm/deal`)
- **جهات الاتصال** (`crm/contact`)
- **الحملات التسويقية** (`crm/campaign`)
- **تحليلات CRM** (`crm/analytics`)
- **أنشطة العملاء** (`crm/activity`)
- **توقعات المبيعات** (`crm/sales_forecast`)

### **🌐 6. إدارة الموقع والتجارة الإلكترونية (Website & E-commerce)**
#### **6.1 إدارة المحتوى**
- **الفئات** (`catalog/category`)
- **المنتجات** (`catalog/product`)
- **الخصائص** (`catalog/attribute`)
- **مجموعات الخصائص** (`catalog/attribute_group`)
- **الخيارات** (`catalog/option`)
- **الفلاتر** (`catalog/filter`)

#### **6.2 المحتوى والتسويق**
- **المدونة** (`catalog/blog`)
- **فئات المدونة** (`catalog/blog_category`)
- **تعليقات المدونة** (`catalog/blog_comment`)
- **علامات المدونة** (`catalog/blog_tag`)
- **صفحات المعلومات** (`catalog/information`)
- **المراجعات** (`catalog/review`)

#### **6.3 تحسين محركات البحث**
- **إدارة SEO** (`catalog/seo`)
- **روابط SEO** (`design/seo_url`)

#### **6.4 التصميم والقوالب**
- **البانرات** (`design/banner`)
- **التخطيطات** (`design/layout`)
- **القوالب** (`design/theme`)
- **الترجمات** (`design/translation`)

#### **6.5 التسعير والعروض الترويجية**
- **الكوبونات** (`marketing/coupon`)
- **بطاقات الهدايا** (`marketing/voucher`)
- **العروض الخاصة** (`catalog/special`)

### **👥 7. الموارد البشرية (Human Resources)**
#### **7.1 إدارة الموظفين**
- **الموظفين** (`hr/employee`)
- **الحضور والانصراف** (`hr/attendance`)
- **لوحة الموارد البشرية** (`hr/hr_dashboard`)

#### **7.2 الرواتب والمستحقات**
- **الرواتب** (`hr/payroll`)
- **الرواتب المتقدمة** (`hr/payroll_advanced`)
- **السلف** (`hr/employee_advance`)

#### **7.3 الإجازات والتقييم**
- **الإجازات** (`hr/leave`)
- **تقييم الأداء** (`hr/performance`)

### **🚚 8. الشحن والتوصيل (Shipping & Delivery)**
#### **8.1 إدارة الشحنات**
- **الشحنات** (`shipping/shipment`)
- **تتبع الشحنات** (`shipping/tracking`)
- **لوحة الشحن** (`shipping/shipping_dashboard`)

#### **8.2 تحضير الطلبات**
- **تحضير الطلبات** (`shipping/prepare_orders`)
- **تنفيذ الطلبات** (`shipping/order_fulfillment`)

### **📄 9. إدارة المستندات (Document Management)**
#### **9.1 المستندات الأساسية**
- **الموافقات** (`documents/approval`)
- **الأرشيف** (`documents/archive`)
- **القوالب** (`documents/templates`)
- **إدارة الإصدارات** (`documents/versioning`)

### **💬 10. التواصل والإشعارات (Communication & Notifications)**
#### **10.1 التواصل الداخلي**
- **الإعلانات** (`communication/announcements`)
- **المحادثات** (`communication/chat`)
- **الرسائل** (`communication/messages`)
- **الفرق** (`communication/teams`)

#### **10.2 إدارة الإشعارات**
- **إعدادات الإشعارات** (`notification/settings`)
- **أتمتة الإشعارات** (`notification/automation`)
- **قوالب الإشعارات** (`notification/templates`)

### **⚙️ 11. سير العمل (Workflow System)**
#### **11.1 تصميم سير العمل**
- **سير العمل** (`workflow/workflow`)
- **المحرر المرئي** (`workflow/visual_editor`)
- **المحرر المرئي المتقدم** (`workflow/advanced_visual_editor`)
- **المصمم** (`workflow/designer`)

#### **11.2 عناصر سير العمل**
- **الإجراءات** (`workflow/actions`)
- **الشروط** (`workflow/conditions`)
- **المحفزات** (`workflow/triggers`)
- **المهام** (`workflow/task`)

### **🤖 12. الذكاء الاصطناعي (Artificial Intelligence)**
#### **12.1 المساعد الذكي**
- **المساعد الذكي** (`ai/ai_assistant`)
- **التحليلات الذكية** (`ai/smart_analytics`)

#### **12.2 التحليلات المتقدمة**
- **إدارة الحملات** (`api/campaign_management`)
- **رحلة العميل** (`api/customer_journey`)
- **تسجيل العملاء المحتملين** (`api/lead_scoring`)
- **التنبؤ بالمبيعات** (`api/sales_forecast`)

### **🏛️ 13. الحوكمة والمخاطر (Governance & Risk)**
#### **13.1 الامتثال والرقابة**
- **الامتثال** (`governance/compliance`)
- **التدقيق الداخلي** (`governance/internal_audit`)
- **الرقابة الداخلية** (`governance/internal_control`)

#### **13.2 إدارة المخاطر**
- **سجل المخاطر** (`governance/risk_register`)
- **العقود القانونية** (`governance/legal_contract`)
- **الاجتماعات** (`governance/meetings`)

### **📊 14. التقارير والتحليلات (Reports & Analytics)**
#### **14.1 التقارير القياسية**
- **تقارير المبيعات** (`report/sale`)
- **تقارير المشتريات** (`report/purchase`)
- **تقارير العملاء** (`report/customer`)
- **تقارير المنتجات** (`report/product`)
- **تقارير التسويق** (`report/marketing`)

#### **14.2 التحليلات المتقدمة**
- **تحليل المخزون** (`report/inventory_analysis`)
- **اتجاهات المخزون** (`report/inventory_trends`)
- **الإحصائيات** (`report/statistics`)

### **🔧 15. الإعدادات والإدارة (Settings & Administration)**
#### **15.1 إعدادات النظام**
- **الإعدادات العامة** (`setting/setting`)
- **إعدادات المتجر** (`setting/store`)

#### **15.2 إدارة المستخدمين**
- **المستخدمين** (`user/user`)
- **مجموعات المستخدمين** (`user/user_group`)
- **الصلاحيات** (`user/permission`)
- **الصلاحيات المتقدمة** (`user/user_permission_advanced`)
- **إعداد المصادقة الثنائية** (`user/two_factor_setup`)

#### **15.3 التوطين**
- **البلدان** (`localisation/country`)
- **العملات** (`localisation/currency`)
- **المناطق الجغرافية** (`localisation/geo_zone`)
- **اللغات** (`localisation/language`)
- **المواقع** (`localisation/location`)
- **حالات الطلبات** (`localisation/order_status`)

#### **15.4 أدوات النظام**
- **النسخ الاحتياطي** (`tool/backup`)
- **سجلات النظام** (`tool/log`)
- **التدقيق** (`tool/audit`)
- **الرسائل** (`tool/messaging`)
- **رفع الملفات** (`tool/upload`)

### **📱 16. نظام ETA المصري (Egyptian Tax Authority)**
#### **16.1 التكامل مع ETA**
- **إدارة ETA** (`eta/eta_management`)
- **لوحة الامتثال** (`eta/compliance_dashboard`)
- **الأكواد** (`eta/codes`)
- **الفواتير** (`eta/invoices`)

### **💳 17. نظام الاشتراكات (Subscription System)**
#### **17.1 إدارة الاشتراكات**
- **الاشتراكات** (`subscription/subscription`)
- **الفوترة والمدفوعات** (`subscription/billing`)

## 📈 إحصائيات النظام
- **إجمالي الوحدات الرئيسية:** 17 وحدة
- **إجمالي الشاشات المقدرة:** 300+ شاشة
- **إجمالي ملفات Controllers:** 500+ ملف (من tree.txt)
- **أكبر وحدة:** النظام المحاسبي (40+ شاشة)
- **ثاني أكبر وحدة:** نظام المخزون (35+ شاشة)
- **ثالث أكبر وحدة:** نظام المشتريات (25+ شاشة)
