📄 Route: common/reset
📂 Controller: controller\common\reset.php
🧱 Models used (2):
   ✅ user/user (47 functions)
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\common\reset.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\common\reset.php (7 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\common\reset.php (7 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (16):
   - action
   - button_cancel
   - button_save
   - cancel
   - confirm
   - entry_confirm
   - entry_password
   - error_confirm
   - error_password
   - footer
   - header
   - heading_title
   - password
   - text_home
   - text_password
   - text_success

❌ Missing in Arabic (9):
   - action
   - button_cancel
   - button_save
   - cancel
   - confirm
   - footer
   - header
   - password
   - text_home

❌ Missing in English (9):
   - action
   - button_cancel
   - button_save
   - cancel
   - confirm
   - footer
   - header
   - password
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 9 items
      - button_save
      - text_home
      - action
      - button_cancel
      - footer
   🟡 MISSING_ENGLISH_VARIABLES: 9 items
      - button_save
      - text_home
      - action
      - button_cancel
      - footer

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 9 متغير عربي و 9 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:32:54
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.