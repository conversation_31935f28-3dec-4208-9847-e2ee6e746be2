📄 Route: hr/leave
📂 Controller: controller\hr\leave.php
🧱 Models used (2):
   - hr/leave
   - user/user
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\hr\leave.php
🇬🇧 English Language Files (1):
   - language\en-gb\hr\leave.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add_leave_request
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_employee
   - column_end_date
   - column_leave_type
   - column_start_date
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_add_leave_request
   - text_ajax_error
   - text_all_leave_types
   - text_all_statuses
   - text_approved_by
   - text_confirm_delete
   - text_date_end
   - text_date_start
   - text_edit_leave_request
   - text_employee
   - text_filter
   - text_home
   - text_leave_list
   - text_leave_type
   - text_reason
   - text_select_approver
   - text_select_employee
   - text_select_leave_type
   - text_status
   - text_status_approved
   - text_status_cancelled
   - text_status_pending
   - text_status_rejected
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in Arabic:
   - button_add_leave_request
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_employee
   - column_end_date
   - column_leave_type
   - column_start_date
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_add_leave_request
   - text_ajax_error
   - text_all_leave_types
   - text_all_statuses
   - text_approved_by
   - text_confirm_delete
   - text_date_end
   - text_date_start
   - text_edit_leave_request
   - text_employee
   - text_filter
   - text_home
   - text_leave_list
   - text_leave_type
   - text_reason
   - text_select_approver
   - text_select_employee
   - text_select_leave_type
   - text_status
   - text_status_approved
   - text_status_cancelled
   - text_status_pending
   - text_status_rejected
   - text_success_add
   - text_success_delete
   - text_success_edit

❌ Missing in English:
   - button_add_leave_request
   - button_close
   - button_filter
   - button_reset
   - button_save
   - column_actions
   - column_employee
   - column_end_date
   - column_leave_type
   - column_start_date
   - column_status
   - error_invalid_request
   - error_not_found
   - error_permission
   - error_required
   - heading_title
   - text_add_leave_request
   - text_ajax_error
   - text_all_leave_types
   - text_all_statuses
   - text_approved_by
   - text_confirm_delete
   - text_date_end
   - text_date_start
   - text_edit_leave_request
   - text_employee
   - text_filter
   - text_home
   - text_leave_list
   - text_leave_type
   - text_reason
   - text_select_approver
   - text_select_employee
   - text_select_leave_type
   - text_status
   - text_status_approved
   - text_status_cancelled
   - text_status_pending
   - text_status_rejected
   - text_success_add
   - text_success_delete
   - text_success_edit

💡 Suggested Arabic Additions:
   - button_add_leave_request = ""  # TODO: ترجمة عربية
   - button_close = ""  # TODO: ترجمة عربية
   - button_filter = ""  # TODO: ترجمة عربية
   - button_reset = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - column_actions = ""  # TODO: ترجمة عربية
   - column_employee = ""  # TODO: ترجمة عربية
   - column_end_date = ""  # TODO: ترجمة عربية
   - column_leave_type = ""  # TODO: ترجمة عربية
   - column_start_date = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - error_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add_leave_request = ""  # TODO: ترجمة عربية
   - text_ajax_error = ""  # TODO: ترجمة عربية
   - text_all_leave_types = ""  # TODO: ترجمة عربية
   - text_all_statuses = ""  # TODO: ترجمة عربية
   - text_approved_by = ""  # TODO: ترجمة عربية
   - text_confirm_delete = ""  # TODO: ترجمة عربية
   - text_date_end = ""  # TODO: ترجمة عربية
   - text_date_start = ""  # TODO: ترجمة عربية
   - text_edit_leave_request = ""  # TODO: ترجمة عربية
   - text_employee = ""  # TODO: ترجمة عربية
   - text_filter = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_leave_list = ""  # TODO: ترجمة عربية
   - text_leave_type = ""  # TODO: ترجمة عربية
   - text_reason = ""  # TODO: ترجمة عربية
   - text_select_approver = ""  # TODO: ترجمة عربية
   - text_select_employee = ""  # TODO: ترجمة عربية
   - text_select_leave_type = ""  # TODO: ترجمة عربية
   - text_status = ""  # TODO: ترجمة عربية
   - text_status_approved = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_pending = ""  # TODO: ترجمة عربية
   - text_status_rejected = ""  # TODO: ترجمة عربية
   - text_success_add = ""  # TODO: ترجمة عربية
   - text_success_delete = ""  # TODO: ترجمة عربية
   - text_success_edit = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add_leave_request = ""  # TODO: English translation
   - button_close = ""  # TODO: English translation
   - button_filter = ""  # TODO: English translation
   - button_reset = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - column_actions = ""  # TODO: English translation
   - column_employee = ""  # TODO: English translation
   - column_end_date = ""  # TODO: English translation
   - column_leave_type = ""  # TODO: English translation
   - column_start_date = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - error_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add_leave_request = ""  # TODO: English translation
   - text_ajax_error = ""  # TODO: English translation
   - text_all_leave_types = ""  # TODO: English translation
   - text_all_statuses = ""  # TODO: English translation
   - text_approved_by = ""  # TODO: English translation
   - text_confirm_delete = ""  # TODO: English translation
   - text_date_end = ""  # TODO: English translation
   - text_date_start = ""  # TODO: English translation
   - text_edit_leave_request = ""  # TODO: English translation
   - text_employee = ""  # TODO: English translation
   - text_filter = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_leave_list = ""  # TODO: English translation
   - text_leave_type = ""  # TODO: English translation
   - text_reason = ""  # TODO: English translation
   - text_select_approver = ""  # TODO: English translation
   - text_select_employee = ""  # TODO: English translation
   - text_select_leave_type = ""  # TODO: English translation
   - text_status = ""  # TODO: English translation
   - text_status_approved = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_pending = ""  # TODO: English translation
   - text_status_rejected = ""  # TODO: English translation
   - text_success_add = ""  # TODO: English translation
   - text_success_delete = ""  # TODO: English translation
   - text_success_edit = ""  # TODO: English translation
