📄 Route: migration/odoo
📂 Controller: controller\migration\odoo.php
🧱 Models used (2):
   ✅ migration/migration (10 functions)
   ✅ migration/odoo (9 functions)
🎨 Twig templates (1):
   ✅ view\template\migration\odoo.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\migration\odoo.php (176 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\migration\odoo.php (40 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (59):
   - action
   - alert_required_fields
   - button_import
   - entry_batch_size
   - entry_delimiter
   - entry_file
   - entry_mapping
   - entry_server_url
   - entry_skip_rows
   - entry_source
   - error_file_type
   - error_mapping
   - error_permission
   - header
   - text_core_data
   - text_products
   - text_progress
   - text_records_imported
   - text_step1_description
   - text_step1_title
   ... و 39 متغير آخر

❌ Missing in Arabic (13):
   - action
   - button_save
   - cancel
   - column_left
   - entry_batch_size
   - entry_delimiter
   - entry_encoding
   - entry_file
   - entry_mapping
   - entry_skip_rows
   - entry_source
   - footer
   - header

❌ Missing in English (29):
   - action
   - alert_required_fields
   - button_import
   - entry_batch_size
   - entry_delimiter
   - entry_file
   - entry_mapping
   - entry_skip_rows
   - entry_source
   - error_encoding
   - error_file_type
   - error_mapping
   - error_permission
   - error_processing
   - header
   ... و 14 متغير آخر

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 13 items
      - entry_encoding
      - cancel
      - button_save
      - column_left
      - action
   🟡 MISSING_ENGLISH_VARIABLES: 29 items
      - action
      - error_file_type
      - header
      - alert_required_fields
      - error_processing

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 13 متغير عربي و 29 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.