📄 Route: accounts/journal_entry
📂 Controller: controller\accounts\journal_entry.php
🧱 Models used (10):
   ✅ core/central_service_manager (60 functions)
   ✅ accounts/journal_entry (19 functions)
   ✅ accounts/audit_trail (13 functions)
   ✅ accounts/chartaccount (19 functions)
   ❌ accounts/journal_template (0 functions)
   ❌ accounts/cost_center (0 functions)
   ❌ accounts/project (0 functions)
   ❌ accounts/department (0 functions)
   ❌ accounts/approval (0 functions)
   ✅ notification/notification (8 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\accounts\journal_entry.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\accounts\journal_entry.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (33):
   - date_format_long
   - error_account_not_found
   - error_both_amounts
   - error_journal_id
   - error_lines_minimum
   - error_permission
   - error_unbalanced
   - text_add
   - text_approved
   - text_customer_payment
   - text_draft
   - text_home
   - text_inventory_movement
   - text_manual
   - text_purchase_order
   - text_select
   - text_success_edit
   - text_success_post
   - text_success_template_save
   - text_supplier_payment
   ... و 13 متغير آخر

❌ Missing in Arabic (33):
   - date_format_long
   - error_both_amounts
   - error_journal_id
   - error_lines_minimum
   - error_permission
   - error_unbalanced
   - text_add
   - text_approved
   - text_customer_payment
   - text_inventory_movement
   - text_purchase_order
   - text_select
   - text_success_post
   - text_success_template_save
   - text_supplier_payment
   ... و 18 متغير آخر

❌ Missing in English (33):
   - date_format_long
   - error_both_amounts
   - error_journal_id
   - error_lines_minimum
   - error_permission
   - error_unbalanced
   - text_add
   - text_approved
   - text_customer_payment
   - text_inventory_movement
   - text_purchase_order
   - text_select
   - text_success_post
   - text_success_template_save
   - text_supplier_payment
   ... و 18 متغير آخر

🗄️ Database Tables Used (3):
   ❌ journal_entry
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 33 items
      - error_unbalanced
      - error_lines_minimum
      - text_success_template_save
      - text_select
      - text_success_post
   🟡 MISSING_ENGLISH_VARIABLES: 33 items
      - error_unbalanced
      - error_lines_minimum
      - text_success_template_save
      - text_select
      - text_success_post
   🔴 INVALID_DATABASE_TABLES: 3 items
      - workflow
      - template
      - journal_entry
   🟢 MISSING_MODEL_FILES: 5 items
      - accounts/journal_template
      - accounts/cost_center
      - accounts/project
      - accounts/department
      - accounts/approval

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 33 متغير عربي و 33 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 3 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 5 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:41
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.