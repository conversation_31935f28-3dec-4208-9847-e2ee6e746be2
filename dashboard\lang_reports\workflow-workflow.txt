📄 Route: workflow/workflow
📂 Controller: controller\workflow\workflow.php
🧱 Models used (1):
   - workflow/workflow
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\workflow\workflow.php
🇬🇧 English Language Files (1):
   - language\en-gb\workflow\workflow.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add
   - button_cancel
   - button_delete
   - button_design
   - button_edit
   - button_save
   - button_setup
   - button_workflow_list
   - column_action
   - column_date_added
   - column_description
   - column_name
   - column_status
   - date_format_short
   - entry_description
   - entry_name
   - entry_status
   - error_active_instances
   - error_name
   - error_permission
   - error_save
   - heading_title
   - heading_title_designer
   - heading_title_setup
   - text_add
   - text_apply
   - text_cancel
   - text_confirm
   - text_confirm_delete
   - text_confirm_new
   - text_database_setup
   - text_delete
   - text_description
   - text_disabled
   - text_edit
   - text_enabled
   - text_error_saving
   - text_execution_instructions
   - text_fit
   - text_home
   - text_list
   - text_name
   - text_new
   - text_new_workflow
   - text_no_results
   - text_node_decision
   - text_node_delay
   - text_node_email
   - text_node_end
   - text_node_start
   - text_node_task
   - text_nodes
   - text_pagination
   - text_properties
   - text_save
   - text_setup
   - text_setup_info
   - text_success
   - text_success_save
   - text_workflow_designer
   - text_workflow_saved
   - text_workflow_tables
   - text_zoom_in
   - text_zoom_out

❌ Missing in Arabic:
   - button_add
   - button_cancel
   - button_delete
   - button_design
   - button_edit
   - button_save
   - button_setup
   - button_workflow_list
   - column_action
   - column_date_added
   - column_description
   - column_name
   - column_status
   - date_format_short
   - entry_description
   - entry_name
   - entry_status
   - error_active_instances
   - error_name
   - error_permission
   - error_save
   - heading_title
   - heading_title_designer
   - heading_title_setup
   - text_add
   - text_apply
   - text_cancel
   - text_confirm
   - text_confirm_delete
   - text_confirm_new
   - text_database_setup
   - text_delete
   - text_description
   - text_disabled
   - text_edit
   - text_enabled
   - text_error_saving
   - text_execution_instructions
   - text_fit
   - text_home
   - text_list
   - text_name
   - text_new
   - text_new_workflow
   - text_no_results
   - text_node_decision
   - text_node_delay
   - text_node_email
   - text_node_end
   - text_node_start
   - text_node_task
   - text_nodes
   - text_pagination
   - text_properties
   - text_save
   - text_setup
   - text_setup_info
   - text_success
   - text_success_save
   - text_workflow_designer
   - text_workflow_saved
   - text_workflow_tables
   - text_zoom_in
   - text_zoom_out

❌ Missing in English:
   - button_add
   - button_cancel
   - button_delete
   - button_design
   - button_edit
   - button_save
   - button_setup
   - button_workflow_list
   - column_action
   - column_date_added
   - column_description
   - column_name
   - column_status
   - date_format_short
   - entry_description
   - entry_name
   - entry_status
   - error_active_instances
   - error_name
   - error_permission
   - error_save
   - heading_title
   - heading_title_designer
   - heading_title_setup
   - text_add
   - text_apply
   - text_cancel
   - text_confirm
   - text_confirm_delete
   - text_confirm_new
   - text_database_setup
   - text_delete
   - text_description
   - text_disabled
   - text_edit
   - text_enabled
   - text_error_saving
   - text_execution_instructions
   - text_fit
   - text_home
   - text_list
   - text_name
   - text_new
   - text_new_workflow
   - text_no_results
   - text_node_decision
   - text_node_delay
   - text_node_email
   - text_node_end
   - text_node_start
   - text_node_task
   - text_nodes
   - text_pagination
   - text_properties
   - text_save
   - text_setup
   - text_setup_info
   - text_success
   - text_success_save
   - text_workflow_designer
   - text_workflow_saved
   - text_workflow_tables
   - text_zoom_in
   - text_zoom_out

💡 Suggested Arabic Additions:
   - button_add = ""  # TODO: ترجمة عربية
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_delete = ""  # TODO: ترجمة عربية
   - button_design = ""  # TODO: ترجمة عربية
   - button_edit = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - button_setup = ""  # TODO: ترجمة عربية
   - button_workflow_list = ""  # TODO: ترجمة عربية
   - column_action = ""  # TODO: ترجمة عربية
   - column_date_added = ""  # TODO: ترجمة عربية
   - column_description = ""  # TODO: ترجمة عربية
   - column_name = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - entry_description = ""  # TODO: ترجمة عربية
   - entry_name = ""  # TODO: ترجمة عربية
   - entry_status = ""  # TODO: ترجمة عربية
   - error_active_instances = ""  # TODO: ترجمة عربية
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_save = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_title_designer = ""  # TODO: ترجمة عربية
   - heading_title_setup = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_apply = ""  # TODO: ترجمة عربية
   - text_cancel = ""  # TODO: ترجمة عربية
   - text_confirm = ""  # TODO: ترجمة عربية
   - text_confirm_delete = ""  # TODO: ترجمة عربية
   - text_confirm_new = ""  # TODO: ترجمة عربية
   - text_database_setup = ""  # TODO: ترجمة عربية
   - text_delete = ""  # TODO: ترجمة عربية
   - text_description = ""  # TODO: ترجمة عربية
   - text_disabled = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_enabled = ""  # TODO: ترجمة عربية
   - text_error_saving = ""  # TODO: ترجمة عربية
   - text_execution_instructions = ""  # TODO: ترجمة عربية
   - text_fit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية
   - text_name = ""  # TODO: ترجمة عربية
   - text_new = ""  # TODO: ترجمة عربية
   - text_new_workflow = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_node_decision = ""  # TODO: ترجمة عربية
   - text_node_delay = ""  # TODO: ترجمة عربية
   - text_node_email = ""  # TODO: ترجمة عربية
   - text_node_end = ""  # TODO: ترجمة عربية
   - text_node_start = ""  # TODO: ترجمة عربية
   - text_node_task = ""  # TODO: ترجمة عربية
   - text_nodes = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_properties = ""  # TODO: ترجمة عربية
   - text_save = ""  # TODO: ترجمة عربية
   - text_setup = ""  # TODO: ترجمة عربية
   - text_setup_info = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_success_save = ""  # TODO: ترجمة عربية
   - text_workflow_designer = ""  # TODO: ترجمة عربية
   - text_workflow_saved = ""  # TODO: ترجمة عربية
   - text_workflow_tables = ""  # TODO: ترجمة عربية
   - text_zoom_in = ""  # TODO: ترجمة عربية
   - text_zoom_out = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add = ""  # TODO: English translation
   - button_cancel = ""  # TODO: English translation
   - button_delete = ""  # TODO: English translation
   - button_design = ""  # TODO: English translation
   - button_edit = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - button_setup = ""  # TODO: English translation
   - button_workflow_list = ""  # TODO: English translation
   - column_action = ""  # TODO: English translation
   - column_date_added = ""  # TODO: English translation
   - column_description = ""  # TODO: English translation
   - column_name = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - entry_description = ""  # TODO: English translation
   - entry_name = ""  # TODO: English translation
   - entry_status = ""  # TODO: English translation
   - error_active_instances = ""  # TODO: English translation
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_save = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_title_designer = ""  # TODO: English translation
   - heading_title_setup = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_apply = ""  # TODO: English translation
   - text_cancel = ""  # TODO: English translation
   - text_confirm = ""  # TODO: English translation
   - text_confirm_delete = ""  # TODO: English translation
   - text_confirm_new = ""  # TODO: English translation
   - text_database_setup = ""  # TODO: English translation
   - text_delete = ""  # TODO: English translation
   - text_description = ""  # TODO: English translation
   - text_disabled = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_enabled = ""  # TODO: English translation
   - text_error_saving = ""  # TODO: English translation
   - text_execution_instructions = ""  # TODO: English translation
   - text_fit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
   - text_name = ""  # TODO: English translation
   - text_new = ""  # TODO: English translation
   - text_new_workflow = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_node_decision = ""  # TODO: English translation
   - text_node_delay = ""  # TODO: English translation
   - text_node_email = ""  # TODO: English translation
   - text_node_end = ""  # TODO: English translation
   - text_node_start = ""  # TODO: English translation
   - text_node_task = ""  # TODO: English translation
   - text_nodes = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_properties = ""  # TODO: English translation
   - text_save = ""  # TODO: English translation
   - text_setup = ""  # TODO: English translation
   - text_setup_info = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_success_save = ""  # TODO: English translation
   - text_workflow_designer = ""  # TODO: English translation
   - text_workflow_saved = ""  # TODO: English translation
   - text_workflow_tables = ""  # TODO: English translation
   - text_zoom_in = ""  # TODO: English translation
   - text_zoom_out = ""  # TODO: English translation
