📄 Route: common/header_new
📂 Controller: controller\common\header_new.php
🧱 Models used (10):
   ✅ user/user (47 functions)
   ✅ tool/image (1 functions)
   ❌ common/security (0 functions)
   ✅ core/central_service_manager (60 functions)
   ✅ communication/unified_notification (16 functions)
   ❌ communication/message (0 functions)
   ✅ workflow/approval (15 functions)
   ✅ inventory/product (78 functions)
   ✅ workflow/task (15 functions)
   ✅ sale/order (27 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ❌ language\ar\common\header_new.php (0 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\common\header_new.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (17):
   - code
   - direction
   - error_invalid_request
   - error_loading_header_data
   - error_marking_notification
   - error_user_not_logged
   - text_expiry_alert
   - text_logged
   - text_low_stock_alert
   - text_notification_marked_read
   - text_overdue_task
   - text_product_expiring
   - text_product_low_stock
   - text_security
   - text_task_overdue
   - text_task_upcoming
   - text_upcoming_task

❌ Missing in Arabic (17):
   - code
   - error_invalid_request
   - error_loading_header_data
   - error_user_not_logged
   - text_expiry_alert
   - text_logged
   - text_low_stock_alert
   - text_notification_marked_read
   - text_overdue_task
   - text_product_expiring
   - text_product_low_stock
   - text_security
   - text_task_overdue
   - text_task_upcoming
   - text_upcoming_task
   ... و 2 متغير آخر

❌ Missing in English (17):
   - code
   - error_invalid_request
   - error_loading_header_data
   - error_user_not_logged
   - text_expiry_alert
   - text_logged
   - text_low_stock_alert
   - text_notification_marked_read
   - text_overdue_task
   - text_product_expiring
   - text_product_low_stock
   - text_security
   - text_task_overdue
   - text_task_upcoming
   - text_upcoming_task
   ... و 2 متغير آخر

🗄️ Database Tables Used (3):
   ❌ quantity
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 17 items
      - text_overdue_task
      - error_invalid_request
      - text_task_overdue
      - text_low_stock_alert
      - text_task_upcoming
   🟡 MISSING_ENGLISH_VARIABLES: 17 items
      - text_overdue_task
      - error_invalid_request
      - text_task_overdue
      - text_low_stock_alert
      - text_task_upcoming
   🔴 INVALID_DATABASE_TABLES: 3 items
      - workflow
      - quantity
      - template
   🟢 MISSING_MODEL_FILES: 2 items
      - common/security
      - communication/message

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 17 متغير عربي و 17 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 3 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 2 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:32:53
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.