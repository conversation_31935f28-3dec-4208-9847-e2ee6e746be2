📄 Route: localisation/return_status
📂 Controller: controller\localisation\return_status.php
🧱 Models used (3):
   ✅ localisation/return_status (7 functions)
   ✅ localisation/language (7 functions)
   ✅ sale/return (13 functions)
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   ✅ language\ar\localisation\return_status.php (12 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\localisation\return_status.php (12 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (11):
   - error_default
   - error_name
   - error_permission
   - error_return
   - heading_title
   - text_add
   - text_default
   - text_edit
   - text_home
   - text_pagination
   - text_success

❌ Missing in Arabic (3):
   - text_default
   - text_home
   - text_pagination

❌ Missing in English (3):
   - text_default
   - text_home
   - text_pagination

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 3 items
      - text_home
      - text_pagination
      - text_default
   🟡 MISSING_ENGLISH_VARIABLES: 3 items
      - text_home
      - text_pagination
      - text_default

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 3 متغير عربي و 3 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:09
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.