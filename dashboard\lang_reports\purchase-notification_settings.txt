📄 Route: purchase/notification_settings
📂 Controller: controller\purchase\notification_settings.php
🧱 Models used (4):
   - purchase/notification_settings
   - setting/setting
   - user/user
   - user/user_group
🎨 Twig templates (1):
   - view\template\purchase\notification_settings.twig
🈯 Arabic Language Files (1):
   - language\ar\purchase\notification_settings.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_email_from_address
   - error_email_from_name
   - error_permission
   - error_preview_data
   - error_push_api_key
   - error_push_provider
   - error_sms_api_key
   - error_sms_provider
   - error_template_content
   - error_template_name
   - error_template_subject
   - error_test_data
   - heading_title
   - text_analytics
   - text_daily
   - text_home
   - text_hourly
   - text_logs
   - text_monthly
   - text_pagination
   - text_success
   - text_success_templates
   - text_templates
   - text_test_success
   - text_weekly

❌ Missing in Arabic:
   - date_format_short
   - error_email_from_address
   - error_email_from_name
   - error_permission
   - error_preview_data
   - error_push_api_key
   - error_push_provider
   - error_sms_api_key
   - error_sms_provider
   - error_template_content
   - error_template_name
   - error_template_subject
   - error_test_data
   - heading_title
   - text_analytics
   - text_daily
   - text_home
   - text_hourly
   - text_logs
   - text_monthly
   - text_pagination
   - text_success
   - text_success_templates
   - text_templates
   - text_test_success
   - text_weekly

❌ Missing in English:
   - date_format_short
   - error_email_from_address
   - error_email_from_name
   - error_permission
   - error_preview_data
   - error_push_api_key
   - error_push_provider
   - error_sms_api_key
   - error_sms_provider
   - error_template_content
   - error_template_name
   - error_template_subject
   - error_test_data
   - heading_title
   - text_analytics
   - text_daily
   - text_home
   - text_hourly
   - text_logs
   - text_monthly
   - text_pagination
   - text_success
   - text_success_templates
   - text_templates
   - text_test_success
   - text_weekly

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_email_from_address = ""  # TODO: ترجمة عربية
   - error_email_from_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_preview_data = ""  # TODO: ترجمة عربية
   - error_push_api_key = ""  # TODO: ترجمة عربية
   - error_push_provider = ""  # TODO: ترجمة عربية
   - error_sms_api_key = ""  # TODO: ترجمة عربية
   - error_sms_provider = ""  # TODO: ترجمة عربية
   - error_template_content = ""  # TODO: ترجمة عربية
   - error_template_name = ""  # TODO: ترجمة عربية
   - error_template_subject = ""  # TODO: ترجمة عربية
   - error_test_data = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_analytics = ""  # TODO: ترجمة عربية
   - text_daily = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_hourly = ""  # TODO: ترجمة عربية
   - text_logs = ""  # TODO: ترجمة عربية
   - text_monthly = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية
   - text_success_templates = ""  # TODO: ترجمة عربية
   - text_templates = ""  # TODO: ترجمة عربية
   - text_test_success = ""  # TODO: ترجمة عربية
   - text_weekly = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_email_from_address = ""  # TODO: English translation
   - error_email_from_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_preview_data = ""  # TODO: English translation
   - error_push_api_key = ""  # TODO: English translation
   - error_push_provider = ""  # TODO: English translation
   - error_sms_api_key = ""  # TODO: English translation
   - error_sms_provider = ""  # TODO: English translation
   - error_template_content = ""  # TODO: English translation
   - error_template_name = ""  # TODO: English translation
   - error_template_subject = ""  # TODO: English translation
   - error_test_data = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_analytics = ""  # TODO: English translation
   - text_daily = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_hourly = ""  # TODO: English translation
   - text_logs = ""  # TODO: English translation
   - text_monthly = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
   - text_success_templates = ""  # TODO: English translation
   - text_templates = ""  # TODO: English translation
   - text_test_success = ""  # TODO: English translation
   - text_weekly = ""  # TODO: English translation
