📄 Route: migration/review
📂 Controller: controller\migration\review.php
🧱 Models used (1):
   - migration/migration
🎨 Twig templates (1):
   - view\template\migration\review.twig
🈯 Arabic Language Files (1):
   - language\ar\migration\migration.php
🇬🇧 English Language Files (1):
   - language\en-gb\migration\migration.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - alert_review_needed
   - button_approve
   - button_reject
   - button_rollback
   - column_action
   - column_date
   - column_destination
   - column_records
   - column_source
   - column_status
   - column_user
   - error_invalid_migration_status
   - error_migration_id
   - error_migration_not_found
   - error_no_backup
   - error_permission
   - error_processing
   - error_reject_reason
   - error_rollback
   - error_validation
   - status_approved
   - status_completed
   - status_failed
   - status_pending
   - status_rejected
   - text_error
   - text_home
   - text_migration
   - text_migration_approved
   - text_migration_rejected
   - text_migration_review
   - text_migration_rolled_back
   - text_success

❌ Missing in Arabic:
   - alert_review_needed
   - button_approve
   - button_reject
   - button_rollback
   - column_action
   - column_date
   - column_destination
   - column_records
   - column_source
   - column_status
   - column_user
   - error_invalid_migration_status
   - error_migration_id
   - error_migration_not_found
   - error_no_backup
   - error_permission
   - error_processing
   - error_reject_reason
   - error_rollback
   - error_validation
   - status_approved
   - status_completed
   - status_failed
   - status_pending
   - status_rejected
   - text_error
   - text_home
   - text_migration
   - text_migration_approved
   - text_migration_rejected
   - text_migration_review
   - text_migration_rolled_back
   - text_success

❌ Missing in English:
   - alert_review_needed
   - button_approve
   - button_reject
   - button_rollback
   - column_action
   - column_date
   - column_destination
   - column_records
   - column_source
   - column_status
   - column_user
   - error_invalid_migration_status
   - error_migration_id
   - error_migration_not_found
   - error_no_backup
   - error_permission
   - error_processing
   - error_reject_reason
   - error_rollback
   - error_validation
   - status_approved
   - status_completed
   - status_failed
   - status_pending
   - status_rejected
   - text_error
   - text_home
   - text_migration
   - text_migration_approved
   - text_migration_rejected
   - text_migration_review
   - text_migration_rolled_back
   - text_success

💡 Suggested Arabic Additions:
   - alert_review_needed = ""  # TODO: ترجمة عربية
   - button_approve = ""  # TODO: ترجمة عربية
   - button_reject = ""  # TODO: ترجمة عربية
   - button_rollback = ""  # TODO: ترجمة عربية
   - column_action = ""  # TODO: ترجمة عربية
   - column_date = ""  # TODO: ترجمة عربية
   - column_destination = ""  # TODO: ترجمة عربية
   - column_records = ""  # TODO: ترجمة عربية
   - column_source = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_user = ""  # TODO: ترجمة عربية
   - error_invalid_migration_status = ""  # TODO: ترجمة عربية
   - error_migration_id = ""  # TODO: ترجمة عربية
   - error_migration_not_found = ""  # TODO: ترجمة عربية
   - error_no_backup = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_processing = ""  # TODO: ترجمة عربية
   - error_reject_reason = ""  # TODO: ترجمة عربية
   - error_rollback = ""  # TODO: ترجمة عربية
   - error_validation = ""  # TODO: ترجمة عربية
   - status_approved = ""  # TODO: ترجمة عربية
   - status_completed = ""  # TODO: ترجمة عربية
   - status_failed = ""  # TODO: ترجمة عربية
   - status_pending = ""  # TODO: ترجمة عربية
   - status_rejected = ""  # TODO: ترجمة عربية
   - text_error = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_migration = ""  # TODO: ترجمة عربية
   - text_migration_approved = ""  # TODO: ترجمة عربية
   - text_migration_rejected = ""  # TODO: ترجمة عربية
   - text_migration_review = ""  # TODO: ترجمة عربية
   - text_migration_rolled_back = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - alert_review_needed = ""  # TODO: English translation
   - button_approve = ""  # TODO: English translation
   - button_reject = ""  # TODO: English translation
   - button_rollback = ""  # TODO: English translation
   - column_action = ""  # TODO: English translation
   - column_date = ""  # TODO: English translation
   - column_destination = ""  # TODO: English translation
   - column_records = ""  # TODO: English translation
   - column_source = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_user = ""  # TODO: English translation
   - error_invalid_migration_status = ""  # TODO: English translation
   - error_migration_id = ""  # TODO: English translation
   - error_migration_not_found = ""  # TODO: English translation
   - error_no_backup = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_processing = ""  # TODO: English translation
   - error_reject_reason = ""  # TODO: English translation
   - error_rollback = ""  # TODO: English translation
   - error_validation = ""  # TODO: English translation
   - status_approved = ""  # TODO: English translation
   - status_completed = ""  # TODO: English translation
   - status_failed = ""  # TODO: English translation
   - status_pending = ""  # TODO: English translation
   - status_rejected = ""  # TODO: English translation
   - text_error = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_migration = ""  # TODO: English translation
   - text_migration_approved = ""  # TODO: English translation
   - text_migration_rejected = ""  # TODO: English translation
   - text_migration_review = ""  # TODO: English translation
   - text_migration_rolled_back = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
