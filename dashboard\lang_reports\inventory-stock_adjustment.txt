📄 Route: inventory/stock_adjustment
📂 Controller: controller\inventory\stock_adjustment.php
🧱 Models used (6):
   - catalog/product
   - common/central_service_manager
   - inventory/branch
   - inventory/stock_adjustment
   - inventory/stock_adjustment_enhanced
   - user/user
🎨 Twig templates (1):
   - view\template\inventory\stock_adjustment.twig
🈯 Arabic Language Files (1):
   - language\ar\inventory\stock_adjustment.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_adjustment_date
   - column_adjustment_name
   - column_adjustment_number
   - column_adjustment_type
   - column_branch
   - column_notes
   - column_reason
   - column_status
   - column_total_items
   - column_total_value
   - column_user
   - date_format_short
   - datetime_format
   - error_adjustment_date
   - error_adjustment_items_required
   - error_adjustment_name
   - error_adjustment_not_found
   - error_advanced_permission
   - error_approval_permission
   - error_branch_required
   - error_cannot_approve
   - error_exception
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_product_required
   - error_quantity_must_be_positive
   - error_quantity_required
   - error_reject_reason
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - error_unit_cost_required
   - heading_title
   - success_approve
   - success_reject
   - text_add
   - text_adjustment_type_counting
   - text_adjustment_type_damage
   - text_adjustment_type_expiry
   - text_adjustment_type_found
   - text_adjustment_type_loss
   - text_adjustment_type_manual
   - text_adjustment_type_system
   - text_all
   - text_approved_success
   - text_edit
   - text_home
   - text_no_reason
   - text_pagination
   - text_posted_success
   - text_reason_category_correction
   - text_reason_category_decrease
   - text_reason_category_increase
   - text_reason_category_transfer
   - text_rejected_success
   - text_status_approved
   - text_status_cancelled
   - text_status_draft
   - text_status_pending_approval
   - text_status_posted
   - text_status_rejected
   - text_submitted_success
   - text_success

❌ Missing in Arabic:
   - column_adjustment_date
   - column_adjustment_name
   - column_adjustment_number
   - column_adjustment_type
   - column_branch
   - column_notes
   - column_reason
   - column_status
   - column_total_items
   - column_total_value
   - column_user
   - date_format_short
   - datetime_format
   - error_adjustment_date
   - error_adjustment_items_required
   - error_adjustment_name
   - error_adjustment_not_found
   - error_advanced_permission
   - error_approval_permission
   - error_branch_required
   - error_cannot_approve
   - error_exception
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_product_required
   - error_quantity_must_be_positive
   - error_quantity_required
   - error_reject_reason
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - error_unit_cost_required
   - heading_title
   - success_approve
   - success_reject
   - text_add
   - text_adjustment_type_counting
   - text_adjustment_type_damage
   - text_adjustment_type_expiry
   - text_adjustment_type_found
   - text_adjustment_type_loss
   - text_adjustment_type_manual
   - text_adjustment_type_system
   - text_all
   - text_approved_success
   - text_edit
   - text_home
   - text_no_reason
   - text_pagination
   - text_posted_success
   - text_reason_category_correction
   - text_reason_category_decrease
   - text_reason_category_increase
   - text_reason_category_transfer
   - text_rejected_success
   - text_status_approved
   - text_status_cancelled
   - text_status_draft
   - text_status_pending_approval
   - text_status_posted
   - text_status_rejected
   - text_submitted_success
   - text_success

❌ Missing in English:
   - column_adjustment_date
   - column_adjustment_name
   - column_adjustment_number
   - column_adjustment_type
   - column_branch
   - column_notes
   - column_reason
   - column_status
   - column_total_items
   - column_total_value
   - column_user
   - date_format_short
   - datetime_format
   - error_adjustment_date
   - error_adjustment_items_required
   - error_adjustment_name
   - error_adjustment_not_found
   - error_advanced_permission
   - error_approval_permission
   - error_branch_required
   - error_cannot_approve
   - error_exception
   - error_insufficient_stock_for_product
   - error_insufficient_stock_for_transfer
   - error_insufficient_stock_for_transfer_item
   - error_invalid_item
   - error_items_required
   - error_movement_failed_for_product
   - error_permission
   - error_product_required
   - error_quantity_must_be_positive
   - error_quantity_required
   - error_reject_reason
   - error_same_branch
   - error_transfer_already_completed
   - error_transfer_no_items
   - error_transfer_not_found
   - error_unit_cost_required
   - heading_title
   - success_approve
   - success_reject
   - text_add
   - text_adjustment_type_counting
   - text_adjustment_type_damage
   - text_adjustment_type_expiry
   - text_adjustment_type_found
   - text_adjustment_type_loss
   - text_adjustment_type_manual
   - text_adjustment_type_system
   - text_all
   - text_approved_success
   - text_edit
   - text_home
   - text_no_reason
   - text_pagination
   - text_posted_success
   - text_reason_category_correction
   - text_reason_category_decrease
   - text_reason_category_increase
   - text_reason_category_transfer
   - text_rejected_success
   - text_status_approved
   - text_status_cancelled
   - text_status_draft
   - text_status_pending_approval
   - text_status_posted
   - text_status_rejected
   - text_submitted_success
   - text_success

💡 Suggested Arabic Additions:
   - column_adjustment_date = ""  # TODO: ترجمة عربية
   - column_adjustment_name = ""  # TODO: ترجمة عربية
   - column_adjustment_number = ""  # TODO: ترجمة عربية
   - column_adjustment_type = ""  # TODO: ترجمة عربية
   - column_branch = ""  # TODO: ترجمة عربية
   - column_notes = ""  # TODO: ترجمة عربية
   - column_reason = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_total_items = ""  # TODO: ترجمة عربية
   - column_total_value = ""  # TODO: ترجمة عربية
   - column_user = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - datetime_format = ""  # TODO: ترجمة عربية
   - error_adjustment_date = ""  # TODO: ترجمة عربية
   - error_adjustment_items_required = ""  # TODO: ترجمة عربية
   - error_adjustment_name = ""  # TODO: ترجمة عربية
   - error_adjustment_not_found = ""  # TODO: ترجمة عربية
   - error_advanced_permission = ""  # TODO: ترجمة عربية
   - error_approval_permission = ""  # TODO: ترجمة عربية
   - error_branch_required = ""  # TODO: ترجمة عربية
   - error_cannot_approve = ""  # TODO: ترجمة عربية
   - error_exception = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_product = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer = ""  # TODO: ترجمة عربية
   - error_insufficient_stock_for_transfer_item = ""  # TODO: ترجمة عربية
   - error_invalid_item = ""  # TODO: ترجمة عربية
   - error_items_required = ""  # TODO: ترجمة عربية
   - error_movement_failed_for_product = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_product_required = ""  # TODO: ترجمة عربية
   - error_quantity_must_be_positive = ""  # TODO: ترجمة عربية
   - error_quantity_required = ""  # TODO: ترجمة عربية
   - error_reject_reason = ""  # TODO: ترجمة عربية
   - error_same_branch = ""  # TODO: ترجمة عربية
   - error_transfer_already_completed = ""  # TODO: ترجمة عربية
   - error_transfer_no_items = ""  # TODO: ترجمة عربية
   - error_transfer_not_found = ""  # TODO: ترجمة عربية
   - error_unit_cost_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - success_approve = ""  # TODO: ترجمة عربية
   - success_reject = ""  # TODO: ترجمة عربية
   - text_add = ""  # TODO: ترجمة عربية
   - text_adjustment_type_counting = ""  # TODO: ترجمة عربية
   - text_adjustment_type_damage = ""  # TODO: ترجمة عربية
   - text_adjustment_type_expiry = ""  # TODO: ترجمة عربية
   - text_adjustment_type_found = ""  # TODO: ترجمة عربية
   - text_adjustment_type_loss = ""  # TODO: ترجمة عربية
   - text_adjustment_type_manual = ""  # TODO: ترجمة عربية
   - text_adjustment_type_system = ""  # TODO: ترجمة عربية
   - text_all = ""  # TODO: ترجمة عربية
   - text_approved_success = ""  # TODO: ترجمة عربية
   - text_edit = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_no_reason = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_posted_success = ""  # TODO: ترجمة عربية
   - text_reason_category_correction = ""  # TODO: ترجمة عربية
   - text_reason_category_decrease = ""  # TODO: ترجمة عربية
   - text_reason_category_increase = ""  # TODO: ترجمة عربية
   - text_reason_category_transfer = ""  # TODO: ترجمة عربية
   - text_rejected_success = ""  # TODO: ترجمة عربية
   - text_status_approved = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_draft = ""  # TODO: ترجمة عربية
   - text_status_pending_approval = ""  # TODO: ترجمة عربية
   - text_status_posted = ""  # TODO: ترجمة عربية
   - text_status_rejected = ""  # TODO: ترجمة عربية
   - text_submitted_success = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_adjustment_date = ""  # TODO: English translation
   - column_adjustment_name = ""  # TODO: English translation
   - column_adjustment_number = ""  # TODO: English translation
   - column_adjustment_type = ""  # TODO: English translation
   - column_branch = ""  # TODO: English translation
   - column_notes = ""  # TODO: English translation
   - column_reason = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_total_items = ""  # TODO: English translation
   - column_total_value = ""  # TODO: English translation
   - column_user = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - datetime_format = ""  # TODO: English translation
   - error_adjustment_date = ""  # TODO: English translation
   - error_adjustment_items_required = ""  # TODO: English translation
   - error_adjustment_name = ""  # TODO: English translation
   - error_adjustment_not_found = ""  # TODO: English translation
   - error_advanced_permission = ""  # TODO: English translation
   - error_approval_permission = ""  # TODO: English translation
   - error_branch_required = ""  # TODO: English translation
   - error_cannot_approve = ""  # TODO: English translation
   - error_exception = ""  # TODO: English translation
   - error_insufficient_stock_for_product = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer = ""  # TODO: English translation
   - error_insufficient_stock_for_transfer_item = ""  # TODO: English translation
   - error_invalid_item = ""  # TODO: English translation
   - error_items_required = ""  # TODO: English translation
   - error_movement_failed_for_product = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_product_required = ""  # TODO: English translation
   - error_quantity_must_be_positive = ""  # TODO: English translation
   - error_quantity_required = ""  # TODO: English translation
   - error_reject_reason = ""  # TODO: English translation
   - error_same_branch = ""  # TODO: English translation
   - error_transfer_already_completed = ""  # TODO: English translation
   - error_transfer_no_items = ""  # TODO: English translation
   - error_transfer_not_found = ""  # TODO: English translation
   - error_unit_cost_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - success_approve = ""  # TODO: English translation
   - success_reject = ""  # TODO: English translation
   - text_add = ""  # TODO: English translation
   - text_adjustment_type_counting = ""  # TODO: English translation
   - text_adjustment_type_damage = ""  # TODO: English translation
   - text_adjustment_type_expiry = ""  # TODO: English translation
   - text_adjustment_type_found = ""  # TODO: English translation
   - text_adjustment_type_loss = ""  # TODO: English translation
   - text_adjustment_type_manual = ""  # TODO: English translation
   - text_adjustment_type_system = ""  # TODO: English translation
   - text_all = ""  # TODO: English translation
   - text_approved_success = ""  # TODO: English translation
   - text_edit = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_no_reason = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_posted_success = ""  # TODO: English translation
   - text_reason_category_correction = ""  # TODO: English translation
   - text_reason_category_decrease = ""  # TODO: English translation
   - text_reason_category_increase = ""  # TODO: English translation
   - text_reason_category_transfer = ""  # TODO: English translation
   - text_rejected_success = ""  # TODO: English translation
   - text_status_approved = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_draft = ""  # TODO: English translation
   - text_status_pending_approval = ""  # TODO: English translation
   - text_status_posted = ""  # TODO: English translation
   - text_status_rejected = ""  # TODO: English translation
   - text_submitted_success = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
