📄 Route: notification/automation
📂 Controller: controller\notification\automation.php
🧱 Models used (8):
   ✅ notification/automation (13 functions)
   ✅ catalog/product (128 functions)
   ❌ inventory/stock (0 functions)
   ✅ core/central_service_manager (60 functions)
   ✅ communication/unified_notification (16 functions)
   ✅ purchase/requisition (17 functions)
   ❌ workflow/automation (0 functions)
   ❌ ai/recommendation_engine (0 functions)
🎨 Twig templates (1):
   ✅ view\template\notification\automation.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\notification\automation.php (144 variables)
🇬🇧 English Language Files (1):
   ❌ language\en-gb\notification\automation.php (0 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (95):
   - action
   - error_add
   - error_permission
   - error_transfer_already_completed
   - error_transfer_not_found
   - text_action_create_task
   - text_action_send_notification
   - text_action_send_sms
   - text_action_types
   - text_action_update_product
   - text_automation_rules
   - text_automation_stats
   - text_new_product_automation
   - text_new_product_automation_desc
   - text_price_change_automation_desc
   - text_test
   - text_test_failed
   - text_test_successful
   - text_trigger_types
   - text_trigger_weekly_check
   ... و 75 متغير آخر

❌ Missing in Arabic (89):
   - action
   - action_types
   - column_left
   - error_add
   - error_transfer_not_found
   - test
   - text_action_send_notification
   - text_action_update_product
   - text_batch_expiry_automation_desc
   - text_logs
   - text_new_product_automation_desc
   - text_price_change_automation_desc
   - text_reorder_point_automation
   - text_test
   - text_trigger_types
   ... و 74 متغير آخر

❌ Missing in English (95):
   - action
   - error_add
   - error_permission
   - error_transfer_already_completed
   - error_transfer_not_found
   - text_action_create_task
   - text_action_send_notification
   - text_action_send_sms
   - text_action_types
   - text_action_update_product
   - text_automation_rules
   - text_automation_stats
   - text_new_product_automation
   - text_price_change_automation_desc
   - text_test
   ... و 80 متغير آخر

🗄️ Database Tables Used (7):
   ❌ filter
   ❌ main
   ❌ statements
   ❌ status
   ❌ stock
   ❌ template
   ❌ workflow

🚨 Issues Found (4):
   🟡 MISSING_ARABIC_VARIABLES: 89 items
      - error_add
      - action
      - error_transfer_not_found
      - text_price_change_automation_desc
      - text_test
   🟡 MISSING_ENGLISH_VARIABLES: 95 items
      - error_add
      - text_new_product_automation
      - action
      - text_action_types
      - text_action_create_task
   🔴 INVALID_DATABASE_TABLES: 7 items
      - workflow
      - filter
      - template
      - stock
      - status
   🟢 MISSING_MODEL_FILES: 3 items
      - inventory/stock
      - workflow/automation
      - ai/recommendation_engine

💡 Recommendations (3):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 89 متغير عربي و 95 متغير إنجليزي
   🔴 مراجعة أسماء الجداول المستخدمة
      تصحيح 7 جدول غير موجود
   🟢 إنشاء ملفات الموديل المفقودة
      إنشاء 3 ملف موديل

📈 Screen Health Score: ❌ 60%
📅 Analysis Date: 2025-07-21 18:33:11
🔧 Total Issues: 4

❌ يحتاج إصلاح عاجل لعدة مشاكل.