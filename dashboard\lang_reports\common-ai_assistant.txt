📄 Route: common/ai_assistant
📂 Controller: controller\common\ai_assistant.php
🧱 Models used (1):
   - common/ai_assistant
🎨 Twig templates (1):
   - view\template\common\ai_assistant.twig
🈯 Arabic Language Files (1):
   - language\ar\common\ai_assistant.php
🇬🇧 English Language Files (1):
   - language\en-gb\common\ai_assistant.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_cancel
   - button_save
   - error_empty_query
   - error_invalid_request
   - text_access_customers
   - text_access_inventory
   - text_access_reports
   - text_access_sales
   - text_advanced_model
   - text_ai_assistant
   - text_ai_data_access
   - text_ai_error
   - text_ai_model
   - text_ai_preferences
   - text_ai_settings
   - text_ai_thinking
   - text_ai_welcome
   - text_ask_ai
   - text_auto_complete
   - text_clear_conversation
   - text_conversation_cleared
   - text_default_model
   - text_just_now
   - text_model_help
   - text_no_conversation
   - text_save_conversation
   - text_settings_saved
   - text_show_suggestions
   - text_suggestion_inventory
   - text_suggestion_reports
   - text_suggestion_sales
   - text_suggestions

❌ Missing in Arabic:
   - button_cancel
   - button_save
   - error_empty_query
   - error_invalid_request
   - text_access_customers
   - text_access_inventory
   - text_access_reports
   - text_access_sales
   - text_advanced_model
   - text_ai_assistant
   - text_ai_data_access
   - text_ai_error
   - text_ai_model
   - text_ai_preferences
   - text_ai_settings
   - text_ai_thinking
   - text_ai_welcome
   - text_ask_ai
   - text_auto_complete
   - text_clear_conversation
   - text_conversation_cleared
   - text_default_model
   - text_just_now
   - text_model_help
   - text_no_conversation
   - text_save_conversation
   - text_settings_saved
   - text_show_suggestions
   - text_suggestion_inventory
   - text_suggestion_reports
   - text_suggestion_sales
   - text_suggestions

❌ Missing in English:
   - button_cancel
   - button_save
   - error_empty_query
   - error_invalid_request
   - text_access_customers
   - text_access_inventory
   - text_access_reports
   - text_access_sales
   - text_advanced_model
   - text_ai_assistant
   - text_ai_data_access
   - text_ai_error
   - text_ai_model
   - text_ai_preferences
   - text_ai_settings
   - text_ai_thinking
   - text_ai_welcome
   - text_ask_ai
   - text_auto_complete
   - text_clear_conversation
   - text_conversation_cleared
   - text_default_model
   - text_just_now
   - text_model_help
   - text_no_conversation
   - text_save_conversation
   - text_settings_saved
   - text_show_suggestions
   - text_suggestion_inventory
   - text_suggestion_reports
   - text_suggestion_sales
   - text_suggestions

💡 Suggested Arabic Additions:
   - button_cancel = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - error_empty_query = ""  # TODO: ترجمة عربية
   - error_invalid_request = ""  # TODO: ترجمة عربية
   - text_access_customers = ""  # TODO: ترجمة عربية
   - text_access_inventory = ""  # TODO: ترجمة عربية
   - text_access_reports = ""  # TODO: ترجمة عربية
   - text_access_sales = ""  # TODO: ترجمة عربية
   - text_advanced_model = ""  # TODO: ترجمة عربية
   - text_ai_assistant = ""  # TODO: ترجمة عربية
   - text_ai_data_access = ""  # TODO: ترجمة عربية
   - text_ai_error = ""  # TODO: ترجمة عربية
   - text_ai_model = ""  # TODO: ترجمة عربية
   - text_ai_preferences = ""  # TODO: ترجمة عربية
   - text_ai_settings = ""  # TODO: ترجمة عربية
   - text_ai_thinking = ""  # TODO: ترجمة عربية
   - text_ai_welcome = ""  # TODO: ترجمة عربية
   - text_ask_ai = ""  # TODO: ترجمة عربية
   - text_auto_complete = ""  # TODO: ترجمة عربية
   - text_clear_conversation = ""  # TODO: ترجمة عربية
   - text_conversation_cleared = ""  # TODO: ترجمة عربية
   - text_default_model = ""  # TODO: ترجمة عربية
   - text_just_now = ""  # TODO: ترجمة عربية
   - text_model_help = ""  # TODO: ترجمة عربية
   - text_no_conversation = ""  # TODO: ترجمة عربية
   - text_save_conversation = ""  # TODO: ترجمة عربية
   - text_settings_saved = ""  # TODO: ترجمة عربية
   - text_show_suggestions = ""  # TODO: ترجمة عربية
   - text_suggestion_inventory = ""  # TODO: ترجمة عربية
   - text_suggestion_reports = ""  # TODO: ترجمة عربية
   - text_suggestion_sales = ""  # TODO: ترجمة عربية
   - text_suggestions = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_cancel = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - error_empty_query = ""  # TODO: English translation
   - error_invalid_request = ""  # TODO: English translation
   - text_access_customers = ""  # TODO: English translation
   - text_access_inventory = ""  # TODO: English translation
   - text_access_reports = ""  # TODO: English translation
   - text_access_sales = ""  # TODO: English translation
   - text_advanced_model = ""  # TODO: English translation
   - text_ai_assistant = ""  # TODO: English translation
   - text_ai_data_access = ""  # TODO: English translation
   - text_ai_error = ""  # TODO: English translation
   - text_ai_model = ""  # TODO: English translation
   - text_ai_preferences = ""  # TODO: English translation
   - text_ai_settings = ""  # TODO: English translation
   - text_ai_thinking = ""  # TODO: English translation
   - text_ai_welcome = ""  # TODO: English translation
   - text_ask_ai = ""  # TODO: English translation
   - text_auto_complete = ""  # TODO: English translation
   - text_clear_conversation = ""  # TODO: English translation
   - text_conversation_cleared = ""  # TODO: English translation
   - text_default_model = ""  # TODO: English translation
   - text_just_now = ""  # TODO: English translation
   - text_model_help = ""  # TODO: English translation
   - text_no_conversation = ""  # TODO: English translation
   - text_save_conversation = ""  # TODO: English translation
   - text_settings_saved = ""  # TODO: English translation
   - text_show_suggestions = ""  # TODO: English translation
   - text_suggestion_inventory = ""  # TODO: English translation
   - text_suggestion_reports = ""  # TODO: English translation
   - text_suggestion_sales = ""  # TODO: English translation
   - text_suggestions = ""  # TODO: English translation
