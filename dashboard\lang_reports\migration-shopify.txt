📄 Route: migration/shopify
📂 Controller: controller\migration\shopify.php
🧱 Models used (1):
   - migration/migration
🎨 Twig templates (1):
   - view\template\migration\shopify.twig
🈯 Arabic Language Files (1):
   - language\ar\migration\migration.php
🇬🇧 English Language Files (1):
   - language\en-gb\migration\migration.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - alert_backup
   - alert_required_fields
   - button_import
   - button_review
   - entry_batch_size
   - entry_delimiter
   - entry_encoding
   - entry_file
   - entry_mapping
   - entry_skip_rows
   - entry_source
   - error_connection
   - error_credentials
   - error_encoding
   - error_file
   - error_invalid_source
   - error_mapping
   - error_permission
   - error_processing
   - error_required
   - text_error
   - text_home
   - text_migration
   - text_records_imported
   - text_shopify_migration
   - text_success

❌ Missing in Arabic:
   - alert_backup
   - alert_required_fields
   - button_import
   - button_review
   - entry_batch_size
   - entry_delimiter
   - entry_encoding
   - entry_file
   - entry_mapping
   - entry_skip_rows
   - entry_source
   - error_connection
   - error_credentials
   - error_encoding
   - error_file
   - error_invalid_source
   - error_mapping
   - error_permission
   - error_processing
   - error_required
   - text_error
   - text_home
   - text_migration
   - text_records_imported
   - text_shopify_migration
   - text_success

❌ Missing in English:
   - alert_backup
   - alert_required_fields
   - button_import
   - button_review
   - entry_batch_size
   - entry_delimiter
   - entry_encoding
   - entry_file
   - entry_mapping
   - entry_skip_rows
   - entry_source
   - error_connection
   - error_credentials
   - error_encoding
   - error_file
   - error_invalid_source
   - error_mapping
   - error_permission
   - error_processing
   - error_required
   - text_error
   - text_home
   - text_migration
   - text_records_imported
   - text_shopify_migration
   - text_success

💡 Suggested Arabic Additions:
   - alert_backup = ""  # TODO: ترجمة عربية
   - alert_required_fields = ""  # TODO: ترجمة عربية
   - button_import = ""  # TODO: ترجمة عربية
   - button_review = ""  # TODO: ترجمة عربية
   - entry_batch_size = ""  # TODO: ترجمة عربية
   - entry_delimiter = ""  # TODO: ترجمة عربية
   - entry_encoding = ""  # TODO: ترجمة عربية
   - entry_file = ""  # TODO: ترجمة عربية
   - entry_mapping = ""  # TODO: ترجمة عربية
   - entry_skip_rows = ""  # TODO: ترجمة عربية
   - entry_source = ""  # TODO: ترجمة عربية
   - error_connection = ""  # TODO: ترجمة عربية
   - error_credentials = ""  # TODO: ترجمة عربية
   - error_encoding = ""  # TODO: ترجمة عربية
   - error_file = ""  # TODO: ترجمة عربية
   - error_invalid_source = ""  # TODO: ترجمة عربية
   - error_mapping = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_processing = ""  # TODO: ترجمة عربية
   - error_required = ""  # TODO: ترجمة عربية
   - text_error = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_migration = ""  # TODO: ترجمة عربية
   - text_records_imported = ""  # TODO: ترجمة عربية
   - text_shopify_migration = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - alert_backup = ""  # TODO: English translation
   - alert_required_fields = ""  # TODO: English translation
   - button_import = ""  # TODO: English translation
   - button_review = ""  # TODO: English translation
   - entry_batch_size = ""  # TODO: English translation
   - entry_delimiter = ""  # TODO: English translation
   - entry_encoding = ""  # TODO: English translation
   - entry_file = ""  # TODO: English translation
   - entry_mapping = ""  # TODO: English translation
   - entry_skip_rows = ""  # TODO: English translation
   - entry_source = ""  # TODO: English translation
   - error_connection = ""  # TODO: English translation
   - error_credentials = ""  # TODO: English translation
   - error_encoding = ""  # TODO: English translation
   - error_file = ""  # TODO: English translation
   - error_invalid_source = ""  # TODO: English translation
   - error_mapping = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_processing = ""  # TODO: English translation
   - error_required = ""  # TODO: English translation
   - text_error = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_migration = ""  # TODO: English translation
   - text_records_imported = ""  # TODO: English translation
   - text_shopify_migration = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
