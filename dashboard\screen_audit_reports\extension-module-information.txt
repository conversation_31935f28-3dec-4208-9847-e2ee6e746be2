📄 Route: extension/module/information
📂 Controller: controller\extension\module\information.php
🧱 Models used (1):
   ✅ setting/setting (5 functions)
🎨 Twig templates (1):
   ✅ view\template\extension\module\information.twig
🈯 Arabic Language Files (1):
   ✅ language\ar\extension\module\information.php (6 variables)
🇬🇧 English Language Files (1):
   ✅ language\en-gb\extension\module\information.php (6 variables)
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables (17):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - entry_status
   - error_permission
   - error_warning
   - footer
   - header
   - heading_title
   - text_disabled
   - text_edit
   - text_enabled
   - text_extension
   - text_home
   - text_success

❌ Missing in Arabic (11):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home

❌ Missing in English (11):
   - action
   - button_cancel
   - button_save
   - cancel
   - column_left
   - error_warning
   - footer
   - header
   - text_disabled
   - text_enabled
   - text_home

🚨 Issues Found (2):
   🟡 MISSING_ARABIC_VARIABLES: 11 items
      - button_save
      - error_warning
      - column_left
      - text_home
      - action
   🟡 MISSING_ENGLISH_VARIABLES: 11 items
      - button_save
      - error_warning
      - column_left
      - text_home
      - action

💡 Recommendations (1):
   🟡 إضافة المتغيرات المفقودة إلى ملفات اللغة
      إضافة 11 متغير عربي و 11 متغير إنجليزي

📈 Screen Health Score: ⚠️ 80%
📅 Analysis Date: 2025-07-21 18:33:23
🔧 Total Issues: 2

⚠️ جيد، لكن يحتاج بعض التحسينات.