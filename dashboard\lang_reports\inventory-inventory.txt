📄 Route: inventory/inventory
📂 Controller: controller\inventory\inventory.php
🧱 Models used (2):
   - core/central_service_manager
   - inventory/inventory
🎨 Twig templates (1):
   - view\template\inventory\inventory.twig
🈯 Arabic Language Files (1):
   - language\ar\inventory\inventory.php
🇬🇧 English Language Files (1):
   - language\en-gb\inventory\inventory.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - column_average_cost
   - column_branch
   - column_product
   - column_quantity
   - column_total_value
   - column_unit
   - heading_title
   - text_home

❌ Missing in Arabic:
   - column_average_cost
   - column_branch
   - column_product
   - column_quantity
   - column_total_value
   - column_unit
   - heading_title
   - text_home

❌ Missing in English:
   - column_average_cost
   - column_branch
   - column_product
   - column_quantity
   - column_total_value
   - column_unit
   - heading_title
   - text_home

💡 Suggested Arabic Additions:
   - column_average_cost = ""  # TODO: ترجمة عربية
   - column_branch = ""  # TODO: ترجمة عربية
   - column_product = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_total_value = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - column_average_cost = ""  # TODO: English translation
   - column_branch = ""  # TODO: English translation
   - column_product = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_total_value = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
