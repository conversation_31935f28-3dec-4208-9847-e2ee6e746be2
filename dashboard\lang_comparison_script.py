#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from pathlib import Path
from collections import defaultdict

class LanguageRouteReporter:
    def __init__(self, dashboard_path):
        self.dashboard_path = Path(dashboard_path)
        self.controller_path = self.dashboard_path / 'controller'
        self.view_path = self.dashboard_path / 'view' / 'template'
        self.lang_path = self.dashboard_path / 'language'
        self.output_path = self.dashboard_path / 'lang_reports'
        self.output_path.mkdir(exist_ok=True)

    def run(self):
        controllers = list(self.controller_path.rglob('*.php'))
        for ctrl_file in controllers:
            route = self._route_from_path(ctrl_file)
            models = self._extract_models(ctrl_file)
            twigs = self._find_related_twig_templates(route)
            langs = self._extract_languages(ctrl_file)
            
            # استخراج مفاتيح اللغة المستخدمة في MVC
            used_keys = set()
            used_keys |= self._extract_keys_from_php(ctrl_file, patterns=[
                r"\$this->language->get\(['\"]([^'\"]+)['\"]\)",
                r"__\(['\"]([^'\"]+)['\"]\)",
                r"\$lang\[['\"]([^'\"]+)['\"]\]"
            ])
            # موديلات مرتبطة: ابحث داخل الموديل إذا لزم
            for mdl in models:
                mdl_path = self.controller_path.parent / 'model' / (mdl + '.php')
                if mdl_path.exists():
                    used_keys |= self._extract_keys_from_php(mdl_path)
            # قوالب twig
            for t in twigs:
                twig_path = self.dashboard_path / t
                used_keys |= self._extract_keys_from_twig(twig_path)

            # ملفات اللغة المرتبطة
            ar_files = self._find_lang_files(langs, 'ar')
            en_files = self._find_lang_files(langs, 'en-gb')
            ar_vars = self._load_language_vars(ar_files)
            en_vars = self._load_language_vars(en_files)

            self._write_report(route, ctrl_file, models, twigs,
                               ar_files, en_files,
                               used_keys, ar_vars, en_vars)

        print(f"\n✅ تم إنشاء التقارير في: {self.output_path.resolve()}")

    def _route_from_path(self, path):
        rel = path.relative_to(self.controller_path)
        return str(rel.with_suffix('')).replace(os.sep, '/')

    def _extract_models(self, path):
        txt = path.read_text(encoding='utf-8', errors='ignore')
        return sorted(set(re.findall(r'\$this->load->model\(["\']([^"\']+)["\']\)', txt)))

    def _extract_languages(self, path):
        txt = path.read_text(encoding='utf-8', errors='ignore')
        return sorted(set(re.findall(r'\$this->load->language\(["\']([^"\']+)["\']\)', txt)))

    def _find_related_twig_templates(self, route):
        twigs = set()
        base = self.view_path / route
        single = self.view_path / (route + '.twig')
        if single.exists():
            twigs.add(str(single.relative_to(self.dashboard_path)))
        if base.exists() and base.is_dir():
            for f in base.glob('*.twig'):
                twigs.add(str(f.relative_to(self.dashboard_path)))
        return sorted(twigs)

    def _find_lang_files(self, routes, lang_code):
        files = []
        for r in routes:
            p = self.lang_path / lang_code / (r + '.php')
            if p.exists():
                files.append(str(p.relative_to(self.dashboard_path)))
        return sorted(files)

    def _load_language_vars(self, files):
        vars_map = set()
        pattern = r"['\"]([^'\"]+)['\"]\s*=>"
        for f in files:
            path = self.dashboard_path / f
            text = path.read_text(encoding='utf-8', errors='ignore')
            # إزالة التعليقات
            text = re.sub(r'/\*.*?\*/', '', text, flags=re.DOTALL)
            text = re.sub(r'//.*', '', text)
            for m in re.findall(pattern, text):
                vars_map.add(m)
        return vars_map

    def _extract_keys_from_php(self, path, patterns=None):
        text = path.read_text(encoding='utf-8', errors='ignore')
        if not patterns:
            patterns = [
                r"\$this->language->get\(['\"]([^'\"]+)['\"]\)",
                r"__\(['\"]([^'\"]+)['\"]\)",
                r"\$lang\[['\"]([^'\"]+)['\"]\]"
            ]
        keys = set()
        for pat in patterns:
            for m in re.findall(pat, text):
                keys.add(m)
        return keys

    def _extract_keys_from_twig(self, path):
        text = path.read_text(encoding='utf-8', errors='ignore')
        patterns = [
            r"\{\{\s*'([^']+)'\s*\|\s*trans\s*\}\}",
            r"\{\{\s*\"([^\"]+)\"\s*\|\s*trans\s*\}\}",
            r"trans\(['\"]([^'\"]+)['\"]\)"
        ]
        keys = set()
        for pat in patterns:
            for m in re.findall(pat, text):
                keys.add(m)
        return keys

    def _write_report(self, route, ctrl, models, twigs,
                      ar_files, en_files,
                      used_keys, ar_vars, en_vars):
        fn = f"{route.replace('/', '-')}.txt"
        rpt = self.output_path / fn
        with rpt.open('w', encoding='utf-8') as f:
            # إحصائيات
            f.write(f"📄 Route: {route}\n")
            f.write(f"📂 Controller: {ctrl.relative_to(self.dashboard_path)}\n")
            f.write(f"🧱 Models used ({len(models)}):\n")
            for m in models:
                f.write(f"   - {m}\n")
            f.write(f"🎨 Twig templates ({len(twigs)}):\n")
            for t in twigs:
                f.write(f"   - {t}\n")
            f.write(f"🈯 Arabic Language Files ({len(ar_files)}):\n")
            for a in ar_files:
                f.write(f"   - {a}\n")
            f.write(f"🇬🇧 English Language Files ({len(en_files)}):\n")
            for e in en_files:
                f.write(f"   - {e}\n")
            f.write("-" * 60 + "\n")

            # تحليل المتغيرات
            f.write("📊 Language Usage Analysis:\n\n")
            # Used
            f.write("✅ Used Variables:\n")
            for k in sorted(used_keys):
                f.write(f"   - {k}\n")
            f.write("\n")

            # Missing
            missing_ar = used_keys - ar_vars
            missing_en = used_keys - en_vars
            if missing_ar:
                f.write("❌ Missing in Arabic:\n")
                for k in sorted(missing_ar):
                    f.write(f"   - {k}\n")
                f.write("\n")
            if missing_en:
                f.write("❌ Missing in English:\n")
                for k in sorted(missing_en):
                    f.write(f"   - {k}\n")
                f.write("\n")

            # Unused
            unused_ar = ar_vars - used_keys
            unused_en = en_vars - used_keys
            if unused_ar:
                f.write("🧹 Unused in Arabic:\n")
                for k in sorted(unused_ar):
                    f.write(f"   - {k}\n")
                f.write("\n")
            if unused_en:
                f.write("🧹 Unused in English:\n")
                for k in sorted(unused_en):
                    f.write(f"   - {k}\n")
                f.write("\n")

            # Suggestions
            f.write("💡 Suggested Arabic Additions:\n")
            for k in sorted(missing_ar):
                f.write(f"   - {k} = \"\"  # TODO: ترجمة عربية\n")
            f.write("\n")
            f.write("💡 Suggested English Additions:\n")
            for k in sorted(missing_en):
                f.write(f"   - {k} = \"\"  # TODO: English translation\n")

# تشغيل مباشر
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Generate per-route language reports')
    parser.add_argument('--path', default='.', help='Path to dashboard folder')
    args = parser.parse_args()

    if not os.path.isdir(args.path):
        print("❌ المسار غير موجود أو ليس مجلدًا.")
    else:
        reporter = LanguageRouteReporter(args.path)
        reporter.run()
