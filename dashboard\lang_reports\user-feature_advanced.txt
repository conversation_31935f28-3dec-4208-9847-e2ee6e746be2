📄 Route: user/feature_advanced
📂 Controller: controller\user\feature_advanced.php
🧱 Models used (2):
   - user/feature_advanced
   - user/user_group
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\user\feature_advanced.php
🇬🇧 English Language Files (1):
   - language\en-gb\user\feature_advanced.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_name
   - error_permission
   - heading_title
   - text_success

❌ Missing in Arabic:
   - error_name
   - error_permission
   - heading_title
   - text_success

❌ Missing in English:
   - error_name
   - error_permission
   - heading_title
   - text_success

💡 Suggested Arabic Additions:
   - error_name = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_success = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_name = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_success = ""  # TODO: English translation
