📄 Route: marketing/analytics
📂 Controller: controller\marketing\analytics.php
🧱 Models used (1):
   - marketing/analytics
🎨 Twig templates (1):
   - view\template\marketing\analytics.twig
🈯 Arabic Language Files (1):
   - language\ar\marketing\analytics.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - error_campaign_id_required
   - error_campaign_not_found
   - error_report_generation
   - error_report_type_required
   - heading_title
   - text_home
   - text_report_generated

❌ Missing in Arabic:
   - error_campaign_id_required
   - error_campaign_not_found
   - error_report_generation
   - error_report_type_required
   - heading_title
   - text_home
   - text_report_generated

❌ Missing in English:
   - error_campaign_id_required
   - error_campaign_not_found
   - error_report_generation
   - error_report_type_required
   - heading_title
   - text_home
   - text_report_generated

💡 Suggested Arabic Additions:
   - error_campaign_id_required = ""  # TODO: ترجمة عربية
   - error_campaign_not_found = ""  # TODO: ترجمة عربية
   - error_report_generation = ""  # TODO: ترجمة عربية
   - error_report_type_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_report_generated = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - error_campaign_id_required = ""  # TODO: English translation
   - error_campaign_not_found = ""  # TODO: English translation
   - error_report_generation = ""  # TODO: English translation
   - error_report_type_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_report_generated = ""  # TODO: English translation
