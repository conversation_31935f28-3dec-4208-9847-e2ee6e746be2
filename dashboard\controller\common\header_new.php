<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP SYSTEM - Professional Header Controller v5.0 (Enterprise Grade)
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * @version 5.0.0 Enterprise Build
 * <AUTHOR> Development Team
 * @copyright 2025 AYM ERP Systems
 * @description Professional header controller with zero direct text strings.
 * All text content is managed through language files for proper internationalization.
 * 
 * Key Features:
 * - Unified notification system
 * - Advanced messaging system
 * - Workflow approvals integration
 * - Inventory alerts
 * - Task management
 * - System performance indicators
 * - Full RTL/LTR support
 * ═══════════════════════════════════════════════════════════════════════════════
 */

class ControllerCommonHeader extends Controller {
    /**
     * Main index method - builds the header
     */
    public function index() {
        // Get page title
        $data['title'] = $this->document->getTitle();
        
        // Set base URL
        if ($this->request->server['HTTPS']) {
            $data['base'] = HTTPS_SERVER;
        } else {
            $data['base'] = HTTP_SERVER;
        }
        
        // Set catalog URL
        if ($this->request->server['HTTPS']) {
            $server = HTTPS_CATALOG;
        } else {
            $server = HTTP_CATALOG;
        }
        
        // Add favicon
        if (is_file(DIR_IMAGE . $this->config->get('config_icon'))) {
            $this->document->addLink($server . 'image/' . $this->config->get('config_icon'), 'icon');
        }
        
        // Set meta data
        $data['description'] = $this->document->getDescription();
        $data['keywords'] = $this->document->getKeywords();
        $data['links'] = $this->document->getLinks();
        $data['styles'] = $this->document->getStyles();
        $data['scripts'] = $this->document->getScripts();
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');
        
        // Load language file
        $this->load->language('common/header');
        
        // Set logged in user info
        $data['text_logged'] = sprintf($this->language->get('text_logged'), $this->user->getUserName());
        
        // Check if user is logged in
        if (!isset($this->request->get['user_token']) || 
            !isset($this->session->data['user_token']) || 
            ($this->request->get['user_token'] != $this->session->data['user_token'])) {
            
            $data['logged'] = '';
            $data['home'] = $this->url->link('common/login', '', true);
        } else {
            $data['logged'] = true;
            
            // Set navigation links
            $data['home'] = $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true);
            $data['logout'] = $this->url->link('common/logout', 'user_token=' . $this->session->data['user_token'], true);
            $data['profile'] = $this->url->link('common/profile', 'user_token=' . $this->session->data['user_token'], true);
            
            // Load required models
            $this->load->model('user/user');
            $this->load->model('tool/image');
            
            // Get user info
            $user_info = $this->model_user_user->getUser($this->user->getId());
            
            if ($user_info) {
                $data['firstname'] = $user_info['firstname'];
                $data['lastname'] = $user_info['lastname'];
                $data['username'] = $user_info['username'];
                $data['user_group'] = $user_info['user_group'];
                
                // Set user image
                if (is_file(DIR_IMAGE . $user_info['image'])) {
                    $data['image'] = $server . 'image/' . $user_info['image'];
                } else {
                    $data['image'] = $server . 'image/profile-placeholder.png';
                }
                
                // Get notifications data
                $data['notifications'] = $this->getUnifiedNotifications($this->user->getId());
                
                // Set notification routes
                $data['notification_mark_read_url'] = $this->url->link('common/header/markNotificationRead', 'user_token=' . $this->session->data['user_token'], true);
                $data['notification_mark_all_read_url'] = $this->url->link('common/header/markAllNotificationsRead', 'user_token=' . $this->session->data['user_token'], true);
                $data['notification_clear_read_url'] = $this->url->link('common/header/clearReadNotifications', 'user_token=' . $this->session->data['user_token'], true);
                $data['notification_tab_data_url'] = $this->url->link('common/header/getTabData', 'user_token=' . $this->session->data['user_token'], true);
                $data['notification_refresh_url'] = $this->url->link('common/header/getUnifiedNotificationsJson', 'user_token=' . $this->session->data['user_token'], true);
                
                // Set KPI routes
                $data['kpi_data_url'] = $this->url->link('common/header/getKpisData', 'user_token=' . $this->session->data['user_token'], true);
                $data['kpi_summary_url'] = $this->url->link('common/header/getKpiSummary', 'user_token=' . $this->session->data['user_token'], true);
                
                // Set system indicators
                $data['system_indicators'] = $this->getSystemIndicators();
            } else {
                $data['firstname'] = '';
                $data['lastname'] = '';
                $data['username'] = '';
                $data['user_group'] = '';
                $data['image'] = '';
            }
            
            // Get alerts
            $data['alerts'] = [];
            
            // Security alerts
            if ($this->user->hasPermission('access', 'common/security')) {
                $this->load->model('common/security');
                
                $security_alerts = $this->model_common_security->getSecurityAlerts();
                
                if ($security_alerts) {
                    $data['alerts'][] = [
                        'title' => $this->language->get('text_security'),
                        'href' => $this->url->link('common/security', 'user_token=' . $this->session->data['user_token'], true),
                        'total' => count($security_alerts)
                    ];
                }
            }
        }
        
        // Return view
        return $this->load->view('common/header', $data);
    }
    
    /**
     * Get unified notifications for header
     */
    private function getUnifiedNotifications($user_id) {
        $result = [
            'notifications' => [],
            'total_count' => 0,
            'unread_count' => 0,
            'stats' => [
                'priority' => [
                    'high' => 0,
                    'normal' => 0,
                    'low' => 0
                ],
                'type' => [
                    'system' => 0,
                    'message' => 0,
                    'approval' => 0,
                    'inventory' => 0,
                    'expiry' => 0,
                    'task' => 0,
                    'other' => 0
                ]
            ]
        ];
        
        try {
            // Load central services
            $this->load->model('core/central_service_manager');
            
            // Get system notifications
            $notifications = $this->getSystemNotifications($user_id);
            
            // Get unread messages
            $messages = $this->getUnreadMessages($user_id);
            
            // Get pending approvals
            $approvals = $this->getPendingApprovals($user_id);
            
            // Get inventory alerts
            $inventory_alerts = $this->getInventoryAlerts($user_id);
            
            // Get expiry alerts
            $expiry_alerts = $this->getExpiryAlerts($user_id);
            
            // Get task alerts
            $task_alerts = $this->getTaskAlerts($user_id);
            
            // Merge all notifications
            $all_notifications = array_merge(
                $notifications,
                $messages,
                $approvals,
                $inventory_alerts,
                $expiry_alerts,
                $task_alerts
            );
            
            // Sort by priority and time
            usort($all_notifications, function($a, $b) {
                // First sort by priority (high, normal, low)
                $priority_order = ['high' => 1, 'normal' => 2, 'low' => 3];
                $priority_a = $priority_order[$a['priority'] ?? 'normal'];
                $priority_b = $priority_order[$b['priority'] ?? 'normal'];
                
                if ($priority_a !== $priority_b) {
                    return $priority_a - $priority_b;
                }
                
                // Then sort by date (newest first)
                return strtotime($b['date_added'] ?? 'now') - strtotime($a['date_added'] ?? 'now');
            });
            
            // Get total count
            $total_count = count($all_notifications);
            
            // Take first 20 notifications for display
            $display_notifications = array_slice($all_notifications, 0, 20);
            
            // Get notification stats
            $stats = $this->getNotificationStats($all_notifications);
            
            // Set result
            $result = [
                'notifications' => $display_notifications,
                'total_count' => $total_count,
                'unread_count' => $stats['unread_count'],
                'stats' => $stats
            ];
        } catch (Exception $e) {
            // Log error
            if (isset($this->model_core_central_service_manager)) {
                $this->model_core_central_service_manager->logError(
                    'header', 
                    'Error getting unified notifications: ' . $e->getMessage()
                );
            }
        }
        
        return $result;
    }

    /**
     * Get system notifications
     */
    private function getSystemNotifications($user_id) {
        $notifications = [];

        try {
            // Use central service manager if available
            if (isset($this->model_core_central_service_manager)) {
                $notifications = $this->model_core_central_service_manager->getNotifications($user_id, 'system', 10);
            } else {
                // Fallback - load model directly
                $this->load->model('communication/unified_notification');
                $notifications = $this->model_communication_unified_notification->getSystemNotifications($user_id, 10);
            }
        } catch (Exception $e) {
            // Silent fail - return empty array
            $notifications = [];
        }

        return $notifications;
    }

    /**
     * Get unread messages
     */
    private function getUnreadMessages($user_id) {
        $messages = [];

        try {
            $this->load->model('communication/message');
            $messages = $this->model_communication_message->getUnreadMessages($user_id, 10);
        } catch (Exception $e) {
            $messages = [];
        }

        return $messages;
    }

    /**
     * Get pending approvals
     */
    private function getPendingApprovals($user_id) {
        $approvals = [];

        try {
            $this->load->model('workflow/approval');
            $approvals = $this->model_workflow_approval->getPendingApprovals($user_id, 10);
        } catch (Exception $e) {
            $approvals = [];
        }

        return $approvals;
    }

    /**
     * Get inventory alerts
     */
    private function getInventoryAlerts($user_id) {
        $alerts = [];

        // Check inventory access permission
        if (!$this->user->hasPermission('access', 'inventory/product')) {
            return $alerts;
        }

        try {
            $this->load->model('inventory/product');

            // Get low stock products
            $low_stock_products = $this->model_inventory_product->getLowStockProducts(10);

            foreach ($low_stock_products as $product) {
                $alerts[] = [
                    'type' => 'inventory',
                    'priority' => 'high',
                    'title' => $this->language->get('text_low_stock_alert'),
                    'message' => sprintf($this->language->get('text_product_low_stock'), $product['name'], $product['quantity']),
                    'date_added' => $product['date_modified'] ?? date('Y-m-d H:i:s'),
                    'icon' => $this->getNotificationIcon('inventory'),
                    'color' => $this->getNotificationColor('high'),
                    'href' => $this->url->link('inventory/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $product['product_id'], true)
                ];
            }
        } catch (Exception $e) {
            $alerts = [];
        }

        return $alerts;
    }

    /**
     * Get expiry alerts
     */
    private function getExpiryAlerts($user_id) {
        $alerts = [];

        // Check inventory access permission
        if (!$this->user->hasPermission('access', 'inventory/product')) {
            return $alerts;
        }

        try {
            $this->load->model('inventory/product');

            // Get products expiring within 30 days
            $expiring_products = $this->model_inventory_product->getExpiringProducts(30, 10);

            foreach ($expiring_products as $product) {
                $alerts[] = [
                    'type' => 'expiry',
                    'priority' => 'normal',
                    'title' => $this->language->get('text_expiry_alert'),
                    'message' => sprintf($this->language->get('text_product_expiring'), $product['name'], $product['expiry_date']),
                    'date_added' => $product['date_modified'] ?? date('Y-m-d H:i:s'),
                    'icon' => $this->getNotificationIcon('expiry'),
                    'color' => $this->getNotificationColor('normal'),
                    'href' => $this->url->link('inventory/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $product['product_id'], true)
                ];
            }
        } catch (Exception $e) {
            $alerts = [];
        }

        return $alerts;
    }

    /**
     * Get task alerts
     */
    private function getTaskAlerts($user_id) {
        $alerts = [];

        // Check task access permission
        if (!$this->user->hasPermission('access', 'workflow/task')) {
            return $alerts;
        }

        try {
            $this->load->model('workflow/task');

            // Get overdue and upcoming tasks
            $overdue_tasks = $this->model_workflow_task->getOverdueTasks($user_id, 5);
            $upcoming_tasks = $this->model_workflow_task->getUpcomingTasks($user_id, 5);

            // Overdue tasks
            foreach ($overdue_tasks as $task) {
                $alerts[] = [
                    'type' => 'task',
                    'priority' => 'high',
                    'title' => $this->language->get('text_overdue_task'),
                    'message' => sprintf($this->language->get('text_task_overdue'), $task['name'], $task['due_date']),
                    'date_added' => $task['date_modified'] ?? date('Y-m-d H:i:s'),
                    'icon' => $this->getNotificationIcon('task'),
                    'color' => $this->getNotificationColor('high'),
                    'href' => $this->url->link('workflow/task/edit', 'user_token=' . $this->session->data['user_token'] . '&task_id=' . $task['task_id'], true)
                ];
            }

            // Upcoming tasks
            foreach ($upcoming_tasks as $task) {
                $alerts[] = [
                    'type' => 'task',
                    'priority' => 'normal',
                    'title' => $this->language->get('text_upcoming_task'),
                    'message' => sprintf($this->language->get('text_task_upcoming'), $task['name'], $task['due_date']),
                    'date_added' => $task['date_modified'] ?? date('Y-m-d H:i:s'),
                    'icon' => $this->getNotificationIcon('task'),
                    'color' => $this->getNotificationColor('normal'),
                    'href' => $this->url->link('workflow/task/edit', 'user_token=' . $this->session->data['user_token'] . '&task_id=' . $task['task_id'], true)
                ];
            }
        } catch (Exception $e) {
            $alerts = [];
        }

        return $alerts;
    }

    /**
     * Get notification statistics
     */
    private function getNotificationStats($notifications) {
        $stats = [
            'unread_count' => 0,
            'priority' => [
                'high' => 0,
                'normal' => 0,
                'low' => 0
            ],
            'type' => [
                'system' => 0,
                'message' => 0,
                'approval' => 0,
                'inventory' => 0,
                'expiry' => 0,
                'task' => 0,
                'other' => 0
            ]
        ];

        foreach ($notifications as $notification) {
            // Count unread
            if (!isset($notification['read']) || !$notification['read']) {
                $stats['unread_count']++;
            }

            // Priority statistics
            $priority = $notification['priority'] ?? 'normal';
            if (isset($stats['priority'][$priority])) {
                $stats['priority'][$priority]++;
            }

            // Type statistics
            $type = $notification['type'] ?? 'other';
            if (isset($stats['type'][$type])) {
                $stats['type'][$type]++;
            }
        }

        return $stats;
    }

    /**
     * Get notification icon by type
     */
    private function getNotificationIcon($type) {
        $icons = [
            'system' => 'fa-cog',
            'message' => 'fa-envelope',
            'approval' => 'fa-check-circle',
            'inventory' => 'fa-cubes',
            'expiry' => 'fa-calendar-times',
            'task' => 'fa-tasks',
            'security' => 'fa-shield',
            'other' => 'fa-bell'
        ];

        return $icons[$type] ?? $icons['other'];
    }

    /**
     * Get notification color by priority
     */
    private function getNotificationColor($priority) {
        $colors = [
            'high' => 'danger',
            'normal' => 'info',
            'low' => 'secondary'
        ];

        return $colors[$priority] ?? $colors['normal'];
    }

    /**
     * Get system indicators
     */
    private function getSystemIndicators() {
        $indicators = [];

        try {
            // Performance indicator
            $indicators['performance'] = $this->getSystemPerformance();

            // Active users
            $indicators['active_users'] = $this->getActiveUsersCount();

            // Today's sales
            $indicators['today_sales'] = $this->getTodaySales();

            // Pending tasks
            $indicators['pending_tasks'] = $this->getPendingTasksCount();

        } catch (Exception $e) {
            // Return default values on error
            $indicators = [
                'performance' => 95,
                'active_users' => 0,
                'today_sales' => '0',
                'pending_tasks' => 0
            ];
        }

        return $indicators;
    }

    /**
     * Get system performance
     */
    private function getSystemPerformance() {
        // Calculate system performance based on various factors
        $performance = 95; // Default value

        // Can add more complex calculations here
        // such as memory usage, database speed, etc.

        return $performance;
    }

    /**
     * Get active users count
     */
    private function getActiveUsersCount() {
        try {
            $this->load->model('user/user');
            return $this->model_user_user->getActiveUsersCount();
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Get today's sales
     */
    private function getTodaySales() {
        try {
            $this->load->model('sale/order');
            $total = $this->model_sale_order->getTodayTotal();

            // Format number
            if ($total >= 1000000) {
                return number_format($total / 1000000, 1) . 'M';
            } elseif ($total >= 1000) {
                return number_format($total / 1000, 1) . 'K';
            } else {
                return number_format($total, 0);
            }
        } catch (Exception $e) {
            return '0';
        }
    }

    /**
     * Get pending tasks count
     */
    private function getPendingTasksCount() {
        try {
            $this->load->model('workflow/task');
            return $this->model_workflow_task->getPendingTasksCount($this->user->getId());
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * AJAX: Get unified notifications JSON
     */
    public function getUnifiedNotificationsJson() {
        $json = [];

        if ($this->user->isLogged()) {
            try {
                // Load central service manager
                $this->load->model('core/central_service_manager');

                // Get notifications data
                $notifications = $this->model_communication_unified_notification->getUserNotifications($this->user->getId(), 50);

                $json['success'] = true;
                $json['notifications'] = $notifications;

                // Get messages data
                $messages = $this->model_communication_message->getUserMessages($this->user->getId(), 20);
                $json['messages'] = $messages;

                // Get approvals data
                $approvals = $this->model_workflow_approval->getPendingApprovals($this->user->getId());
                $json['approvals'] = $approvals;

            } catch (Exception $e) {
                $json['error'] = $this->language->get('error_loading_header_data') . ': ' . $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_user_not_logged');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: Mark notification as read
     */
    public function markNotificationRead() {
        $json = [];

        if ($this->user->isLogged() && isset($this->request->post['notification_id'])) {
            try {
                $notification_id = (int)$this->request->post['notification_id'];
                $type = $this->request->post['type'] ?? 'notification';

                switch ($type) {
                    case 'notification':
                        $this->load->model('communication/unified_notification');
                        $this->model_communication_unified_notification->markAsRead($notification_id);
                        break;
                    case 'message':
                        $this->load->model('communication/message');
                        $this->model_communication_message->markAsRead($notification_id);
                        break;
                    case 'approval':
                        $this->load->model('workflow/approval');
                        $this->model_workflow_approval->markAsRead($notification_id);
                        break;
                    // Can add other types as needed
                }

                $json['success'] = true;
                $json['message'] = $this->language->get('text_notification_marked_read');

            } catch (Exception $e) {
                $json['error'] = $this->language->get('error_marking_notification') . ': ' . $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_request');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
}
