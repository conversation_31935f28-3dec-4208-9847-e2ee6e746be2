📄 Route: purchase/requisition
📂 Controller: controller\purchase\requisition.php
🧱 Models used (2):
   - purchase/quotation
   - purchase/requisition
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\purchase\requisition.php
🇬🇧 English Language Files (1):
   - language\en-gb\purchase\requisition.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_add_requisition
   - button_execute
   - button_save
   - button_view_quotations
   - column_action
   - column_branch
   - column_date_added
   - column_description
   - column_product
   - column_quantity
   - column_req_number
   - column_requisition_id
   - column_status
   - column_total
   - column_unit
   - column_unit_price
   - column_user_groups
   - date_format_short
   - error_invalid_status_transition
   - error_loading_form
   - error_permission
   - error_quotation_not_found
   - error_reason_required
   - heading_title
   - text_add_item
   - text_add_requisition
   - text_all_statuses
   - text_approve_selected
   - text_approved_requisitions
   - text_close
   - text_confirm_approve
   - text_confirm_delete
   - text_delete_selected
   - text_filter_date_end
   - text_filter_date_start
   - text_filter_status
   - text_history_status_change
   - text_pagination
   - text_pending_requisitions
   - text_prompt_reject_reason
   - text_reason
   - text_refresh_list
   - text_reject_selected
   - text_rejected_requisitions
   - text_req_number
   - text_requisition_list
   - text_select_action
   - text_select_product
   - text_status_approved
   - text_status_cancelled
   - text_status_converted
   - text_status_draft
   - text_status_pending
   - text_status_rejected
   - text_success_add_requisition
   - text_success_approve
   - text_success_delete_requisition
   - text_success_edit_requisition
   - text_success_reject
   - text_total_requisitions

❌ Missing in Arabic:
   - button_add_requisition
   - button_execute
   - button_save
   - button_view_quotations
   - column_action
   - column_branch
   - column_date_added
   - column_description
   - column_product
   - column_quantity
   - column_req_number
   - column_requisition_id
   - column_status
   - column_total
   - column_unit
   - column_unit_price
   - column_user_groups
   - date_format_short
   - error_invalid_status_transition
   - error_loading_form
   - error_permission
   - error_quotation_not_found
   - error_reason_required
   - heading_title
   - text_add_item
   - text_add_requisition
   - text_all_statuses
   - text_approve_selected
   - text_approved_requisitions
   - text_close
   - text_confirm_approve
   - text_confirm_delete
   - text_delete_selected
   - text_filter_date_end
   - text_filter_date_start
   - text_filter_status
   - text_history_status_change
   - text_pagination
   - text_pending_requisitions
   - text_prompt_reject_reason
   - text_reason
   - text_refresh_list
   - text_reject_selected
   - text_rejected_requisitions
   - text_req_number
   - text_requisition_list
   - text_select_action
   - text_select_product
   - text_status_approved
   - text_status_cancelled
   - text_status_converted
   - text_status_draft
   - text_status_pending
   - text_status_rejected
   - text_success_add_requisition
   - text_success_approve
   - text_success_delete_requisition
   - text_success_edit_requisition
   - text_success_reject
   - text_total_requisitions

❌ Missing in English:
   - button_add_requisition
   - button_execute
   - button_save
   - button_view_quotations
   - column_action
   - column_branch
   - column_date_added
   - column_description
   - column_product
   - column_quantity
   - column_req_number
   - column_requisition_id
   - column_status
   - column_total
   - column_unit
   - column_unit_price
   - column_user_groups
   - date_format_short
   - error_invalid_status_transition
   - error_loading_form
   - error_permission
   - error_quotation_not_found
   - error_reason_required
   - heading_title
   - text_add_item
   - text_add_requisition
   - text_all_statuses
   - text_approve_selected
   - text_approved_requisitions
   - text_close
   - text_confirm_approve
   - text_confirm_delete
   - text_delete_selected
   - text_filter_date_end
   - text_filter_date_start
   - text_filter_status
   - text_history_status_change
   - text_pagination
   - text_pending_requisitions
   - text_prompt_reject_reason
   - text_reason
   - text_refresh_list
   - text_reject_selected
   - text_rejected_requisitions
   - text_req_number
   - text_requisition_list
   - text_select_action
   - text_select_product
   - text_status_approved
   - text_status_cancelled
   - text_status_converted
   - text_status_draft
   - text_status_pending
   - text_status_rejected
   - text_success_add_requisition
   - text_success_approve
   - text_success_delete_requisition
   - text_success_edit_requisition
   - text_success_reject
   - text_total_requisitions

💡 Suggested Arabic Additions:
   - button_add_requisition = ""  # TODO: ترجمة عربية
   - button_execute = ""  # TODO: ترجمة عربية
   - button_save = ""  # TODO: ترجمة عربية
   - button_view_quotations = ""  # TODO: ترجمة عربية
   - column_action = ""  # TODO: ترجمة عربية
   - column_branch = ""  # TODO: ترجمة عربية
   - column_date_added = ""  # TODO: ترجمة عربية
   - column_description = ""  # TODO: ترجمة عربية
   - column_product = ""  # TODO: ترجمة عربية
   - column_quantity = ""  # TODO: ترجمة عربية
   - column_req_number = ""  # TODO: ترجمة عربية
   - column_requisition_id = ""  # TODO: ترجمة عربية
   - column_status = ""  # TODO: ترجمة عربية
   - column_total = ""  # TODO: ترجمة عربية
   - column_unit = ""  # TODO: ترجمة عربية
   - column_unit_price = ""  # TODO: ترجمة عربية
   - column_user_groups = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_invalid_status_transition = ""  # TODO: ترجمة عربية
   - error_loading_form = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - error_quotation_not_found = ""  # TODO: ترجمة عربية
   - error_reason_required = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - text_add_item = ""  # TODO: ترجمة عربية
   - text_add_requisition = ""  # TODO: ترجمة عربية
   - text_all_statuses = ""  # TODO: ترجمة عربية
   - text_approve_selected = ""  # TODO: ترجمة عربية
   - text_approved_requisitions = ""  # TODO: ترجمة عربية
   - text_close = ""  # TODO: ترجمة عربية
   - text_confirm_approve = ""  # TODO: ترجمة عربية
   - text_confirm_delete = ""  # TODO: ترجمة عربية
   - text_delete_selected = ""  # TODO: ترجمة عربية
   - text_filter_date_end = ""  # TODO: ترجمة عربية
   - text_filter_date_start = ""  # TODO: ترجمة عربية
   - text_filter_status = ""  # TODO: ترجمة عربية
   - text_history_status_change = ""  # TODO: ترجمة عربية
   - text_pagination = ""  # TODO: ترجمة عربية
   - text_pending_requisitions = ""  # TODO: ترجمة عربية
   - text_prompt_reject_reason = ""  # TODO: ترجمة عربية
   - text_reason = ""  # TODO: ترجمة عربية
   - text_refresh_list = ""  # TODO: ترجمة عربية
   - text_reject_selected = ""  # TODO: ترجمة عربية
   - text_rejected_requisitions = ""  # TODO: ترجمة عربية
   - text_req_number = ""  # TODO: ترجمة عربية
   - text_requisition_list = ""  # TODO: ترجمة عربية
   - text_select_action = ""  # TODO: ترجمة عربية
   - text_select_product = ""  # TODO: ترجمة عربية
   - text_status_approved = ""  # TODO: ترجمة عربية
   - text_status_cancelled = ""  # TODO: ترجمة عربية
   - text_status_converted = ""  # TODO: ترجمة عربية
   - text_status_draft = ""  # TODO: ترجمة عربية
   - text_status_pending = ""  # TODO: ترجمة عربية
   - text_status_rejected = ""  # TODO: ترجمة عربية
   - text_success_add_requisition = ""  # TODO: ترجمة عربية
   - text_success_approve = ""  # TODO: ترجمة عربية
   - text_success_delete_requisition = ""  # TODO: ترجمة عربية
   - text_success_edit_requisition = ""  # TODO: ترجمة عربية
   - text_success_reject = ""  # TODO: ترجمة عربية
   - text_total_requisitions = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_add_requisition = ""  # TODO: English translation
   - button_execute = ""  # TODO: English translation
   - button_save = ""  # TODO: English translation
   - button_view_quotations = ""  # TODO: English translation
   - column_action = ""  # TODO: English translation
   - column_branch = ""  # TODO: English translation
   - column_date_added = ""  # TODO: English translation
   - column_description = ""  # TODO: English translation
   - column_product = ""  # TODO: English translation
   - column_quantity = ""  # TODO: English translation
   - column_req_number = ""  # TODO: English translation
   - column_requisition_id = ""  # TODO: English translation
   - column_status = ""  # TODO: English translation
   - column_total = ""  # TODO: English translation
   - column_unit = ""  # TODO: English translation
   - column_unit_price = ""  # TODO: English translation
   - column_user_groups = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - error_invalid_status_transition = ""  # TODO: English translation
   - error_loading_form = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - error_quotation_not_found = ""  # TODO: English translation
   - error_reason_required = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - text_add_item = ""  # TODO: English translation
   - text_add_requisition = ""  # TODO: English translation
   - text_all_statuses = ""  # TODO: English translation
   - text_approve_selected = ""  # TODO: English translation
   - text_approved_requisitions = ""  # TODO: English translation
   - text_close = ""  # TODO: English translation
   - text_confirm_approve = ""  # TODO: English translation
   - text_confirm_delete = ""  # TODO: English translation
   - text_delete_selected = ""  # TODO: English translation
   - text_filter_date_end = ""  # TODO: English translation
   - text_filter_date_start = ""  # TODO: English translation
   - text_filter_status = ""  # TODO: English translation
   - text_history_status_change = ""  # TODO: English translation
   - text_pagination = ""  # TODO: English translation
   - text_pending_requisitions = ""  # TODO: English translation
   - text_prompt_reject_reason = ""  # TODO: English translation
   - text_reason = ""  # TODO: English translation
   - text_refresh_list = ""  # TODO: English translation
   - text_reject_selected = ""  # TODO: English translation
   - text_rejected_requisitions = ""  # TODO: English translation
   - text_req_number = ""  # TODO: English translation
   - text_requisition_list = ""  # TODO: English translation
   - text_select_action = ""  # TODO: English translation
   - text_select_product = ""  # TODO: English translation
   - text_status_approved = ""  # TODO: English translation
   - text_status_cancelled = ""  # TODO: English translation
   - text_status_converted = ""  # TODO: English translation
   - text_status_draft = ""  # TODO: English translation
   - text_status_pending = ""  # TODO: English translation
   - text_status_rejected = ""  # TODO: English translation
   - text_success_add_requisition = ""  # TODO: English translation
   - text_success_approve = ""  # TODO: English translation
   - text_success_delete_requisition = ""  # TODO: English translation
   - text_success_edit_requisition = ""  # TODO: English translation
   - text_success_reject = ""  # TODO: English translation
   - text_total_requisitions = ""  # TODO: English translation
