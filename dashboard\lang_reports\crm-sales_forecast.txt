📄 Route: crm/sales_forecast
📂 Controller: controller\crm\sales_forecast.php
🧱 Models used (2):
   - crm/sales_forecast
   - tool/activity_log
🎨 Twig templates (1):
   - view\template\crm\sales_forecast.twig
🈯 Arabic Language Files (1):
   - language\ar\crm\sales_forecast.php
🇬🇧 English Language Files (0):
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - date_format_short
   - error_algorithm_required
   - error_auto_generate
   - error_not_found
   - error_permission
   - heading_title
   - heading_title_analytics
   - heading_title_create
   - heading_title_scenarios
   - heading_title_view
   - text_auto_all
   - text_auto_best
   - text_auto_ensemble
   - text_factor_competition
   - text_factor_economic
   - text_factor_market
   - text_factor_marketing
   - text_factor_seasonality
   - text_home
   - text_method_arima
   - text_method_exponential
   - text_method_linear
   - text_method_moving_avg
   - text_method_neural
   - text_method_seasonal
   - text_period_daily
   - text_period_monthly
   - text_period_quarterly
   - text_period_weekly
   - text_period_yearly
   - text_scenario_custom
   - text_scenario_optimistic
   - text_scenario_pessimistic
   - text_scenario_realistic
   - text_success_auto_generate
   - text_success_create
   - text_type_customers
   - text_type_orders
   - text_type_revenue
   - text_type_units

❌ Missing in Arabic:
   - date_format_short
   - error_algorithm_required
   - error_auto_generate
   - error_not_found
   - error_permission
   - heading_title
   - heading_title_analytics
   - heading_title_create
   - heading_title_scenarios
   - heading_title_view
   - text_auto_all
   - text_auto_best
   - text_auto_ensemble
   - text_factor_competition
   - text_factor_economic
   - text_factor_market
   - text_factor_marketing
   - text_factor_seasonality
   - text_home
   - text_method_arima
   - text_method_exponential
   - text_method_linear
   - text_method_moving_avg
   - text_method_neural
   - text_method_seasonal
   - text_period_daily
   - text_period_monthly
   - text_period_quarterly
   - text_period_weekly
   - text_period_yearly
   - text_scenario_custom
   - text_scenario_optimistic
   - text_scenario_pessimistic
   - text_scenario_realistic
   - text_success_auto_generate
   - text_success_create
   - text_type_customers
   - text_type_orders
   - text_type_revenue
   - text_type_units

❌ Missing in English:
   - date_format_short
   - error_algorithm_required
   - error_auto_generate
   - error_not_found
   - error_permission
   - heading_title
   - heading_title_analytics
   - heading_title_create
   - heading_title_scenarios
   - heading_title_view
   - text_auto_all
   - text_auto_best
   - text_auto_ensemble
   - text_factor_competition
   - text_factor_economic
   - text_factor_market
   - text_factor_marketing
   - text_factor_seasonality
   - text_home
   - text_method_arima
   - text_method_exponential
   - text_method_linear
   - text_method_moving_avg
   - text_method_neural
   - text_method_seasonal
   - text_period_daily
   - text_period_monthly
   - text_period_quarterly
   - text_period_weekly
   - text_period_yearly
   - text_scenario_custom
   - text_scenario_optimistic
   - text_scenario_pessimistic
   - text_scenario_realistic
   - text_success_auto_generate
   - text_success_create
   - text_type_customers
   - text_type_orders
   - text_type_revenue
   - text_type_units

💡 Suggested Arabic Additions:
   - date_format_short = ""  # TODO: ترجمة عربية
   - error_algorithm_required = ""  # TODO: ترجمة عربية
   - error_auto_generate = ""  # TODO: ترجمة عربية
   - error_not_found = ""  # TODO: ترجمة عربية
   - error_permission = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - heading_title_analytics = ""  # TODO: ترجمة عربية
   - heading_title_create = ""  # TODO: ترجمة عربية
   - heading_title_scenarios = ""  # TODO: ترجمة عربية
   - heading_title_view = ""  # TODO: ترجمة عربية
   - text_auto_all = ""  # TODO: ترجمة عربية
   - text_auto_best = ""  # TODO: ترجمة عربية
   - text_auto_ensemble = ""  # TODO: ترجمة عربية
   - text_factor_competition = ""  # TODO: ترجمة عربية
   - text_factor_economic = ""  # TODO: ترجمة عربية
   - text_factor_market = ""  # TODO: ترجمة عربية
   - text_factor_marketing = ""  # TODO: ترجمة عربية
   - text_factor_seasonality = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_method_arima = ""  # TODO: ترجمة عربية
   - text_method_exponential = ""  # TODO: ترجمة عربية
   - text_method_linear = ""  # TODO: ترجمة عربية
   - text_method_moving_avg = ""  # TODO: ترجمة عربية
   - text_method_neural = ""  # TODO: ترجمة عربية
   - text_method_seasonal = ""  # TODO: ترجمة عربية
   - text_period_daily = ""  # TODO: ترجمة عربية
   - text_period_monthly = ""  # TODO: ترجمة عربية
   - text_period_quarterly = ""  # TODO: ترجمة عربية
   - text_period_weekly = ""  # TODO: ترجمة عربية
   - text_period_yearly = ""  # TODO: ترجمة عربية
   - text_scenario_custom = ""  # TODO: ترجمة عربية
   - text_scenario_optimistic = ""  # TODO: ترجمة عربية
   - text_scenario_pessimistic = ""  # TODO: ترجمة عربية
   - text_scenario_realistic = ""  # TODO: ترجمة عربية
   - text_success_auto_generate = ""  # TODO: ترجمة عربية
   - text_success_create = ""  # TODO: ترجمة عربية
   - text_type_customers = ""  # TODO: ترجمة عربية
   - text_type_orders = ""  # TODO: ترجمة عربية
   - text_type_revenue = ""  # TODO: ترجمة عربية
   - text_type_units = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - date_format_short = ""  # TODO: English translation
   - error_algorithm_required = ""  # TODO: English translation
   - error_auto_generate = ""  # TODO: English translation
   - error_not_found = ""  # TODO: English translation
   - error_permission = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - heading_title_analytics = ""  # TODO: English translation
   - heading_title_create = ""  # TODO: English translation
   - heading_title_scenarios = ""  # TODO: English translation
   - heading_title_view = ""  # TODO: English translation
   - text_auto_all = ""  # TODO: English translation
   - text_auto_best = ""  # TODO: English translation
   - text_auto_ensemble = ""  # TODO: English translation
   - text_factor_competition = ""  # TODO: English translation
   - text_factor_economic = ""  # TODO: English translation
   - text_factor_market = ""  # TODO: English translation
   - text_factor_marketing = ""  # TODO: English translation
   - text_factor_seasonality = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_method_arima = ""  # TODO: English translation
   - text_method_exponential = ""  # TODO: English translation
   - text_method_linear = ""  # TODO: English translation
   - text_method_moving_avg = ""  # TODO: English translation
   - text_method_neural = ""  # TODO: English translation
   - text_method_seasonal = ""  # TODO: English translation
   - text_period_daily = ""  # TODO: English translation
   - text_period_monthly = ""  # TODO: English translation
   - text_period_quarterly = ""  # TODO: English translation
   - text_period_weekly = ""  # TODO: English translation
   - text_period_yearly = ""  # TODO: English translation
   - text_scenario_custom = ""  # TODO: English translation
   - text_scenario_optimistic = ""  # TODO: English translation
   - text_scenario_pessimistic = ""  # TODO: English translation
   - text_scenario_realistic = ""  # TODO: English translation
   - text_success_auto_generate = ""  # TODO: English translation
   - text_success_create = ""  # TODO: English translation
   - text_type_customers = ""  # TODO: English translation
   - text_type_orders = ""  # TODO: English translation
   - text_type_revenue = ""  # TODO: English translation
   - text_type_units = ""  # TODO: English translation
