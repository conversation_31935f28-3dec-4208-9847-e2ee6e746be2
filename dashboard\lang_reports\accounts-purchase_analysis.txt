📄 Route: accounts/purchase_analysis
📂 Controller: controller\accounts\purchase_analysis.php
🧱 Models used (2):
   - accounts/purchase_analysis
   - core/central_service_manager
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\purchase_analysis.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\purchase_analysis.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - heading_title
   - text_home
   - text_list
   - text_no_results

❌ Missing in Arabic:
   - heading_title
   - text_home
   - text_list
   - text_no_results

❌ Missing in English:
   - heading_title
   - text_home
   - text_list
   - text_no_results

💡 Suggested Arabic Additions:
   - heading_title = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_list = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - heading_title = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_list = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
