📄 Route: accounts/vat_report
📂 Controller: controller\accounts\vat_report.php
🧱 Models used (3):
   - accounts/vat_report
   - core/central_service_manager
   - localisation/branch
🎨 Twig templates (0):
🈯 Arabic Language Files (1):
   - language\ar\accounts\vat_report.php
🇬🇧 English Language Files (1):
   - language\en-gb\accounts\vat_report.php
------------------------------------------------------------
📊 Language Usage Analysis:

✅ Used Variables:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_end_required
   - error_date_range
   - error_date_start_required
   - error_no_analysis_data
   - error_no_data
   - error_no_data_export
   - heading_title
   - print_title
   - text_advanced_analysis
   - text_analysis_view
   - text_form
   - text_from
   - text_home
   - text_net_vat
   - text_no_results
   - text_period
   - text_success_analysis
   - text_success_generate
   - text_to
   - text_total_vat_purchases
   - text_total_vat_sales
   - text_vat_purchases
   - text_vat_report
   - text_vat_return
   - text_vat_return_submitted
   - text_vat_sales

❌ Missing in Arabic:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_end_required
   - error_date_range
   - error_date_start_required
   - error_no_analysis_data
   - error_no_data
   - error_no_data_export
   - heading_title
   - print_title
   - text_advanced_analysis
   - text_analysis_view
   - text_form
   - text_from
   - text_home
   - text_net_vat
   - text_no_results
   - text_period
   - text_success_analysis
   - text_success_generate
   - text_to
   - text_total_vat_purchases
   - text_total_vat_sales
   - text_vat_purchases
   - text_vat_report
   - text_vat_return
   - text_vat_return_submitted
   - text_vat_sales

❌ Missing in English:
   - button_filter
   - code
   - date_format_short
   - direction
   - entry_date_end
   - entry_date_start
   - error_date_end_required
   - error_date_range
   - error_date_start_required
   - error_no_analysis_data
   - error_no_data
   - error_no_data_export
   - heading_title
   - print_title
   - text_advanced_analysis
   - text_analysis_view
   - text_form
   - text_from
   - text_home
   - text_net_vat
   - text_no_results
   - text_period
   - text_success_analysis
   - text_success_generate
   - text_to
   - text_total_vat_purchases
   - text_total_vat_sales
   - text_vat_purchases
   - text_vat_report
   - text_vat_return
   - text_vat_return_submitted
   - text_vat_sales

💡 Suggested Arabic Additions:
   - button_filter = ""  # TODO: ترجمة عربية
   - code = ""  # TODO: ترجمة عربية
   - date_format_short = ""  # TODO: ترجمة عربية
   - direction = ""  # TODO: ترجمة عربية
   - entry_date_end = ""  # TODO: ترجمة عربية
   - entry_date_start = ""  # TODO: ترجمة عربية
   - error_date_end_required = ""  # TODO: ترجمة عربية
   - error_date_range = ""  # TODO: ترجمة عربية
   - error_date_start_required = ""  # TODO: ترجمة عربية
   - error_no_analysis_data = ""  # TODO: ترجمة عربية
   - error_no_data = ""  # TODO: ترجمة عربية
   - error_no_data_export = ""  # TODO: ترجمة عربية
   - heading_title = ""  # TODO: ترجمة عربية
   - print_title = ""  # TODO: ترجمة عربية
   - text_advanced_analysis = ""  # TODO: ترجمة عربية
   - text_analysis_view = ""  # TODO: ترجمة عربية
   - text_form = ""  # TODO: ترجمة عربية
   - text_from = ""  # TODO: ترجمة عربية
   - text_home = ""  # TODO: ترجمة عربية
   - text_net_vat = ""  # TODO: ترجمة عربية
   - text_no_results = ""  # TODO: ترجمة عربية
   - text_period = ""  # TODO: ترجمة عربية
   - text_success_analysis = ""  # TODO: ترجمة عربية
   - text_success_generate = ""  # TODO: ترجمة عربية
   - text_to = ""  # TODO: ترجمة عربية
   - text_total_vat_purchases = ""  # TODO: ترجمة عربية
   - text_total_vat_sales = ""  # TODO: ترجمة عربية
   - text_vat_purchases = ""  # TODO: ترجمة عربية
   - text_vat_report = ""  # TODO: ترجمة عربية
   - text_vat_return = ""  # TODO: ترجمة عربية
   - text_vat_return_submitted = ""  # TODO: ترجمة عربية
   - text_vat_sales = ""  # TODO: ترجمة عربية

💡 Suggested English Additions:
   - button_filter = ""  # TODO: English translation
   - code = ""  # TODO: English translation
   - date_format_short = ""  # TODO: English translation
   - direction = ""  # TODO: English translation
   - entry_date_end = ""  # TODO: English translation
   - entry_date_start = ""  # TODO: English translation
   - error_date_end_required = ""  # TODO: English translation
   - error_date_range = ""  # TODO: English translation
   - error_date_start_required = ""  # TODO: English translation
   - error_no_analysis_data = ""  # TODO: English translation
   - error_no_data = ""  # TODO: English translation
   - error_no_data_export = ""  # TODO: English translation
   - heading_title = ""  # TODO: English translation
   - print_title = ""  # TODO: English translation
   - text_advanced_analysis = ""  # TODO: English translation
   - text_analysis_view = ""  # TODO: English translation
   - text_form = ""  # TODO: English translation
   - text_from = ""  # TODO: English translation
   - text_home = ""  # TODO: English translation
   - text_net_vat = ""  # TODO: English translation
   - text_no_results = ""  # TODO: English translation
   - text_period = ""  # TODO: English translation
   - text_success_analysis = ""  # TODO: English translation
   - text_success_generate = ""  # TODO: English translation
   - text_to = ""  # TODO: English translation
   - text_total_vat_purchases = ""  # TODO: English translation
   - text_total_vat_sales = ""  # TODO: English translation
   - text_vat_purchases = ""  # TODO: English translation
   - text_vat_report = ""  # TODO: English translation
   - text_vat_return = ""  # TODO: English translation
   - text_vat_return_submitted = ""  # TODO: English translation
   - text_vat_sales = ""  # TODO: English translation
